{"customers": [{"id": "cust_001", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+84 ***********", "company": "Tech Solutions Vietnam", "position": "CEO", "industry": "Technology", "location": "Ho Chi Minh City, Vietnam", "website": "https://techsolutions.vn", "source": "Website Form", "status": "qualified", "notes": "Interested in enterprise solutions. Follow up next week.", "tags": ["enterprise", "high-value", "technology"], "createdAt": "2024-01-15T09:00:00Z", "updatedAt": "2024-01-20T14:30:00Z", "lastContact": "2024-01-20T14:30:00Z", "score": 85}, {"id": "cust_002", "firstName": "Tran", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+84 ***********", "company": "Vietnam Manufacturing Co.", "position": "Operations Manager", "industry": "Manufacturing", "location": "Hanoi, Vietnam", "website": "https://vnmanufacturing.com", "source": "Trade Show", "status": "contacted", "notes": "Needs cost analysis for automation solutions.", "tags": ["manufacturing", "automation", "cost-sensitive"], "createdAt": "2024-01-18T11:15:00Z", "updatedAt": "2024-01-22T16:45:00Z", "lastContact": "2024-01-22T16:45:00Z", "score": 72}, {"id": "cust_003", "firstName": "Le", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+84 ***********", "company": "Retail Chain Vietnam", "position": "IT Director", "industry": "Retail", "location": "Da <PERSON>g, Vietnam", "website": "https://retailchain.vn", "source": "LinkedIn", "status": "new", "notes": "Exploring digital transformation options.", "tags": ["retail", "digital-transformation", "multi-location"], "createdAt": "2024-01-25T08:30:00Z", "updatedAt": "2024-01-25T08:30:00Z", "lastContact": "", "score": 68}, {"id": "cust_004", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+84 ***********", "company": "Healthcare Solutions Vietnam", "position": "CTO", "industry": "Healthcare", "location": "Ho Chi Minh City, Vietnam", "website": "https://healthcarevn.com", "source": "Referral", "status": "converted", "notes": "Successfully implemented patient management system.", "tags": ["healthcare", "patient-management", "converted"], "createdAt": "2024-01-10T10:00:00Z", "updatedAt": "2024-01-30T12:00:00Z", "lastContact": "2024-01-30T12:00:00Z", "score": 95}, {"id": "cust_005", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+84 ***********", "company": "Logistics Express Vietnam", "position": "General Manager", "industry": "Logistics", "location": "Hai Phong, Vietnam", "website": "https://logisticsexpress.vn", "source": "Cold Email", "status": "lost", "notes": "Budget constraints. May revisit next quarter.", "tags": ["logistics", "budget-constraints", "future-opportunity"], "createdAt": "2024-01-12T14:20:00Z", "updatedAt": "2024-01-28T09:15:00Z", "lastContact": "2024-01-28T09:15:00Z", "score": 45}], "forms": [{"id": "form_001", "name": "Website Contact Form", "description": "Main contact form for website visitors", "fields": [{"id": "field_001", "name": "firstName", "label": "First Name", "type": "text", "required": true, "placeholder": "Enter your first name"}, {"id": "field_002", "name": "lastName", "label": "Last Name", "type": "text", "required": true, "placeholder": "Enter your last name"}, {"id": "field_003", "name": "email", "label": "Email Address", "type": "email", "required": true, "placeholder": "Enter your email address"}, {"id": "field_004", "name": "phone", "label": "Phone Number", "type": "phone", "required": false, "placeholder": "Enter your phone number"}, {"id": "field_005", "name": "company", "label": "Company", "type": "text", "required": true, "placeholder": "Enter your company name"}, {"id": "field_006", "name": "industry", "label": "Industry", "type": "select", "required": true, "options": ["Technology", "Manufacturing", "Healthcare", "Retail", "Logistics", "Finance", "Education", "Other"]}, {"id": "field_007", "name": "message", "label": "Message", "type": "textarea", "required": true, "placeholder": "Tell us about your requirements"}], "isActive": true, "submissions": 127, "createdAt": "2024-01-01T00:00:00Z"}, {"id": "form_002", "name": "Trade Show Lead Capture", "description": "Quick lead capture form for trade shows and events", "fields": [{"id": "field_008", "name": "fullName", "label": "Full Name", "type": "text", "required": true, "placeholder": "Enter your full name"}, {"id": "field_009", "name": "email", "label": "Email", "type": "email", "required": true, "placeholder": "Enter your email"}, {"id": "field_010", "name": "company", "label": "Company", "type": "text", "required": true, "placeholder": "Company name"}, {"id": "field_011", "name": "position", "label": "Position", "type": "text", "required": false, "placeholder": "Your position"}, {"id": "field_012", "name": "interest", "label": "Interest Level", "type": "select", "required": true, "options": ["High", "Medium", "Low", "Just Browsing"]}, {"id": "field_013", "name": "followUp", "label": "Follow-up Preference", "type": "select", "required": true, "options": ["Email", "Phone", "LinkedIn", "No Follow-up"]}], "isActive": true, "submissions": 89, "createdAt": "2024-01-05T00:00:00Z"}, {"id": "form_003", "name": "Product Demo Request", "description": "Form for requesting product demonstrations", "fields": [{"id": "field_014", "name": "contactName", "label": "Contact Name", "type": "text", "required": true, "placeholder": "Your name"}, {"id": "field_015", "name": "email", "label": "Email", "type": "email", "required": true, "placeholder": "Your email"}, {"id": "field_016", "name": "phone", "label": "Phone", "type": "phone", "required": true, "placeholder": "Your phone number"}, {"id": "field_017", "name": "companySize", "label": "Company Size", "type": "select", "required": true, "options": ["1-10 employees", "11-50 employees", "51-200 employees", "201-1000 employees", "1000+ employees"]}, {"id": "field_018", "name": "timeline", "label": "Implementation Timeline", "type": "select", "required": true, "options": ["Immediate", "Within 3 months", "3-6 months", "6-12 months", "Just exploring"]}, {"id": "field_019", "name": "budget", "label": "Budget Range", "type": "select", "required": false, "options": ["Under $10K", "$10K-$50K", "$50K-$100K", "$100K-$500K", "$500K+", "Not determined"]}, {"id": "field_020", "name": "requirements", "label": "Specific Requirements", "type": "textarea", "required": false, "placeholder": "Describe your specific needs and requirements"}], "isActive": true, "submissions": 45, "createdAt": "2024-01-08T00:00:00Z"}], "analytics": {"totalCustomers": 5, "activeForms": 3, "conversionRate": 20, "averageScore": 73, "statusDistribution": {"new": 1, "contacted": 1, "qualified": 1, "converted": 1, "lost": 1}, "sourceDistribution": {"Website Form": 1, "Trade Show": 1, "LinkedIn": 1, "Referral": 1, "Cold Email": 1}, "industryDistribution": {"Technology": 1, "Manufacturing": 1, "Retail": 1, "Healthcare": 1, "Logistics": 1}}, "settings": {"autoAssignLeads": true, "emailNotifications": true, "scoreThreshold": 70, "autoFollowUpDays": 3, "duplicateDetection": true, "dataRetentionDays": 365}}