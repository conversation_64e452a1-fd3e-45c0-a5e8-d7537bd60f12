{"modules": [{"id": "booking", "name": "Booking", "description": "Book flights, hotels, and transportation for business travel", "icon": "✈️", "features": ["Flight booking", "Hotel reservations", "Transportation booking", "Group travel management"]}, {"id": "policy", "name": "Travel Policy", "description": "Manage and enforce corporate travel policies", "icon": "📋", "features": ["Policy configuration", "Approval workflows", "Budget controls", "Compliance monitoring"]}, {"id": "reporting", "name": "Expense Reports", "description": "Create and manage expense reports for business travel", "icon": "💰", "features": ["Automated expense capture", "Receipt management", "Report generation", "Analytics dashboard"]}, {"id": "integration", "name": "System Integrations", "description": "Connect TMS with other business systems", "icon": "🔄", "features": ["ERP integration", "Single Sign-On (SSO)", "API connectivity", "Data synchronization"]}]}