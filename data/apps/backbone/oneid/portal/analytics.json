{"lastUpdated": "2024-03-15T10:00:00.000Z", "overview": {"totalCompanies": 10, "totalUsers": 45, "totalRevenue": 125000, "totalApiCalls": 1250000, "growthRate": 18.5, "activeRate": 93.3}, "consumption": {"daily": [{"date": "2024-03-08", "apiCalls": 180000, "storage": 2.1, "bandwidth": 22.5, "connections": 320}, {"date": "2024-03-09", "apiCalls": 195000, "storage": 2.2, "bandwidth": 24.8, "connections": 342}, {"date": "2024-03-10", "apiCalls": 210000, "storage": 2.3, "bandwidth": 26.2, "connections": 358}, {"date": "2024-03-11", "apiCalls": 225000, "storage": 2.3, "bandwidth": 28.1, "connections": 375}, {"date": "2024-03-12", "apiCalls": 240000, "storage": 2.4, "bandwidth": 30.5, "connections": 392}, {"date": "2024-03-13", "apiCalls": 165000, "storage": 2.4, "bandwidth": 20.8, "connections": 285}, {"date": "2024-03-14", "apiCalls": 145000, "storage": 2.4, "bandwidth": 18.3, "connections": 258}], "monthly": [{"month": "2024-01", "apiCalls": 850000, "storage": 1.8, "bandwidth": 120.5, "cost": 3200}, {"month": "2024-02", "apiCalls": 920000, "storage": 2.0, "bandwidth": 135.2, "cost": 3450}, {"month": "2024-03", "apiCalls": 1250000, "storage": 2.4, "bandwidth": 156.7, "cost": 4650}], "byCompany": [{"companyId": "comp_005_retailmax", "name": "RetailMax Corporation", "apiCalls": 450000, "storage": 0.8, "bandwidth": 65.2, "cost": 1250, "percentage": 36}, {"companyId": "comp_001_techcorp", "name": "TechCorp Solutions", "apiCalls": 380000, "storage": 0.6, "bandwidth": 48.5, "cost": 980, "percentage": 30.4}, {"companyId": "comp_002_healthplus", "name": "HealthPlus Medical Group", "apiCalls": 220000, "storage": 0.4, "bandwidth": 28.3, "cost": 650, "percentage": 17.6}, {"companyId": "comp_003_greenfinance", "name": "Green Finance Solutions", "apiCalls": 125000, "storage": 0.3, "bandwidth": 18.7, "cost": 420, "percentage": 10}, {"companyId": "comp_004_ed<PERSON><PERSON>n", "name": "EduLearn Academy", "apiCalls": 75000, "storage": 0.3, "bandwidth": 12.0, "cost": 280, "percentage": 6}]}, "growth": {"companies": [{"month": "2024-01", "total": 5, "new": 1, "growth": 25}, {"month": "2024-02", "total": 6, "new": 1, "growth": 20}, {"month": "2024-03", "total": 10, "new": 4, "growth": 66.7}], "users": [{"month": "2024-01", "total": 25, "new": 8, "growth": 47.1}, {"month": "2024-02", "total": 28, "new": 3, "growth": 12}, {"month": "2024-03", "total": 45, "new": 17, "growth": 60.7}], "revenue": [{"month": "2024-01", "total": 85000, "new": 15000, "growth": 21.4}, {"month": "2024-02", "total": 92000, "new": 7000, "growth": 8.2}, {"month": "2024-03", "total": 125000, "new": 33000, "growth": 35.9}]}, "reports": {"available": [{"id": "company-performance", "name": "Company Performance Report", "description": "Detailed analysis of company metrics and KPIs", "lastGenerated": "2024-03-14T09:00:00.000Z", "format": "PDF"}, {"id": "user-activity", "name": "User Activity Report", "description": "User engagement and activity patterns", "lastGenerated": "2024-03-14T09:00:00.000Z", "format": "Excel"}, {"id": "resource-consumption", "name": "Resource Consumption Report", "description": "API usage, storage, and bandwidth analysis", "lastGenerated": "2024-03-14T09:00:00.000Z", "format": "PDF"}, {"id": "financial-summary", "name": "Financial Summary Report", "description": "Revenue, subscriptions, and billing analysis", "lastGenerated": "2024-03-14T09:00:00.000Z", "format": "Excel"}], "scheduled": [{"id": "weekly-summary", "name": "Weekly Summary", "frequency": "weekly", "nextRun": "2024-03-18T09:00:00.000Z", "recipients": ["<EMAIL>"]}, {"id": "monthly-detailed", "name": "Monthly Detailed Report", "frequency": "monthly", "nextRun": "2024-04-01T09:00:00.000Z", "recipients": ["<EMAIL>", "<EMAIL>"]}]}, "alerts": {"active": [{"id": "high-api-usage", "type": "warning", "message": "API usage is approaching 80% of monthly limit", "company": "comp_005_retailmax", "timestamp": "2024-03-14T14:30:00.000Z", "acknowledged": false}, {"id": "service-degraded", "type": "error", "message": "Analytics Suite experiencing performance issues", "service": "analytics-suite", "timestamp": "2024-03-14T12:15:00.000Z", "acknowledged": true}], "resolved": [{"id": "storage-limit", "type": "warning", "message": "Storage usage exceeded 90% for TechCorp Solutions", "company": "comp_001_techcorp", "timestamp": "2024-03-12T10:00:00.000Z", "resolvedAt": "2024-03-13T08:30:00.000Z"}]}}