# WISE Users OneID Integration

This documentation covers the scripts and middleware created to integrate WISE users with the OneID authentication system and grant them access to the eduwise platform.

## Overview

The integration consists of two main components:

1. **User Migration Script** (`add-wise-users-to-oneid.ts`) - Migrates existing WISE users to OneID
2. **Access Control Middleware** (`middleware/accessControl.ts`) - Manages access permissions for the eduwise system

## Files Created

### Scripts
- `/src/scripts/add-wise-users-to-oneid.ts` - Main migration script

### Middleware
- `/src/app/web/wise/eduwise/middleware/accessControl.ts` - Access control for eduwise system

## Usage

### 1. Running the User Migration Script

The migration script reads users from `/data/apps/web/wise/eduwise/users.json` and creates them in OneID with appropriate permissions.

```bash
# Navigate to the project root
cd /Users/<USER>/Workspace/abn.green

# Run the migration script
npx tsx src/scripts/add-wise-users-to-oneid.ts
```

#### What the script does:

1. **Initializes OneID system**
2. **Creates eduwise permissions and role**:
   - Permission: `eduwise_access` (module read access)
   - Role: `eduwise_user` (standard user role for eduwise platform)
3. **Processes users in batches**:
   - Filters only active users (status = 1)
   - Creates OneID accounts for each user
   - Assigns the `eduwise_user` role
   - Preserves original WISE data in user metadata
4. **Generates detailed logs** including error reports

#### Default Settings:
- **Default Password**: `WiseEdu2024!` (users should change on first login)
- **Batch Size**: 10 users per batch (to avoid overwhelming the system)
- **Only Active Users**: Only users with `status: 1` are migrated

#### Output:
The script provides real-time progress updates and creates error logs if any users fail to migrate.

### 2. Using the Access Control Middleware

The access control middleware ensures that only users with proper permissions can access eduwise features.

#### Available Functions:

```typescript
import eduwiseAccessControl from '@/app/web/wise/eduwise/middleware/accessControl';

// Check if user has eduwise access
const accessResult = await eduwiseAccessControl.checkEduwiseAccess(request);

// Protect API routes
export const GET = async (req: NextRequest) => {
  return await eduwiseAccessControl.requireEduwiseAccess(req, async (req, user) => {
    // Your protected route logic here
    return NextResponse.json({ message: 'Access granted', user });
  });
};

// Grant access to a user (admin function)
await eduwiseAccessControl.grantEduwiseAccess(userId, 'admin');

// Revoke access from a user (admin function)
await eduwiseAccessControl.revokeEduwiseAccess(userId);
```

#### Client-side Usage:

```typescript
import { useEduwiseAccess } from '@/app/web/wise/eduwise/middleware/accessControl';

function MyComponent() {
  const { checkAccess } = useEduwiseAccess();
  
  const verifyAccess = async () => {
    const result = await checkAccess();
    if (!result.hasAccess) {
      // Redirect to login or show access denied message
    }
  };
}
```

## Integration with Existing System

### Authentication Flow

The eduwise system already integrates with OneID through:

1. **`useOneIDAuth` hook** - Manages authentication state
2. **Header component** - Shows login/logout based on auth status
3. **Layout component** - Handles authentication errors

### Access Control

The new middleware adds an additional layer that checks for specific eduwise permissions:

1. **User must be authenticated** (handled by existing OneID integration)
2. **User must have `eduwise_user` role** (new requirement added by migration script)
3. **User must have `eduwise_access` permission** (alternative to role-based access)

## Permissions Structure

### Created Permissions:
- **Name**: `eduwise_access`
- **Description**: Access to WISE eduwise learning platform
- **Resource**: `module`
- **Action**: `read`
- **Access Level**: `own`

### Created Roles:
- **Name**: `eduwise_user`
- **Description**: Standard user role for WISE eduwise platform
- **Permissions**: [`eduwise_access`]
- **System Role**: `false`

## User Data Migration

### Original WISE User Structure:
```json
{
  "id": "1",
  "username": "example",
  "email": "<EMAIL>",
  "fullName": "Full Name",
  "phone": "0123456789",
  "password": "password123",
  "organization": "Company",
  "business_areas": "Industry",
  "references": "Source",
  "point": 100,
  "status": 1,
  "createdAt": "2024-01-01 00:00:00"
}
```

### OneID User Metadata:
The original WISE data is preserved in the user's metadata:
```json
{
  "originalWiseId": "1",
  "organization": "Company",
  "businessAreas": "Industry", 
  "references": "Source",
  "wisePoints": 100,
  "originalCreatedAt": "2024-01-01 00:00:00",
  "migratedAt": "2024-12-21T10:30:00.000Z",
  "phone": "0123456789"
}
```

## Error Handling and Logging

### Migration Script Errors:
- Detailed error logging with user information
- Error files saved as JSON for review
- Batch processing continues even if individual users fail

### Access Control Errors:
- Graceful handling of authentication failures
- Appropriate HTTP status codes (401, 403, 500)
- Clear error messages for debugging

## Security Considerations

1. **Password Security**: Default password should be changed on first login
2. **Permission Validation**: Multiple checks (role + permission) for access control
3. **Session Management**: Relies on OneID's secure session handling
4. **Audit Trail**: All role assignments are logged with timestamps

## Monitoring and Maintenance

### Key Metrics to Monitor:
- Number of successful user migrations
- Failed login attempts
- Users without proper eduwise permissions
- Access denied attempts

### Regular Maintenance:
- Review error logs from migration
- Update default passwords policy
- Monitor user access patterns
- Regular permission audits

## Troubleshooting

### Common Issues:

1. **Migration fails with "OneID not initialized"**
   - Ensure OneID system is properly configured
   - Check database connections

2. **Users can't access eduwise after migration**
   - Verify user has `eduwise_user` role
   - Check permission assignments
   - Confirm user authentication status

3. **"Permission denied" errors**
   - Verify access control middleware is properly configured
   - Check if eduwise role and permission exist
   - Confirm user role assignments

### Debug Commands:

```bash
# Check OneID system status
npx tsx -e "import { initializeOneID } from './src/app/backbone/oneid'; initializeOneID().then(oneid => console.log('OneID initialized'))"

# List all roles
npx tsx -e "import { initializeOneID } from './src/app/backbone/oneid'; initializeOneID().then(async oneid => { const roles = await oneid.accessControl.listRoles(); console.log(roles.map(r => r.name)); })"

# Check user roles
npx tsx -e "import { initializeOneID } from './src/app/backbone/oneid'; initializeOneID().then(async oneid => { const roles = await oneid.accessControl.getUserRoles('USER_ID'); console.log(roles); })"
```

## Next Steps

1. **Run the migration script** to add all WISE users to OneID
2. **Test authentication flow** with migrated users
3. **Implement access control** in protected eduwise routes
4. **Set up monitoring** for access patterns and errors
5. **Plan user communication** about password changes and new login process

## Support

For issues with this integration, check:
1. Migration error logs (JSON files generated by script)
2. OneID system logs
3. Application error logs
4. Database connectivity

The integration is designed to be backward-compatible with the existing eduwise authentication system while adding the new OneID-based access controls.
