/**
 * UI Functionality Test for Tenant Creation
 * This script tests the actual form submission through the browser
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BASE_URL = 'http://localhost:3001';
const RECEPTION_URL = '/minirent/apps/reception/tenants';
const API_URL = '/api/minirent/tenants';

// Test scenarios
const testScenarios = [
  {
    name: 'Complete Professional Tenant',
    data: {
      name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
      email: '<EMAIL>',
      phone: '**********',
      idNumber: '123456789012',
      dateOfBirth: '1990-01-15',
      gender: 'male',
      nationality: 'Việt Nam',
      hometown: 'Hà Nội',
      occupation: '<PERSON><PERSON> sư phần mềm',
      industry: 'IT',
      company: 'Tech Solutions Ltd',
      emergencyContact: {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        phone: '**********',
        relationship: 'Mẹ'
      },
      vehicleInfo: {
        licensePlate: '30A-12345',
        type: 'xe máy'
      },
      notes: '<PERSON>h<PERSON>ch thuê chuyên nghiệp, có kinh nghiệm thuê trọ'
    },
    expectedFields: ['name', 'email', 'phone', 'idNumber', 'emergencyContact', 'vehicleInfo']
  },
  {
    name: 'Student Tenant',
    data: {
      name: 'Trần Thị Student',
      email: '<EMAIL>',
      phone: '**********',
      idNumber: '987654321098',
      dateOfBirth: '2001-09-18',
      gender: 'female',
      nationality: 'Việt Nam',
      hometown: 'Đà Nẵng',
      occupation: 'Sinh viên',
      industry: 'Giáo dục',
      company: 'Đại học Bách Khoa',
      emergencyContact: {
        name: 'Trần Văn Bố',
        phone: '**********',
        relationship: 'Bố'
      },
      notes: 'Sinh viên năm cuối, gia đình hỗ trợ thanh toán'
    },
    expectedFields: ['name', 'email', 'phone', 'idNumber', 'emergencyContact']
  },
  {
    name: 'Minimal Required Fields',
    data: {
      name: 'Lê Văn Minimal',
      email: '<EMAIL>',
      phone: '**********',
      idNumber: '456789123456'
    },
    expectedFields: ['name', 'email', 'phone', 'idNumber']
  }
];

// Validation test cases
const validationTests = [
  {
    name: 'Empty Required Fields',
    data: {
      name: '',
      email: '',
      phone: '',
      idNumber: ''
    },
    shouldFail: true,
    expectedError: 'Required fields missing',
    expectedFields: []
  },
  {
    name: 'Invalid Email Format',
    data: {
      name: 'Test User',
      email: 'invalid-email-format',
      phone: '**********',
      idNumber: '123456789'
    },
    shouldFail: true,
    expectedError: 'Invalid email format',
    expectedFields: ['name', 'email', 'phone', 'idNumber']
  },
  {
    name: 'Invalid Phone Format',
    data: {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '123', // Too short
      idNumber: '123456789'
    },
    shouldFail: false, // Phone validation might be lenient
    expectedError: 'Invalid phone format',
    expectedFields: ['name', 'email', 'phone', 'idNumber']
  }
];

async function testTenantCreation(testData) {
  try {
    console.log(`🧪 Testing: ${testData.name}`);
    
    const response = await fetch(`${BASE_URL}${API_URL}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData.data),
    });

    const result = await response.json();

    if (!response.ok) {
      if (testData.shouldFail) {
        console.log(`✅ Expected failure: ${result.error || 'Unknown error'}`);
        return { success: true, expected: true, result };
      } else {
        throw new Error(`HTTP ${response.status}: ${result.error || response.statusText}`);
      }
    }

    if (testData.shouldFail) {
      console.log(`⚠️  Expected failure but got success`);
      return { success: false, expected: false, result };
    }

    // Verify expected fields are present
    for (const field of testData.expectedFields) {
      if (field === 'emergencyContact') {
        if (!result.emergencyContact || typeof result.emergencyContact !== 'object') {
          throw new Error(`Missing or invalid emergencyContact field`);
        }
      } else if (field === 'vehicleInfo') {
        if (!result.vehicleInfo || typeof result.vehicleInfo !== 'object') {
          throw new Error(`Missing or invalid vehicleInfo field`);
        }
      } else if (!result[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    console.log(`✅ Success! Created tenant ID: ${result.id}`);
    return { success: true, expected: true, result };

  } catch (error) {
    console.error(`❌ Failed: ${error.message}`);
    return { success: false, expected: false, error: error.message };
  }
}

async function verifyTenantData(tenantId, originalData) {
  try {
    console.log(`🔍 Verifying tenant data for ID: ${tenantId}`);
    
    const response = await fetch(`${BASE_URL}${API_URL}/${tenantId}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const tenant = await response.json();

    // Verify basic fields
    const fieldsToCheck = ['name', 'email', 'phone', 'idNumber'];
    for (const field of fieldsToCheck) {
      if (originalData[field] && tenant[field] !== originalData[field]) {
        throw new Error(`Field mismatch: ${field}. Expected: ${originalData[field]}, Got: ${tenant[field]}`);
      }
    }

    // Verify emergency contact if provided
    if (originalData.emergencyContact && originalData.emergencyContact.name) {
      if (!tenant.emergencyContact || tenant.emergencyContact.name !== originalData.emergencyContact.name) {
        throw new Error(`Emergency contact name mismatch`);
      }
    }

    // Verify vehicle info if provided
    if (originalData.vehicleInfo && originalData.vehicleInfo.licensePlate) {
      if (!tenant.vehicleInfo || tenant.vehicleInfo.licensePlate !== originalData.vehicleInfo.licensePlate) {
        throw new Error(`Vehicle license plate mismatch`);
      }
    }

    console.log(`✅ Data verification passed`);
    return { success: true, tenant };

  } catch (error) {
    console.error(`❌ Data verification failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runUIFunctionalityTests() {
  console.log('🚀 Starting UI Functionality Tests...\n');
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    scenarios: [],
    validations: []
  };

  // Test creation scenarios
  console.log('📝 Testing Creation Scenarios...\n');
  for (const scenario of testScenarios) {
    results.total++;
    const createResult = await testTenantCreation(scenario);
    
    if (createResult.success && createResult.expected) {
      // Verify the created tenant data
      const verifyResult = await verifyTenantData(createResult.result.id, scenario.data);
      
      if (verifyResult.success) {
        results.passed++;
        results.scenarios.push({ name: scenario.name, status: 'PASSED', id: createResult.result.id });
      } else {
        results.failed++;
        results.scenarios.push({ name: scenario.name, status: 'FAILED', error: verifyResult.error });
      }
    } else {
      results.failed++;
      results.scenarios.push({ name: scenario.name, status: 'FAILED', error: createResult.error });
    }
    
    console.log(''); // Add spacing
  }

  // Test validation scenarios
  console.log('🔒 Testing Validation Scenarios...\n');
  for (const validation of validationTests) {
    results.total++;
    const validationResult = await testTenantCreation(validation);
    
    if (validationResult.expected) {
      results.passed++;
      results.validations.push({ name: validation.name, status: 'PASSED' });
    } else {
      results.failed++;
      results.validations.push({ name: validation.name, status: 'FAILED', error: validationResult.error });
    }
    
    console.log(''); // Add spacing
  }

  // Summary
  console.log('📊 Test Summary:');
  console.log(`Total Tests: ${results.total}`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%\n`);

  // Detailed results
  console.log('📋 Detailed Results:');
  console.log('\nCreation Scenarios:');
  results.scenarios.forEach(scenario => {
    const icon = scenario.status === 'PASSED' ? '✅' : '❌';
    console.log(`  ${icon} ${scenario.name}: ${scenario.status}`);
    if (scenario.id) console.log(`     Tenant ID: ${scenario.id}`);
    if (scenario.error) console.log(`     Error: ${scenario.error}`);
  });

  console.log('\nValidation Tests:');
  results.validations.forEach(validation => {
    const icon = validation.status === 'PASSED' ? '✅' : '❌';
    console.log(`  ${icon} ${validation.name}: ${validation.status}`);
    if (validation.error) console.log(`     Error: ${validation.error}`);
  });

  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! The tenant creation functionality is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
  }

  return results;
}

// Run the tests
runUIFunctionalityTests().catch(error => {
  console.error('💥 Test execution failed:', error.message);
  process.exit(1);
});
