import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Domain to path mapping
const domainMappings: Record<string, string> = {
  're.abnasia.org': '/real-estate',
  'cars.abnasia.org': '/cars',
  'farm.abnasia.org': '/farm',
  'games.abnasia.org': '/games',
  'a.abnasia.org': '/appagent',
  'f.abnasia.org': '/farmerapp',
  'cars.localhost': '/cars',
  'farmer.localhost': '/farmerapp',
  'games.localhost': '/games',
  'qr.abnasia.org': '/qr',
  'teome.abnasia.org': '/web/minilanding',
  'dev.abnasia.org': '/web/abnasia',
  'abnasia.org': '/web/abnasia',
  'edu.abnasia.org': '/platforms/edu',
  'wise.abnasia.org': '/web/wise/eduwise',
  'md.abnasia.org': '/tools/markdown/preview'
};

// Paths that always should be accessible without auth
const publicPaths = [
  '/login',
  '/auth/login',
  '/auth/signin',
  '/register',
  '/reset-password',
  '/api/auth',
  '/api/backbone/oneid/auth',
  '/api/farmers',
  '/api/test-farmer',
  '/api/farming-activities',
  '/farmerapp/self-service',
  '/_next',
  '/favicon.ico',
  '/images',
  '/fonts',
  '/sw.js',
  '/workbox',
  '/farmerapp',
  '/games',
  '/books',
  '/book-recommendations',
  '/aikols',
  '/appagent',
  '/workdone',
  '/giapha',
  '/abnhealth',
  '/australia',
  '/gooddeals',
  '/watchdogs',
  '/real-estate',
  '/cars',
  '/manifest.json',
  '/robots.txt',
  '/sitemap.xml',
  '/service-worker.js',
  '/qr',
  '/contests',
  '/edufun',
  '/canvas',
  '/portal',
  '/go',
  '/personalfinance',
  '/families',
  '/minirent',
  '/tools',
  '/vouchers',
  '/bomero',
  '/abncx',
  '/web',
  '/hr',
  '/companies',
  '/tools/okr',
  '/greenos',
  '/edu/english/repeatme',
  '/booksy',
  '/learning',
  '/liveevents',
  '/tools/abncommunication',
  '/exchanges/artx',
  '/exchanges/xapi',
  '/platforms/edu',
  '/platforms/healthcare',
  '/platforms/retails',
  '/platforms',
  '/backbone/oneid/login',
  '/backbone',
  '/ecommerce'
];

// OneID protected paths - these require OneID authentication
const oneIDProtectedPaths = [
  '/web/dev/1'
];

// OneID authentication handler
async function handleOneIDAuth(request: NextRequest, pathname: string) {
  try {
    // Get OneID token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    let token = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log(`[MIDDLEWARE] Found token in Authorization header`);
    } else {
      // Try to get token from cookie
      token = request.cookies.get('oneid-token')?.value;
      if (token) {
        console.log(`[MIDDLEWARE] Found token in cookie`);
      }
    }

    if (!token) {
      console.log(`[MIDDLEWARE] No OneID token found in header or cookie, redirecting to login`);
      console.log(`[MIDDLEWARE] Available cookies:`, request.cookies.getAll().map(c => c.name));
      return redirectToOneIDLogin(request, pathname);
    }

    // For test tokens, do simple verification
    console.log(`[MIDDLEWARE] Verifying token...`);
    try {
      // Try to decode our simple test token
      const tokenData = JSON.parse(Buffer.from(token, 'base64').toString());
      console.log(`[MIDDLEWARE] Token decoded successfully for user:`, tokenData.username);

      // Check if token is expired
      if (tokenData.exp && tokenData.exp < Math.floor(Date.now() / 1000)) {
        console.log(`[MIDDLEWARE] Token expired, redirecting to login`);
        return redirectToOneIDLogin(request, pathname);
      }

      console.log(`[MIDDLEWARE] Token is valid`);
    } catch (error) {
      console.log(`[MIDDLEWARE] Token verification failed:`, error);
      return redirectToOneIDLogin(request, pathname);
    }

    // For test system, allow access if token is valid
    console.log(`[MIDDLEWARE] OneID access granted for ${pathname} (test mode)`);
    return NextResponse.next();

  } catch (error) {
    console.error(`[MIDDLEWARE] OneID auth error:`, error);
    return redirectToOneIDLogin(request, pathname);
  }
}

function redirectToOneIDLogin(request: NextRequest, pathname: string) {
  const baseUrl = getBaseUrl(request);
  const callbackUrl = encodeURIComponent(`${baseUrl}${pathname}${request.nextUrl.search}`);

  // Use common OneID login page for all OneID-protected routes
  const loginUrl = `${baseUrl}/backbone/oneid/login?redirect=${callbackUrl}`;

  console.log(`[MIDDLEWARE] Pathname: ${pathname}, LoginUrl: ${loginUrl}`);
  return NextResponse.redirect(loginUrl);
}

function getBaseUrl(request: NextRequest): string {
  const protocol = request.nextUrl.protocol;
  const host = request.nextUrl.host;
  return `${protocol}//${host}`;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const hostname = request.headers.get('host') || request.nextUrl.hostname;

  console.log(`[MIDDLEWARE] Processing: ${pathname}`);
  const domainWithoutPort = hostname.split(':')[0];
  
  console.log(`\n[MIDDLEWARE] Request: ${hostname}${pathname}`);
  
  // Check if this is an API request or asset request first
  const isApiRequest = pathname.startsWith('/api/');
  const isAssetRequest = pathname.match(/\.(jpg|jpeg|png|gif|svg|css|js|woff|woff2|ttf|eot)$/i);
  
  // Handle domain mappings
  if (domainMappings[domainWithoutPort]) {
    const basePath = domainMappings[domainWithoutPort];
    console.log(`[MIDDLEWARE] Domain mapping: ${domainWithoutPort} -> ${basePath}`);
    
    const newUrl = request.nextUrl.clone();
    
    // Special case for root path
    if (pathname === '/' || pathname === '') {
      newUrl.pathname = basePath;
      console.log(`[MIDDLEWARE] Rewriting: / -> ${basePath}`);
      return NextResponse.rewrite(newUrl);
    }
    
    // If already starts with basePath, don't modify
    if (pathname.startsWith(basePath)) {
      console.log(`[MIDDLEWARE] Path already has prefix: ${pathname}`);
      return NextResponse.next();
    }
    
    // API requests should be passed through without modification
    if (isApiRequest) {
      console.log(`[MIDDLEWARE] API request, passing through: ${pathname}`);
      return NextResponse.next();
    }
    
    // Asset requests should be passed through without modification
    if (isAssetRequest) {
      console.log(`[MIDDLEWARE] Asset request, passing through: ${pathname}`);
      return NextResponse.next();
    }
    
    // Handle login redirections for subdomains
    if (pathname.includes('/login')) {
      console.log(`[MIDDLEWARE] Login path detected: ${pathname}`);
      // Keep on main login path without prefixing with basePath
      if (pathname === '/login' || pathname === '/auth/login') {
        console.log(`[MIDDLEWARE] Using main login page`);
        return NextResponse.next();
      }
    }
    
    // All other paths - prefix with basePath if not already
    newUrl.pathname = `${basePath}${pathname}`;
    console.log(`[MIDDLEWARE] Rewriting: ${pathname} -> ${newUrl.pathname}`);
    return NextResponse.rewrite(newUrl);
  }
  
  // No domain mapping - proceed with standard auth checks
  
  // Check for OneID protected paths FIRST (before public paths check)
  if (oneIDProtectedPaths.some(path => pathname.startsWith(path)) && !pathname.includes('/login')) {
    console.log(`[MIDDLEWARE] OneID protected path detected: ${pathname}`);
    return await handleOneIDAuth(request, pathname);
  }

  // Always allow public paths and API/asset requests
  if (publicPaths.some(path => pathname.startsWith(path)) || isApiRequest || isAssetRequest) {
    console.log(`[MIDDLEWARE] Public path/API/Asset, allowing access: ${pathname}`);
    return NextResponse.next();
  }
  
  // For protected routes, check authentication
  console.log(`[MIDDLEWARE] Protected route, checking auth: ${pathname}`);
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET || 'DEVELOPMENT_SECRET_KEY_DO_NOT_USE_IN_PRODUCTION',
  });
  
  if (!token) {
    // Get protocol and host from request URL
    const protocol = request.nextUrl.protocol;
    const host = request.nextUrl.host;
    
    // Construct the base URL for login
    let baseUrl = process.env.NEXTAUTH_URL;
    if (!baseUrl) {
      // Ensure we have a valid protocol and host
      if (!protocol || !host) {
        console.error('[Middleware] Missing protocol or host for URL construction');
        return NextResponse.next();
      }
      baseUrl = `${protocol}//${host}`;
    }
    
    // Ensure baseUrl is properly formatted
    try {
      // Construct the callback URL 
      const callbackPath = encodeURI(`${pathname}${request.nextUrl.search}`);
      const callbackUrl = `${baseUrl}${callbackPath}`;
      
      // Construct the login URL
      const loginUrl = new URL('/auth/login', baseUrl);
      loginUrl.searchParams.set('callbackUrl', callbackUrl);
      
      console.log(`[Middleware] Redirecting to login. URL: ${loginUrl.toString()}`);
      return NextResponse.redirect(loginUrl);
    } catch (error) {
      console.error('[Middleware] Error constructing redirect URL:', error);
      return NextResponse.next();
    }
  }
  
  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}; 