import { TenantActivity, AdminNote, ServiceRequest, RoomChangeEntry } from '../types';
import { readJsonFile, writeJsonFile } from './fileUtils';

export class ActivityLogger {
  private static async getActivities(): Promise<TenantActivity[]> {
    try {
      return await readJsonFile('tenant-activities.json');
    } catch (error) {
      console.error('Error reading activities:', error);
      return [];
    }
  }

  private static async saveActivities(activities: TenantActivity[]): Promise<void> {
    try {
      await writeJsonFile('tenant-activities.json', activities);
    } catch (error) {
      console.error('Error saving activities:', error);
      throw error;
    }
  }

  static async logActivity(activity: Omit<TenantActivity, 'id' | 'createdAt'>): Promise<TenantActivity> {
    const activities = await this.getActivities();
    
    const newActivity: TenantActivity = {
      ...activity,
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString()
    };

    activities.push(newActivity);
    await this.saveActivities(activities);
    
    return newActivity;
  }

  static async getTenantActivities(
    tenantId: string, 
    viewerRole: 'tenant' | 'reception' | 'manager'
  ): Promise<TenantActivity[]> {
    const activities = await this.getActivities();
    
    return activities
      .filter(activity => activity.tenantId === tenantId)
      .filter(activity => activity.isVisible[viewerRole])
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  static async getAllActivities(viewerRole: 'reception' | 'manager'): Promise<TenantActivity[]> {
    const activities = await this.getActivities();
    
    return activities
      .filter(activity => activity.isVisible[viewerRole])
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // Convenience methods for common activity types
  static async logProfileUpdate(
    tenantId: string,
    field: string,
    oldValue: any,
    newValue: any,
    performedBy: TenantActivity['performedBy']
  ): Promise<TenantActivity> {
    return this.logActivity({
      tenantId,
      type: 'profile_update',
      action: `Updated ${field}`,
      description: `Changed ${field} from "${oldValue}" to "${newValue}"`,
      performedBy,
      metadata: { field, oldValue, newValue },
      isVisible: {
        tenant: true,
        reception: true,
        manager: true
      }
    });
  }

  static async logNoteAdded(
    tenantId: string,
    noteContent: string,
    noteType: string,
    performedBy: TenantActivity['performedBy']
  ): Promise<TenantActivity> {
    return this.logActivity({
      tenantId,
      type: 'note_added',
      action: 'Added note',
      description: `Added ${noteType} note: ${noteContent.substring(0, 100)}${noteContent.length > 100 ? '...' : ''}`,
      performedBy,
      metadata: { noteType, noteContent },
      isVisible: {
        tenant: noteType !== 'private',
        reception: true,
        manager: true
      }
    });
  }

  static async logRoomChange(
    tenantId: string,
    fromRoom: string,
    toRoom: string,
    reason: string,
    performedBy: TenantActivity['performedBy']
  ): Promise<TenantActivity> {
    return this.logActivity({
      tenantId,
      type: 'room_change',
      action: 'Room change requested',
      description: `Requested room change from ${fromRoom} to ${toRoom}. Reason: ${reason}`,
      performedBy,
      metadata: { fromRoom, toRoom, reason },
      isVisible: {
        tenant: true,
        reception: true,
        manager: true
      }
    });
  }

  static async logServiceRequest(
    tenantId: string,
    serviceType: string,
    title: string,
    performedBy: TenantActivity['performedBy']
  ): Promise<TenantActivity> {
    return this.logActivity({
      tenantId,
      type: 'service_request',
      action: 'Service requested',
      description: `Requested ${serviceType} service: ${title}`,
      performedBy,
      metadata: { serviceType, title },
      isVisible: {
        tenant: true,
        reception: true,
        manager: true
      }
    });
  }

  static async logPaymentRecorded(
    tenantId: string,
    amount: number,
    method: string,
    performedBy: TenantActivity['performedBy']
  ): Promise<TenantActivity> {
    return this.logActivity({
      tenantId,
      type: 'payment_recorded',
      action: 'Payment recorded',
      description: `Payment of ${amount.toLocaleString('vi-VN')} VND recorded via ${method}`,
      performedBy,
      metadata: { amount, method },
      isVisible: {
        tenant: true,
        reception: true,
        manager: true
      }
    });
  }

  static async logStatusChange(
    tenantId: string,
    oldStatus: string,
    newStatus: string,
    performedBy: TenantActivity['performedBy']
  ): Promise<TenantActivity> {
    return this.logActivity({
      tenantId,
      type: 'status_change',
      action: 'Status changed',
      description: `Status changed from ${oldStatus} to ${newStatus}`,
      performedBy,
      metadata: { oldStatus, newStatus },
      isVisible: {
        tenant: true,
        reception: true,
        manager: true
      }
    });
  }
}

// Admin Notes Service
export class AdminNotesService {
  private static async getNotes(): Promise<AdminNote[]> {
    try {
      return await readJsonFile('admin-notes.json');
    } catch (error) {
      console.error('Error reading admin notes:', error);
      return [];
    }
  }

  private static async saveNotes(notes: AdminNote[]): Promise<void> {
    try {
      await writeJsonFile('admin-notes.json', notes);
    } catch (error) {
      console.error('Error saving admin notes:', error);
      throw error;
    }
  }

  static async addNote(note: Omit<AdminNote, 'id' | 'createdAt'>): Promise<AdminNote> {
    const notes = await this.getNotes();
    
    const newNote: AdminNote = {
      ...note,
      id: `note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString()
    };

    notes.push(newNote);
    await this.saveNotes(notes);
    
    // Log the activity
    await ActivityLogger.logNoteAdded(
      note.tenantId,
      note.content,
      note.type,
      note.addedBy
    );
    
    return newNote;
  }

  static async getTenantNotes(
    tenantId: string,
    viewerRole: 'reception' | 'manager'
  ): Promise<AdminNote[]> {
    const notes = await this.getNotes();
    
    return notes
      .filter(note => note.tenantId === tenantId)
      .filter(note => !note.isPrivate || viewerRole === 'manager')
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  static async updateNote(
    noteId: string,
    updates: Partial<Pick<AdminNote, 'content' | 'type' | 'isPrivate'>>,
    updatedBy: AdminNote['addedBy']
  ): Promise<AdminNote | null> {
    const notes = await this.getNotes();
    const noteIndex = notes.findIndex(note => note.id === noteId);
    
    if (noteIndex === -1) {
      return null;
    }

    const updatedNote = {
      ...notes[noteIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    notes[noteIndex] = updatedNote;
    await this.saveNotes(notes);
    
    return updatedNote;
  }

  static async deleteNote(noteId: string): Promise<boolean> {
    const notes = await this.getNotes();
    const noteIndex = notes.findIndex(note => note.id === noteId);
    
    if (noteIndex === -1) {
      return false;
    }

    notes.splice(noteIndex, 1);
    await this.saveNotes(notes);
    
    return true;
  }
}
