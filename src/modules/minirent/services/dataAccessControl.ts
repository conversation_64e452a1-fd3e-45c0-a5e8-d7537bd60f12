import { TenantActivity, AdminNote, ServiceRequest, RoomChangeEntry } from '../types';

export type UserRole = 'tenant' | 'reception' | 'manager' | 'admin';

export interface AccessContext {
  userId: string;
  role: UserRole;
  tenantId?: string; // For tenant role, this should be their own tenant ID
}

export class DataAccessControl {
  /**
   * Filter activities based on user role and access permissions
   */
  static filterActivities(
    activities: TenantActivity[], 
    context: AccessContext,
    targetTenantId?: string
  ): TenantActivity[] {
    return activities.filter(activity => {
      // If targeting specific tenant, filter by tenant ID
      if (targetTenantId && activity.tenantId !== targetTenantId) {
        return false;
      }

      // Role-based filtering
      switch (context.role) {
        case 'tenant':
          // Tenants can only see their own activities that are marked as visible to tenants
          return activity.tenantId === context.tenantId && activity.isVisible.tenant;
        
        case 'reception':
          // Reception can see activities visible to reception
          return activity.isVisible.reception;
        
        case 'manager':
        case 'admin':
          // Managers and admins can see all activities
          return activity.isVisible.manager;
        
        default:
          return false;
      }
    });
  }

  /**
   * Filter admin notes based on user role and access permissions
   */
  static filterAdminNotes(
    notes: AdminNote[], 
    context: AccessContext,
    targetTenantId?: string
  ): AdminNote[] {
    return notes.filter(note => {
      // If targeting specific tenant, filter by tenant ID
      if (targetTenantId && note.tenantId !== targetTenantId) {
        return false;
      }

      // Role-based filtering
      switch (context.role) {
        case 'tenant':
          // Tenants cannot see admin notes
          return false;
        
        case 'reception':
          // Reception can see non-private notes
          return !note.isPrivate;
        
        case 'manager':
        case 'admin':
          // Managers and admins can see all notes including private ones
          return true;
        
        default:
          return false;
      }
    });
  }

  /**
   * Filter service requests based on user role and access permissions
   */
  static filterServiceRequests(
    requests: ServiceRequest[], 
    context: AccessContext,
    targetTenantId?: string
  ): ServiceRequest[] {
    return requests.filter(request => {
      // If targeting specific tenant, filter by tenant ID
      if (targetTenantId && request.tenantId !== targetTenantId) {
        return false;
      }

      // Role-based filtering
      switch (context.role) {
        case 'tenant':
          // Tenants can only see their own service requests
          return request.tenantId === context.tenantId;
        
        case 'reception':
        case 'manager':
        case 'admin':
          // Staff can see all service requests
          return true;
        
        default:
          return false;
      }
    });
  }

  /**
   * Filter room changes based on user role and access permissions
   */
  static filterRoomChanges(
    changes: RoomChangeEntry[], 
    context: AccessContext,
    targetTenantId?: string
  ): RoomChangeEntry[] {
    return changes.filter(change => {
      // If targeting specific tenant, filter by tenant ID
      if (targetTenantId && change.tenantId !== targetTenantId) {
        return false;
      }

      // Role-based filtering
      switch (context.role) {
        case 'tenant':
          // Tenants can only see their own room changes
          return change.tenantId === context.tenantId;
        
        case 'reception':
        case 'manager':
        case 'admin':
          // Staff can see all room changes
          return true;
        
        default:
          return false;
      }
    });
  }

  /**
   * Check if user can edit tenant data
   */
  static canEditTenant(context: AccessContext, targetTenantId: string): boolean {
    switch (context.role) {
      case 'tenant':
        // Tenants can only edit their own basic profile information
        return context.tenantId === targetTenantId;
      
      case 'reception':
      case 'manager':
      case 'admin':
        // Staff can edit any tenant data
        return true;
      
      default:
        return false;
    }
  }

  /**
   * Check if user can add admin notes
   */
  static canAddAdminNotes(context: AccessContext): boolean {
    return ['reception', 'manager', 'admin'].includes(context.role);
  }

  /**
   * Check if user can create service requests
   */
  static canCreateServiceRequests(context: AccessContext, targetTenantId?: string): boolean {
    switch (context.role) {
      case 'tenant':
        // Tenants can create service requests for themselves
        return !targetTenantId || context.tenantId === targetTenantId;
      
      case 'reception':
      case 'manager':
      case 'admin':
        // Staff can create service requests for any tenant
        return true;
      
      default:
        return false;
    }
  }

  /**
   * Check if user can request room changes
   */
  static canRequestRoomChanges(context: AccessContext, targetTenantId?: string): boolean {
    switch (context.role) {
      case 'tenant':
        // Tenants can request room changes for themselves
        return !targetTenantId || context.tenantId === targetTenantId;
      
      case 'reception':
      case 'manager':
      case 'admin':
        // Staff can request room changes for any tenant
        return true;
      
      default:
        return false;
    }
  }

  /**
   * Get allowed fields for tenant data editing based on user role
   */
  static getAllowedTenantFields(context: AccessContext): string[] {
    switch (context.role) {
      case 'tenant':
        // Tenants can only edit basic contact information
        return [
          'phone', 
          'email', 
          'emergencyContact', 
          'vehicleInfo'
        ];
      
      case 'reception':
        // Reception can edit most fields except sensitive admin data
        return [
          'name', 
          'phone', 
          'email', 
          'idNumber',
          'dateOfBirth',
          'gender',
          'nationality',
          'hometown',
          'occupation',
          'industry',
          'company',
          'emergencyContact',
          'vehicleInfo',
          'notes'
        ];
      
      case 'manager':
      case 'admin':
        // Managers and admins can edit all fields
        return [
          'name', 
          'phone', 
          'email', 
          'idNumber',
          'dateOfBirth',
          'gender',
          'nationality',
          'hometown',
          'occupation',
          'industry',
          'company',
          'status',
          'emergencyContact',
          'vehicleInfo',
          'notes',
          'currentRoomId',
          'moveInDate',
          'moveOutDate'
        ];
      
      default:
        return [];
    }
  }

  /**
   * Sanitize tenant data based on user role and access permissions
   */
  static sanitizeTenantData(tenantData: any, context: AccessContext): any {
    const allowedFields = this.getAllowedTenantFields(context);
    
    // For tenants, hide sensitive administrative information
    if (context.role === 'tenant') {
      const sanitized = { ...tenantData };
      
      // Remove admin-only fields
      delete sanitized.adminNotes;
      delete sanitized.riskLevel;
      
      // Filter activity history to only show tenant-visible activities
      if (sanitized.activityHistory) {
        sanitized.activityHistory = this.filterActivities(
          sanitized.activityHistory, 
          context, 
          tenantData.id
        );
      }
      
      return sanitized;
    }
    
    // For staff roles, return full data
    return tenantData;
  }
}
