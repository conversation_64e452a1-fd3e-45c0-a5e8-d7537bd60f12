import { ActionContext } from '../actionsStore';
import fs from 'fs';
import path from 'path';

// Security configuration
interface SecurityConfig {
  enableAuthentication: boolean;
  enableAuthorization: boolean;
  enableRateLimit: boolean;
  enableAuditLog: boolean;
  allowAnonymousActions: string[];
  adminActions: string[];
  rateLimitByUser: boolean;
  maxFailedAttempts: number;
  lockoutDurationMs: number;
}

// Rate limit entry
interface RateLimitEntry {
  count: number;
  windowStart: number;
  blocked: boolean;
  blockedUntil?: number;
}

// Audit log entry
interface AuditLogEntry {
  id: string;
  timestamp: string;
  userId: string;
  actionKey: string;
  source: string;
  success: boolean;
  error?: string;
  ipAddress?: string;
  userAgent?: string;
  permissions: string[];
}

export class ActionSecurityManager {
  private config: SecurityConfig;
  private rateLimitStore: Map<string, RateLimitEntry> = new Map();
  private failedAttempts: Map<string, number> = new Map();
  private dataDir: string;

  constructor(config?: Partial<SecurityConfig>) {
    this.config = {
      enableAuthentication: true,
      enableAuthorization: true,
      enableRateLimit: true,
      enableAuditLog: true,
      allowAnonymousActions: ['system.getInfo'],
      adminActions: ['platform.createUser', 'cache.operation', 'platform.fileOperation'],
      rateLimitByUser: true,
      maxFailedAttempts: 5,
      lockoutDurationMs: 15 * 60 * 1000, // 15 minutes
      ...config
    };

    this.dataDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/security');
    this.ensureSecurityDir();
  }

  private ensureSecurityDir(): void {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  // Authenticate user
  async authenticateUser(context: ActionContext): Promise<{
    authenticated: boolean;
    userId?: string;
    error?: string;
  }> {
    if (!this.config.enableAuthentication) {
      return { authenticated: true, userId: context.userId };
    }

    // Check if user is anonymous and action allows anonymous access
    if (!context.userId || context.userId === 'anonymous') {
      return {
        authenticated: false,
        error: 'Authentication required'
      };
    }

    // Check if user is locked out
    const failedCount = this.failedAttempts.get(context.userId) || 0;
    if (failedCount >= this.config.maxFailedAttempts) {
      return {
        authenticated: false,
        error: 'Account temporarily locked due to too many failed attempts'
      };
    }

    // In a real implementation, you would validate the user token/session here
    // For now, we assume any non-anonymous userId is valid
    return {
      authenticated: true,
      userId: context.userId
    };
  }

  // Authorize action execution
  async authorizeAction(
    actionKey: string,
    context: ActionContext
  ): Promise<{
    authorized: boolean;
    error?: string;
  }> {
    if (!this.config.enableAuthorization) {
      return { authorized: true };
    }

    // Check if action allows anonymous access
    if (this.config.allowAnonymousActions.includes(actionKey)) {
      return { authorized: true };
    }

    // Check if user is authenticated
    const authResult = await this.authenticateUser(context);
    if (!authResult.authenticated) {
      return {
        authorized: false,
        error: authResult.error || 'Authentication required'
      };
    }

    // Check admin actions
    if (this.config.adminActions.includes(actionKey)) {
      const hasAdminPermission = context.permissions?.includes('admin') || 
                                context.permissions?.includes('admin.actions');
      if (!hasAdminPermission) {
        return {
          authorized: false,
          error: 'Admin permissions required for this action'
        };
      }
    }

    // Check specific action permissions
    const requiredPermission = this.getRequiredPermission(actionKey);
    if (requiredPermission && !context.permissions?.includes(requiredPermission)) {
      return {
        authorized: false,
        error: `Permission required: ${requiredPermission}`
      };
    }

    return { authorized: true };
  }

  // Check rate limits
  async checkRateLimit(
    actionKey: string,
    context: ActionContext,
    maxCalls: number = 100,
    windowMs: number = 60000
  ): Promise<{
    allowed: boolean;
    error?: string;
    remainingCalls?: number;
    resetTime?: number;
  }> {
    if (!this.config.enableRateLimit) {
      return { allowed: true };
    }

    const identifier = this.config.rateLimitByUser 
      ? `${context.userId}:${actionKey}`
      : actionKey;

    const now = Date.now();
    const entry = this.rateLimitStore.get(identifier);

    if (!entry) {
      // First call
      this.rateLimitStore.set(identifier, {
        count: 1,
        windowStart: now,
        blocked: false
      });
      return {
        allowed: true,
        remainingCalls: maxCalls - 1,
        resetTime: now + windowMs
      };
    }

    // Check if window has expired
    if (now - entry.windowStart >= windowMs) {
      // Reset window
      this.rateLimitStore.set(identifier, {
        count: 1,
        windowStart: now,
        blocked: false
      });
      return {
        allowed: true,
        remainingCalls: maxCalls - 1,
        resetTime: now + windowMs
      };
    }

    // Check if blocked
    if (entry.blocked && entry.blockedUntil && now < entry.blockedUntil) {
      return {
        allowed: false,
        error: 'Rate limit exceeded. Please try again later.',
        remainingCalls: 0,
        resetTime: entry.blockedUntil
      };
    }

    // Increment count
    entry.count++;

    if (entry.count > maxCalls) {
      // Block for the remainder of the window
      entry.blocked = true;
      entry.blockedUntil = entry.windowStart + windowMs;
      
      return {
        allowed: false,
        error: 'Rate limit exceeded. Please try again later.',
        remainingCalls: 0,
        resetTime: entry.blockedUntil
      };
    }

    return {
      allowed: true,
      remainingCalls: maxCalls - entry.count,
      resetTime: entry.windowStart + windowMs
    };
  }

  // Log security events
  async logSecurityEvent(
    actionKey: string,
    context: ActionContext,
    success: boolean,
    error?: string,
    additionalData?: Record<string, any>
  ): Promise<void> {
    if (!this.config.enableAuditLog) {
      return;
    }

    const logEntry: AuditLogEntry = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      userId: context.userId || 'anonymous',
      actionKey,
      source: context.source,
      success,
      error,
      permissions: context.permissions || [],
      ...additionalData
    };

    // Write to audit log file
    const logFile = path.join(this.dataDir, `audit_${new Date().toISOString().split('T')[0]}.json`);
    
    try {
      let logs: AuditLogEntry[] = [];
      if (fs.existsSync(logFile)) {
        const content = fs.readFileSync(logFile, 'utf8');
        logs = JSON.parse(content);
      }
      
      logs.push(logEntry);
      fs.writeFileSync(logFile, JSON.stringify(logs, null, 2));
    } catch (error) {
      console.error('Error writing audit log:', error);
    }

    // Track failed attempts
    if (!success && context.userId && context.userId !== 'anonymous') {
      const currentCount = this.failedAttempts.get(context.userId) || 0;
      this.failedAttempts.set(context.userId, currentCount + 1);
      
      // Auto-clear failed attempts after lockout duration
      setTimeout(() => {
        this.failedAttempts.delete(context.userId!);
      }, this.config.lockoutDurationMs);
    } else if (success && context.userId) {
      // Clear failed attempts on successful action
      this.failedAttempts.delete(context.userId);
    }
  }

  // Validate input payload
  validateInput(payload: any): {
    valid: boolean;
    errors: string[];
    sanitized?: any;
  } {
    const errors: string[] = [];
    
    if (payload === null || payload === undefined) {
      return { valid: true, errors: [], sanitized: {} };
    }

    if (typeof payload !== 'object') {
      errors.push('Payload must be an object');
      return { valid: false, errors };
    }

    // Check for dangerous properties
    const dangerousKeys = ['__proto__', 'constructor', 'prototype'];
    for (const key of dangerousKeys) {
      if (key in payload) {
        errors.push(`Dangerous property not allowed: ${key}`);
      }
    }

    // Check payload size
    try {
      const serialized = JSON.stringify(payload);
      if (serialized.length > 1024 * 1024) { // 1MB limit
        errors.push('Payload too large (max 1MB)');
      }
    } catch (e) {
      errors.push('Payload is not serializable');
    }

    if (errors.length > 0) {
      return { valid: false, errors };
    }

    // Sanitize payload
    const sanitized = this.sanitizePayload(payload);
    return { valid: true, errors: [], sanitized };
  }

  // Sanitize payload to remove dangerous content
  private sanitizePayload(payload: any, depth: number = 0): any {
    if (depth > 10) {
      return {}; // Prevent deep recursion
    }

    if (payload === null || payload === undefined) {
      return payload;
    }

    if (typeof payload === 'string') {
      // Remove potential script tags and dangerous content
      return payload
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .slice(0, 10000); // Limit string length
    }

    if (typeof payload === 'number') {
      return isFinite(payload) ? payload : 0;
    }

    if (typeof payload === 'boolean') {
      return payload;
    }

    if (Array.isArray(payload)) {
      return payload
        .slice(0, 1000) // Limit array length
        .map(item => this.sanitizePayload(item, depth + 1));
    }

    if (typeof payload === 'object') {
      const sanitized: any = {};
      const dangerousKeys = ['__proto__', 'constructor', 'prototype'];
      
      for (const [key, value] of Object.entries(payload)) {
        if (dangerousKeys.includes(key)) {
          continue;
        }
        
        if (key.startsWith('_') && key.length > 1) {
          continue; // Skip private properties
        }
        
        sanitized[key] = this.sanitizePayload(value, depth + 1);
      }
      
      return sanitized;
    }

    return payload;
  }

  // Get required permission for action
  private getRequiredPermission(actionKey: string): string | null {
    const [category] = actionKey.split('.');
    
    const permissionMap: Record<string, string> = {
      'system': 'system.read',
      'data': 'data.read',
      'cache': 'cache.read',
      'notification': 'notification.send',
      'platform': 'platform.execute'
    };

    return permissionMap[category] || null;
  }

  // Get security statistics
  getSecurityStats(): {
    rateLimitEntries: number;
    failedAttempts: number;
    blockedUsers: number;
    auditLogEnabled: boolean;
  } {
    const blockedUsers = Array.from(this.rateLimitStore.values())
      .filter(entry => entry.blocked).length;

    return {
      rateLimitEntries: this.rateLimitStore.size,
      failedAttempts: this.failedAttempts.size,
      blockedUsers,
      auditLogEnabled: this.config.enableAuditLog
    };
  }

  // Clear rate limits (admin function)
  clearRateLimits(): void {
    this.rateLimitStore.clear();
    this.failedAttempts.clear();
  }

  // Update security configuration
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Default security manager instance
export const actionSecurity = new ActionSecurityManager();
