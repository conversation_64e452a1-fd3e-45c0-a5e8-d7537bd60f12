// File Utilities for JSON data operations
import fs from 'fs';
import path from 'path';
import { promises as fsPromises } from 'fs';

/**
 * Ensures that a directory exists, creating it if necessary
 */
export const ensureDirectoryExists = async (dirPath: string): Promise<void> => {
  try {
    // Convert relative path to absolute path if needed
    const absolutePath = path.isAbsolute(dirPath) ? dirPath : path.join(process.cwd(), dirPath);
    
    await fsPromises.access(absolutePath);
  } catch {
    // Directory doesn't exist, create it
    await fsPromises.mkdir(absolutePath, { recursive: true });
  }
};

/**
 * Reads a JSON file and returns the parsed data
 * Returns an empty array if the file doesn't exist
 */
export const readJsonFile = async <T>(filePath: string): Promise<T> => {
  try {
    // Convert relative path to absolute path if needed
    const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(process.cwd(), filePath);
    
    const data = await fsPromises.readFile(absolutePath, 'utf8');
    return JSON.parse(data) as T;
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      // File doesn't exist, return empty array as default
      return [] as unknown as T;
    }
    console.error(`Error reading JSON file ${filePath}:`, error);
    throw error;
  }
};

/**
 * Writes data to a JSON file
 * Creates the directory if it doesn't exist
 */
export const writeJsonFile = async <T>(filePath: string, data: T): Promise<void> => {
  try {
    // Convert relative path to absolute path if needed
    const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(process.cwd(), filePath);
    
    // Ensure the directory exists
    const dirPath = path.dirname(absolutePath);
    await ensureDirectoryExists(dirPath);
    
    // Write the file
    await fsPromises.writeFile(absolutePath, JSON.stringify(data, null, 2), 'utf8');
  } catch (error) {
    console.error(`Error writing JSON file ${filePath}:`, error);
    throw error;
  }
};

/**
 * Checks if a file exists
 */
export const fileExists = async (filePath: string): Promise<boolean> => {
  try {
    const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(process.cwd(), filePath);
    await fsPromises.access(absolutePath);
    return true;
  } catch {
    return false;
  }
};

/**
 * Deletes a file if it exists
 */
export const deleteFile = async (filePath: string): Promise<boolean> => {
  try {
    const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(process.cwd(), filePath);
    await fsPromises.unlink(absolutePath);
    return true;
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      // File doesn't exist, consider it successfully deleted
      return true;
    }
    console.error(`Error deleting file ${filePath}:`, error);
    return false;
  }
};

/**
 * Copies a file from source to destination
 */
export const copyFile = async (sourcePath: string, destPath: string): Promise<void> => {
  try {
    const absoluteSourcePath = path.isAbsolute(sourcePath) ? sourcePath : path.join(process.cwd(), sourcePath);
    const absoluteDestPath = path.isAbsolute(destPath) ? destPath : path.join(process.cwd(), destPath);
    
    // Ensure destination directory exists
    const destDir = path.dirname(absoluteDestPath);
    await ensureDirectoryExists(destDir);
    
    await fsPromises.copyFile(absoluteSourcePath, absoluteDestPath);
  } catch (error) {
    console.error(`Error copying file from ${sourcePath} to ${destPath}:`, error);
    throw error;
  }
};

/**
 * Gets file stats (size, modification time, etc.)
 */
export const getFileStats = async (filePath: string): Promise<fs.Stats | null> => {
  try {
    const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(process.cwd(), filePath);
    return await fsPromises.stat(absolutePath);
  } catch (error) {
    console.error(`Error getting file stats for ${filePath}:`, error);
    return null;
  }
};

/**
 * Lists files in a directory
 */
export const listFiles = async (dirPath: string): Promise<string[]> => {
  try {
    const absolutePath = path.isAbsolute(dirPath) ? dirPath : path.join(process.cwd(), dirPath);
    return await fsPromises.readdir(absolutePath);
  } catch (error) {
    console.error(`Error listing files in directory ${dirPath}:`, error);
    return [];
  }
};

/**
 * Creates a backup of a file by copying it with a timestamp suffix
 */
export const backupFile = async (filePath: string): Promise<string | null> => {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const ext = path.extname(filePath);
    const baseName = path.basename(filePath, ext);
    const dirName = path.dirname(filePath);
    
    const backupPath = path.join(dirName, `${baseName}_backup_${timestamp}${ext}`);
    
    await copyFile(filePath, backupPath);
    return backupPath;
  } catch (error) {
    console.error(`Error creating backup for ${filePath}:`, error);
    return null;
  }
};

/**
 * Simple in-memory cache for frequently accessed files
 */
const fileCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Reads a JSON file with caching to reduce I/O operations
 */
export const readJsonFileWithCache = async <T>(filePath: string): Promise<T> => {
  const now = Date.now();
  const cachedData = fileCache.get(filePath);
  
  if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
    return cachedData.data as T;
  }
  
  const data = await readJsonFile<T>(filePath);
  fileCache.set(filePath, { data, timestamp: now });
  return data;
};

/**
 * Invalidates cache for a specific file
 */
export const invalidateCache = (filePath: string): void => {
  fileCache.delete(filePath);
};

/**
 * Clears the entire file cache
 */
export const clearCache = (): void => {
  fileCache.clear();
};

/**
 * Gets the size of the file cache
 */
export const getCacheSize = (): number => {
  return fileCache.size;
};

/**
 * Appends data to a JSON array file
 * If the file doesn't exist, creates it with the new data as the first element
 */
export const appendToJsonArray = async <T>(filePath: string, newData: T): Promise<void> => {
  try {
    const existingData = await readJsonFile<T[]>(filePath);
    const updatedData = Array.isArray(existingData) ? [...existingData, newData] : [newData];
    await writeJsonFile(filePath, updatedData);
    
    // Invalidate cache since file was modified
    invalidateCache(filePath);
  } catch (error) {
    console.error(`Error appending to JSON array file ${filePath}:`, error);
    throw error;
  }
};

/**
 * Updates an item in a JSON array file by ID
 */
export const updateJsonArrayItem = async <T extends { id: string }>(
  filePath: string, 
  id: string, 
  updates: Partial<T>
): Promise<boolean> => {
  try {
    const data = await readJsonFile<T[]>(filePath);
    const itemIndex = data.findIndex(item => item.id === id);
    
    if (itemIndex === -1) {
      return false;
    }
    
    data[itemIndex] = { ...data[itemIndex], ...updates };
    await writeJsonFile(filePath, data);
    
    // Invalidate cache since file was modified
    invalidateCache(filePath);
    return true;
  } catch (error) {
    console.error(`Error updating item in JSON array file ${filePath}:`, error);
    return false;
  }
};

/**
 * Removes an item from a JSON array file by ID
 */
export const removeFromJsonArray = async <T extends { id: string }>(
  filePath: string, 
  id: string
): Promise<boolean> => {
  try {
    const data = await readJsonFile<T[]>(filePath);
    const initialLength = data.length;
    const filteredData = data.filter(item => item.id !== id);
    
    if (filteredData.length === initialLength) {
      return false; // Item not found
    }
    
    await writeJsonFile(filePath, filteredData);
    
    // Invalidate cache since file was modified
    invalidateCache(filePath);
    return true;
  } catch (error) {
    console.error(`Error removing item from JSON array file ${filePath}:`, error);
    return false;
  }
};
