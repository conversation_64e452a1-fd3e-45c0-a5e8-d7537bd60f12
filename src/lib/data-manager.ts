import fs from 'fs';
import path from 'path';

export interface DataFile<T> {
  data: T[];
  metadata: {
    lastUpdated: string;
    version: string;
    totalCount: number;
  };
}

export class DataManager {
  private static dataDir = path.join(process.cwd(), 'data/apps/tools');

  static async ensureDataDir() {
    const dirs = [
      this.dataDir,
      path.join(this.dataDir, 'requests'),
      path.join(this.dataDir, 'workflows'),
      path.join(this.dataDir, 'users'),
      path.join(this.dataDir, 'settings'),
      path.join(this.dataDir, 'analytics')
    ];

    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
  }

  static async readFile<T>(filePath: string): Promise<DataFile<T>> {
    await this.ensureDataDir();
    
    const fullPath = path.join(this.dataDir, filePath);
    
    if (!fs.existsSync(fullPath)) {
      const defaultData: DataFile<T> = {
        data: [],
        metadata: {
          lastUpdated: new Date().toISOString(),
          version: '1.0.0',
          totalCount: 0
        }
      };
      await this.writeFile(filePath, defaultData);
      return defaultData;
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      const parsed = JSON.parse(content);
      
      // Handle legacy format (direct array or object without metadata)
      if (Array.isArray(parsed)) {
        return {
          data: parsed,
          metadata: {
            lastUpdated: new Date().toISOString(),
            version: '1.0.0',
            totalCount: parsed.length
          }
        };
      } else if (parsed.data) {
        return parsed;
      } else {
        // Convert old format to new format
        const keys = Object.keys(parsed);
        const dataKey = keys.find(key => Array.isArray(parsed[key]));
        if (dataKey) {
          return {
            data: parsed[dataKey],
            metadata: {
              lastUpdated: new Date().toISOString(),
              version: '1.0.0',
              totalCount: parsed[dataKey].length
            }
          };
        }
      }
      
      throw new Error('Invalid data format');
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      throw error;
    }
  }

  static async writeFile<T>(filePath: string, data: DataFile<T>): Promise<void> {
    await this.ensureDataDir();
    
    const fullPath = path.join(this.dataDir, filePath);
    
    // Update metadata
    data.metadata.lastUpdated = new Date().toISOString();
    data.metadata.totalCount = data.data.length;
    
    try {
      fs.writeFileSync(fullPath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error writing file ${filePath}:`, error);
      throw error;
    }
  }

  static async findById<T extends { id: string }>(filePath: string, id: string): Promise<T | null> {
    const fileData = await this.readFile<T>(filePath);
    return fileData.data.find(item => item.id === id) || null;
  }

  static async create<T extends { id: string }>(filePath: string, item: T): Promise<T> {
    const fileData = await this.readFile<T>(filePath);
    
    // Check if item with same ID already exists
    const existingIndex = fileData.data.findIndex(existing => existing.id === item.id);
    if (existingIndex !== -1) {
      throw new Error(`Item with ID ${item.id} already exists`);
    }
    
    // Add timestamps if not present
    const now = new Date().toISOString();
    const itemWithTimestamps = {
      ...item,
      createdAt: (item as any).createdAt || now,
      updatedAt: now
    };
    
    fileData.data.push(itemWithTimestamps as T);
    await this.writeFile(filePath, fileData);
    
    return itemWithTimestamps as T;
  }

  static async update<T extends { id: string }>(filePath: string, id: string, updates: Partial<T>): Promise<T | null> {
    const fileData = await this.readFile<T>(filePath);
    
    const index = fileData.data.findIndex(item => item.id === id);
    if (index === -1) {
      return null;
    }
    
    // Update item with timestamp
    const updatedItem = {
      ...fileData.data[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    fileData.data[index] = updatedItem;
    await this.writeFile(filePath, fileData);
    
    return updatedItem;
  }

  static async delete<T extends { id: string }>(filePath: string, id: string): Promise<boolean> {
    const fileData = await this.readFile<T>(filePath);
    
    const index = fileData.data.findIndex(item => item.id === id);
    if (index === -1) {
      return false;
    }
    
    fileData.data.splice(index, 1);
    await this.writeFile(filePath, fileData);
    
    return true;
  }

  static async list<T>(filePath: string, filters?: {
    status?: string;
    category?: string;
    search?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ data: T[]; total: number; metadata: any }> {
    const fileData = await this.readFile<T>(filePath);
    let filteredData = [...fileData.data];
    
    // Apply filters
    if (filters) {
      if (filters.status) {
        filteredData = filteredData.filter((item: any) => item.status === filters.status);
      }
      
      if (filters.category) {
        filteredData = filteredData.filter((item: any) => item.category === filters.category);
      }
      
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredData = filteredData.filter((item: any) => 
          (item.title && item.title.toLowerCase().includes(searchLower)) ||
          (item.name && item.name.toLowerCase().includes(searchLower)) ||
          (item.description && item.description.toLowerCase().includes(searchLower))
        );
      }
      
      // Apply sorting
      if (filters.sortBy) {
        filteredData.sort((a: any, b: any) => {
          const aValue = a[filters.sortBy!];
          const bValue = b[filters.sortBy!];
          
          let comparison = 0;
          if (aValue < bValue) comparison = -1;
          else if (aValue > bValue) comparison = 1;
          
          return filters.sortOrder === 'desc' ? -comparison : comparison;
        });
      }
      
      // Apply pagination
      const total = filteredData.length;
      if (filters.offset !== undefined && filters.limit !== undefined) {
        filteredData = filteredData.slice(filters.offset, filters.offset + filters.limit);
      } else if (filters.limit !== undefined) {
        filteredData = filteredData.slice(0, filters.limit);
      }
      
      return {
        data: filteredData,
        total,
        metadata: fileData.metadata
      };
    }
    
    return {
      data: filteredData,
      total: filteredData.length,
      metadata: fileData.metadata
    };
  }

  static generateId(prefix: string = 'item'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  static async getStats<T>(filePath: string, groupBy?: string): Promise<any> {
    const fileData = await this.readFile<T>(filePath);
    
    const stats = {
      total: fileData.data.length,
      lastUpdated: fileData.metadata.lastUpdated
    };
    
    if (groupBy && fileData.data.length > 0) {
      const groups = fileData.data.reduce((acc: any, item: any) => {
        const key = item[groupBy] || 'unknown';
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {});
      
      (stats as any).groupedBy = {
        field: groupBy,
        counts: groups
      };
    }
    
    return stats;
  }

  // Backup and restore functionality
  static async backup(filePath: string): Promise<string> {
    const fileData = await this.readFile(filePath);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    
    await this.writeFile(backupPath, fileData);
    return backupPath;
  }

  static async restore<T>(filePath: string, backupPath: string): Promise<void> {
    const backupData = await this.readFile<T>(backupPath);
    await this.writeFile(filePath, backupData);
  }
}
