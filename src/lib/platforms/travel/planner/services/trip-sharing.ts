import { readJsonFileWithCache, writeJsonFile, invalidateCache } from './file-utils';
import { CustomerTripPlan } from '@/types/travel-planner';

export interface SharedTrip {
  id: string;
  tripPlanId: string;
  ownerId: string;
  shareToken: string;
  title: string;
  description?: string;
  isPublic: boolean;
  allowComments: boolean;
  allowCollaboration: boolean;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  viewCount: number;
  sharedWith: SharedTripAccess[];
  collaborators: TripCollaborator[];
}

export interface SharedTripAccess {
  userId?: string;
  email?: string;
  accessLevel: 'view' | 'comment' | 'edit';
  grantedAt: Date;
  grantedBy: string;
}

export interface TripCollaborator {
  userId: string;
  role: 'viewer' | 'editor' | 'co-owner';
  joinedAt: Date;
  invitedBy: string;
  isActive: boolean;
}

export interface TripComment {
  id: string;
  tripShareId: string;
  userId: string;
  userName: string;
  content: string;
  parentCommentId?: string;
  createdAt: Date;
  updatedAt?: Date;
  isDeleted: boolean;
  reactions: TripCommentReaction[];
}

export interface TripCommentReaction {
  userId: string;
  type: 'like' | 'love' | 'helpful' | 'funny';
  createdAt: Date;
}

export interface CollaborativeEdit {
  id: string;
  tripShareId: string;
  userId: string;
  userName: string;
  editType: 'add_service' | 'remove_service' | 'modify_service' | 'change_dates' | 'update_budget';
  description: string;
  changes: Record<string, any>;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
}

const SHARED_TRIPS_FILE = 'shared-trips.json';
const TRIP_COMMENTS_FILE = 'trip-comments.json';
const COLLABORATIVE_EDITS_FILE = 'collaborative-edits.json';

export class TripSharingService {
  /**
   * Share a trip plan
   */
  async shareTrip(
    tripPlanId: string,
    ownerId: string,
    options: {
      title: string;
      description?: string;
      isPublic?: boolean;
      allowComments?: boolean;
      allowCollaboration?: boolean;
      expiresAt?: Date;
      sharedWith?: SharedTripAccess[];
    }
  ): Promise<SharedTrip> {
    const sharedTrips = await readJsonFileWithCache<SharedTrip[]>(SHARED_TRIPS_FILE);
    
    // Check if trip is already shared
    const existingShare = sharedTrips.find(share => 
      share.tripPlanId === tripPlanId && share.ownerId === ownerId
    );
    
    if (existingShare) {
      throw new Error('Trip is already shared');
    }

    const shareToken = this.generateShareToken();
    
    const sharedTrip: SharedTrip = {
      id: `share-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      tripPlanId,
      ownerId,
      shareToken,
      title: options.title,
      description: options.description,
      isPublic: options.isPublic || false,
      allowComments: options.allowComments || false,
      allowCollaboration: options.allowCollaboration || false,
      expiresAt: options.expiresAt,
      createdAt: new Date(),
      updatedAt: new Date(),
      viewCount: 0,
      sharedWith: options.sharedWith || [],
      collaborators: []
    };

    sharedTrips.push(sharedTrip);
    await writeJsonFile(SHARED_TRIPS_FILE, sharedTrips);
    invalidateCache(SHARED_TRIPS_FILE);

    return sharedTrip;
  }

  /**
   * Get shared trip by token
   */
  async getSharedTrip(shareToken: string, viewerId?: string): Promise<SharedTrip | null> {
    const sharedTrips = await readJsonFileWithCache<SharedTrip[]>(SHARED_TRIPS_FILE);
    const sharedTrip = sharedTrips.find(trip => trip.shareToken === shareToken);
    
    if (!sharedTrip) {
      return null;
    }

    // Check if trip has expired
    if (sharedTrip.expiresAt && new Date() > new Date(sharedTrip.expiresAt)) {
      return null;
    }

    // Increment view count if viewer is not the owner
    if (viewerId && viewerId !== sharedTrip.ownerId) {
      sharedTrip.viewCount++;
      const tripIndex = sharedTrips.findIndex(t => t.id === sharedTrip.id);
      if (tripIndex !== -1) {
        sharedTrips[tripIndex] = sharedTrip;
        await writeJsonFile(SHARED_TRIPS_FILE, sharedTrips);
        invalidateCache(SHARED_TRIPS_FILE);
      }
    }

    return sharedTrip;
  }

  /**
   * Get user's shared trips
   */
  async getUserSharedTrips(userId: string): Promise<SharedTrip[]> {
    const sharedTrips = await readJsonFileWithCache<SharedTrip[]>(SHARED_TRIPS_FILE);
    return sharedTrips.filter(trip => 
      trip.ownerId === userId || 
      trip.collaborators.some(collab => collab.userId === userId && collab.isActive)
    );
  }

  /**
   * Add collaborator to shared trip
   */
  async addCollaborator(
    shareId: string,
    userId: string,
    role: TripCollaborator['role'],
    invitedBy: string
  ): Promise<SharedTrip> {
    const sharedTrips = await readJsonFileWithCache<SharedTrip[]>(SHARED_TRIPS_FILE);
    const tripIndex = sharedTrips.findIndex(trip => trip.id === shareId);

    if (tripIndex === -1) {
      throw new Error('Shared trip not found');
    }

    const sharedTrip = sharedTrips[tripIndex];

    // Check if user is already a collaborator
    const existingCollaborator = sharedTrip.collaborators.find(collab => collab.userId === userId);
    if (existingCollaborator) {
      throw new Error('User is already a collaborator');
    }

    const collaborator: TripCollaborator = {
      userId,
      role,
      joinedAt: new Date(),
      invitedBy,
      isActive: true
    };

    sharedTrip.collaborators.push(collaborator);
    sharedTrip.updatedAt = new Date();

    await writeJsonFile(SHARED_TRIPS_FILE, sharedTrips);
    invalidateCache(SHARED_TRIPS_FILE);

    return sharedTrip;
  }

  /**
   * Add comment to shared trip
   */
  async addComment(
    tripShareId: string,
    userId: string,
    userName: string,
    content: string,
    parentCommentId?: string
  ): Promise<TripComment> {
    const comments = await readJsonFileWithCache<TripComment[]>(TRIP_COMMENTS_FILE);
    
    const comment: TripComment = {
      id: `comment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      tripShareId,
      userId,
      userName,
      content,
      parentCommentId,
      createdAt: new Date(),
      isDeleted: false,
      reactions: []
    };

    comments.push(comment);
    await writeJsonFile(TRIP_COMMENTS_FILE, comments);
    invalidateCache(TRIP_COMMENTS_FILE);

    return comment;
  }

  /**
   * Get comments for shared trip
   */
  async getTripComments(tripShareId: string): Promise<TripComment[]> {
    const comments = await readJsonFileWithCache<TripComment[]>(TRIP_COMMENTS_FILE);
    return comments.filter(comment => 
      comment.tripShareId === tripShareId && !comment.isDeleted
    );
  }

  /**
   * Submit collaborative edit
   */
  async submitCollaborativeEdit(
    tripShareId: string,
    userId: string,
    userName: string,
    editType: CollaborativeEdit['editType'],
    description: string,
    changes: Record<string, any>
  ): Promise<CollaborativeEdit> {
    const edits = await readJsonFileWithCache<CollaborativeEdit[]>(COLLABORATIVE_EDITS_FILE);
    
    const edit: CollaborativeEdit = {
      id: `edit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      tripShareId,
      userId,
      userName,
      editType,
      description,
      changes,
      status: 'pending',
      createdAt: new Date()
    };

    edits.push(edit);
    await writeJsonFile(COLLABORATIVE_EDITS_FILE, edits);
    invalidateCache(COLLABORATIVE_EDITS_FILE);

    return edit;
  }

  /**
   * Get pending edits for shared trip
   */
  async getPendingEdits(tripShareId: string): Promise<CollaborativeEdit[]> {
    const edits = await readJsonFileWithCache<CollaborativeEdit[]>(COLLABORATIVE_EDITS_FILE);
    return edits.filter(edit => 
      edit.tripShareId === tripShareId && edit.status === 'pending'
    );
  }

  /**
   * Review collaborative edit
   */
  async reviewEdit(
    editId: string,
    reviewerId: string,
    status: 'approved' | 'rejected'
  ): Promise<CollaborativeEdit> {
    const edits = await readJsonFileWithCache<CollaborativeEdit[]>(COLLABORATIVE_EDITS_FILE);
    const editIndex = edits.findIndex(edit => edit.id === editId);

    if (editIndex === -1) {
      throw new Error('Edit not found');
    }

    edits[editIndex].status = status;
    edits[editIndex].reviewedAt = new Date();
    edits[editIndex].reviewedBy = reviewerId;

    await writeJsonFile(COLLABORATIVE_EDITS_FILE, edits);
    invalidateCache(COLLABORATIVE_EDITS_FILE);

    return edits[editIndex];
  }

  /**
   * Generate share token
   */
  private generateShareToken(): string {
    return `trip-${Date.now()}-${Math.random().toString(36).substr(2, 16)}`;
  }

  /**
   * Update shared trip settings
   */
  async updateSharedTrip(
    shareId: string,
    updates: Partial<Pick<SharedTrip, 'title' | 'description' | 'isPublic' | 'allowComments' | 'allowCollaboration' | 'expiresAt'>>
  ): Promise<SharedTrip> {
    const sharedTrips = await readJsonFileWithCache<SharedTrip[]>(SHARED_TRIPS_FILE);
    const tripIndex = sharedTrips.findIndex(trip => trip.id === shareId);

    if (tripIndex === -1) {
      throw new Error('Shared trip not found');
    }

    sharedTrips[tripIndex] = {
      ...sharedTrips[tripIndex],
      ...updates,
      updatedAt: new Date()
    };

    await writeJsonFile(SHARED_TRIPS_FILE, sharedTrips);
    invalidateCache(SHARED_TRIPS_FILE);

    return sharedTrips[tripIndex];
  }

  /**
   * Delete shared trip
   */
  async deleteSharedTrip(shareId: string, ownerId: string): Promise<boolean> {
    const sharedTrips = await readJsonFileWithCache<SharedTrip[]>(SHARED_TRIPS_FILE);
    const tripIndex = sharedTrips.findIndex(trip => 
      trip.id === shareId && trip.ownerId === ownerId
    );

    if (tripIndex === -1) {
      return false;
    }

    sharedTrips.splice(tripIndex, 1);
    await writeJsonFile(SHARED_TRIPS_FILE, sharedTrips);
    invalidateCache(SHARED_TRIPS_FILE);

    return true;
  }
}

// Export a singleton instance
export const tripSharingService = new TripSharingService();
