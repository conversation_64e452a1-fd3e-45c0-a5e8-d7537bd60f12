import { travelServiceRepository } from './repository';
import { generateTripOptions } from '../trip-generator';
import { TripPlan, TripOption, CustomerTripPlan, BookedService, UserPreferences } from '@/types/travel-planner';
import { readJsonFileWithCache, writeJsonFile, invalidateCache } from './file-utils';
import { userPreferencesService } from './user-preferences';
import { bookingHistoryService, BookingHistoryEntry } from './booking-history';

const CUSTOMER_TRIP_PLANS_FILE = 'customer-trip-plans.json';

interface TripPlannerOptions {
  destination: string;
  duration: number;
  budget: number;
  groupSize: number;
  userId?: string;
  preferences?: {
    preferredAirlines?: string[];
    preferredAccommodationTypes?: string[];
    preferredActivities?: string[];
    maxFlightLayovers?: number;
    minAccommodationRating?: number;
  };
}

export class TripPlannerService {
  /**
   * Build a trip plan using preferred travel services
   */
  async buildTripPlan(options: TripPlannerOptions): Promise<TripPlan> {
    // Get user preferences if userId is provided
    let userPreferences: UserPreferences | null = null;
    if (options.userId) {
      userPreferences = await userPreferencesService.getUserPreferences(options.userId);
    }

    // Merge user preferences with options
    const mergedPreferences = {
      ...options.preferences,
      ...(userPreferences && {
        preferredAirlines: userPreferences.preferredAirlines,
        preferredAccommodationTypes: userPreferences.preferredAccommodationTypes,
        preferredActivities: userPreferences.preferredActivities,
        maxFlightLayovers: userPreferences.maxFlightLayovers,
        minAccommodationRating: userPreferences.minAccommodationRating,
      })
    };

    // Get services for the destination
    const availableServices = await travelServiceRepository.getServicesForDestination(options.destination);
    
    // Prioritize services based on their priority value
    const prioritizedFlights = availableServices.flights.sort((a, b) => a.priority - b.priority);
    const prioritizedAccommodations = availableServices.accommodations.sort((a, b) => a.priority - b.priority);
    const prioritizedActivities = availableServices.activities.sort((a, b) => a.priority - b.priority);
    
    // Apply customer preferences if provided
    let selectedFlights = prioritizedFlights;
    let selectedAccommodations = prioritizedAccommodations;
    let selectedActivities = prioritizedActivities;
    
    if (mergedPreferences) {
      // Filter flights by preferred airlines if specified
      if (mergedPreferences.preferredAirlines && mergedPreferences.preferredAirlines.length > 0) {
        selectedFlights = prioritizedFlights.filter(service =>
          service.airlines.some(airline => mergedPreferences.preferredAirlines?.includes(airline))
        );

        // If no matching flights, fallback to all flights
        if (selectedFlights.length === 0) {
          selectedFlights = prioritizedFlights;
        }
      }

      // Filter accommodations by preferred types if specified
      if (mergedPreferences.preferredAccommodationTypes && mergedPreferences.preferredAccommodationTypes.length > 0) {
        selectedAccommodations = prioritizedAccommodations.filter(service =>
          service.accommodationTypes.some(type => mergedPreferences.preferredAccommodationTypes?.includes(type))
        );

        // If no matching accommodations, fallback to all accommodations
        if (selectedAccommodations.length === 0) {
          selectedAccommodations = prioritizedAccommodations;
        }
      }

      // Filter activities by preferred types if specified
      if (mergedPreferences.preferredActivities && mergedPreferences.preferredActivities.length > 0) {
        selectedActivities = prioritizedActivities.filter(service =>
          service.activityTypes.some(type => mergedPreferences.preferredActivities?.includes(type))
        );

        // If no matching activities, fallback to all activities
        if (selectedActivities.length === 0) {
          selectedActivities = prioritizedActivities;
        }
      }
    }
    
    // For now, use the existing trip generator but enhance it with our prioritized services
    // In a real implementation, this would make actual API calls to the services
    
    // Prepare service IDs to prioritize
    const prioritizedServiceIds = [
      ...selectedFlights.map(s => s.id),
      ...selectedAccommodations.map(s => s.id),
      ...selectedActivities.map(s => s.id)
    ];
    
    // Generate trip options using the existing generator
    // The existing generator doesn't take service IDs, so we'll need to associate them later
    const tripOptions = generateTripOptions(
      options.destination,
      options.duration,
      options.budget,
      options.groupSize
    );
    
    // Enhance the generated options with our prioritized service information
    const enhancedOptions = tripOptions.map(option => {
      // Assign services to flights, accommodations, and activities
      const enhancedOption: TripOption = {
        ...option,
        // Add metadata about which services were used
        platforms: [
          ...new Set([
            ...(selectedFlights.length > 0 ? [selectedFlights[0].platform] : []),
            ...(selectedAccommodations.length > 0 ? [selectedAccommodations[0].platform] : []),
            ...(selectedActivities.length > 0 ? [selectedActivities[0].platform] : [])
          ])
        ]
      };
      
      return enhancedOption;
    });
    
    // Create the trip plan
    const tripPlan: TripPlan = {
      id: `plan-${Date.now()}`,
      createdAt: new Date(),
      parentId: null,
      options: enhancedOptions,
      destination: options.destination,
      duration: options.duration,
      budget: options.budget,
      groupSize: options.groupSize,
      isSelected: false
    };
    
    return tripPlan;
  }
  
  /**
   * Save a customer's trip plan
   */
  async saveCustomerTripPlan(customerId: string, tripPlan: TripPlan, status: 'draft' | 'confirmed' = 'draft'): Promise<CustomerTripPlan> {
    // Read existing customer trip plans
    const customerPlans = await readJsonFileWithCache<CustomerTripPlan[]>(CUSTOMER_TRIP_PLANS_FILE);
    
    // Create a customer trip plan
    const customerPlan: CustomerTripPlan = {
      ...tripPlan,
      customerId,
      status,
      bookedServices: [] // No services booked yet
    };
    
    // Check if this plan already exists for the customer
    const existingPlanIndex = customerPlans.findIndex(
      plan => plan.customerId === customerId && plan.id === tripPlan.id
    );
    
    if (existingPlanIndex !== -1) {
      // Update existing plan
      customerPlans[existingPlanIndex] = customerPlan;
    } else {
      // Add new plan
      customerPlans.push(customerPlan);
    }
    
    // Save to file
    await writeJsonFile(CUSTOMER_TRIP_PLANS_FILE, customerPlans);
    
    return customerPlan;
  }
  
  /**
   * Get all trip plans for a customer
   */
  async getCustomerTripPlans(customerId: string): Promise<CustomerTripPlan[]> {
    const customerPlans = await readJsonFileWithCache<CustomerTripPlan[]>(CUSTOMER_TRIP_PLANS_FILE);
    return customerPlans.filter(plan => plan.customerId === customerId);
  }
  
  /**
   * Book services for a trip plan
   */
  async bookTripServices(
    customerId: string,
    tripPlanId: string,
    servicesToBook: { serviceId: string; serviceType: 'flight' | 'accommodation' | 'activity' }[],
    paymentMethod?: string
  ): Promise<CustomerTripPlan> {
    // In a real application, this would make actual API calls to the services

    // Read existing customer trip plans
    const customerPlans = await readJsonFileWithCache<CustomerTripPlan[]>(CUSTOMER_TRIP_PLANS_FILE);

    // Find the customer's plan
    const planIndex = customerPlans.findIndex(
      plan => plan.customerId === customerId && plan.id === tripPlanId
    );

    if (planIndex === -1) {
      throw new Error(`Trip plan with ID ${tripPlanId} not found for customer ${customerId}`);
    }

    const plan = customerPlans[planIndex];

    // For each service to book, simulate a booking
    const bookedServices = await Promise.all(
      servicesToBook.map(async service => {
        // In a real application, this would call the service's booking API
        const bookingReference = `BOOKING-${Date.now()}-${Math.floor(Math.random() * 10000)}`;

        // Get the service details
        const serviceDetails = await travelServiceRepository.getServiceById(service.serviceId);

        if (!serviceDetails) {
          throw new Error(`Service with ID ${service.serviceId} not found`);
        }

        // Simulate a price (would come from the actual booking in a real app)
        const price = Math.floor(Math.random() * 500) + 100;

        const bookedService: BookedService = {
          serviceId: service.serviceId,
          serviceType: service.serviceType,
          bookingReference,
          price,
          status: 'pending',
          bookingDate: new Date(),
          details: serviceDetails
        };

        return bookedService;
      })
    );

    // Calculate total price
    const totalPrice = bookedServices.reduce((sum, service) => sum + service.price, 0);

    // Update the plan with the booked services
    plan.bookedServices = [...plan.bookedServices, ...bookedServices];
    plan.status = 'confirmed';
    plan.totalPrice = totalPrice;
    plan.bookingDate = new Date();

    // Save the updated plans
    await writeJsonFile(CUSTOMER_TRIP_PLANS_FILE, customerPlans);

    // Add to booking history
    const bookingHistoryEntry: BookingHistoryEntry = {
      id: `booking-${Date.now()}`,
      customerId,
      tripPlanId,
      bookingDate: new Date(),
      totalAmount: totalPrice,
      status: 'pending',
      services: bookedServices,
      paymentMethod,
      confirmationNumber: `CONF-${Date.now()}`
    };

    await bookingHistoryService.addBooking(bookingHistoryEntry);

    return plan;
  }
  
  /**
   * Cancel a trip plan
   */
  async cancelTripPlan(customerId: string, tripPlanId: string): Promise<CustomerTripPlan> {
    // Read existing customer trip plans
    const customerPlans = await readJsonFileWithCache<CustomerTripPlan[]>(CUSTOMER_TRIP_PLANS_FILE);
    
    // Find the customer's plan
    const planIndex = customerPlans.findIndex(
      plan => plan.customerId === customerId && plan.id === tripPlanId
    );
    
    if (planIndex === -1) {
      throw new Error(`Trip plan with ID ${tripPlanId} not found for customer ${customerId}`);
    }
    
    // Update the plan status
    customerPlans[planIndex].status = 'cancelled';
    
    // In a real application, this would also call the services' cancellation APIs
    // for each booked service
    
    // Save the updated plans
    await writeJsonFile(CUSTOMER_TRIP_PLANS_FILE, customerPlans);
    
    return customerPlans[planIndex];
  }
}

// Export a singleton instance
export const tripPlannerService = new TripPlannerService(); 