import fs from 'fs';
import path from 'path';
import { promises as fsPromises } from 'fs';

const DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'platforms', 'travel', 'planner');

// Ensure the data directory exists
export const ensureDataDir = async (): Promise<void> => {
  try {
    await fsPromises.mkdir(DATA_DIR, { recursive: true });
  } catch (error) {
    console.error('Error creating data directory:', error);
    throw error;
  }
};

// Read a JSON file
export const readJsonFile = async <T>(filename: string): Promise<T> => {
  try {
    const filePath = path.join(DATA_DIR, filename);
    const data = await fsPromises.readFile(filePath, 'utf8');
    return JSON.parse(data) as T;
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      // File doesn't exist, return empty array or object based on expected type
      return ([] as unknown) as T;
    }
    console.error(`Error reading file ${filename}:`, error);
    throw error;
  }
};

// Write to a JSON file
export const writeJsonFile = async <T>(filename: string, data: T): Promise<void> => {
  try {
    await ensureDataDir();
    const filePath = path.join(DATA_DIR, filename);
    await fsPromises.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
  } catch (error) {
    console.error(`Error writing to file ${filename}:`, error);
    throw error;
  }
};

// Check if a file exists
export const fileExists = async (filename: string): Promise<boolean> => {
  try {
    const filePath = path.join(DATA_DIR, filename);
    await fsPromises.access(filePath);
    return true;
  } catch {
    return false;
  }
};

// Simple in-memory cache to reduce file I/O
const fileCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Read JSON file with caching
export const readJsonFileWithCache = async <T>(filename: string): Promise<T> => {
  const now = Date.now();
  const cachedData = fileCache.get(filename);
  
  if (cachedData && now - cachedData.timestamp < CACHE_TTL) {
    return cachedData.data as T;
  }
  
  const data = await readJsonFile<T>(filename);
  fileCache.set(filename, { data, timestamp: now });
  return data;
};

// Invalidate cache for a specific file
export const invalidateCache = (filename: string): void => {
  fileCache.delete(filename);
};

// Clear the entire cache
export const clearCache = (): void => {
  fileCache.clear();
};