import { readJsonFileWithCache, writeJsonFile, invalidateCache } from './file-utils';
import { CustomerTripPlan, BookedService } from '@/types/travel-planner';

const BOOKING_HISTORY_FILE = 'booking-history.json';

export interface BookingHistoryEntry {
  id: string;
  customerId: string;
  tripPlanId: string;
  bookingDate: Date;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'refunded';
  services: BookedService[];
  paymentMethod?: string;
  confirmationNumber?: string;
  notes?: string;
}

export class BookingHistoryService {
  /**
   * Get booking history for a customer
   */
  async getCustomerBookingHistory(customerId: string): Promise<BookingHistoryEntry[]> {
    const allBookings = await readJsonFileWithCache<BookingHistoryEntry[]>(BOOKING_HISTORY_FILE);
    return allBookings.filter(booking => booking.customerId === customerId);
  }

  /**
   * Get a specific booking by ID
   */
  async getBookingById(bookingId: string): Promise<BookingHistoryEntry | null> {
    const allBookings = await readJsonFileWithCache<BookingHistoryEntry[]>(BOOKING_HISTORY_FILE);
    return allBookings.find(booking => booking.id === bookingId) || null;
  }

  /**
   * Add a new booking to history
   */
  async addBooking(booking: BookingHistoryEntry): Promise<BookingHistoryEntry> {
    const allBookings = await readJsonFileWithCache<BookingHistoryEntry[]>(BOOKING_HISTORY_FILE);
    
    // Ensure unique ID
    if (allBookings.some(b => b.id === booking.id)) {
      throw new Error(`Booking with ID ${booking.id} already exists`);
    }
    
    allBookings.push(booking);
    await writeJsonFile(BOOKING_HISTORY_FILE, allBookings);
    invalidateCache(BOOKING_HISTORY_FILE);
    
    return booking;
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(
    bookingId: string, 
    status: BookingHistoryEntry['status'],
    notes?: string
  ): Promise<BookingHistoryEntry> {
    const allBookings = await readJsonFileWithCache<BookingHistoryEntry[]>(BOOKING_HISTORY_FILE);
    const bookingIndex = allBookings.findIndex(booking => booking.id === bookingId);
    
    if (bookingIndex === -1) {
      throw new Error(`Booking with ID ${bookingId} not found`);
    }
    
    allBookings[bookingIndex].status = status;
    if (notes) {
      allBookings[bookingIndex].notes = notes;
    }
    
    await writeJsonFile(BOOKING_HISTORY_FILE, allBookings);
    invalidateCache(BOOKING_HISTORY_FILE);
    
    return allBookings[bookingIndex];
  }

  /**
   * Update booking confirmation number
   */
  async updateBookingConfirmation(
    bookingId: string, 
    confirmationNumber: string
  ): Promise<BookingHistoryEntry> {
    const allBookings = await readJsonFileWithCache<BookingHistoryEntry[]>(BOOKING_HISTORY_FILE);
    const bookingIndex = allBookings.findIndex(booking => booking.id === bookingId);
    
    if (bookingIndex === -1) {
      throw new Error(`Booking with ID ${bookingId} not found`);
    }
    
    allBookings[bookingIndex].confirmationNumber = confirmationNumber;
    allBookings[bookingIndex].status = 'confirmed';
    
    await writeJsonFile(BOOKING_HISTORY_FILE, allBookings);
    invalidateCache(BOOKING_HISTORY_FILE);
    
    return allBookings[bookingIndex];
  }

  /**
   * Get all bookings (for admin purposes)
   */
  async getAllBookings(): Promise<BookingHistoryEntry[]> {
    return await readJsonFileWithCache<BookingHistoryEntry[]>(BOOKING_HISTORY_FILE);
  }

  /**
   * Get bookings by status
   */
  async getBookingsByStatus(status: BookingHistoryEntry['status']): Promise<BookingHistoryEntry[]> {
    const allBookings = await readJsonFileWithCache<BookingHistoryEntry[]>(BOOKING_HISTORY_FILE);
    return allBookings.filter(booking => booking.status === status);
  }

  /**
   * Get booking statistics for a customer
   */
  async getCustomerBookingStats(customerId: string): Promise<{
    totalBookings: number;
    totalSpent: number;
    completedTrips: number;
    cancelledBookings: number;
    averageBookingValue: number;
  }> {
    const customerBookings = await this.getCustomerBookingHistory(customerId);
    
    const totalBookings = customerBookings.length;
    const totalSpent = customerBookings
      .filter(b => b.status === 'completed')
      .reduce((sum, booking) => sum + booking.totalAmount, 0);
    const completedTrips = customerBookings.filter(b => b.status === 'completed').length;
    const cancelledBookings = customerBookings.filter(b => b.status === 'cancelled').length;
    const averageBookingValue = totalBookings > 0 ? totalSpent / totalBookings : 0;
    
    return {
      totalBookings,
      totalSpent,
      completedTrips,
      cancelledBookings,
      averageBookingValue
    };
  }

  /**
   * Delete a booking (soft delete by marking as cancelled)
   */
  async deleteBooking(bookingId: string): Promise<boolean> {
    try {
      await this.updateBookingStatus(bookingId, 'cancelled', 'Booking deleted by user');
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Export a singleton instance
export const bookingHistoryService = new BookingHistoryService();
