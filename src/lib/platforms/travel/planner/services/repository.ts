import { 
  readJsonFileWithCache, 
  writeJsonFile, 
  invalidateCache 
} from './file-utils';
import { 
  TravelService, 
  FlightService, 
  AccommodationService, 
  ActivityService,
  ServiceSearchParams,
  ServiceSearchResult
} from './types';
import { Flight, Accommodation, Activity } from '@/types/travel-planner';

// File names
const FLIGHT_SERVICES_FILE = 'flight-services.json';
const ACCOMMODATION_SERVICES_FILE = 'accommodation-services.json';
const ACTIVITY_SERVICES_FILE = 'activity-services.json';
const TRIP_OPTIONS_FILE = 'trip-options.json';

/**
 * Repository for travel services that reads/writes JSON files
 */
export class TravelServiceRepository {
  /**
   * Get all flight services
   */
  async getFlightServices(): Promise<FlightService[]> {
    return await readJsonFileWithCache<FlightService[]>(FLIGHT_SERVICES_FILE);
  }

  /**
   * Get all accommodation services
   */
  async getAccommodationServices(): Promise<AccommodationService[]> {
    return await readJsonFileWithCache<AccommodationService[]>(ACCOMMODATION_SERVICES_FILE);
  }

  /**
   * Get all activity services
   */
  async getActivityServices(): Promise<ActivityService[]> {
    return await readJsonFileWithCache<ActivityService[]>(ACTIVITY_SERVICES_FILE);
  }

  /**
   * Get all travel services (flights, accommodations, activities)
   */
  async getAllServices(): Promise<TravelService[]> {
    const [flights, accommodations, activities] = await Promise.all([
      this.getFlightServices(),
      this.getAccommodationServices(),
      this.getActivityServices()
    ]);

    return [...flights, ...accommodations, ...activities];
  }

  /**
   * Get a specific service by ID
   */
  async getServiceById(id: string): Promise<TravelService | null> {
    const allServices = await this.getAllServices();
    return allServices.find(service => service.id === id) || null;
  }

  /**
   * Add a new flight service
   */
  async addFlightService(service: FlightService): Promise<FlightService> {
    const services = await this.getFlightServices();
    
    // Ensure ID is unique
    if (services.some(s => s.id === service.id)) {
      throw new Error(`Flight service with ID ${service.id} already exists`);
    }

    services.push(service);
    await writeJsonFile(FLIGHT_SERVICES_FILE, services);
    invalidateCache(FLIGHT_SERVICES_FILE);
    
    return service;
  }

  /**
   * Add a new accommodation service
   */
  async addAccommodationService(service: AccommodationService): Promise<AccommodationService> {
    const services = await this.getAccommodationServices();
    
    // Ensure ID is unique
    if (services.some(s => s.id === service.id)) {
      throw new Error(`Accommodation service with ID ${service.id} already exists`);
    }

    services.push(service);
    await writeJsonFile(ACCOMMODATION_SERVICES_FILE, services);
    invalidateCache(ACCOMMODATION_SERVICES_FILE);
    
    return service;
  }

  /**
   * Add a new activity service
   */
  async addActivityService(service: ActivityService): Promise<ActivityService> {
    const services = await this.getActivityServices();
    
    // Ensure ID is unique
    if (services.some(s => s.id === service.id)) {
      throw new Error(`Activity service with ID ${service.id} already exists`);
    }

    services.push(service);
    await writeJsonFile(ACTIVITY_SERVICES_FILE, services);
    invalidateCache(ACTIVITY_SERVICES_FILE);
    
    return service;
  }

  /**
   * Update an existing flight service
   */
  async updateFlightService(id: string, serviceUpdate: Partial<FlightService>): Promise<FlightService> {
    const services = await this.getFlightServices();
    const index = services.findIndex(service => service.id === id);
    
    if (index === -1) {
      throw new Error(`Flight service with ID ${id} not found`);
    }

    // Merge the existing service with updates
    const updatedService = {
      ...services[index],
      ...serviceUpdate,
      id, // Ensure ID remains the same
      type: 'flight' as const // Ensure type remains the same
    };

    services[index] = updatedService;
    await writeJsonFile(FLIGHT_SERVICES_FILE, services);
    invalidateCache(FLIGHT_SERVICES_FILE);
    
    return updatedService;
  }

  /**
   * Update an existing accommodation service
   */
  async updateAccommodationService(id: string, serviceUpdate: Partial<AccommodationService>): Promise<AccommodationService> {
    const services = await this.getAccommodationServices();
    const index = services.findIndex(service => service.id === id);
    
    if (index === -1) {
      throw new Error(`Accommodation service with ID ${id} not found`);
    }

    // Merge the existing service with updates
    const updatedService = {
      ...services[index],
      ...serviceUpdate,
      id, // Ensure ID remains the same
      type: 'accommodation' as const // Ensure type remains the same
    };

    services[index] = updatedService;
    await writeJsonFile(ACCOMMODATION_SERVICES_FILE, services);
    invalidateCache(ACCOMMODATION_SERVICES_FILE);
    
    return updatedService;
  }

  /**
   * Update an existing activity service
   */
  async updateActivityService(id: string, serviceUpdate: Partial<ActivityService>): Promise<ActivityService> {
    const services = await this.getActivityServices();
    const index = services.findIndex(service => service.id === id);
    
    if (index === -1) {
      throw new Error(`Activity service with ID ${id} not found`);
    }

    // Merge the existing service with updates
    const updatedService = {
      ...services[index],
      ...serviceUpdate,
      id, // Ensure ID remains the same
      type: 'activity' as const // Ensure type remains the same
    };

    services[index] = updatedService;
    await writeJsonFile(ACTIVITY_SERVICES_FILE, services);
    invalidateCache(ACTIVITY_SERVICES_FILE);
    
    return updatedService;
  }

  /**
   * Delete a service by ID
   */
  async deleteService(id: string): Promise<boolean> {
    // Try to find and delete from each service type
    try {
      const flightServices = await this.getFlightServices();
      const flightIndex = flightServices.findIndex(s => s.id === id);
      if (flightIndex !== -1) {
        flightServices.splice(flightIndex, 1);
        await writeJsonFile(FLIGHT_SERVICES_FILE, flightServices);
        invalidateCache(FLIGHT_SERVICES_FILE);
        return true;
      }

      const accommodationServices = await this.getAccommodationServices();
      const accommodationIndex = accommodationServices.findIndex(s => s.id === id);
      if (accommodationIndex !== -1) {
        accommodationServices.splice(accommodationIndex, 1);
        await writeJsonFile(ACCOMMODATION_SERVICES_FILE, accommodationServices);
        invalidateCache(ACCOMMODATION_SERVICES_FILE);
        return true;
      }

      const activityServices = await this.getActivityServices();
      const activityIndex = activityServices.findIndex(s => s.id === id);
      if (activityIndex !== -1) {
        activityServices.splice(activityIndex, 1);
        await writeJsonFile(ACTIVITY_SERVICES_FILE, activityServices);
        invalidateCache(ACTIVITY_SERVICES_FILE);
        return true;
      }

      return false; // Service not found
    } catch (error) {
      console.error(`Error deleting service with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get trip options from JSON file
   */
  async getTripOptions() {
    return await readJsonFileWithCache(TRIP_OPTIONS_FILE);
  }

  /**
   * Get available services for a specific destination
   */
  async getServicesForDestination(destination: string): Promise<{
    flights: FlightService[];
    accommodations: AccommodationService[];
    activities: ActivityService[];
  }> {
    const [flights, accommodations, activities] = await Promise.all([
      this.getFlightServices(),
      this.getAccommodationServices(),
      this.getActivityServices(),
    ]);

    return {
      flights: flights.filter(flight => 
        flight.routes.some(route => 
          route.destination.toLowerCase() === destination.toLowerCase()
        )
      ),
      accommodations: accommodations.filter(accommodation => 
        accommodation.destinations.some(
          dest => dest.toLowerCase() === destination.toLowerCase()
        )
      ),
      activities: activities.filter(activity => 
        activity.destinations.some(
          dest => dest.toLowerCase() === destination.toLowerCase()
        )
      ),
    };
  }
  
  /**
   * Search for services based on search parameters
   */
  async searchServices(params: ServiceSearchParams): Promise<{
    flights?: ServiceSearchResult<Flight>;
    accommodations?: ServiceSearchResult<Accommodation>;
    activities?: ServiceSearchResult<Activity>;
  }> {
    const result: {
      flights?: ServiceSearchResult<Flight>;
      accommodations?: ServiceSearchResult<Accommodation>;
      activities?: ServiceSearchResult<Activity>;
    } = {};

    // This is a simplified mock search implementation
    // In a real application, you would call the actual service APIs

    if (params.type === 'flight' || !params.type) {
      const flightServices = await this.getFlightServices();
      
      // Filter relevant flight services (those that match the route)
      const relevantServices = flightServices.filter(service => 
        !params.origin || !params.destination || service.routes.some(route => 
          route.origin.toLowerCase() === (params.origin?.toLowerCase() || '') &&
          route.destination.toLowerCase() === (params.destination?.toLowerCase() || '')
        )
      );
      
      if (relevantServices.length > 0) {
        // For demo purposes, just pick the first matching service
        const service = relevantServices[0];
        
        // Mock flight data - in a real application, this would come from the API
        const mockFlights: Flight[] = [
          {
            id: `flight-${Date.now()}-1`,
            airline: service.airlines[0],
            flightNumber: `${service.airlines[0].substring(0, 2)}123`,
            departureAirport: 'JFK',
            departureTime: new Date().toISOString(),
            arrivalAirport: 'LHR',
            arrivalTime: new Date(Date.now() + 7 * 3600000).toISOString(),
            duration: '7h 0m',
            price: 800,
            class: 'economy',
            layovers: [],
            platform: service.platform
          },
          {
            id: `flight-${Date.now()}-2`,
            airline: service.airlines[1] || service.airlines[0],
            flightNumber: `${(service.airlines[1] || service.airlines[0]).substring(0, 2)}456`,
            departureAirport: 'JFK',
            departureTime: new Date(Date.now() + 3600000).toISOString(),
            arrivalAirport: 'LHR',
            arrivalTime: new Date(Date.now() + 8 * 3600000).toISOString(),
            duration: '7h 30m',
            price: 750,
            class: 'economy',
            layovers: [],
            platform: service.platform
          },
        ];
        
        result.flights = {
          service,
          items: mockFlights,
          totalResults: mockFlights.length
        };
      }
    }

    if (params.type === 'accommodation' || !params.type) {
      const accommodationServices = await this.getAccommodationServices();
      
      // Filter relevant accommodation services
      const relevantServices = accommodationServices.filter(service => 
        !params.destination || service.destinations.some(dest => 
          dest.toLowerCase() === params.destination?.toLowerCase()
        )
      );
      
      if (relevantServices.length > 0) {
        // For demo purposes, just pick the first matching service
        const service = relevantServices[0];
        
        // Mock accommodation data
        const mockAccommodations: Accommodation[] = [
          {
            id: `accommodation-${Date.now()}-1`,
            name: `${service.name} Hotel`,
            type: service.accommodationTypes[0] || 'hotel',
            location: params.destination || 'London',
            checkIn: new Date().toISOString(),
            checkOut: new Date(Date.now() + 5 * 24 * 3600000).toISOString(),
            nights: 5,
            price: 200 * 5,
            amenities: service.amenities.slice(0, 4),
            rating: 4.5,
            reviewCount: 120,
            imageUrl: '/images/cover/cover-01.jpg',
            platform: service.platform
          },
          {
            id: `accommodation-${Date.now()}-2`,
            name: `${service.name} Resort`,
            type: service.accommodationTypes[1] || service.accommodationTypes[0] || 'hotel',
            location: params.destination || 'London',
            checkIn: new Date().toISOString(),
            checkOut: new Date(Date.now() + 5 * 24 * 3600000).toISOString(),
            nights: 5,
            price: 300 * 5,
            amenities: service.amenities.slice(0, 6),
            rating: 4.8,
            reviewCount: 85,
            imageUrl: '/images/cover/cover-02.jpg',
            platform: service.platform
          },
        ];
        
        result.accommodations = {
          service,
          items: mockAccommodations,
          totalResults: mockAccommodations.length
        };
      }
    }

    if (params.type === 'activity' || !params.type) {
      const activityServices = await this.getActivityServices();
      
      // Filter relevant activity services
      const relevantServices = activityServices.filter(service => 
        !params.destination || service.destinations.some(dest => 
          dest.toLowerCase() === params.destination?.toLowerCase()
        )
      );
      
      if (relevantServices.length > 0) {
        // For demo purposes, just pick the first matching service
        const service = relevantServices[0];
        
        // Mock activity data
        const mockActivities: Activity[] = [
          {
            id: `activity-${Date.now()}-1`,
            name: `${params.destination || 'City'} Walking Tour`,
            description: `Explore the highlights of ${params.destination || 'the city'} with a knowledgeable local guide.`,
            location: params.destination || 'London',
            date: new Date(Date.now() + 24 * 3600000).toISOString(),
            duration: '3 hours',
            price: 45,
            category: 'Sightseeing Tour',
            rating: 4.6,
            reviewCount: 230,
            imageUrl: '/images/illustration/illustration-01.svg',
            platform: service.platform
          },
          {
            id: `activity-${Date.now()}-2`,
            name: `${params.destination || 'Local'} Food Experience`,
            description: `Taste the authentic flavors of ${params.destination || 'the region'} with a culinary expert.`,
            location: params.destination || 'London',
            date: new Date(Date.now() + 2 * 24 * 3600000).toISOString(),
            duration: '4 hours',
            price: 65,
            category: 'Food Tour',
            rating: 4.9,
            reviewCount: 175,
            imageUrl: '/images/illustration/illustration-02.svg',
            platform: service.platform
          },
        ];
        
        result.activities = {
          service,
          items: mockActivities,
          totalResults: mockActivities.length
        };
      }
    }

    return result;
  }
}

// Export a singleton instance
export const travelServiceRepository = new TravelServiceRepository(); 