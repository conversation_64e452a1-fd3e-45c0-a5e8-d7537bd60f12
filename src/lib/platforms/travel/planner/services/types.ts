import { Flight, Accommodation, Activity } from '@/types/travel-planner';

export interface TravelService {
  id: string;
  name: string;
  description: string;
  type: 'flight' | 'accommodation' | 'activity';
  platform: string;
  baseUrl: string;
  logo: string;
  apiKey?: string;
  isActive: boolean;
  priority: number;
  commission: number;  // Percentage commission for the service
  metadata: Record<string, any>;
}

export interface FlightService extends TravelService {
  type: 'flight';
  airlines: string[];
  routes: {
    origin: string;
    destination: string;
  }[];
  features: ('direct' | 'refundable' | 'firstClass' | 'businessClass' | 'economy')[];
  searchEndpoint: string;
  bookingEndpoint: string;
  cancellationPolicy: string;
}

export interface AccommodationService extends TravelService {
  type: 'accommodation';
  accommodationTypes: ('hotel' | 'apartment' | 'hostel' | 'resort' | 'villa')[];
  destinations: string[];
  amenities: string[];
  searchEndpoint: string;
  bookingEndpoint: string;
  cancellationPolicy: string;
}

export interface ActivityService extends TravelService {
  type: 'activity';
  activityTypes: string[];
  destinations: string[];
  searchEndpoint: string;
  bookingEndpoint: string;
  cancellationPolicy: string;
}

export interface ServiceSearchParams {
  type: 'flight' | 'accommodation' | 'activity';
  destination?: string;
  origin?: string;
  checkIn?: Date;
  checkOut?: Date;
  duration?: number;
  passengers?: number;
  budget?: number;
  [key: string]: any;
}

export interface ServiceSearchResult<T> {
  service: TravelService;
  items: T[];
  totalResults: number;
  filters?: Record<string, any>;
  metadata?: Record<string, any>;
} 