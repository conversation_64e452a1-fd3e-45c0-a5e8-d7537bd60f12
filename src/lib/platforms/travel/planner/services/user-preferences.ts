import { readJsonFileWithCache, writeJsonFile, invalidateCache } from './file-utils';
import { UserPreferences } from '@/types/travel-planner';

const USER_PREFERENCES_FILE = 'user-preferences.json';

export class UserPreferencesService {
  /**
   * Get user preferences by user ID
   */
  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    const allPreferences = await readJsonFileWithCache<UserPreferences[]>(USER_PREFERENCES_FILE);
    return allPreferences.find(pref => pref.userId === userId) || null;
  }

  /**
   * Save or update user preferences
   */
  async saveUserPreferences(preferences: UserPreferences): Promise<UserPreferences> {
    const allPreferences = await readJsonFileWithCache<UserPreferences[]>(USER_PREFERENCES_FILE);
    
    // Find existing preferences for this user
    const existingIndex = allPreferences.findIndex(pref => pref.userId === preferences.userId);
    
    if (existingIndex !== -1) {
      // Update existing preferences
      allPreferences[existingIndex] = preferences;
    } else {
      // Add new preferences
      allPreferences.push(preferences);
    }
    
    await writeJsonFile(USER_PREFERENCES_FILE, allPreferences);
    invalidateCache(USER_PREFERENCES_FILE);
    
    return preferences;
  }

  /**
   * Update specific preference fields
   */
  async updateUserPreferences(
    userId: string, 
    updates: Partial<Omit<UserPreferences, 'userId'>>
  ): Promise<UserPreferences> {
    const existingPreferences = await this.getUserPreferences(userId);
    
    if (!existingPreferences) {
      // Create default preferences if none exist
      const defaultPreferences: UserPreferences = {
        userId,
        preferredAirlines: [],
        preferredAccommodationTypes: [],
        preferredActivities: [],
        budgetRange: { min: 500, max: 5000 },
        travelStyle: 'comfort',
        maxFlightLayovers: 1,
        minAccommodationRating: 3.5,
        dietaryRestrictions: [],
        accessibilityNeeds: [],
        savedDestinations: [],
        notificationPreferences: {
          email: true,
          sms: false,
          priceAlerts: true,
          bookingReminders: true
        }
      };
      
      const updatedPreferences = { ...defaultPreferences, ...updates };
      return await this.saveUserPreferences(updatedPreferences);
    }
    
    const updatedPreferences = { ...existingPreferences, ...updates };
    return await this.saveUserPreferences(updatedPreferences);
  }

  /**
   * Add a destination to saved destinations
   */
  async addSavedDestination(userId: string, destination: string): Promise<UserPreferences> {
    const preferences = await this.getUserPreferences(userId);
    
    if (!preferences) {
      return await this.updateUserPreferences(userId, {
        savedDestinations: [destination]
      });
    }
    
    if (!preferences.savedDestinations.includes(destination)) {
      preferences.savedDestinations.push(destination);
      return await this.saveUserPreferences(preferences);
    }
    
    return preferences;
  }

  /**
   * Remove a destination from saved destinations
   */
  async removeSavedDestination(userId: string, destination: string): Promise<UserPreferences> {
    const preferences = await this.getUserPreferences(userId);
    
    if (!preferences) {
      return await this.updateUserPreferences(userId, { savedDestinations: [] });
    }
    
    preferences.savedDestinations = preferences.savedDestinations.filter(dest => dest !== destination);
    return await this.saveUserPreferences(preferences);
  }

  /**
   * Get all users with preferences (for admin purposes)
   */
  async getAllUserPreferences(): Promise<UserPreferences[]> {
    return await readJsonFileWithCache<UserPreferences[]>(USER_PREFERENCES_FILE);
  }

  /**
   * Delete user preferences
   */
  async deleteUserPreferences(userId: string): Promise<boolean> {
    const allPreferences = await readJsonFileWithCache<UserPreferences[]>(USER_PREFERENCES_FILE);
    const filteredPreferences = allPreferences.filter(pref => pref.userId !== userId);
    
    if (filteredPreferences.length === allPreferences.length) {
      return false; // User not found
    }
    
    await writeJsonFile(USER_PREFERENCES_FILE, filteredPreferences);
    invalidateCache(USER_PREFERENCES_FILE);
    
    return true;
  }
}

// Export a singleton instance
export const userPreferencesService = new UserPreferencesService();
