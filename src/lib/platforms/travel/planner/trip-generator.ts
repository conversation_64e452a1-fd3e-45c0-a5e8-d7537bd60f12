import { TripOption, Flight, Accommodation, Activity, Layover } from '@/types/travel-planner';

// Mock data for trip generation
const airlines = [
  { name: 'Delta Airlines', code: 'DL' },
  { name: 'United Airlines', code: 'UA' },
  { name: 'American Airlines', code: 'AA' },
  { name: 'British Airways', code: 'BA' },
  { name: '<PERSON><PERSON><PERSON>sa', code: 'LH' },
  { name: 'Air France', code: 'AF' },
  { name: 'Emirates', code: 'EK' },
  { name: 'Singapore Airlines', code: 'SQ' },
  { name: 'Cathay Pacific', code: 'CX' },
  { name: 'Qatar Airways', code: 'QR' },
];

const airports: Record<string, { code: string, name: string }> = {
  'New York': { code: 'JFK', name: 'John F. Kennedy International Airport' },
  'Los Angeles': { code: 'LAX', name: 'Los Angeles International Airport' },
  'Chicago': { code: 'ORD', name: "O'Hare International Airport" },
  'London': { code: 'LHR', name: 'Heathrow Airport' },
  'Paris': { code: 'CDG', name: 'Charles <PERSON> Airport' },
  'Tokyo': { code: 'HND', name: 'Haneda Airport' },
  'Sydney': { code: 'SYD', name: 'Sydney Airport' },
  'Dubai': { code: 'DXB', name: 'Dubai International Airport' },
  'Singapore': { code: 'SIN', name: 'Singapore Changi Airport' },
  'Hong Kong': { code: 'HKG', name: 'Hong Kong International Airport' },
  'Bali': { code: 'DPS', name: 'Ngurah Rai International Airport' },
};

const hotelChains = [
  'Marriott',
  'Hilton',
  'Hyatt',
  'InterContinental',
  'Sheraton',
  'Westin',
  'Four Seasons',
  'Ritz-Carlton',
  'Mandarin Oriental',
  'St. Regis',
];

const activityTypes = [
  'Sightseeing Tour',
  'Museum Visit',
  'Food Tour',
  'Adventure Activity',
  'Cultural Experience',
  'Beach Day',
  'Shopping Trip',
  'Nightlife',
  'Spa Day',
  'Nature Excursion',
];

const platforms = [
  'Expedia',
  'Booking.com',
  'Airbnb',
  'Agoda',
  'Hotels.com',
  'Skyscanner',
  'Kayak',
  'TripAdvisor',
  'GetYourGuide',
  'Viator',
];

// Helper functions
const generateRandomId = (): string => {
  return Math.random().toString(36).substring(2, 15);
};

const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

const getRandomItems = <T>(array: T[], count: number): T[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

const getRandomNumber = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const getRandomPrice = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Generate a random flight
const generateFlight = (
  origin: string,
  destination: string,
  departureDate: Date,
  returnDate?: Date
): Flight => {
  const airline = getRandomItem(airlines);
  const flightNumber = `${airline.code}${getRandomNumber(100, 9999)}`;
  const departureAirport = airports[origin]?.code || 'JFK';
  const arrivalAirport = airports[destination]?.code || 'CDG';
  
  const departureTime = new Date(departureDate);
  departureTime.setHours(getRandomNumber(6, 22), getRandomNumber(0, 59));
  
  const flightDurationHours = getRandomNumber(3, 14);
  const flightDurationMinutes = getRandomNumber(0, 59);
  
  const arrivalTime = new Date(departureTime);
  arrivalTime.setHours(
    departureTime.getHours() + flightDurationHours,
    departureTime.getMinutes() + flightDurationMinutes
  );
  
  const layoverCount = Math.random() > 0.7 ? getRandomNumber(1, 2) : 0;
  const layovers: Layover[] = [];
  
  for (let i = 0; i < layoverCount; i++) {
    const layoverAirports = Object.values(airports)
      .filter(airport => airport.code !== departureAirport && airport.code !== arrivalAirport)
      .map(airport => airport.code);
    
    const layoverAirport = getRandomItem(layoverAirports);
    const layoverDurationHours = getRandomNumber(1, 3);
    const layoverDurationMinutes = getRandomNumber(0, 59);
    
    layovers.push({
      airport: layoverAirport,
      duration: `${layoverDurationHours}h ${layoverDurationMinutes}m`,
    });
  }
  
  const flightClass = getRandomItem(['economy', 'premium_economy', 'business', 'first']) as 'economy' | 'premium_economy' | 'business' | 'first';
  const basePrice = getRandomPrice(200, 1500);
  const classMultiplier = flightClass === 'economy' ? 1 : 
                          flightClass === 'premium_economy' ? 1.5 : 
                          flightClass === 'business' ? 3 : 5;
  
  return {
    id: generateRandomId(),
    airline: airline.name,
    flightNumber,
    departureAirport,
    departureTime: departureTime.toISOString(),
    arrivalAirport,
    arrivalTime: arrivalTime.toISOString(),
    duration: `${flightDurationHours}h ${flightDurationMinutes}m`,
    price: Math.round(basePrice * classMultiplier),
    class: flightClass,
    layovers,
    platform: getRandomItem(platforms),
  };
};

// Generate a random accommodation
const generateAccommodation = (
  destination: string,
  checkInDate: Date,
  nights: number
): Accommodation => {
  const hotelChain = getRandomItem(hotelChains);
  const accommodationType = getRandomItem(['hotel', 'apartment', 'hostel', 'resort', 'villa']) as 'hotel' | 'apartment' | 'hostel' | 'resort' | 'villa';
  
  const checkIn = new Date(checkInDate);
  const checkOut = new Date(checkIn);
  checkOut.setDate(checkOut.getDate() + nights);
  
  const amenities = getRandomItems([
    'Free WiFi',
    'Swimming Pool',
    'Fitness Center',
    'Spa',
    'Restaurant',
    'Bar',
    'Room Service',
    'Concierge',
    'Airport Shuttle',
    'Parking',
    'Breakfast Included',
    'Pet Friendly',
    'Business Center',
    'Laundry Service',
    'Air Conditioning',
  ], getRandomNumber(3, 8));
  
  const basePrice = getRandomPrice(50, 500);
  const typeMultiplier = accommodationType === 'hostel' ? 0.5 : 
                         accommodationType === 'apartment' ? 1.2 : 
                         accommodationType === 'hotel' ? 1 : 
                         accommodationType === 'resort' ? 2 : 2.5;
  
  return {
    id: generateRandomId(),
    name: `${hotelChain} ${destination} ${accommodationType === 'hotel' ? 'Hotel' : accommodationType === 'resort' ? 'Resort' : accommodationType === 'villa' ? 'Villa' : accommodationType === 'apartment' ? 'Suites' : 'Hostel'}`,
    type: accommodationType,
    location: destination,
    checkIn: checkIn.toISOString(),
    checkOut: checkOut.toISOString(),
    nights,
    price: Math.round(basePrice * typeMultiplier * nights),
    amenities,
    rating: getRandomNumber(30, 50) / 10,
    reviewCount: getRandomNumber(50, 2000),
    imageUrl: `/images/cover/cover-0${getRandomNumber(1, 5)}.${accommodationType === 'hotel' || accommodationType === 'resort' ? 'jpg' : 'png'}`,
    platform: getRandomItem(platforms),
  };
};

// Generate a random activity
const generateActivity = (
  destination: string,
  date: Date
): Activity => {
  const activityType = getRandomItem(activityTypes);
  const activityDate = new Date(date);
  activityDate.setHours(getRandomNumber(8, 18), 0, 0);
  
  const durationHours = getRandomNumber(1, 8);
  
  const categories = [
    'Cultural',
    'Adventure',
    'Food & Drink',
    'Nature',
    'Entertainment',
    'Relaxation',
    'Shopping',
    'Historical',
    'Educational',
    'Sports',
  ];
  
  return {
    id: generateRandomId(),
    name: `${destination} ${activityType}`,
    description: `Experience the best of ${destination} with this amazing ${activityType.toLowerCase()}.`,
    location: destination,
    date: activityDate.toISOString(),
    duration: `${durationHours} ${durationHours === 1 ? 'hour' : 'hours'}`,
    price: getRandomPrice(20, 200),
    category: getRandomItem(categories),
    rating: getRandomNumber(35, 50) / 10,
    reviewCount: getRandomNumber(20, 500),
    imageUrl: `/images/illustration/illustration-0${getRandomNumber(1, 4)}.svg`,
    platform: getRandomItem(platforms),
  };
};

// Generate trip options
export const generateTripOptions = (
  destination: string,
  duration: number,
  budget: number,
  groupSize: number,
  baseOption?: TripOption
): TripOption[] => {
  const options: TripOption[] = [];
  const optionCount = getRandomNumber(3, 5);
  
  // Determine origin (for demo purposes, we'll use New York as default)
  const origin = 'New York';
  
  // Calculate dates
  const today = new Date();
  const departureDate = new Date(today);
  departureDate.setDate(departureDate.getDate() + 30); // 30 days from now
  
  const returnDate = new Date(departureDate);
  returnDate.setDate(returnDate.getDate() + duration);
  
  for (let i = 0; i < optionCount; i++) {
    // Generate flights
    const outboundFlight = generateFlight(origin, destination, departureDate);
    const returnFlight = generateFlight(destination, origin, returnDate);
    
    // Generate accommodation
    const accommodation = generateAccommodation(
      destination,
      new Date(outboundFlight.arrivalTime),
      duration
    );
    
    // Generate activities (1-3 per day)
    const activities: Activity[] = [];
    const activityDays = Math.min(duration, 7); // Limit to 7 days of activities for simplicity
    
    for (let day = 0; day < activityDays; day++) {
      const activityDate = new Date(new Date(outboundFlight.arrivalTime));
      activityDate.setDate(activityDate.getDate() + day);
      
      const dailyActivities = getRandomNumber(1, 3);
      
      for (let j = 0; j < dailyActivities; j++) {
        activities.push(generateActivity(destination, activityDate));
      }
    }
    
    // Calculate total price
    const flightPrice = (outboundFlight.price + returnFlight.price) * groupSize;
    const accommodationPrice = accommodation.price * (groupSize > 1 ? Math.ceil(groupSize / 2) : 1); // Assume 2 people per room
    const activitiesPrice = activities.reduce((sum, activity) => sum + activity.price, 0) * groupSize;
    
    const totalPrice = flightPrice + accommodationPrice + activitiesPrice;
    
    // Generate tags
    const tags: string[] = [];
    
    if (totalPrice < budget * 0.7) {
      tags.push('Budget-friendly');
    } else if (totalPrice > budget * 1.2) {
      tags.push('Luxury');
    } else {
      tags.push('Mid-range');
    }
    
    if (outboundFlight.layovers.length === 0 && returnFlight.layovers.length === 0) {
      tags.push('Direct flights');
    }
    
    if (accommodation.rating >= 4.5) {
      tags.push('Top-rated accommodation');
    }
    
    if (activities.length > 10) {
      tags.push('Activity-packed');
    }
    
    if (accommodation.type === 'resort' || accommodation.type === 'villa') {
      tags.push('Luxury stay');
    }
    
    // Create option title based on key features
    const flightClass = outboundFlight.class === returnFlight.class ? outboundFlight.class : 'mixed';
    const flightClassDisplay = flightClass === 'economy' ? 'Economy' : 
                              flightClass === 'premium_economy' ? 'Premium Economy' : 
                              flightClass === 'business' ? 'Business' : 
                              flightClass === 'mixed' ? 'Mixed Class' : 'First Class';
    
    const title = `${duration}-day ${destination} Trip | ${flightClassDisplay} | ${accommodation.type.charAt(0).toUpperCase() + accommodation.type.slice(1)}`;
    
    // Create option description
    const description = `${duration}-day trip to ${destination} with ${flightClassDisplay.toLowerCase()} flights, ${accommodation.nights}-night stay at ${accommodation.name}, and ${activities.length} activities. Perfect for ${groupSize} ${groupSize === 1 ? 'traveler' : 'travelers'}.`;
    
    // Create the option
    const option: TripOption = {
      id: generateRandomId(),
      title,
      description,
      price: totalPrice,
      flights: [outboundFlight, returnFlight],
      accommodations: [accommodation],
      activities,
      totalDuration: duration,
      tags,
      imageUrl: `/images/cover/cover-0${getRandomNumber(1, 5)}.${getRandomNumber(1, 3) === 1 ? 'png' : 'jpg'}`,
      rating: getRandomNumber(35, 49) / 10,
      reviewCount: getRandomNumber(10, 100),
      platforms: [...new Set([outboundFlight.platform, returnFlight.platform, accommodation.platform, ...activities.map(a => a.platform)])],
    };
    
    options.push(option);
  }
  
  // If we have a base option, create variations based on it
  if (baseOption) {
    return options.map(option => {
      // Modify some aspects while keeping others the same
      const variationType = getRandomNumber(1, 3);
      
      if (variationType === 1) {
        // Vary flights
        return {
          ...option,
          title: `${option.title} (Flight Variation)`,
          description: `${option.description} This is a flight variation.`,
          tags: [...option.tags, 'Flight variation'],
        };
      } else if (variationType === 2) {
        // Vary accommodation
        return {
          ...option,
          title: `${option.title} (Accommodation Variation)`,
          description: `${option.description} This is an accommodation variation.`,
          tags: [...option.tags, 'Accommodation variation'],
        };
      } else {
        // Vary activities
        return {
          ...option,
          title: `${option.title} (Activities Variation)`,
          description: `${option.description} This is an activities variation.`,
          tags: [...option.tags, 'Activities variation'],
        };
      }
    });
  }
  
  return options;
}; 