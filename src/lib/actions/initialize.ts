/**
 * Actions Store Initialization
 * 
 * This file handles the initialization of the Actions Store system,
 * including action registration, security setup, and integration configuration.
 */

import { actionsStore } from '../actionsStore';
import { actionSecurity } from '../security/actionSecurity';
import { initializeActions } from './index';

// Initialization status
let initialized = false;
let initializationPromise: Promise<void> | null = null;

// Initialize the Actions Store system
export async function initializeActionsStore(): Promise<void> {
  if (initialized) {
    return;
  }

  if (initializationPromise) {
    return initializationPromise;
  }

  initializationPromise = performInitialization();
  return initializationPromise;
}

async function performInitialization(): Promise<void> {
  try {
    console.log('🚀 Initializing ABN Actions Store...');

    // 1. Initialize core actions
    console.log('📋 Registering core actions...');
    initializeActions();

    // 2. Setup security configuration
    console.log('🔒 Configuring security settings...');
    await setupSecurity();

    // 3. Verify data directories
    console.log('📁 Verifying data directories...');
    await verifyDataDirectories();

    // 4. Load configuration
    console.log('⚙️  Loading configuration...');
    await loadConfiguration();

    // 5. Perform health checks
    console.log('🏥 Performing health checks...');
    await performHealthChecks();

    initialized = true;
    console.log('✅ Actions Store initialized successfully');

  } catch (error) {
    console.error('❌ Failed to initialize Actions Store:', error);
    throw error;
  }
}

async function setupSecurity(): Promise<void> {
  // Load security configuration from settings
  const fs = await import('fs');
  const path = await import('path');
  
  const configPath = path.join(process.cwd(), 'data/apps/platforms/actionStore/config/settings.json');
  
  if (fs.existsSync(configPath)) {
    try {
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      
      if (config.actionStore?.security) {
        actionSecurity.updateConfig(config.actionStore.security);
        console.log('🔒 Security configuration loaded from settings');
      }
    } catch (error) {
      console.warn('⚠️  Could not load security configuration:', error);
    }
  }
}

async function verifyDataDirectories(): Promise<void> {
  const fs = await import('fs');
  const path = await import('path');
  
  const baseDir = path.join(process.cwd(), 'data/apps/platforms/actionStore');
  const requiredDirs = [
    'config',
    'actions',
    'logs',
    'security',
    'notifications',
    'qr',
    'cache'
  ];

  for (const dir of requiredDirs) {
    const dirPath = path.join(baseDir, dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  }
}

async function loadConfiguration(): Promise<void> {
  const fs = await import('fs');
  const path = await import('path');
  
  const configPath = path.join(process.cwd(), 'data/apps/platforms/actionStore/config/settings.json');
  
  if (!fs.existsSync(configPath)) {
    // Create default configuration
    const defaultConfig = {
      actionStore: {
        version: "1.0.0",
        name: "ABN Actions Store",
        description: "Centralized action execution system for ABN Asia platform",
        settings: {
          enableLogging: true,
          logRetentionDays: 30,
          enableRateLimit: true,
          defaultRateLimit: {
            maxCalls: 100,
            windowMs: 60000
          },
          enablePermissions: true,
          enableMetrics: true,
          enableCaching: false,
          maxLogFileSize: "10MB",
          enableDebugMode: false
        },
        security: {
          requireAuthentication: true,
          allowAnonymousActions: ["system.getInfo"],
          adminActions: ["platform.createUser", "cache.operation", "platform.fileOperation"],
          rateLimitByUser: true,
          enableAuditLog: true
        },
        integrations: {
          chatbot: {
            enabled: true,
            allowedActions: ["system.getInfo", "data.query", "notification.send", "platform.analytics"],
            maxPayloadSize: "1MB"
          },
          apiServer: {
            enabled: true,
            allowedActions: "*",
            requireApiKey: false,
            maxPayloadSize: "5MB"
          },
          dashboard: {
            enabled: true,
            allowedActions: "*",
            requireLogin: true
          }
        }
      },
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: "1.0.0"
      }
    };

    fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2));
    console.log('⚙️  Created default configuration file');
  }
}

async function performHealthChecks(): Promise<void> {
  // Check if actions are registered
  const actions = actionsStore.listAvailableActions();
  if (actions.length === 0) {
    throw new Error('No actions registered');
  }
  console.log(`✅ ${actions.length} actions registered`);

  // Check security system
  const securityStats = actionSecurity.getSecurityStats();
  console.log(`🔒 Security system active (audit log: ${securityStats.auditLogEnabled})`);

  // Test a simple action execution
  try {
    const result = await actionsStore.callAction('system.getInfo', {}, {
      userId: 'system',
      source: 'system',
      permissions: ['system.read']
    });
    
    if (result.success) {
      console.log('✅ Test action execution successful');
    } else {
      console.warn('⚠️  Test action execution failed:', result.error);
    }
  } catch (error) {
    console.warn('⚠️  Could not test action execution:', error);
  }
}

// Get initialization status
export function isInitialized(): boolean {
  return initialized;
}

// Get system status
export async function getSystemStatus(): Promise<{
  initialized: boolean;
  actionsCount: number;
  securityEnabled: boolean;
  integrationsEnabled: string[];
  lastInitialized?: string;
}> {
  const actions = actionsStore.listAvailableActions();
  const securityStats = actionSecurity.getSecurityStats();
  
  return {
    initialized,
    actionsCount: actions.length,
    securityEnabled: securityStats.auditLogEnabled,
    integrationsEnabled: ['chatbot', 'api', 'dashboard'],
    lastInitialized: initialized ? new Date().toISOString() : undefined
  };
}

// Cleanup function
export async function cleanupActionsStore(): Promise<void> {
  console.log('🧹 Cleaning up Actions Store...');
  
  // Clear rate limits
  actionSecurity.clearRateLimits();
  
  // Clean up old logs (if needed)
  await cleanupOldLogs();
  
  console.log('✅ Actions Store cleanup completed');
}

async function cleanupOldLogs(): Promise<void> {
  const fs = await import('fs');
  const path = await import('path');
  
  const logsDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/logs');
  
  if (!fs.existsSync(logsDir)) {
    return;
  }
  
  const retentionDays = 30; // Default retention
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
  
  try {
    const files = fs.readdirSync(logsDir);
    let deletedCount = 0;
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }
    }
    
    if (deletedCount > 0) {
      console.log(`🗑️  Deleted ${deletedCount} old log files`);
    }
  } catch (error) {
    console.warn('⚠️  Could not cleanup old logs:', error);
  }
}

// Auto-initialize when module is imported (in production)
if (process.env.NODE_ENV === 'production') {
  initializeActionsStore().catch(error => {
    console.error('Failed to auto-initialize Actions Store:', error);
  });
}

// Export for manual initialization in development
export { actionsStore, actionSecurity };
