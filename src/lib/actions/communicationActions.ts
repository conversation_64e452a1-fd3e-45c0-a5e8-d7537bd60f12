import { actionsStore, Action, ActionParameter, ActionMetadata } from '../actionsStore';
import fs from 'fs';
import path from 'path';

// Email service configuration
interface EmailConfig {
  provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
  apiKey?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUser?: string;
  smtpPass?: string;
  fromEmail: string;
  fromName?: string;
}

// Telegram configuration
interface TelegramConfig {
  botToken: string;
  defaultChatId?: string;
}

// LLM configuration
interface LLMConfig {
  provider: 'openai' | 'groq' | 'anthropic' | 'local';
  apiKey?: string;
  baseUrl?: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
}

// Load configuration from JSON files
function loadEmailConfig(): EmailConfig {
  try {
    const configPath = path.join(process.cwd(), 'data/apps/platforms/actionStore/config/email.json');
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    }
  } catch (error) {
    console.warn('Email config not found, using defaults');
  }
  
  return {
    provider: 'smtp',
    smtpHost: process.env.SMTP_HOST || 'localhost',
    smtpPort: parseInt(process.env.SMTP_PORT || '587'),
    smtpUser: process.env.SMTP_USER || '',
    smtpPass: process.env.SMTP_PASS || '',
    fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
    fromName: process.env.FROM_NAME || 'ABN Green Platform'
  };
}

function loadTelegramConfig(): TelegramConfig {
  try {
    const configPath = path.join(process.cwd(), 'data/apps/platforms/actionStore/config/telegram.json');
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    }
  } catch (error) {
    console.warn('Telegram config not found, using defaults');
  }
  
  return {
    botToken: process.env.TELEGRAM_BOT_TOKEN || '',
    defaultChatId: process.env.TELEGRAM_DEFAULT_CHAT_ID
  };
}

function loadLLMConfig(): LLMConfig {
  try {
    const configPath = path.join(process.cwd(), 'data/apps/platforms/actionStore/config/llm.json');
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    }
  } catch (error) {
    console.warn('LLM config not found, using defaults');
  }
  
  return {
    provider: 'openai',
    apiKey: process.env.OPENAI_API_KEY || process.env.GROQ_API_KEY || '',
    model: process.env.LLM_MODEL || 'gpt-3.5-turbo',
    maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '1000'),
    temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7')
  };
}

// Email sending function
async function sendEmail(payload: any): Promise<any> {
  const config = loadEmailConfig();
  const { to, subject, body, html, cc, bcc, attachments } = payload;

  // Log the email attempt
  const logData = {
    timestamp: new Date().toISOString(),
    to,
    subject,
    provider: config.provider,
    success: false,
    error: null
  };

  try {
    // For demonstration, we'll simulate email sending
    // In production, integrate with actual email services
    
    if (config.provider === 'smtp') {
      // SMTP implementation would go here
      console.log(`Sending email via SMTP to ${to}: ${subject}`);
    } else if (config.provider === 'sendgrid') {
      // SendGrid implementation would go here
      console.log(`Sending email via SendGrid to ${to}: ${subject}`);
    }

    // Simulate successful send
    logData.success = true;
    
    // Save to logs
    const logsDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/logs/email');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    const logFile = path.join(logsDir, `${new Date().toISOString().split('T')[0]}.json`);
    let logs = [];
    if (fs.existsSync(logFile)) {
      logs = JSON.parse(fs.readFileSync(logFile, 'utf8'));
    }
    logs.push(logData);
    fs.writeFileSync(logFile, JSON.stringify(logs, null, 2));

    return {
      success: true,
      messageId: `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      to,
      subject,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logData.error = error instanceof Error ? error.message : 'Unknown error';
    throw error;
  }
}

// Telegram message sending function
async function sendTelegramMessage(payload: any): Promise<any> {
  const config = loadTelegramConfig();
  const { chatId, message, parseMode = 'HTML', disablePreview = false } = payload;

  if (!config.botToken) {
    throw new Error('Telegram bot token not configured');
  }

  const targetChatId = chatId || config.defaultChatId;
  if (!targetChatId) {
    throw new Error('No chat ID provided and no default chat ID configured');
  }

  try {
    // For demonstration, we'll simulate Telegram API call
    // In production, make actual API call to Telegram
    console.log(`Sending Telegram message to ${targetChatId}: ${message}`);

    // Simulate API call
    const response = {
      ok: true,
      result: {
        message_id: Math.floor(Math.random() * 1000000),
        date: Math.floor(Date.now() / 1000),
        chat: { id: targetChatId },
        text: message
      }
    };

    // Log the message
    const logData = {
      timestamp: new Date().toISOString(),
      chatId: targetChatId,
      message,
      messageId: response.result.message_id,
      success: true
    };

    const logsDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/logs/telegram');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    const logFile = path.join(logsDir, `${new Date().toISOString().split('T')[0]}.json`);
    let logs = [];
    if (fs.existsSync(logFile)) {
      logs = JSON.parse(fs.readFileSync(logFile, 'utf8'));
    }
    logs.push(logData);
    fs.writeFileSync(logFile, JSON.stringify(logs, null, 2));

    return {
      success: true,
      messageId: response.result.message_id,
      chatId: targetChatId,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    throw new Error(`Failed to send Telegram message: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// LLM calling function
async function callLLM(payload: any): Promise<any> {
  const config = loadLLMConfig();
  const { 
    prompt, 
    systemMessage, 
    model = config.model, 
    maxTokens = config.maxTokens, 
    temperature = config.temperature,
    provider = config.provider 
  } = payload;

  if (!config.apiKey && provider !== 'local') {
    throw new Error(`API key not configured for ${provider}`);
  }

  try {
    // For demonstration, we'll simulate LLM API calls
    // In production, integrate with actual LLM APIs
    
    let response;
    const requestData = {
      prompt,
      systemMessage,
      model,
      maxTokens,
      temperature
    };

    if (provider === 'openai') {
      console.log(`Calling OpenAI API with model ${model}`);
      // OpenAI API implementation would go here
      response = {
        choices: [{
          message: {
            content: `This is a simulated response from OpenAI ${model} for prompt: "${prompt.substring(0, 50)}..."`
          }
        }],
        usage: {
          prompt_tokens: prompt.length / 4,
          completion_tokens: 50,
          total_tokens: (prompt.length / 4) + 50
        }
      };
    } else if (provider === 'groq') {
      console.log(`Calling Groq API with model ${model}`);
      // Groq API implementation would go here
      response = {
        choices: [{
          message: {
            content: `This is a simulated response from Groq ${model} for prompt: "${prompt.substring(0, 50)}..."`
          }
        }],
        usage: {
          prompt_tokens: prompt.length / 4,
          completion_tokens: 50,
          total_tokens: (prompt.length / 4) + 50
        }
      };
    } else if (provider === 'anthropic') {
      console.log(`Calling Anthropic API with model ${model}`);
      // Anthropic API implementation would go here
      response = {
        content: [{
          text: `This is a simulated response from Anthropic ${model} for prompt: "${prompt.substring(0, 50)}..."`
        }],
        usage: {
          input_tokens: prompt.length / 4,
          output_tokens: 50
        }
      };
    }

    // Log the LLM call
    const logData = {
      timestamp: new Date().toISOString(),
      provider,
      model,
      promptLength: prompt.length,
      responseLength: response?.choices?.[0]?.message?.content?.length || response?.content?.[0]?.text?.length || 0,
      usage: response?.usage || {},
      success: true
    };

    const logsDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/logs/llm');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    const logFile = path.join(logsDir, `${new Date().toISOString().split('T')[0]}.json`);
    let logs = [];
    if (fs.existsSync(logFile)) {
      logs = JSON.parse(fs.readFileSync(logFile, 'utf8'));
    }
    logs.push(logData);
    fs.writeFileSync(logFile, JSON.stringify(logs, null, 2));

    return {
      success: true,
      provider,
      model,
      response: response?.choices?.[0]?.message?.content || response?.content?.[0]?.text || 'No response',
      usage: response?.usage || {},
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    throw new Error(`Failed to call ${provider} LLM: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Register communication actions
export function registerCommunicationActions(): void {
  // Email action
  const emailAction: Action = {
    key: 'communication.sendEmail',
    description: 'Send email messages via configured email provider',
    category: 'communication',
    tags: ['email', 'notification', 'messaging'],
    parameters: [
      {
        name: 'to',
        type: 'string',
        required: true,
        description: 'Recipient email address'
      },
      {
        name: 'subject',
        type: 'string',
        required: true,
        description: 'Email subject line'
      },
      {
        name: 'body',
        type: 'string',
        required: true,
        description: 'Email body content (plain text)'
      },
      {
        name: 'html',
        type: 'string',
        required: false,
        description: 'Email body content (HTML format)'
      },
      {
        name: 'cc',
        type: 'string',
        required: false,
        description: 'CC recipients (comma-separated)'
      },
      {
        name: 'bcc',
        type: 'string',
        required: false,
        description: 'BCC recipients (comma-separated)'
      }
    ],
    metadata: {
      version: '1.0.0',
      author: 'ABN Actions Store',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      permissions: ['communication.email'],
      rateLimit: {
        maxCalls: 50,
        windowMs: 60000 // 50 emails per minute
      }
    },
    run: sendEmail
  };

  actionsStore.registerAction(emailAction);

  // Telegram action
  const telegramAction: Action = {
    key: 'communication.sendTelegram',
    description: 'Send messages via Telegram bot',
    category: 'communication',
    tags: ['telegram', 'notification', 'messaging', 'bot'],
    parameters: [
      {
        name: 'message',
        type: 'string',
        required: true,
        description: 'Message content to send'
      },
      {
        name: 'chatId',
        type: 'string',
        required: false,
        description: 'Telegram chat ID (uses default if not provided)'
      },
      {
        name: 'parseMode',
        type: 'string',
        required: false,
        description: 'Message parse mode (HTML, Markdown, or none)',
        defaultValue: 'HTML',
        validation: {
          enum: ['HTML', 'Markdown', 'MarkdownV2', 'none']
        }
      },
      {
        name: 'disablePreview',
        type: 'boolean',
        required: false,
        description: 'Disable web page preview',
        defaultValue: false
      }
    ],
    metadata: {
      version: '1.0.0',
      author: 'ABN Actions Store',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      permissions: ['communication.telegram'],
      rateLimit: {
        maxCalls: 30,
        windowMs: 60000 // 30 messages per minute
      }
    },
    run: sendTelegramMessage
  };

  actionsStore.registerAction(telegramAction);

  // LLM action
  const llmAction: Action = {
    key: 'communication.callLLM',
    description: 'Call Large Language Model APIs (OpenAI, Groq, Anthropic, etc.)',
    category: 'communication',
    tags: ['llm', 'ai', 'openai', 'groq', 'anthropic', 'gpt'],
    parameters: [
      {
        name: 'prompt',
        type: 'string',
        required: true,
        description: 'The prompt to send to the LLM'
      },
      {
        name: 'systemMessage',
        type: 'string',
        required: false,
        description: 'System message to set context for the LLM'
      },
      {
        name: 'provider',
        type: 'string',
        required: false,
        description: 'LLM provider to use',
        defaultValue: 'openai',
        validation: {
          enum: ['openai', 'groq', 'anthropic', 'local']
        }
      },
      {
        name: 'model',
        type: 'string',
        required: false,
        description: 'Model name to use (e.g., gpt-3.5-turbo, llama2-70b-4096)'
      },
      {
        name: 'maxTokens',
        type: 'number',
        required: false,
        description: 'Maximum tokens in response',
        validation: {
          min: 1,
          max: 4000
        }
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        description: 'Sampling temperature (0.0 to 2.0)',
        validation: {
          min: 0.0,
          max: 2.0
        }
      }
    ],
    metadata: {
      version: '1.0.0',
      author: 'ABN Actions Store',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      permissions: ['communication.llm'],
      rateLimit: {
        maxCalls: 20,
        windowMs: 60000 // 20 LLM calls per minute
      }
    },
    run: callLLM
  };

  actionsStore.registerAction(llmAction);

  console.log('Communication actions registered successfully');
}
