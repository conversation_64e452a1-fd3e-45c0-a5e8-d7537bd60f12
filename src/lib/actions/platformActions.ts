import { Action, ActionContext, registerAction } from '../actionsStore';
import fs from 'fs';
import path from 'path';

// Platform Action 1: Create User
const createUserAction: Action = {
  key: 'platform.createUser',
  description: 'Create a new user in the ABN platform with OneID integration',
  category: 'user-management',
  tags: ['user', 'create', 'oneid', 'platform'],
  parameters: [
    {
      name: 'email',
      type: 'string',
      required: true,
      description: 'User email address',
      validation: {
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      }
    },
    {
      name: 'username',
      type: 'string',
      required: true,
      description: 'Unique username',
      validation: {
        pattern: '^[a-zA-Z0-9_]{3,20}$'
      }
    },
    {
      name: 'fullName',
      type: 'string',
      required: true,
      description: 'User full name'
    },
    {
      name: 'userType',
      type: 'string',
      required: false,
      description: 'Type of user account',
      defaultValue: 'individual',
      validation: {
        enum: ['individual', 'company', 'employee']
      }
    },
    {
      name: 'permissions',
      type: 'array',
      required: false,
      description: 'Initial user permissions',
      defaultValue: []
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN OneID',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['user.create', 'admin.users']
  },
  run: async (payload, context) => {
    const { email, username, fullName, userType = 'individual', permissions = [] } = payload;
    
    // Generate unique user ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newUser = {
      id: userId,
      email,
      username,
      fullName,
      userType,
      permissions,
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: context?.userId || 'system'
    };
    
    // Save to OneID users file
    const usersDir = path.join(process.cwd(), 'data/apps/backbone/oneid');
    if (!fs.existsSync(usersDir)) {
      fs.mkdirSync(usersDir, { recursive: true });
    }
    
    const usersFile = path.join(usersDir, 'users.json');
    let users: any[] = [];
    
    if (fs.existsSync(usersFile)) {
      users = JSON.parse(fs.readFileSync(usersFile, 'utf8'));
    }
    
    // Check for duplicates
    const existingUser = users.find(u => u.email === email || u.username === username);
    if (existingUser) {
      throw new Error('User with this email or username already exists');
    }
    
    users.push(newUser);
    fs.writeFileSync(usersFile, JSON.stringify(users, null, 2));
    
    return {
      userId,
      email,
      username,
      fullName,
      userType,
      status: 'created',
      timestamp: new Date().toISOString()
    };
  }
};

// Platform Action 2: Generate QR Code
const generateQRAction: Action = {
  key: 'platform.generateQR',
  description: 'Generate QR codes for various platform purposes',
  category: 'utilities',
  tags: ['qr', 'generate', 'code', 'utilities'],
  parameters: [
    {
      name: 'data',
      type: 'string',
      required: true,
      description: 'Data to encode in QR code'
    },
    {
      name: 'type',
      type: 'string',
      required: false,
      description: 'QR code type/purpose',
      defaultValue: 'general',
      validation: {
        enum: ['general', 'airtag', 'payment', 'contact', 'url', 'wifi']
      }
    },
    {
      name: 'size',
      type: 'number',
      required: false,
      description: 'QR code size in pixels',
      defaultValue: 200,
      validation: {
        min: 100,
        max: 1000
      }
    },
    {
      name: 'format',
      type: 'string',
      required: false,
      description: 'Output format',
      defaultValue: 'png',
      validation: {
        enum: ['png', 'svg', 'pdf']
      }
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN QR System',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['qr.generate']
  },
  run: async (payload, context) => {
    const { data, type = 'general', size = 200, format = 'png' } = payload;
    
    // Generate QR code ID
    const qrId = `qr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Simulate QR code generation (in real implementation, this would generate actual QR codes)
    const qrData = {
      id: qrId,
      data,
      type,
      size,
      format,
      url: `/api/qr/image/${qrId}`,
      createdAt: new Date().toISOString(),
      createdBy: context?.userId || 'anonymous'
    };
    
    // Save QR code metadata
    const qrDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/qr');
    if (!fs.existsSync(qrDir)) {
      fs.mkdirSync(qrDir, { recursive: true });
    }
    
    const qrFile = path.join(qrDir, 'codes.json');
    let codes: any[] = [];
    
    if (fs.existsSync(qrFile)) {
      codes = JSON.parse(fs.readFileSync(qrFile, 'utf8'));
    }
    
    codes.push(qrData);
    fs.writeFileSync(qrFile, JSON.stringify(codes, null, 2));
    
    return {
      qrId,
      url: qrData.url,
      type,
      size,
      format,
      timestamp: new Date().toISOString()
    };
  }
};

// Platform Action 3: Analytics Query
const analyticsQueryAction: Action = {
  key: 'platform.analytics',
  description: 'Query analytics data across ABN platform modules',
  category: 'analytics',
  tags: ['analytics', 'metrics', 'reporting', 'data'],
  parameters: [
    {
      name: 'metric',
      type: 'string',
      required: true,
      description: 'Metric to query',
      validation: {
        enum: ['users', 'transactions', 'revenue', 'activity', 'performance', 'errors']
      }
    },
    {
      name: 'timeRange',
      type: 'string',
      required: false,
      description: 'Time range for analytics',
      defaultValue: '24h',
      validation: {
        enum: ['1h', '24h', '7d', '30d', '90d', '1y']
      }
    },
    {
      name: 'module',
      type: 'string',
      required: false,
      description: 'Specific module to analyze (optional for platform-wide metrics)'
    },
    {
      name: 'groupBy',
      type: 'string',
      required: false,
      description: 'Group results by time period',
      validation: {
        enum: ['hour', 'day', 'week', 'month']
      }
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN Analytics',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['analytics.read']
  },
  run: async (payload, context) => {
    const { metric, timeRange = '24h', module, groupBy } = payload;
    
    // Calculate time range
    const now = new Date();
    const timeRangeMs = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
      '1y': 365 * 24 * 60 * 60 * 1000
    }[timeRange];
    
    const startTime = new Date(now.getTime() - timeRangeMs);
    
    // Simulate analytics data (in real implementation, this would query actual analytics)
    const generateMockData = () => {
      const dataPoints = Math.min(100, Math.floor(timeRangeMs / (60 * 60 * 1000))); // Max 100 points
      const data = [];
      
      for (let i = 0; i < dataPoints; i++) {
        const timestamp = new Date(startTime.getTime() + (i * timeRangeMs / dataPoints));
        const value = Math.floor(Math.random() * 1000) + 100;
        
        data.push({
          timestamp: timestamp.toISOString(),
          value,
          metric
        });
      }
      
      return data;
    };
    
    const data = generateMockData();
    const total = data.reduce((sum, point) => sum + point.value, 0);
    const average = Math.round(total / data.length);
    const max = Math.max(...data.map(point => point.value));
    const min = Math.min(...data.map(point => point.value));
    
    return {
      metric,
      timeRange,
      module: module || 'platform-wide',
      groupBy,
      summary: {
        total,
        average,
        max,
        min,
        dataPoints: data.length
      },
      data,
      timestamp: new Date().toISOString()
    };
  }
};

// Platform Action 4: File Operations
const fileOperationAction: Action = {
  key: 'platform.fileOperation',
  description: 'Perform file operations like read, write, delete, or list files',
  category: 'file-system',
  tags: ['file', 'storage', 'operations', 'data'],
  parameters: [
    {
      name: 'operation',
      type: 'string',
      required: true,
      description: 'File operation to perform',
      validation: {
        enum: ['read', 'write', 'delete', 'list', 'exists', 'stats']
      }
    },
    {
      name: 'path',
      type: 'string',
      required: true,
      description: 'File or directory path (relative to data directory)'
    },
    {
      name: 'content',
      type: 'string',
      required: false,
      description: 'Content to write (required for write operation)'
    },
    {
      name: 'encoding',
      type: 'string',
      required: false,
      description: 'File encoding',
      defaultValue: 'utf8',
      validation: {
        enum: ['utf8', 'base64', 'binary']
      }
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN File System',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['file.read', 'file.write'],
    rateLimit: {
      maxCalls: 20,
      windowMs: 60000
    }
  },
  run: async (payload, context) => {
    const { operation, path: filePath, content, encoding = 'utf8' } = payload;
    
    // Ensure path is within data directory for security
    const dataDir = path.join(process.cwd(), 'data');
    const fullPath = path.join(dataDir, filePath);
    
    if (!fullPath.startsWith(dataDir)) {
      throw new Error('File path must be within data directory');
    }
    
    try {
      switch (operation) {
        case 'read':
          if (!fs.existsSync(fullPath)) {
            throw new Error('File does not exist');
          }
          const fileContent = fs.readFileSync(fullPath, encoding as BufferEncoding);
          return {
            path: filePath,
            content: fileContent,
            size: fs.statSync(fullPath).size,
            encoding
          };
          
        case 'write':
          if (!content) {
            throw new Error('Content is required for write operation');
          }
          
          // Ensure directory exists
          const dir = path.dirname(fullPath);
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
          }
          
          fs.writeFileSync(fullPath, content, encoding as BufferEncoding);
          return {
            path: filePath,
            written: true,
            size: fs.statSync(fullPath).size,
            timestamp: new Date().toISOString()
          };
          
        case 'delete':
          if (!fs.existsSync(fullPath)) {
            return { path: filePath, deleted: false, reason: 'File does not exist' };
          }
          
          fs.unlinkSync(fullPath);
          return {
            path: filePath,
            deleted: true,
            timestamp: new Date().toISOString()
          };
          
        case 'list':
          if (!fs.existsSync(fullPath)) {
            throw new Error('Directory does not exist');
          }
          
          const items = fs.readdirSync(fullPath).map(item => {
            const itemPath = path.join(fullPath, item);
            const stats = fs.statSync(itemPath);
            return {
              name: item,
              type: stats.isDirectory() ? 'directory' : 'file',
              size: stats.size,
              modified: stats.mtime.toISOString()
            };
          });
          
          return {
            path: filePath,
            items,
            count: items.length
          };
          
        case 'exists':
          return {
            path: filePath,
            exists: fs.existsSync(fullPath)
          };
          
        case 'stats':
          if (!fs.existsSync(fullPath)) {
            throw new Error('File does not exist');
          }
          
          const stats = fs.statSync(fullPath);
          return {
            path: filePath,
            size: stats.size,
            isFile: stats.isFile(),
            isDirectory: stats.isDirectory(),
            created: stats.birthtime.toISOString(),
            modified: stats.mtime.toISOString(),
            accessed: stats.atime.toISOString()
          };
          
        default:
          throw new Error(`Unknown file operation: ${operation}`);
      }
    } catch (error) {
      throw new Error(`File operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
};

// Register all platform actions
export function registerPlatformActions(): void {
  registerAction(createUserAction);
  registerAction(generateQRAction);
  registerAction(analyticsQueryAction);
  registerAction(fileOperationAction);
  
  console.log('Platform actions registered successfully');
}
