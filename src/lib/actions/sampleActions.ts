import { Action, ActionContext, registerAction } from '../actionsStore';
import fs from 'fs';
import path from 'path';

// Sample Action 1: Get System Information
const getSystemInfoAction: Action = {
  key: 'system.getInfo',
  description: 'Retrieve system information including platform details, memory usage, and uptime',
  category: 'system',
  tags: ['system', 'monitoring', 'info'],
  parameters: [
    {
      name: 'includeMemory',
      type: 'boolean',
      required: false,
      description: 'Include memory usage information',
      defaultValue: true
    },
    {
      name: 'includeUptime',
      type: 'boolean',
      required: false,
      description: 'Include system uptime information',
      defaultValue: true
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN System',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['system.read']
  },
  run: async (payload, context) => {
    const { includeMemory = true, includeUptime = true } = payload;
    
    const info: any = {
      platform: process.platform,
      nodeVersion: process.version,
      timestamp: new Date().toISOString()
    };

    if (includeMemory) {
      const memUsage = process.memoryUsage();
      info.memory = {
        rss: `${Math.round(memUsage.rss / 1024 / 1024)} MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`,
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)} MB`
      };
    }

    if (includeUptime) {
      info.uptime = `${Math.round(process.uptime())} seconds`;
    }

    return info;
  }
};

// Sample Action 2: Send Notification
const sendNotificationAction: Action = {
  key: 'notification.send',
  description: 'Send a notification through various channels (email, telegram, webhook)',
  category: 'communication',
  tags: ['notification', 'email', 'telegram', 'webhook'],
  parameters: [
    {
      name: 'channel',
      type: 'string',
      required: true,
      description: 'Notification channel',
      validation: {
        enum: ['email', 'telegram', 'webhook', 'log']
      }
    },
    {
      name: 'recipient',
      type: 'string',
      required: true,
      description: 'Recipient identifier (email, chat ID, webhook URL)'
    },
    {
      name: 'subject',
      type: 'string',
      required: false,
      description: 'Notification subject/title'
    },
    {
      name: 'message',
      type: 'string',
      required: true,
      description: 'Notification message content'
    },
    {
      name: 'priority',
      type: 'string',
      required: false,
      description: 'Notification priority level',
      defaultValue: 'normal',
      validation: {
        enum: ['low', 'normal', 'high', 'urgent']
      }
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN Communication',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['notification.send'],
    rateLimit: {
      maxCalls: 10,
      windowMs: 60000 // 1 minute
    }
  },
  run: async (payload, context) => {
    const { channel, recipient, subject, message, priority = 'normal' } = payload;
    
    // Simulate notification sending
    const notificationId = `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Log notification (in real implementation, this would send actual notifications)
    const logData = {
      id: notificationId,
      channel,
      recipient,
      subject,
      message,
      priority,
      timestamp: new Date().toISOString(),
      sentBy: context?.userId || 'system',
      status: 'sent'
    };

    // Save to notifications log
    const notificationsDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/notifications');
    if (!fs.existsSync(notificationsDir)) {
      fs.mkdirSync(notificationsDir, { recursive: true });
    }
    
    const logFile = path.join(notificationsDir, `${new Date().toISOString().split('T')[0]}.json`);
    let logs: any[] = [];
    
    if (fs.existsSync(logFile)) {
      logs = JSON.parse(fs.readFileSync(logFile, 'utf8'));
    }
    
    logs.push(logData);
    fs.writeFileSync(logFile, JSON.stringify(logs, null, 2));

    return {
      notificationId,
      status: 'sent',
      channel,
      recipient,
      timestamp: new Date().toISOString()
    };
  }
};

// Sample Action 3: Data Query
const dataQueryAction: Action = {
  key: 'data.query',
  description: 'Query data from various ABN platform modules',
  category: 'data',
  tags: ['data', 'query', 'search', 'platform'],
  parameters: [
    {
      name: 'module',
      type: 'string',
      required: true,
      description: 'Platform module to query',
      validation: {
        enum: ['booksy', 'abnmoney', 'minirent', 'hr', 'abnpaymenthub', 'platforms']
      }
    },
    {
      name: 'entity',
      type: 'string',
      required: true,
      description: 'Entity type to query (e.g., users, orders, properties)'
    },
    {
      name: 'filters',
      type: 'object',
      required: false,
      description: 'Query filters as key-value pairs'
    },
    {
      name: 'limit',
      type: 'number',
      required: false,
      description: 'Maximum number of results to return',
      defaultValue: 10,
      validation: {
        min: 1,
        max: 100
      }
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN Data',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['data.read']
  },
  run: async (payload, context) => {
    const { module, entity, filters = {}, limit = 10 } = payload;
    
    // Construct data file path
    const dataPath = path.join(process.cwd(), 'data/apps', module, `${entity}.json`);
    
    if (!fs.existsSync(dataPath)) {
      throw new Error(`Data file not found: ${module}/${entity}.json`);
    }
    
    try {
      const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
      let results = Array.isArray(data) ? data : data.data || [];
      
      // Apply filters
      if (Object.keys(filters).length > 0) {
        results = results.filter((item: any) => {
          return Object.entries(filters).every(([key, value]) => {
            return item[key] === value || (typeof item[key] === 'string' && item[key].includes(value as string));
          });
        });
      }
      
      // Apply limit
      results = results.slice(0, limit);
      
      return {
        module,
        entity,
        filters,
        count: results.length,
        data: results,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Error reading data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
};

// Sample Action 4: Cache Operations
const cacheOperationAction: Action = {
  key: 'cache.operation',
  description: 'Perform cache operations like get, set, delete, or clear',
  category: 'cache',
  tags: ['cache', 'performance', 'storage'],
  parameters: [
    {
      name: 'operation',
      type: 'string',
      required: true,
      description: 'Cache operation to perform',
      validation: {
        enum: ['get', 'set', 'delete', 'clear', 'stats']
      }
    },
    {
      name: 'key',
      type: 'string',
      required: false,
      description: 'Cache key (required for get, set, delete operations)'
    },
    {
      name: 'value',
      type: 'object',
      required: false,
      description: 'Value to set (required for set operation)'
    },
    {
      name: 'ttl',
      type: 'number',
      required: false,
      description: 'Time to live in seconds (for set operation)',
      validation: {
        min: 1
      }
    }
  ],
  metadata: {
    version: '1.0.0',
    author: 'ABN Cache',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: ['cache.read', 'cache.write']
  },
  run: async (payload, context) => {
    const { operation, key, value, ttl } = payload;
    
    // Simulate cache operations (in real implementation, this would use actual cache service)
    const cacheDir = path.join(process.cwd(), 'data/apps/platforms/actionStore/cache');
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }
    
    const cacheFile = path.join(cacheDir, 'cache.json');
    let cache: Record<string, any> = {};
    
    if (fs.existsSync(cacheFile)) {
      cache = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
    }
    
    switch (operation) {
      case 'get':
        if (!key) throw new Error('Key is required for get operation');
        return {
          key,
          value: cache[key] || null,
          exists: key in cache
        };
        
      case 'set':
        if (!key) throw new Error('Key is required for set operation');
        if (value === undefined) throw new Error('Value is required for set operation');
        
        cache[key] = {
          value,
          timestamp: new Date().toISOString(),
          ttl: ttl || null
        };
        
        fs.writeFileSync(cacheFile, JSON.stringify(cache, null, 2));
        return {
          key,
          value,
          set: true,
          timestamp: new Date().toISOString()
        };
        
      case 'delete':
        if (!key) throw new Error('Key is required for delete operation');
        
        const existed = key in cache;
        delete cache[key];
        fs.writeFileSync(cacheFile, JSON.stringify(cache, null, 2));
        
        return {
          key,
          deleted: existed
        };
        
      case 'clear':
        cache = {};
        fs.writeFileSync(cacheFile, JSON.stringify(cache, null, 2));
        return {
          cleared: true,
          timestamp: new Date().toISOString()
        };
        
      case 'stats':
        return {
          totalKeys: Object.keys(cache).length,
          keys: Object.keys(cache),
          timestamp: new Date().toISOString()
        };
        
      default:
        throw new Error(`Unknown cache operation: ${operation}`);
    }
  }
};

// Register all sample actions
export function registerSampleActions(): void {
  registerAction(getSystemInfoAction);
  registerAction(sendNotificationAction);
  registerAction(dataQueryAction);
  registerAction(cacheOperationAction);
  
  console.log('Sample actions registered successfully');
}
