import { callAction } from './index';

/**
 * Communication Helper Functions
 * 
 * These helper functions provide convenient wrappers around the communication actions
 * with common use cases and simplified interfaces.
 */

// Email helper functions
export async function sendNotificationEmail(
  to: string | string[],
  subject: string,
  message: string,
  options: {
    html?: string;
    cc?: string[];
    bcc?: string[];
    priority?: 'high' | 'normal' | 'low';
  } = {}
) {
  const recipients = Array.isArray(to) ? to.join(',') : to;
  const cc = options.cc ? options.cc.join(',') : undefined;
  const bcc = options.bcc ? options.bcc.join(',') : undefined;

  return await callAction('communication.sendEmail', {
    to: recipients,
    subject,
    body: message,
    html: options.html,
    cc,
    bcc
  });
}

export async function sendWelcomeEmail(userEmail: string, userName: string) {
  const subject = 'Welcome to ABN Green Platform';
  const body = `Dear ${userName},\n\nWelcome to the ABN Green Platform! We're excited to have you join our community focused on sustainable solutions.\n\nBest regards,\nThe ABN Green Team`;
  const html = `
    <h1>Welcome to ABN Green Platform</h1>
    <p>Dear <strong>${userName}</strong>,</p>
    <p>Welcome to the ABN Green Platform! We're excited to have you join our community focused on sustainable solutions.</p>
    <p>Best regards,<br>The ABN Green Team</p>
  `;

  return await sendNotificationEmail(userEmail, subject, body, { html });
}

export async function sendAlertEmail(
  recipients: string[],
  alertType: 'error' | 'warning' | 'info',
  title: string,
  details: string
) {
  const subject = `[${alertType.toUpperCase()}] ${title}`;
  const body = `Alert Type: ${alertType}\nTitle: ${title}\n\nDetails:\n${details}\n\nTimestamp: ${new Date().toISOString()}`;
  
  return await sendNotificationEmail(recipients, subject, body, {
    priority: alertType === 'error' ? 'high' : 'normal'
  });
}

// Telegram helper functions
export async function sendTelegramNotification(
  message: string,
  options: {
    chatId?: string;
    parseMode?: 'HTML' | 'Markdown' | 'MarkdownV2' | 'none';
    disablePreview?: boolean;
    silent?: boolean;
  } = {}
) {
  return await callAction('communication.sendTelegram', {
    message,
    chatId: options.chatId,
    parseMode: options.parseMode || 'HTML',
    disablePreview: options.disablePreview || false
  });
}

export async function sendTelegramAlert(
  alertType: 'error' | 'warning' | 'info' | 'success',
  title: string,
  details?: string
) {
  const icons = {
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    success: '✅'
  };

  const message = `${icons[alertType]} <b>${title}</b>\n\n${details || ''}\n\n<i>Time: ${new Date().toLocaleString()}</i>`;
  
  return await sendTelegramNotification(message, { parseMode: 'HTML' });
}

export async function sendSystemStatusUpdate(
  status: 'online' | 'offline' | 'maintenance',
  details?: string
) {
  const statusIcons = {
    online: '🟢',
    offline: '🔴',
    maintenance: '🟡'
  };

  const message = `${statusIcons[status]} <b>System Status: ${status.toUpperCase()}</b>\n\n${details || ''}\n\n<i>Time: ${new Date().toLocaleString()}</i>`;
  
  return await sendTelegramNotification(message, { parseMode: 'HTML' });
}

// LLM helper functions
export async function askLLM(
  question: string,
  options: {
    provider?: 'openai' | 'groq' | 'anthropic' | 'local';
    model?: string;
    systemMessage?: string;
    maxTokens?: number;
    temperature?: number;
  } = {}
) {
  return await callAction('communication.callLLM', {
    prompt: question,
    systemMessage: options.systemMessage || 'You are a helpful AI assistant.',
    provider: options.provider || 'openai',
    model: options.model,
    maxTokens: options.maxTokens || 500,
    temperature: options.temperature || 0.7
  });
}

export async function generateContent(
  contentType: 'email' | 'article' | 'summary' | 'description',
  topic: string,
  options: {
    tone?: 'professional' | 'casual' | 'friendly' | 'formal';
    length?: 'short' | 'medium' | 'long';
    audience?: string;
    provider?: 'openai' | 'groq' | 'anthropic';
  } = {}
) {
  const toneInstructions = {
    professional: 'Use a professional and business-appropriate tone.',
    casual: 'Use a casual and conversational tone.',
    friendly: 'Use a warm and friendly tone.',
    formal: 'Use a formal and academic tone.'
  };

  const lengthInstructions = {
    short: 'Keep it concise, around 50-100 words.',
    medium: 'Write a moderate length piece, around 200-300 words.',
    long: 'Write a comprehensive piece, around 500-800 words.'
  };

  const systemMessage = `You are a professional content writer. ${toneInstructions[options.tone || 'professional']} ${lengthInstructions[options.length || 'medium']} ${options.audience ? `The target audience is: ${options.audience}.` : ''}`;

  const prompt = `Write a ${contentType} about: ${topic}`;

  return await askLLM(prompt, {
    systemMessage,
    provider: options.provider,
    maxTokens: options.length === 'long' ? 1000 : options.length === 'medium' ? 500 : 200,
    temperature: 0.7
  });
}

export async function analyzeText(
  text: string,
  analysisType: 'sentiment' | 'summary' | 'keywords' | 'translation',
  options: {
    targetLanguage?: string;
    provider?: 'openai' | 'groq' | 'anthropic';
  } = {}
) {
  const prompts = {
    sentiment: `Analyze the sentiment of the following text and provide a score from -1 (very negative) to 1 (very positive), along with a brief explanation:\n\n${text}`,
    summary: `Provide a concise summary of the following text:\n\n${text}`,
    keywords: `Extract the main keywords and key phrases from the following text:\n\n${text}`,
    translation: `Translate the following text to ${options.targetLanguage || 'English'}:\n\n${text}`
  };

  return await askLLM(prompts[analysisType], {
    provider: options.provider,
    maxTokens: 300,
    temperature: 0.3
  });
}

// Batch operations
export async function sendBulkEmails(
  recipients: Array<{
    email: string;
    name?: string;
    customData?: Record<string, any>;
  }>,
  template: {
    subject: string;
    body: string;
    html?: string;
  },
  options: {
    batchSize?: number;
    delayBetweenBatches?: number;
  } = {}
) {
  const batchSize = options.batchSize || 10;
  const delay = options.delayBetweenBatches || 1000;
  const results = [];

  for (let i = 0; i < recipients.length; i += batchSize) {
    const batch = recipients.slice(i, i + batchSize);
    const batchPromises = batch.map(async (recipient) => {
      // Simple template variable replacement
      let personalizedSubject = template.subject.replace(/\{name\}/g, recipient.name || 'User');
      let personalizedBody = template.body.replace(/\{name\}/g, recipient.name || 'User');
      let personalizedHtml = template.html?.replace(/\{name\}/g, recipient.name || 'User');

      // Replace custom data variables
      if (recipient.customData) {
        Object.entries(recipient.customData).forEach(([key, value]) => {
          const placeholder = `{${key}}`;
          personalizedSubject = personalizedSubject.replace(new RegExp(placeholder, 'g'), String(value));
          personalizedBody = personalizedBody.replace(new RegExp(placeholder, 'g'), String(value));
          if (personalizedHtml) {
            personalizedHtml = personalizedHtml.replace(new RegExp(placeholder, 'g'), String(value));
          }
        });
      }

      return await sendNotificationEmail(recipient.email, personalizedSubject, personalizedBody, {
        html: personalizedHtml
      });
    });

    const batchResults = await Promise.allSettled(batchPromises);
    results.push(...batchResults);

    // Delay between batches to avoid rate limiting
    if (i + batchSize < recipients.length && delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return results;
}

// Communication workflow helpers
export async function sendMultiChannelNotification(
  message: {
    title: string;
    content: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
  },
  channels: {
    email?: string[];
    telegram?: boolean;
    llmGenerated?: boolean;
  }
) {
  const results: any = {};

  // Send email notifications
  if (channels.email && channels.email.length > 0) {
    results.email = await sendAlertEmail(
      channels.email,
      message.priority === 'critical' ? 'error' : message.priority === 'high' ? 'warning' : 'info',
      message.title,
      message.content
    );
  }

  // Send Telegram notification
  if (channels.telegram) {
    const alertType = message.priority === 'critical' ? 'error' : 
                     message.priority === 'high' ? 'warning' : 
                     message.priority === 'medium' ? 'info' : 'info';
    
    results.telegram = await sendTelegramAlert(alertType, message.title, message.content);
  }

  // Generate AI-enhanced content if requested
  if (channels.llmGenerated) {
    results.llmEnhanced = await generateContent(
      'description',
      `${message.title}: ${message.content}`,
      {
        tone: message.priority === 'critical' ? 'formal' : 'professional',
        length: 'medium'
      }
    );
  }

  return results;
}

export default {
  // Email helpers
  sendNotificationEmail,
  sendWelcomeEmail,
  sendAlertEmail,
  
  // Telegram helpers
  sendTelegramNotification,
  sendTelegramAlert,
  sendSystemStatusUpdate,
  
  // LLM helpers
  askLLM,
  generateContent,
  analyzeText,
  
  // Batch operations
  sendBulkEmails,
  
  // Workflow helpers
  sendMultiChannelNotification
};
