import { registerSampleActions } from './sampleActions';
import { registerPlatformActions } from './platformActions';
import { registerCommunicationActions } from './communicationActions';

// Initialize all actions
export function initializeActions(): void {
  try {
    // Register sample actions
    registerSampleActions();

    // Register platform actions
    registerPlatformActions();

    // Register communication actions
    registerCommunicationActions();

    console.log('All actions initialized successfully');
  } catch (error) {
    console.error('Error initializing actions:', error);
  }
}

// Re-export main actionsStore functionality
export * from '../actionsStore';

// Re-export initialization utilities
export * from './initialize';
