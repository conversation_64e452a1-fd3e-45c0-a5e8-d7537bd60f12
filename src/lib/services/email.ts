import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { Client } from '@microsoft/microsoft-graph-client';
import { AuthenticationProvider } from '@microsoft/microsoft-graph-client';
import nodemailer from 'nodemailer';

export interface EmailAccount {
  id: string;
  email: string;
  name: string;
  type: 'sales' | 'support' | 'marketing' | 'general';
  provider: 'gmail' | 'outlook' | 'exchange' | 'other';
  status: 'connected' | 'disconnected' | 'sync_error';
  unreadCount: number;
  totalEmails: number;
  lastSync: string;
}

export interface Email {
  id: string;
  accountId: string;
  threadId: string;
  from: {
    email: string;
    name: string;
  };
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  snippet: string;
  timestamp: string;
  isRead: boolean;
  isStarred: boolean;
  isFlagged: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  labels: string[];
  attachments: EmailAttachment[];
  assignedTo?: string;
  status: 'new' | 'in_progress' | 'resolved' | 'archived';
  responseTime?: number;
  isFromCustomer: boolean;
}

export interface EmailAttachment {
  id: string;
  filename: string;
  mimeType: string;
  size: number;
  url?: string;
}

class GmailService {
  private oauth2Client: OAuth2Client;
  private gmail: any;

  constructor() {
    this.oauth2Client = new OAuth2Client(
      process.env.GMAIL_CLIENT_ID,
      process.env.GMAIL_CLIENT_SECRET,
      'urn:ietf:wg:oauth:2.0:oob'
    );

    this.oauth2Client.setCredentials({
      refresh_token: process.env.GMAIL_REFRESH_TOKEN,
    });

    this.gmail = google.gmail({ version: 'v1', auth: this.oauth2Client });
  }

  async getProfile(): Promise<any> {
    try {
      const response = await this.gmail.users.getProfile({ userId: 'me' });
      return response.data;
    } catch (error) {
      console.error('Error fetching Gmail profile:', error);
      throw error;
    }
  }

  async getMessages(maxResults: number = 20): Promise<Email[]> {
    try {
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        maxResults,
        q: 'in:inbox',
      });

      const messages = response.data.messages || [];
      const emailPromises = messages.map((message: any) => this.getMessage(message.id));
      
      return await Promise.all(emailPromises);
    } catch (error) {
      console.error('Error fetching Gmail messages:', error);
      throw error;
    }
  }

  async getMessage(messageId: string): Promise<Email> {
    try {
      const response = await this.gmail.users.messages.get({
        userId: 'me',
        id: messageId,
        format: 'full',
      });

      const message = response.data;
      const headers = message.payload.headers;

      const getHeaderValue = (name: string) => {
        const header = headers.find((h: any) => h.name.toLowerCase() === name.toLowerCase());
        return header ? header.value : '';
      };

      const fromHeader = getHeaderValue('From');
      const fromMatch = fromHeader.match(/^(.+?)\s*<(.+?)>$/) || [null, fromHeader, fromHeader];

      return {
        id: message.id,
        accountId: 'gmail_primary',
        threadId: message.threadId,
        from: {
          name: fromMatch[1]?.trim() || fromMatch[2],
          email: fromMatch[2] || fromMatch[1],
        },
        to: [getHeaderValue('To')],
        cc: getHeaderValue('Cc') ? [getHeaderValue('Cc')] : undefined,
        subject: getHeaderValue('Subject'),
        body: this.extractBody(message.payload),
        snippet: message.snippet || '',
        timestamp: new Date(parseInt(message.internalDate)).toISOString(),
        isRead: !message.labelIds?.includes('UNREAD'),
        isStarred: message.labelIds?.includes('STARRED') || false,
        isFlagged: message.labelIds?.includes('IMPORTANT') || false,
        priority: this.determinePriority(message),
        labels: message.labelIds || [],
        attachments: this.extractAttachments(message.payload),
        status: 'new',
        isFromCustomer: !fromHeader.includes('@abngreen.com'),
      };
    } catch (error) {
      console.error('Error fetching Gmail message:', error);
      throw error;
    }
  }

  private extractBody(payload: any): string {
    if (payload.body.data) {
      return Buffer.from(payload.body.data, 'base64').toString('utf-8');
    }

    if (payload.parts) {
      for (const part of payload.parts) {
        if (part.mimeType === 'text/plain' && part.body.data) {
          return Buffer.from(part.body.data, 'base64').toString('utf-8');
        }
      }
      
      for (const part of payload.parts) {
        if (part.mimeType === 'text/html' && part.body.data) {
          return Buffer.from(part.body.data, 'base64').toString('utf-8');
        }
      }
    }

    return '';
  }

  private extractAttachments(payload: any): EmailAttachment[] {
    const attachments: EmailAttachment[] = [];

    if (payload.parts) {
      payload.parts.forEach((part: any) => {
        if (part.filename && part.filename.length > 0) {
          attachments.push({
            id: part.body.attachmentId,
            filename: part.filename,
            mimeType: part.mimeType,
            size: part.body.size || 0,
          });
        }
      });
    }

    return attachments;
  }

  private determinePriority(message: any): 'low' | 'normal' | 'high' | 'urgent' {
    if (message.labelIds?.includes('IMPORTANT')) return 'high';
    if (message.labelIds?.includes('CATEGORY_PROMOTIONS')) return 'low';
    return 'normal';
  }

  async sendMessage(to: string, subject: string, body: string): Promise<void> {
    try {
      const raw = this.createRawMessage(to, subject, body);
      
      await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw,
        },
      });
    } catch (error) {
      console.error('Error sending Gmail message:', error);
      throw error;
    }
  }

  private createRawMessage(to: string, subject: string, body: string): string {
    const messageParts = [
      `To: ${to}`,
      `Subject: ${subject}`,
      'Content-Type: text/html; charset=utf-8',
      '',
      body,
    ];

    return Buffer.from(messageParts.join('\n')).toString('base64url');
  }
}

class OutlookService {
  private client: Client;

  constructor() {
    // Custom authentication provider for Microsoft Graph
    const authProvider: AuthenticationProvider = {
      getAccessToken: async () => {
        // Implement OAuth flow to get access token
        return process.env.MICROSOFT_ACCESS_TOKEN || '';
      },
    };

    this.client = Client.initWithMiddleware({ authProvider });
  }

  async getProfile(): Promise<any> {
    try {
      return await this.client.api('/me').get();
    } catch (error) {
      console.error('Error fetching Outlook profile:', error);
      throw error;
    }
  }

  async getMessages(maxResults: number = 20): Promise<Email[]> {
    try {
      const response = await this.client
        .api('/me/messages')
        .top(maxResults)
        .orderby('receivedDateTime desc')
        .get();

      return response.value.map((message: any) => this.convertToEmail(message));
    } catch (error) {
      console.error('Error fetching Outlook messages:', error);
      throw error;
    }
  }

  private convertToEmail(message: any): Email {
    return {
      id: message.id,
      accountId: 'outlook_primary',
      threadId: message.conversationId,
      from: {
        name: message.from?.emailAddress?.name || '',
        email: message.from?.emailAddress?.address || '',
      },
      to: message.toRecipients?.map((r: any) => r.emailAddress.address) || [],
      cc: message.ccRecipients?.map((r: any) => r.emailAddress.address),
      subject: message.subject || '',
      body: message.body?.content || '',
      snippet: message.bodyPreview || '',
      timestamp: message.receivedDateTime,
      isRead: message.isRead,
      isStarred: message.flag?.flagStatus === 'flagged',
      isFlagged: message.importance === 'high',
      priority: message.importance === 'high' ? 'high' : 'normal',
      labels: message.categories || [],
      attachments: message.attachments?.map((att: any) => ({
        id: att.id,
        filename: att.name,
        mimeType: att.contentType,
        size: att.size,
      })) || [],
      status: 'new',
      isFromCustomer: !message.from?.emailAddress?.address?.includes('@abngreen.com'),
    };
  }

  async sendMessage(to: string, subject: string, body: string): Promise<void> {
    try {
      const message = {
        subject,
        body: {
          contentType: 'HTML',
          content: body,
        },
        toRecipients: [
          {
            emailAddress: {
              address: to,
            },
          },
        ],
      };

      await this.client.api('/me/sendMail').post({ message });
    } catch (error) {
      console.error('Error sending Outlook message:', error);
      throw error;
    }
  }
}

class SMTPService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendMessage(to: string, subject: string, body: string): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: process.env.SMTP_USER,
        to,
        subject,
        html: body,
      });
    } catch (error) {
      console.error('Error sending SMTP message:', error);
      throw error;
    }
  }
}

export class EmailService {
  private gmailService: GmailService;
  private outlookService: OutlookService;
  private smtpService: SMTPService;

  constructor() {
    this.gmailService = new GmailService();
    this.outlookService = new OutlookService();
    this.smtpService = new SMTPService();
  }

  async getAccounts(): Promise<EmailAccount[]> {
    const accounts: EmailAccount[] = [];

    try {
      // Try to get Gmail account
      const gmailProfile = await this.gmailService.getProfile();
      accounts.push({
        id: 'gmail_primary',
        email: gmailProfile.emailAddress,
        name: 'Gmail Account',
        type: 'sales',
        provider: 'gmail',
        status: 'connected',
        unreadCount: gmailProfile.messagesTotal || 0,
        totalEmails: gmailProfile.messagesTotal || 0,
        lastSync: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Gmail account not available:', error);
    }

    try {
      // Try to get Outlook account
      const outlookProfile = await this.outlookService.getProfile();
      accounts.push({
        id: 'outlook_primary',
        email: outlookProfile.mail || outlookProfile.userPrincipalName,
        name: 'Outlook Account',
        type: 'support',
        provider: 'outlook',
        status: 'connected',
        unreadCount: 0, // Would need separate API call
        totalEmails: 0, // Would need separate API call
        lastSync: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Outlook account not available:', error);
    }

    return accounts;
  }

  async getMessages(accountId: string, maxResults: number = 20): Promise<Email[]> {
    if (accountId === 'gmail_primary') {
      return await this.gmailService.getMessages(maxResults);
    } else if (accountId === 'outlook_primary') {
      return await this.outlookService.getMessages(maxResults);
    }
    
    return [];
  }

  async sendMessage(accountId: string, to: string, subject: string, body: string): Promise<void> {
    if (accountId === 'gmail_primary') {
      await this.gmailService.sendMessage(to, subject, body);
    } else if (accountId === 'outlook_primary') {
      await this.outlookService.sendMessage(to, subject, body);
    } else {
      // Fallback to SMTP
      await this.smtpService.sendMessage(to, subject, body);
    }
  }
}

export const emailService = new EmailService();