import { 
  WeatherData, 
  WeatherDataCreate, 
  WeatherDataUpdate, 
  WeatherCondition, 
  WeatherDataSource 
} from '../../domain/models/WeatherData';
import { WeatherDataRepository } from '../../domain/repositories/WeatherDataRepository';

/**
 * Service layer for weather data operations
 * Uses repository to access data but knows nothing about storage details
 */
export class WeatherDataService {
  constructor(private weatherDataRepository: WeatherDataRepository) {}

  async getWeatherDataById(id: string): Promise<WeatherData | null> {
    return this.weatherDataRepository.getWeatherDataById(id);
  }

  async getAllWeatherData(): Promise<WeatherData[]> {
    return this.weatherDataRepository.getAllWeatherData();
  }

  async createWeatherData(weatherData: WeatherDataCreate): Promise<WeatherData> {
    return this.weatherDataRepository.createWeatherData(weatherData);
  }

  async updateWeatherData(id: string, updates: Partial<Omit<WeatherData, 'id'>>): Promise<WeatherData | null> {
    const weatherData = await this.weatherDataRepository.getWeatherDataById(id);
    if (!weatherData) return null;
    
    return this.weatherDataRepository.updateWeatherData({
      id,
      ...updates
    });
  }

  async deleteWeatherData(id: string): Promise<boolean> {
    return this.weatherDataRepository.deleteWeatherData(id);
  }

  async getWeatherDataByLocation(latitude: number, longitude: number, radius?: number): Promise<WeatherData[]> {
    return this.weatherDataRepository.getWeatherDataByLocation(latitude, longitude, radius);
  }

  async getWeatherDataByDateRange(startDate: Date, endDate: Date): Promise<WeatherData[]> {
    return this.weatherDataRepository.getWeatherDataByDateRange(startDate, endDate);
  }

  async getWeatherDataByLocationAndDateRange(
    latitude: number, 
    longitude: number, 
    startDate: Date, 
    endDate: Date,
    radius?: number
  ): Promise<WeatherData[]> {
    return this.weatherDataRepository.getWeatherDataByLocationAndDateRange(
      latitude, longitude, startDate, endDate, radius
    );
  }

  async getWeatherDataByCondition(condition: WeatherCondition): Promise<WeatherData[]> {
    return this.weatherDataRepository.getWeatherDataByCondition(condition);
  }

  async getWeatherDataBySource(source: WeatherDataSource): Promise<WeatherData[]> {
    return this.weatherDataRepository.getWeatherDataBySource(source);
  }

  async getCurrentWeatherForLocation(latitude: number, longitude: number): Promise<WeatherData | null> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    const recentWeatherData = await this.getWeatherDataByLocationAndDateRange(
      latitude, longitude, oneHourAgo, now, 5 // 5km radius
    );
    
    // Return the most recent weather data
    return recentWeatherData.length > 0 
      ? recentWeatherData.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
      : null;
  }

  async getWeatherForecast(latitude: number, longitude: number, days: number = 7): Promise<WeatherData[]> {
    const now = new Date();
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    
    return this.getWeatherDataByLocationAndDateRange(
      latitude, longitude, now, futureDate, 10 // 10km radius for forecast
    );
  }

  async getWeatherHistory(latitude: number, longitude: number, days: number = 30): Promise<WeatherData[]> {
    const now = new Date();
    const pastDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    
    return this.getWeatherDataByLocationAndDateRange(
      latitude, longitude, pastDate, now, 10 // 10km radius for history
    );
  }

  async getWeatherAlerts(latitude: number, longitude: number): Promise<WeatherData[]> {
    const alertConditions = [
      WeatherCondition.THUNDERSTORM,
      WeatherCondition.HEAVY_RAIN,
      WeatherCondition.SNOW,
      WeatherCondition.EXTREME_HEAT,
      WeatherCondition.EXTREME_COLD
    ];
    
    const now = new Date();
    const next24Hours = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    
    const upcomingWeather = await this.getWeatherDataByLocationAndDateRange(
      latitude, longitude, now, next24Hours, 20 // 20km radius for alerts
    );
    
    return upcomingWeather.filter(weather => 
      alertConditions.includes(weather.condition)
    );
  }

  async getWeatherStatistics(
    latitude: number, 
    longitude: number, 
    startDate: Date, 
    endDate: Date
  ): Promise<{
    averageTemperature: number;
    maxTemperature: number;
    minTemperature: number;
    totalRainfall: number;
    averageHumidity: number;
    averageWindSpeed: number;
    mostCommonCondition: WeatherCondition;
    conditionDistribution: Record<WeatherCondition, number>;
  }> {
    const weatherData = await this.getWeatherDataByLocationAndDateRange(
      latitude, longitude, startDate, endDate, 15 // 15km radius for statistics
    );
    
    if (weatherData.length === 0) {
      throw new Error('No weather data found for the specified location and date range');
    }
    
    const temperatures = weatherData.map(w => w.temperature);
    const rainfalls = weatherData.map(w => w.rainfall || 0);
    const humidities = weatherData.map(w => w.humidity);
    const windSpeeds = weatherData.map(w => w.windSpeed);
    
    const conditionCounts: Record<WeatherCondition, number> = {} as Record<WeatherCondition, number>;
    weatherData.forEach(w => {
      conditionCounts[w.condition] = (conditionCounts[w.condition] || 0) + 1;
    });
    
    const mostCommonCondition = Object.entries(conditionCounts)
      .sort(([,a], [,b]) => b - a)[0][0] as WeatherCondition;
    
    return {
      averageTemperature: temperatures.reduce((a, b) => a + b, 0) / temperatures.length,
      maxTemperature: Math.max(...temperatures),
      minTemperature: Math.min(...temperatures),
      totalRainfall: rainfalls.reduce((a, b) => a + b, 0),
      averageHumidity: humidities.reduce((a, b) => a + b, 0) / humidities.length,
      averageWindSpeed: windSpeeds.reduce((a, b) => a + b, 0) / windSpeeds.length,
      mostCommonCondition,
      conditionDistribution: conditionCounts
    };
  }

  async bulkCreateWeatherData(weatherDataArray: WeatherDataCreate[]): Promise<WeatherData[]> {
    const results: WeatherData[] = [];
    
    for (const weatherData of weatherDataArray) {
      try {
        const created = await this.createWeatherData(weatherData);
        results.push(created);
      } catch (error) {
        console.error('Failed to create weather data:', error);
        // Continue with other records
      }
    }
    
    return results;
  }

  async getWeatherDataSummary(): Promise<{
    totalRecords: number;
    dateRange: { earliest: Date; latest: Date };
    locationCoverage: { minLat: number; maxLat: number; minLon: number; maxLon: number };
    sourceDistribution: Record<WeatherDataSource, number>;
    conditionDistribution: Record<WeatherCondition, number>;
  }> {
    const allWeatherData = await this.getAllWeatherData();
    
    if (allWeatherData.length === 0) {
      throw new Error('No weather data available');
    }
    
    const timestamps = allWeatherData.map(w => new Date(w.timestamp));
    const latitudes = allWeatherData.map(w => w.latitude);
    const longitudes = allWeatherData.map(w => w.longitude);
    
    const sourceDistribution: Record<WeatherDataSource, number> = {} as Record<WeatherDataSource, number>;
    const conditionDistribution: Record<WeatherCondition, number> = {} as Record<WeatherCondition, number>;
    
    allWeatherData.forEach(w => {
      sourceDistribution[w.source] = (sourceDistribution[w.source] || 0) + 1;
      conditionDistribution[w.condition] = (conditionDistribution[w.condition] || 0) + 1;
    });
    
    return {
      totalRecords: allWeatherData.length,
      dateRange: {
        earliest: new Date(Math.min(...timestamps.map(t => t.getTime()))),
        latest: new Date(Math.max(...timestamps.map(t => t.getTime())))
      },
      locationCoverage: {
        minLat: Math.min(...latitudes),
        maxLat: Math.max(...latitudes),
        minLon: Math.min(...longitudes),
        maxLon: Math.max(...longitudes)
      },
      sourceDistribution,
      conditionDistribution
    };
  }
}
