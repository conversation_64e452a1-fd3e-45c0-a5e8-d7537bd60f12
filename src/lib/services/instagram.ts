import axios from 'axios';

export interface InstagramAccount {
  id: string;
  username: string;
  name: string;
  profilePicture: string;
  followers: number;
  following: number;
  posts: number;
  isVerified: boolean;
  isBusinessAccount: boolean;
  status: 'connected' | 'disconnected' | 'limited';
  unreadMessages: number;
  unreadStoryReplies: number;
  lastActivity: string;
}

export interface InstagramConversation {
  id: string;
  type: 'direct_message' | 'story_reply' | 'post_comment' | 'reel_comment';
  accountId: string;
  participantId: string;
  participantUsername: string;
  participantName: string;
  participantPicture?: string;
  isVerified: boolean;
  isFollowing: boolean;
  lastMessage: InstagramMessage;
  unreadCount: number;
  status: 'active' | 'archived' | 'restricted';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    storyId?: string;
    postId?: string;
    mediaType?: 'photo' | 'video' | 'carousel';
    source: string;
  };
}

export interface InstagramMessage {
  id: string;
  conversationId: string;
  senderId: string;
  senderUsername: string;
  senderName: string;
  senderPicture?: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'story_share' | 'post_share' | 'reel_share' | 'voice_message' | 'disappearing_message';
  content: string;
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  attachments: InstagramAttachment[];
  reactions: InstagramReaction[];
  mentions: string[];
  hashtags: string[];
  isEdited: boolean;
  isDeleted: boolean;
  isFromBusiness: boolean;
  metadata: any;
}

export interface InstagramAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'story' | 'reel';
  url: string;
  thumbnailUrl?: string;
  width?: number;
  height?: number;
  duration?: number;
  size?: number;
}

export interface InstagramReaction {
  type: 'like' | 'fire' | 'heart' | 'laugh' | 'wow' | 'sad' | 'angry';
  count: number;
  users: string[];
  userReacted: boolean;
}

export class InstagramService {
  private baseURL = 'https://graph.instagram.com';
  private fbGraphURL = 'https://graph.facebook.com/v18.0';
  private accessToken: string;

  constructor() {
    this.accessToken = process.env.INSTAGRAM_ACCESS_TOKEN || '';
  }

  private async makeRequest(endpoint: string, method: 'GET' | 'POST' = 'GET', data?: any, useInstagramAPI = true): Promise<any> {
    try {
      const baseUrl = useInstagramAPI ? this.baseURL : this.fbGraphURL;
      const response = await axios({
        method,
        url: `${baseUrl}${endpoint}`,
        params: method === 'GET' ? { access_token: this.accessToken, ...data } : { access_token: this.accessToken },
        data: method === 'POST' ? data : undefined,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Check for Instagram API errors
      if (response.data.error) {
        throw new Error(`Instagram API Error: ${response.data.error.message}`);
      }

      return response.data;
    } catch (error: any) {
      console.error('Instagram API Error:', error.response?.data || error.message);
      throw new Error(`Instagram API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async getAccount(): Promise<InstagramAccount> {
    try {
      // Get basic account info
      const accountInfo = await this.makeRequest('/me', 'GET', {
        fields: 'id,username,account_type,media_count',
      });

      // Get additional business account info if available
      let businessInfo = null;
      try {
        businessInfo = await this.makeRequest(`/${accountInfo.id}`, 'GET', {
          fields: 'biography,followers_count,follows_count,name,profile_picture_url,is_verified,is_business_account',
        }, false);
      } catch (error) {
        console.log('Business account info not available, using basic info');
      }

      return {
        id: accountInfo.id,
        username: accountInfo.username,
        name: businessInfo?.name || accountInfo.username,
        profilePicture: businessInfo?.profile_picture_url || '',
        followers: businessInfo?.followers_count || 0,
        following: businessInfo?.follows_count || 0,
        posts: accountInfo.media_count || 0,
        isVerified: businessInfo?.is_verified || false,
        isBusinessAccount: businessInfo?.is_business_account || false,
        status: 'connected',
        unreadMessages: 0, // Would need separate API call
        unreadStoryReplies: 0, // Would need separate API call
        lastActivity: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching Instagram account:', error);
      throw error;
    }
  }

  async getMedia(limit: number = 20): Promise<any[]> {
    try {
      const response = await this.makeRequest('/me/media', 'GET', {
        fields: 'id,caption,media_type,media_url,thumbnail_url,timestamp,like_count,comments_count',
        limit,
      });

      return response.data || [];
    } catch (error) {
      console.error('Error fetching Instagram media:', error);
      throw error;
    }
  }

  async getMediaComments(mediaId: string): Promise<any[]> {
    try {
      const response = await this.makeRequest(`/${mediaId}/comments`, 'GET', {
        fields: 'id,text,username,timestamp,like_count,replies',
      });

      return response.data || [];
    } catch (error) {
      console.error('Error fetching Instagram media comments:', error);
      throw error;
    }
  }

  async replyToComment(commentId: string, message: string): Promise<void> {
    try {
      await this.makeRequest(`/${commentId}/replies`, 'POST', {
        message,
      });
    } catch (error) {
      console.error('Error replying to Instagram comment:', error);
      throw error;
    }
  }

  async getStories(): Promise<any[]> {
    try {
      const response = await this.makeRequest('/me/stories', 'GET', {
        fields: 'id,media_type,media_url,thumbnail_url,timestamp',
      });

      return response.data || [];
    } catch (error) {
      console.error('Error fetching Instagram stories:', error);
      return []; // Stories API might not be available for all accounts
    }
  }

  // Instagram Messaging API - requires instagram_business_manage_messages permission
  async getDirectMessages(): Promise<InstagramConversation[]> {
    try {
      // Get conversations using Instagram Messaging API
      const response = await this.makeRequest('/me/conversations', 'GET', {
        platform: 'instagram',
        folder: 'inbox',
        tags: 'HUMAN_AGENT', // Required for customer service
      });

      if (!response.data) {
        console.log('No Instagram conversations found');
        return [];
      }

      const conversations: InstagramConversation[] = [];
      
      for (const conversation of response.data) {
        const messages = await this.getConversationMessages(conversation.id);
        const lastMessage = messages[messages.length - 1];
        
        if (lastMessage) {
          conversations.push({
            id: conversation.id,
            type: 'direct_message',
            accountId: 'ig_business_main',
            participantId: conversation.participants?.data?.[0]?.id || '',
            participantUsername: conversation.participants?.data?.[0]?.username || '',
            participantName: conversation.participants?.data?.[0]?.name || '',
            participantPicture: conversation.participants?.data?.[0]?.profile_pic || '',
            isVerified: conversation.participants?.data?.[0]?.is_verified_user || false,
            isFollowing: conversation.participants?.data?.[0]?.is_user_follow_business || false,
            lastMessage,
            unreadCount: conversation.unread_count || 0,
            status: 'active',
            priority: conversation.unread_count > 5 ? 'high' : 'medium',
            tags: ['instagram-dm'],
            createdAt: conversation.updated_time,
            updatedAt: conversation.updated_time,
            metadata: {
              source: 'instagram_messaging',
            },
          });
        }
      }

      return conversations;
    } catch (error) {
      console.error('Error fetching Instagram direct messages:', error);
      // Fall back to mock data for development
      return this.getMockDirectMessages();
    }
  }

  async getConversationMessages(conversationId: string): Promise<InstagramMessage[]> {
    try {
      const response = await this.makeRequest(`/${conversationId}/messages`, 'GET', {
        fields: 'id,created_time,from,to,message,attachments,reactions',
      });

      return response.data?.map((msg: any) => ({
        id: msg.id,
        conversationId,
        senderId: msg.from?.id || '',
        senderUsername: msg.from?.username || '',
        senderName: msg.from?.name || '',
        senderPicture: msg.from?.profile_pic || '',
        type: msg.attachments?.data?.[0]?.type || 'text',
        content: msg.message || msg.attachments?.data?.[0]?.type || 'Message',
        timestamp: msg.created_time,
        status: 'delivered',
        attachments: this.parseInstagramAttachments(msg.attachments?.data || []),
        reactions: msg.reactions?.data?.map((r: any) => ({
          type: r.reaction,
          count: 1,
          users: [r.id],
          userReacted: false,
        })) || [],
        mentions: this.extractMentions(msg.message || ''),
        hashtags: this.extractHashtags(msg.message || ''),
        isEdited: false,
        isDeleted: false,
        isFromBusiness: msg.from?.id === 'me',
        metadata: msg,
      })) || [];
    } catch (error) {
      console.error('Error fetching conversation messages:', error);
      return [];
    }
  }

  private parseInstagramAttachments(attachments: any[]): InstagramAttachment[] {
    return attachments.map((attachment: any) => ({
      id: attachment.id,
      type: attachment.type,
      url: attachment.payload?.url || '',
      thumbnailUrl: attachment.payload?.preview_url,
      width: attachment.payload?.width,
      height: attachment.payload?.height,
      duration: attachment.payload?.duration,
      size: attachment.payload?.size,
    }));
  }

  private getMockDirectMessages(): InstagramConversation[] {
    // Mock data for demonstration - replace with actual API calls when approved
    return [
      {
        id: 'ig_dm_001',
        type: 'direct_message',
        accountId: 'ig_business_main',
        participantId: 'user_ig_001',
        participantUsername: 'customer_user',
        participantName: 'Customer User',
        participantPicture: '/avatars/customer1.jpg',
        isVerified: false,
        isFollowing: true,
        lastMessage: {
          id: 'ig_msg_001',
          conversationId: 'ig_dm_001',
          senderId: 'user_ig_001',
          senderUsername: 'customer_user',
          senderName: 'Customer User',
          type: 'text',
          content: 'Hi! I saw your latest post about solar panels. Do you have more info?',
          timestamp: new Date().toISOString(),
          status: 'delivered',
          attachments: [],
          reactions: [],
          mentions: [],
          hashtags: [],
          isEdited: false,
          isDeleted: false,
          isFromBusiness: false,
          metadata: {},
        },
        unreadCount: 1,
        status: 'active',
        priority: 'medium',
        tags: ['inquiry', 'solar'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata: {
          source: 'direct_message',
        },
      },
    ];
  }

  async sendDirectMessage(recipientId: string, message: string, messageType: 'text' | 'image' | 'sticker' = 'text', attachmentUrl?: string): Promise<void> {
    try {
      let payload: any = {
        recipient: {
          id: recipientId,
        },
      };

      // Build message payload based on type
      switch (messageType) {
        case 'text':
          payload.message = {
            text: message,
          };
          break;
        
        case 'image':
          if (!attachmentUrl) {
            throw new Error('Image URL is required for image messages');
          }
          payload.message = {
            attachment: {
              type: 'image',
              payload: {
                url: attachmentUrl,
              },
            },
          };
          break;
        
        case 'sticker':
          payload.message = {
            attachment: {
              type: 'template',
              payload: {
                template_type: 'generic',
                elements: [{
                  title: message,
                  image_url: attachmentUrl,
                }],
              },
            },
          };
          break;
        
        default:
          throw new Error(`Unsupported message type: ${messageType}`);
      }

      // Add required tags for customer service
      payload.messaging_type = 'RESPONSE'; // Within 24 hours
      payload.tag = 'HUMAN_AGENT'; // For customer service responses

      const response = await this.makeRequest('/me/messages', 'POST', payload);
      
      if (response.message_id) {
        console.log('Instagram message sent successfully:', response.message_id);
      } else {
        throw new Error('Failed to send Instagram message');
      }
    } catch (error) {
      console.error('Error sending Instagram direct message:', error);
      throw error;
    }
  }

  async sendImageMessage(recipientId: string, imageUrl: string, caption?: string): Promise<void> {
    return this.sendDirectMessage(recipientId, caption || 'Image', 'image', imageUrl);
  }

  async sendStickerMessage(recipientId: string, stickerUrl: string, message?: string): Promise<void> {
    return this.sendDirectMessage(recipientId, message || 'Sticker', 'sticker', stickerUrl);
  }

  async reactToMessage(messageId: string, reaction: string): Promise<void> {
    try {
      const payload = {
        recipient: {
          id: messageId,
        },
        sender_action: 'react',
        reaction: reaction,
      };

      await this.makeRequest('/me/messages', 'POST', payload);
    } catch (error) {
      console.error('Error reacting to Instagram message:', error);
      throw error;
    }
  }

  async getInsights(mediaId?: string): Promise<any> {
    try {
      const endpoint = mediaId ? `/${mediaId}/insights` : '/me/insights';
      const metrics = mediaId 
        ? ['impressions', 'reach', 'likes', 'comments', 'saves', 'shares']
        : ['impressions', 'reach', 'profile_views', 'follower_count'];

      const response = await this.makeRequest(endpoint, 'GET', {
        metric: metrics.join(','),
        period: mediaId ? undefined : 'day',
      });

      return response.data || [];
    } catch (error) {
      console.error('Error fetching Instagram insights:', error);
      return [];
    }
  }

  async searchHashtag(hashtag: string): Promise<any[]> {
    try {
      const response = await this.makeRequest('/ig_hashtag_search', 'GET', {
        q: hashtag,
      });

      return response.data || [];
    } catch (error) {
      console.error('Error searching Instagram hashtag:', error);
      return [];
    }
  }

  async getHashtagMedia(hashtagId: string): Promise<any[]> {
    try {
      const response = await this.makeRequest(`/${hashtagId}/recent_media`, 'GET', {
        fields: 'id,caption,media_type,media_url,timestamp,like_count,comments_count',
      });

      return response.data || [];
    } catch (error) {
      console.error('Error fetching hashtag media:', error);
      return [];
    }
  }

  // Convert comments to conversation format
  async convertCommentsToConversations(mediaId: string, mediaType: 'post' | 'reel' = 'post'): Promise<InstagramConversation[]> {
    try {
      const comments = await this.getMediaComments(mediaId);
      const conversations: InstagramConversation[] = [];

      // Group comments by user
      const commentsByUser = new Map<string, any[]>();
      
      comments.forEach(comment => {
        if (!commentsByUser.has(comment.username)) {
          commentsByUser.set(comment.username, []);
        }
        commentsByUser.get(comment.username)!.push(comment);
      });

      // Convert each user's comments to a conversation
      commentsByUser.forEach((userComments, username) => {
        const latestComment = userComments[userComments.length - 1];
        
        const conversation: InstagramConversation = {
          id: `ig_conv_${mediaId}_${username}`,
          type: mediaType === 'reel' ? 'reel_comment' : 'post_comment',
          accountId: 'ig_business_main',
          participantId: latestComment.id,
          participantUsername: username,
          participantName: username, // Would need separate API call for full name
          isVerified: false, // Would need separate API call
          isFollowing: false, // Would need separate API call
          lastMessage: {
            id: latestComment.id,
            conversationId: `ig_conv_${mediaId}_${username}`,
            senderId: latestComment.id,
            senderUsername: username,
            senderName: username,
            type: 'text',
            content: latestComment.text,
            timestamp: latestComment.timestamp,
            status: 'delivered',
            attachments: [],
            reactions: [],
            mentions: this.extractMentions(latestComment.text),
            hashtags: this.extractHashtags(latestComment.text),
            isEdited: false,
            isDeleted: false,
            isFromBusiness: false,
            metadata: {},
          },
          unreadCount: userComments.length,
          status: 'active',
          priority: userComments.length > 2 ? 'high' : 'medium',
          tags: [mediaType, 'comment'],
          createdAt: userComments[0].timestamp,
          updatedAt: latestComment.timestamp,
          metadata: {
            postId: mediaId,
            mediaType: mediaType === 'reel' ? 'video' : 'photo',
            source: `${mediaType}_comment`,
          },
        };

        conversations.push(conversation);
      });

      return conversations;
    } catch (error) {
      console.error('Error converting comments to conversations:', error);
      return [];
    }
  }

  private extractMentions(text: string): string[] {
    const mentionRegex = /@(\w+)/g;
    const mentions: string[] = [];
    let match;

    while ((match = mentionRegex.exec(text)) !== null) {
      mentions.push(match[1]);
    }

    return mentions;
  }

  private extractHashtags(text: string): string[] {
    const hashtagRegex = /#(\w+)/g;
    const hashtags: string[] = [];
    let match;

    while ((match = hashtagRegex.exec(text)) !== null) {
      hashtags.push(match[1]);
    }

    return hashtags;
  }

  // Webhook verification for Instagram
  static verifyWebhook(mode: string, token: string, challenge: string): string | null {
    const verifyToken = process.env.INSTAGRAM_VERIFY_TOKEN || process.env.FACEBOOK_VERIFY_TOKEN;
    
    if (mode === 'subscribe' && token === verifyToken) {
      return challenge;
    }
    
    return null;
  }

  // Process incoming webhook data
  static processWebhook(body: any): any {
    if (body.object === 'instagram') {
      body.entry.forEach((entry: any) => {
        // Process messaging events
        if (entry.messaging) {
          entry.messaging.forEach((event: any) => {
            if (event.message) {
              // Handle incoming message
              console.log('New Instagram message:', event);
            }
          });
        }

        // Process changes (comments, mentions, etc.)
        if (entry.changes) {
          entry.changes.forEach((change: any) => {
            if (change.field === 'comments') {
              // Handle new comment
              console.log('New Instagram comment:', change.value);
            } else if (change.field === 'mentions') {
              // Handle new mention
              console.log('New Instagram mention:', change.value);
            }
          });
        }
      });
    }

    return { status: 'ok' };
  }
}

export const instagramService = new InstagramService();