import axios from 'axios';

export interface WhatsAppAccount {
  id: string;
  phone: string;
  name: string;
  businessName: string;
  profilePicture: string;
  isVerified: boolean;
  isBusinessAccount: boolean;
  status: 'connected' | 'disconnected' | 'limited';
  unreadMessages: number;
  totalChats: number;
  lastActivity: string;
  qrCode?: string;
}

export interface WhatsAppConversation {
  id: string;
  accountId: string;
  contactId: string;
  contactName: string;
  contactPhone: string;
  contactPicture?: string;
  isGroup: boolean;
  groupMembers?: number;
  lastMessage: WhatsAppMessage;
  unreadCount: number;
  status: 'active' | 'archived' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    source: string;
    businessContext?: string;
  };
}

export interface WhatsAppMessage {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderPhone: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'location' | 'contact' | 'sticker' | 'voice_note' | 'system' | 'template';
  content: string;
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  attachments: WhatsAppAttachment[];
  quotedMessage?: {
    id: string;
    content: string;
    senderName: string;
  };
  isFromBusiness: boolean;
  metadata: any;
}

export interface WhatsAppAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'voice' | 'location';
  url: string;
  thumbnailUrl?: string;
  filename?: string;
  size?: number;
  duration?: number;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
}

export interface WhatsAppTemplate {
  id: string;
  name: string;
  category: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION';
  language: string;
  status: 'APPROVED' | 'PENDING' | 'REJECTED';
  components: WhatsAppTemplateComponent[];
}

export interface WhatsAppTemplateComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
  format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT';
  text?: string;
  parameters?: WhatsAppTemplateParameter[];
  buttons?: WhatsAppTemplateButton[];
}

export interface WhatsAppTemplateParameter {
  type: 'text' | 'currency' | 'date_time' | 'image' | 'document' | 'video';
  text?: string;
  currency?: {
    fallback_value: string;
    code: string;
    amount_1000: number;
  };
  date_time?: {
    fallback_value: string;
  };
  image?: {
    link: string;
  };
  document?: {
    link: string;
    filename: string;
  };
  video?: {
    link: string;
  };
}

export interface WhatsAppTemplateButton {
  type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER';
  text: string;
  url?: string;
  phone_number?: string;
}

export class WhatsAppService {
  private baseURL = 'https://graph.facebook.com/v18.0';
  private phoneNumberId: string;
  private accessToken: string;
  private businessAccountId: string;

  constructor() {
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID || '';
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN || '';
    this.businessAccountId = process.env.WHATSAPP_BUSINESS_ACCOUNT_ID || '';
  }

  private async makeRequest(endpoint: string, method: 'GET' | 'POST' = 'GET', data?: any): Promise<any> {
    try {
      const response = await axios({
        method,
        url: `${this.baseURL}${endpoint}`,
        params: method === 'GET' ? { access_token: this.accessToken, ...data } : { access_token: this.accessToken },
        data: method === 'POST' ? data : undefined,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      return response.data;
    } catch (error: any) {
      console.error('WhatsApp API Error:', error.response?.data || error.message);
      throw new Error(`WhatsApp API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async getBusinessProfile(): Promise<any> {
    try {
      const response = await this.makeRequest(`/${this.phoneNumberId}`, 'GET', {
        fields: 'verified_name,display_phone_number,quality_rating,business_profile',
      });

      return response;
    } catch (error) {
      console.error('Error fetching WhatsApp business profile:', error);
      throw error;
    }
  }

  async sendTextMessage(to: string, text: string): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'text',
        text: {
          body: text,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp text message:', error);
      throw error;
    }
  }

  async sendImageMessage(to: string, imageUrl: string, caption?: string): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'image',
        image: {
          link: imageUrl,
          caption,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp image message:', error);
      throw error;
    }
  }

  async sendVideoMessage(to: string, videoUrl: string, caption?: string): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'video',
        video: {
          link: videoUrl,
          caption,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp video message:', error);
      throw error;
    }
  }

  async sendDocumentMessage(to: string, documentUrl: string, filename: string, caption?: string): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'document',
        document: {
          link: documentUrl,
          filename,
          caption,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp document message:', error);
      throw error;
    }
  }

  async sendAudioMessage(to: string, audioUrl: string): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'audio',
        audio: {
          link: audioUrl,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp audio message:', error);
      throw error;
    }
  }

  async sendLocationMessage(to: string, latitude: number, longitude: number, name?: string, address?: string): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'location',
        location: {
          latitude,
          longitude,
          name,
          address,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp location message:', error);
      throw error;
    }
  }

  async sendContactMessage(to: string, contacts: any[]): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'contacts',
        contacts,
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp contact message:', error);
      throw error;
    }
  }

  async sendTemplateMessage(to: string, templateName: string, languageCode: string, parameters?: any[]): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: languageCode,
          },
          components: parameters ? [
            {
              type: 'body',
              parameters,
            },
          ] : undefined,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp template message:', error);
      throw error;
    }
  }

  async sendInteractiveMessage(to: string, type: 'button' | 'list', interactive: any): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to,
        type: 'interactive',
        interactive: {
          type,
          ...interactive,
        },
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error sending WhatsApp interactive message:', error);
      throw error;
    }
  }

  async markMessageAsRead(messageId: string): Promise<any> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        status: 'read',
        message_id: messageId,
      };

      return await this.makeRequest(`/${this.phoneNumberId}/messages`, 'POST', payload);
    } catch (error) {
      console.error('Error marking WhatsApp message as read:', error);
      throw error;
    }
  }

  async getMessageTemplates(): Promise<WhatsAppTemplate[]> {
    try {
      const response = await this.makeRequest(`/${this.businessAccountId}/message_templates`, 'GET', {
        fields: 'id,name,category,status,language,components',
      });

      return response.data || [];
    } catch (error) {
      console.error('Error fetching WhatsApp message templates:', error);
      throw error;
    }
  }

  async createMessageTemplate(template: Partial<WhatsAppTemplate>): Promise<any> {
    try {
      const payload = {
        name: template.name,
        category: template.category,
        language: template.language,
        components: template.components,
      };

      return await this.makeRequest(`/${this.businessAccountId}/message_templates`, 'POST', payload);
    } catch (error) {
      console.error('Error creating WhatsApp message template:', error);
      throw error;
    }
  }

  async uploadMedia(mediaUrl: string, type: 'image' | 'video' | 'audio' | 'document'): Promise<string> {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        media_url: mediaUrl,
        type,
      };

      const response = await this.makeRequest(`/${this.phoneNumberId}/media`, 'POST', payload);
      return response.id;
    } catch (error) {
      console.error('Error uploading WhatsApp media:', error);
      throw error;
    }
  }

  async getMediaUrl(mediaId: string): Promise<string> {
    try {
      const response = await this.makeRequest(`/${mediaId}`, 'GET');
      return response.url;
    } catch (error) {
      console.error('Error getting WhatsApp media URL:', error);
      throw error;
    }
  }

  async downloadMedia(mediaId: string): Promise<Buffer> {
    try {
      const mediaInfo = await this.makeRequest(`/${mediaId}`, 'GET');
      
      const response = await axios.get(mediaInfo.url, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
        responseType: 'arraybuffer',
      });

      return Buffer.from(response.data);
    } catch (error) {
      console.error('Error downloading WhatsApp media:', error);
      throw error;
    }
  }

  // Convert webhook messages to conversation format
  convertWebhookToMessage(webhookData: any): WhatsAppMessage | null {
    try {
      const message = webhookData.messages?.[0];
      if (!message) return null;

      const contact = webhookData.contacts?.[0];
      const profile = contact?.profile;

      return {
        id: message.id,
        conversationId: `wa_${message.from}`,
        senderId: message.from,
        senderName: profile?.name || contact?.wa_id || message.from,
        senderPhone: message.from,
        type: message.type,
        content: this.extractMessageContent(message),
        timestamp: new Date(parseInt(message.timestamp) * 1000).toISOString(),
        status: 'delivered',
        attachments: this.extractAttachments(message),
        isFromBusiness: false,
        metadata: message,
      };
    } catch (error) {
      console.error('Error converting webhook to message:', error);
      return null;
    }
  }

  private extractMessageContent(message: any): string {
    switch (message.type) {
      case 'text':
        return message.text?.body || '';
      case 'image':
        return message.image?.caption || 'Image';
      case 'video':
        return message.video?.caption || 'Video';
      case 'audio':
        return 'Audio message';
      case 'voice':
        return 'Voice message';
      case 'document':
        return message.document?.filename || 'Document';
      case 'location':
        return `Location: ${message.location?.latitude}, ${message.location?.longitude}`;
      case 'contacts':
        return `Contact: ${message.contacts?.[0]?.name?.formatted_name || 'Contact'}`;
      case 'sticker':
        return 'Sticker';
      default:
        return 'Message';
    }
  }

  private extractAttachments(message: any): WhatsAppAttachment[] {
    const attachments: WhatsAppAttachment[] = [];

    if (message.image) {
      attachments.push({
        id: message.image.id,
        type: 'image',
        url: '', // Would need to fetch using media ID
        filename: message.image.caption,
        size: parseInt(message.image.file_size || '0'),
      });
    }

    if (message.video) {
      attachments.push({
        id: message.video.id,
        type: 'video',
        url: '', // Would need to fetch using media ID
        filename: message.video.caption,
        size: parseInt(message.video.file_size || '0'),
      });
    }

    if (message.audio) {
      attachments.push({
        id: message.audio.id,
        type: 'audio',
        url: '', // Would need to fetch using media ID
        duration: parseInt(message.audio.duration || '0'),
        size: parseInt(message.audio.file_size || '0'),
      });
    }

    if (message.voice) {
      attachments.push({
        id: message.voice.id,
        type: 'voice',
        url: '', // Would need to fetch using media ID
        duration: parseInt(message.voice.duration || '0'),
        size: parseInt(message.voice.file_size || '0'),
      });
    }

    if (message.document) {
      attachments.push({
        id: message.document.id,
        type: 'document',
        url: '', // Would need to fetch using media ID
        filename: message.document.filename,
        size: parseInt(message.document.file_size || '0'),
      });
    }

    if (message.location) {
      attachments.push({
        id: 'location',
        type: 'location',
        url: '',
        location: {
          latitude: message.location.latitude,
          longitude: message.location.longitude,
          address: message.location.address,
        },
      });
    }

    return attachments;
  }

  // Webhook verification for WhatsApp
  static verifyWebhook(mode: string, token: string, challenge: string): string | null {
    const verifyToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN;
    
    if (mode === 'subscribe' && token === verifyToken) {
      return challenge;
    }
    
    return null;
  }

  // Process incoming webhook data
  static processWebhook(body: any): any {
    const service = new WhatsAppService();
    
    if (body.object === 'whatsapp_business_account') {
      body.entry.forEach((entry: any) => {
        entry.changes.forEach((change: any) => {
          if (change.field === 'messages') {
            const value = change.value;
            
            // Process incoming messages
            if (value.messages) {
              value.messages.forEach((message: any) => {
                const convertedMessage = service.convertWebhookToMessage(value);
                if (convertedMessage) {
                  console.log('New WhatsApp message:', convertedMessage);
                  // Here you would typically save to database or trigger handlers
                }
              });
            }

            // Process message status updates
            if (value.statuses) {
              value.statuses.forEach((status: any) => {
                console.log('WhatsApp message status update:', status);
                // Update message status in database
              });
            }
          }
        });
      });
    }

    return { status: 'ok' };
  }
}

export const whatsappService = new WhatsAppService();