import { Farmer, FarmerCreate, FarmerUpdate, Lot } from '../../domain/models/Farmer';
import { FarmerRepository } from '../../domain/repositories/FarmerRepository';

/**
 * Service layer for farmer operations
 * Uses repository to access data but knows nothing about storage details
 */
export class FarmerService {
  constructor(private farmerRepository: FarmerRepository) {}

  async getFarmerDetails(farmerId: string): Promise<Farmer | null> {
    return this.farmerRepository.getFarmerByFarmerId(farmerId);
  }

  async getAllFarmers(): Promise<Farmer[]> {
    return this.farmerRepository.getAllFarmers();
  }

  async createFarmer(farmerData: FarmerCreate): Promise<Farmer> {
    return this.farmerRepository.createFarmer(farmerData);
  }

  async updateFarmer(id: string, updates: Partial<Omit<Farmer, 'id' | 'lots'>>): Promise<Farmer | null> {
    const farmer = await this.farmerRepository.getFarmerById(id);
    if (!farmer) return null;
    
    return this.farmerRepository.updateFarmer({
      id,
      ...updates
    });
  }

  async deleteFarmer(id: string): Promise<boolean> {
    return this.farmerRepository.deleteFarmer(id);
  }

  async addLotToFarmer(farmerId: string, lot: Omit<Lot, 'id'>): Promise<Farmer | null> {
    const farmer = await this.farmerRepository.getFarmerById(farmerId);
    if (!farmer) return null;

    const newLot: Lot = {
      id: `lot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...lot
    };

    const updatedLots = [...farmer.lots, newLot];
    
    return this.farmerRepository.updateFarmer({
      id: farmerId,
      lots: updatedLots
    });
  }

  async removeLotFromFarmer(farmerId: string, lotId: string): Promise<Farmer | null> {
    const farmer = await this.farmerRepository.getFarmerById(farmerId);
    if (!farmer) return null;

    const updatedLots = farmer.lots.filter(lot => lot.id !== lotId);
    
    return this.farmerRepository.updateFarmer({
      id: farmerId,
      lots: updatedLots
    });
  }
}
