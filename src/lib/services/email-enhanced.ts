import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { Client } from '@microsoft/microsoft-graph-client';
import { AuthenticationProvider } from '@microsoft/microsoft-graph-client';
import nodemailer from 'nodemailer';
import * as Imap from 'imap';
import { simpleParser } from 'mailparser';
import fs from 'fs';
import path from 'path';

export interface EmailAccount {
  id: string;
  email: string;
  name: string;
  type: 'sales' | 'support' | 'marketing' | 'general';
  provider: 'gmail' | 'outlook' | 'exchange' | 'imap' | 'other';
  status: 'connected' | 'disconnected' | 'sync_error';
  unreadCount: number;
  totalEmails: number;
  lastSync: string;
  config?: ImapConfig;
  credentials?: EmailCredentials;
}

export interface ImapConfig {
  host: string;
  port: number;
  secure: boolean;
  username: string;
  password: string;
  authTimeout?: number;
  connTimeout?: number;
  keepalive?: boolean;
  tlsOptions?: any;
}

export interface EmailCredentials {
  type: 'oauth' | 'password' | 'app_password';
  clientId?: string;
  clientSecret?: string;
  refreshToken?: string;
  accessToken?: string;
  password?: string;
  username?: string;
}

export interface Email {
  id: string;
  accountId: string;
  threadId: string;
  from: {
    email: string;
    name: string;
  };
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  snippet: string;
  timestamp: string;
  isRead: boolean;
  isStarred: boolean;
  isFlagged: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  labels: string[];
  attachments: EmailAttachment[];
  assignedTo?: string;
  status: 'new' | 'in_progress' | 'resolved' | 'archived';
  responseTime?: number;
  isFromCustomer: boolean;
}

export interface EmailAttachment {
  id: string;
  filename: string;
  mimeType: string;
  size: number;
  url?: string;
}

class ImapService {
  private accounts: Map<string, EmailAccount> = new Map();
  private connections: Map<string, Imap> = new Map();

  constructor() {
    this.loadAccounts();
  }

  private loadAccounts(): void {
    try {
      const accountsPath = path.join(process.cwd(), 'data', 'apps', 'email', 'accounts.json');
      if (fs.existsSync(accountsPath)) {
        const accountsData = JSON.parse(fs.readFileSync(accountsPath, 'utf8'));
        accountsData.forEach((account: EmailAccount) => {
          this.accounts.set(account.id, account);
        });
      }
    } catch (error) {
      console.error('Error loading email accounts:', error);
    }
  }

  private saveAccounts(): void {
    try {
      const accountsPath = path.join(process.cwd(), 'data', 'apps', 'email', 'accounts.json');
      const accountsDir = path.dirname(accountsPath);
      
      if (!fs.existsSync(accountsDir)) {
        fs.mkdirSync(accountsDir, { recursive: true });
      }
      
      const accountsArray = Array.from(this.accounts.values());
      fs.writeFileSync(accountsPath, JSON.stringify(accountsArray, null, 2));
    } catch (error) {
      console.error('Error saving email accounts:', error);
    }
  }

  async addAccount(account: EmailAccount): Promise<void> {
    try {
      // Test connection first
      await this.testConnection(account);
      
      // If successful, add to accounts
      this.accounts.set(account.id, { ...account, status: 'connected' });
      this.saveAccounts();
      
      console.log(`Email account ${account.email} added successfully`);
    } catch (error) {
      console.error(`Error adding email account ${account.email}:`, error);
      throw error;
    }
  }

  async removeAccount(accountId: string): Promise<void> {
    try {
      // Close connection if exists
      const connection = this.connections.get(accountId);
      if (connection) {
        connection.end();
        this.connections.delete(accountId);
      }
      
      // Remove from accounts
      this.accounts.delete(accountId);
      this.saveAccounts();
      
      console.log(`Email account ${accountId} removed successfully`);
    } catch (error) {
      console.error(`Error removing email account ${accountId}:`, error);
      throw error;
    }
  }

  async testConnection(account: EmailAccount): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!account.config) {
        reject(new Error('No IMAP configuration found'));
        return;
      }

      const imap = new Imap({
        user: account.config.username,
        password: account.config.password,
        host: account.config.host,
        port: account.config.port,
        tls: account.config.secure,
        authTimeout: account.config.authTimeout || 10000,
        connTimeout: account.config.connTimeout || 10000,
        keepalive: account.config.keepalive || false,
        tlsOptions: account.config.tlsOptions || { rejectUnauthorized: false },
      });

      imap.once('ready', () => {
        console.log(`IMAP connection test successful for ${account.email}`);
        imap.end();
        resolve(true);
      });

      imap.once('error', (err: Error) => {
        console.error(`IMAP connection test failed for ${account.email}:`, err);
        reject(err);
      });

      imap.connect();
    });
  }

  private async getConnection(accountId: string): Promise<Imap> {
    return new Promise((resolve, reject) => {
      const account = this.accounts.get(accountId);
      if (!account || !account.config) {
        reject(new Error(`No account configuration found for ${accountId}`));
        return;
      }

      // Check if connection already exists and is ready
      const existingConnection = this.connections.get(accountId);
      if (existingConnection && existingConnection.state === 'authenticated') {
        resolve(existingConnection);
        return;
      }

      // Create new connection
      const imap = new Imap({
        user: account.config.username,
        password: account.config.password,
        host: account.config.host,
        port: account.config.port,
        tls: account.config.secure,
        authTimeout: account.config.authTimeout || 10000,
        connTimeout: account.config.connTimeout || 10000,
        keepalive: account.config.keepalive || true,
        tlsOptions: account.config.tlsOptions || { rejectUnauthorized: false },
      });

      imap.once('ready', () => {
        console.log(`IMAP connection established for ${account.email}`);
        this.connections.set(accountId, imap);
        resolve(imap);
      });

      imap.once('error', (err: Error) => {
        console.error(`IMAP connection failed for ${account.email}:`, err);
        this.connections.delete(accountId);
        reject(err);
      });

      imap.once('end', () => {
        console.log(`IMAP connection ended for ${account.email}`);
        this.connections.delete(accountId);
      });

      imap.connect();
    });
  }

  async getMessages(accountId: string, folder: string = 'INBOX', limit: number = 20): Promise<Email[]> {
    try {
      const imap = await this.getConnection(accountId);
      const account = this.accounts.get(accountId);
      
      if (!account) {
        throw new Error(`Account ${accountId} not found`);
      }

      return new Promise((resolve, reject) => {
        imap.openBox(folder, false, (err: Error, box: any) => {
          if (err) {
            reject(err);
            return;
          }

          if (box.messages.total === 0) {
            resolve([]);
            return;
          }

          const searchCriteria = ['ALL'];
          const fetchOptions = {
            bodies: 'HEADER.FIELDS (FROM TO SUBJECT DATE)',
            struct: true,
            envelope: true,
            markSeen: false,
          };

          imap.search(searchCriteria, (err: Error, results: number[]) => {
            if (err) {
              reject(err);
              return;
            }

            if (!results || results.length === 0) {
              resolve([]);
              return;
            }

            // Get the most recent messages
            const messageIds = results.slice(-limit).reverse();
            const emails: Email[] = [];
            let processedCount = 0;

            const f = imap.fetch(messageIds, fetchOptions);

            f.on('message', (msg: any, seqno: number) => {
              let email: Partial<Email> = {
                id: `${accountId}_${seqno}`,
                accountId,
                threadId: `${accountId}_${seqno}`,
                attachments: [],
                labels: [],
                status: 'new',
              };

              msg.on('body', (stream: any, info: any) => {
                simpleParser(stream, (err: Error, parsed: any) => {
                  if (err) {
                    console.error('Error parsing email:', err);
                    return;
                  }

                  email = {
                    ...email,
                    from: {
                      name: parsed.from?.text || parsed.from?.value?.[0]?.name || 'Unknown',
                      email: parsed.from?.value?.[0]?.address || parsed.from?.text || '<EMAIL>',
                    },
                    to: parsed.to?.value?.map((addr: any) => addr.address) || [],
                    cc: parsed.cc?.value?.map((addr: any) => addr.address) || [],
                    subject: parsed.subject || 'No Subject',
                    body: parsed.html || parsed.text || '',
                    snippet: (parsed.text || '').substring(0, 200) + '...',
                    timestamp: parsed.date ? new Date(parsed.date).toISOString() : new Date().toISOString(),
                    attachments: parsed.attachments?.map((att: any) => ({
                      id: `${accountId}_${seqno}_${att.filename}`,
                      filename: att.filename,
                      mimeType: att.contentType,
                      size: att.size || 0,
                    })) || [],
                  };
                });
              });

              msg.once('attributes', (attrs: any) => {
                email.isRead = !attrs.flags.includes('\\Seen');
                email.isStarred = attrs.flags.includes('\\Flagged');
                email.isFlagged = attrs.flags.includes('\\Flagged');
                email.priority = attrs.flags.includes('\\Flagged') ? 'high' : 'normal';
                email.isFromCustomer = !email.from?.email?.includes('@abngreen.com');
              });

              msg.once('end', () => {
                emails.push(email as Email);
                processedCount++;
                
                if (processedCount === messageIds.length) {
                  resolve(emails);
                }
              });
            });

            f.once('error', (err: Error) => {
              reject(err);
            });

            f.once('end', () => {
              if (processedCount === 0) {
                resolve([]);
              }
            });
          });
        });
      });
    } catch (error) {
      console.error(`Error fetching messages for ${accountId}:`, error);
      throw error;
    }
  }

  async getFolders(accountId: string): Promise<string[]> {
    try {
      const imap = await this.getConnection(accountId);
      
      return new Promise((resolve, reject) => {
        imap.getBoxes((err: Error, boxes: any) => {
          if (err) {
            reject(err);
            return;
          }

          const folders: string[] = [];
          const extractFolders = (obj: any, prefix: string = '') => {
            Object.keys(obj).forEach(key => {
              const fullPath = prefix ? `${prefix}${obj[key].delimiter}${key}` : key;
              folders.push(fullPath);
              
              if (obj[key].children) {
                extractFolders(obj[key].children, fullPath);
              }
            });
          };

          extractFolders(boxes);
          resolve(folders);
        });
      });
    } catch (error) {
      console.error(`Error fetching folders for ${accountId}:`, error);
      throw error;
    }
  }

  async getUnreadCount(accountId: string, folder: string = 'INBOX'): Promise<number> {
    try {
      const imap = await this.getConnection(accountId);
      
      return new Promise((resolve, reject) => {
        imap.openBox(folder, true, (err: Error, box: any) => {
          if (err) {
            reject(err);
            return;
          }

          imap.search(['UNSEEN'], (err: Error, results: number[]) => {
            if (err) {
              reject(err);
              return;
            }

            resolve(results ? results.length : 0);
          });
        });
      });
    } catch (error) {
      console.error(`Error getting unread count for ${accountId}:`, error);
      return 0;
    }
  }

  getAllAccounts(): EmailAccount[] {
    return Array.from(this.accounts.values());
  }

  getAccount(accountId: string): EmailAccount | undefined {
    return this.accounts.get(accountId);
  }

  async updateAccountStatus(accountId: string, status: 'connected' | 'disconnected' | 'sync_error'): Promise<void> {
    const account = this.accounts.get(accountId);
    if (account) {
      account.status = status;
      account.lastSync = new Date().toISOString();
      this.accounts.set(accountId, account);
      this.saveAccounts();
    }
  }

  async syncAccount(accountId: string): Promise<void> {
    try {
      const account = this.accounts.get(accountId);
      if (!account) {
        throw new Error(`Account ${accountId} not found`);
      }

      console.log(`Starting sync for account ${account.email}`);
      
      // Update unread count
      const unreadCount = await this.getUnreadCount(accountId);
      account.unreadCount = unreadCount;
      
      // Update last sync time
      account.lastSync = new Date().toISOString();
      account.status = 'connected';
      
      this.accounts.set(accountId, account);
      this.saveAccounts();
      
      console.log(`Sync completed for account ${account.email}. Unread: ${unreadCount}`);
    } catch (error) {
      console.error(`Error syncing account ${accountId}:`, error);
      await this.updateAccountStatus(accountId, 'sync_error');
      throw error;
    }
  }

  async closeAllConnections(): Promise<void> {
    for (const [accountId, connection] of this.connections) {
      try {
        connection.end();
        console.log(`Closed connection for ${accountId}`);
      } catch (error) {
        console.error(`Error closing connection for ${accountId}:`, error);
      }
    }
    this.connections.clear();
  }
}

// Enhanced Email Service with IMAP support
export class EnhancedEmailService {
  private imapService: ImapService;

  constructor() {
    this.imapService = new ImapService();
  }

  async getAccounts(): Promise<EmailAccount[]> {
    return this.imapService.getAllAccounts();
  }

  async getMessages(accountId: string, maxResults: number = 20, folder: string = 'INBOX'): Promise<Email[]> {
    const account = this.imapService.getAccount(accountId);
    if (account) {
      return await this.imapService.getMessages(accountId, folder, maxResults);
    }
    return [];
  }

  // IMAP account management methods
  async addImapAccount(account: EmailAccount): Promise<void> {
    return await this.imapService.addAccount(account);
  }

  async removeImapAccount(accountId: string): Promise<void> {
    return await this.imapService.removeAccount(accountId);
  }

  async testImapConnection(account: EmailAccount): Promise<boolean> {
    return await this.imapService.testConnection(account);
  }

  async getImapFolders(accountId: string): Promise<string[]> {
    return await this.imapService.getFolders(accountId);
  }

  async getUnreadCount(accountId: string, folder: string = 'INBOX'): Promise<number> {
    const account = this.imapService.getAccount(accountId);
    if (account) {
      return await this.imapService.getUnreadCount(accountId, folder);
    }
    return 0;
  }

  async syncAccount(accountId: string): Promise<void> {
    const account = this.imapService.getAccount(accountId);
    if (account) {
      return await this.imapService.syncAccount(accountId);
    }
  }

  async syncAllAccounts(): Promise<void> {
    const accounts = this.imapService.getAllAccounts();
    const syncPromises = accounts.map(account => 
      this.syncAccount(account.id).catch(error => 
        console.error(`Failed to sync account ${account.id}:`, error)
      )
    );
    
    await Promise.all(syncPromises);
  }

  async closeAllConnections(): Promise<void> {
    await this.imapService.closeAllConnections();
  }

  // Get all messages from all accounts
  async getAllMessages(maxResults: number = 20): Promise<Email[]> {
    const accounts = await this.getAccounts();
    const allMessages: Email[] = [];
    
    for (const account of accounts) {
      try {
        const messages = await this.getMessages(account.id, maxResults);
        allMessages.push(...messages);
      } catch (error) {
        console.error(`Error fetching messages for ${account.id}:`, error);
      }
    }
    
    // Sort by timestamp descending
    return allMessages.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  async sendMessage(accountId: string, to: string, subject: string, body: string): Promise<void> {
    // For now, use SMTP for sending (can be enhanced later)
    const smtpService = new SMTPService();
    await smtpService.sendMessage(to, subject, body);
  }
}

class SMTPService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendMessage(to: string, subject: string, body: string): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: process.env.SMTP_USER,
        to,
        subject,
        html: body,
      });
    } catch (error) {
      console.error('Error sending SMTP message:', error);
      throw error;
    }
  }
}

export const enhancedEmailService = new EnhancedEmailService();