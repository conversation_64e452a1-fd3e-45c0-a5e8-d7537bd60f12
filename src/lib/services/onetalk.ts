import { emailService, EmailAccount, Email } from './email';
import { enhancedEmailService } from './email-enhanced';
import { facebookService, FacebookPage, FacebookConversation, FacebookMessage } from './facebook';
import { instagramService, InstagramAccount, InstagramConversation, InstagramMessage } from './instagram';
import { whatsappService, WhatsAppAccount, WhatsAppConversation, WhatsAppMessage } from './whatsapp';
import { zaloService, ZaloAccount, ZaloConversation, ZaloMessage } from './zalo';

export type Platform = 'email' | 'facebook' | 'instagram' | 'whatsapp' | 'zalo';

export interface UnifiedAccount {
  id: string;
  platform: Platform;
  name: string;
  identifier: string; // email, phone, username, etc.
  displayName: string;
  profilePicture?: string;
  status: 'connected' | 'disconnected' | 'limited' | 'error';
  unreadCount: number;
  totalItems: number; // emails, messages, etc.
  lastActivity: string;
  metadata: any;
}

export interface UnifiedConversation {
  id: string;
  platform: Platform;
  accountId: string;
  participantId: string;
  participantName: string;
  participantIdentifier: string; // email, phone, username
  participantPicture?: string;
  lastMessage: UnifiedMessage;
  unreadCount: number;
  status: 'active' | 'archived' | 'blocked' | 'spam';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  metadata: any;
}

export interface UnifiedMessage {
  id: string;
  platform: Platform;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderIdentifier: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'location' | 'sticker' | 'system' | 'template';
  content: string;
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  attachments: UnifiedAttachment[];
  mentions: string[];
  reactions: UnifiedReaction[];
  isFromBusiness: boolean;
  metadata: any;
}

export interface UnifiedAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'file' | 'location' | 'link';
  url: string;
  thumbnailUrl?: string;
  filename?: string;
  size?: number;
  duration?: number;
  metadata: any;
}

export interface UnifiedReaction {
  type: string;
  count: number;
  users: string[];
  userReacted: boolean;
}

export interface QuickReply {
  id: string;
  title: string;
  content: string;
  category: string;
  platform?: Platform | 'all';
  language: 'en' | 'vi' | 'all';
  usageCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface OneTalkStats {
  totalAccounts: number;
  connectedAccounts: number;
  totalUnread: number;
  totalConversations: number;
  activeConversations: number;
  avgResponseTime: number;
  platformStats: Record<Platform, {
    accounts: number;
    unread: number;
    conversations: number;
    status: 'healthy' | 'warning' | 'error';
  }>;
}

export class OneTalkService {
  private platforms: Record<Platform, any> = {
    email: emailService,
    facebook: facebookService,
    instagram: instagramService,
    whatsapp: whatsappService,
    zalo: zaloService,
  };

  async getAllAccounts(): Promise<UnifiedAccount[]> {
    const accounts: UnifiedAccount[] = [];

    try {
      // Get email accounts (both original and enhanced)
      const emailAccounts = await emailService.getAccounts();
      accounts.push(...emailAccounts.map(this.convertEmailAccount));
      
      // Get additional IMAP accounts
      const enhancedEmailAccounts = await enhancedEmailService.getAccounts();
      const additionalAccounts = enhancedEmailAccounts.filter(acc => 
        !emailAccounts.some(existingAcc => existingAcc.id === acc.id)
      );
      accounts.push(...additionalAccounts.map(this.convertEmailAccount));
    } catch (error) {
      console.error('Error fetching email accounts:', error);
    }

    try {
      // Get Facebook pages
      const facebookPages = await facebookService.getPages();
      accounts.push(...facebookPages.map(this.convertFacebookPage));
    } catch (error) {
      console.error('Error fetching Facebook pages:', error);
    }

    try {
      // Get Instagram account
      const instagramAccount = await instagramService.getAccount();
      accounts.push(this.convertInstagramAccount(instagramAccount));
    } catch (error) {
      console.error('Error fetching Instagram account:', error);
    }

    try {
      // Get WhatsApp business profile
      const whatsappProfile = await whatsappService.getBusinessProfile();
      accounts.push(this.convertWhatsAppAccount(whatsappProfile));
    } catch (error) {
      console.error('Error fetching WhatsApp profile:', error);
    }

    try {
      // Get Zalo OA profile
      const zaloProfile = await zaloService.getProfile();
      accounts.push(this.convertZaloAccount(zaloProfile));
    } catch (error) {
      console.error('Error fetching Zalo profile:', error);
    }

    return accounts;
  }

  async getAllConversations(platform?: Platform, accountId?: string): Promise<UnifiedConversation[]> {
    const conversations: UnifiedConversation[] = [];

    if (!platform || platform === 'email') {
      try {
        let emails: Email[] = [];
        
        if (accountId) {
          // Get emails from specific account
          const account = await enhancedEmailService.getAccounts().then(accounts => 
            accounts.find(acc => acc.id === accountId)
          );
          
          if (account) {
            emails = await enhancedEmailService.getMessages(accountId);
          } else {
            // Fallback to original email service
            emails = await emailService.getMessages(accountId);
          }
        } else {
          // Get emails from all accounts
          emails = await enhancedEmailService.getAllMessages();
        }
        
        // Convert emails to conversations (group by sender)
        const emailConversations = this.convertEmailsToConversations(emails);
        conversations.push(...emailConversations);
      } catch (error) {
        console.error('Error fetching email conversations:', error);
      }
    }

    if (!platform || platform === 'facebook') {
      try {
        // Would need page access token and page ID
        // const fbConversations = await facebookService.getConversations(pageId, pageAccessToken);
        // conversations.push(...fbConversations.map(this.convertFacebookConversation));
      } catch (error) {
        console.error('Error fetching Facebook conversations:', error);
      }
    }

    if (!platform || platform === 'instagram') {
      try {
        const igConversations = await instagramService.getDirectMessages();
        conversations.push(...igConversations.map(this.convertInstagramConversation));
      } catch (error) {
        console.error('Error fetching Instagram conversations:', error);
      }
    }

    if (!platform || platform === 'whatsapp') {
      // WhatsApp conversations would come from webhooks
      // conversations.push(...this.getStoredWhatsAppConversations());
    }

    if (!platform || platform === 'zalo') {
      // Zalo conversations would come from webhooks
      // conversations.push(...this.getStoredZaloConversations());
    }

    return conversations.sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
  }

  async sendMessage(
    platform: Platform,
    accountId: string,
    recipientId: string,
    content: string,
    type: 'text' | 'image' | 'template' = 'text',
    metadata?: any
  ): Promise<void> {
    switch (platform) {
      case 'email':
        try {
          // Try enhanced email service first
          await enhancedEmailService.sendMessage(accountId, recipientId, metadata?.subject || 'Response', content);
        } catch (error) {
          // Fallback to original email service
          await emailService.sendMessage(accountId, recipientId, metadata?.subject || 'Response', content);
        }
        break;
      
      case 'facebook':
        await facebookService.sendMessage(accountId, recipientId, content, metadata?.pageAccessToken);
        break;
      
      case 'instagram':
        await instagramService.sendDirectMessage(recipientId, content);
        break;
      
      case 'whatsapp':
        if (type === 'image' && metadata?.imageUrl) {
          await whatsappService.sendImageMessage(recipientId, metadata.imageUrl, content);
        } else {
          await whatsappService.sendTextMessage(recipientId, content);
        }
        break;
      
      case 'zalo':
        if (type === 'image' && metadata?.imageUrl) {
          await zaloService.sendImageMessage(recipientId, metadata.imageUrl, content);
        } else {
          await zaloService.sendTextMessage(recipientId, content);
        }
        break;
      
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  async getQuickReplies(platform?: Platform, category?: string): Promise<QuickReply[]> {
    // This would typically fetch from a database
    // For now, return mock data
    const mockQuickReplies: QuickReply[] = [
      {
        id: '1',
        title: 'Thank You (Vietnamese)',
        content: 'Cảm ơn bạn đã liên hệ với ABN Green. Chúng tôi sẽ phản hồi trong thời gian sớm nhất.',
        category: 'greeting',
        platform: 'all',
        language: 'vi',
        usageCount: 156,
        isActive: true,
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-01-16T00:00:00Z',
      },
      {
        id: '2',
        title: 'Thank You (English)',
        content: 'Thank you for contacting ABN Green. We will respond as soon as possible.',
        category: 'greeting',
        platform: 'all',
        language: 'en',
        usageCount: 98,
        isActive: true,
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-01-16T00:00:00Z',
      },
      {
        id: '3',
        title: 'Solar Consultation',
        content: 'Để tư vấn chính xác, xin vui lòng cung cấp: 1) Diện tích mái nhà 2) Địa chỉ lắp đặt 3) Hóa đơn điện 3 tháng gần nhất',
        category: 'consultation',
        platform: 'zalo',
        language: 'vi',
        usageCount: 67,
        isActive: true,
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-01-16T00:00:00Z',
      },
    ];

    return mockQuickReplies.filter(reply => {
      if (platform && reply.platform !== 'all' && reply.platform !== platform) {
        return false;
      }
      if (category && reply.category !== category) {
        return false;
      }
      return reply.isActive;
    });
  }

  async getStats(): Promise<OneTalkStats> {
    const accounts = await this.getAllAccounts();
    const conversations = await this.getAllConversations();

    const stats: OneTalkStats = {
      totalAccounts: accounts.length,
      connectedAccounts: accounts.filter(acc => acc.status === 'connected').length,
      totalUnread: accounts.reduce((sum, acc) => sum + acc.unreadCount, 0),
      totalConversations: conversations.length,
      activeConversations: conversations.filter(conv => conv.status === 'active').length,
      avgResponseTime: 12, // Mock value - would calculate from actual data
      platformStats: {
        email: {
          accounts: accounts.filter(acc => acc.platform === 'email').length,
          unread: accounts.filter(acc => acc.platform === 'email').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'email').length,
          status: 'healthy',
        },
        facebook: {
          accounts: accounts.filter(acc => acc.platform === 'facebook').length,
          unread: accounts.filter(acc => acc.platform === 'facebook').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'facebook').length,
          status: 'healthy',
        },
        instagram: {
          accounts: accounts.filter(acc => acc.platform === 'instagram').length,
          unread: accounts.filter(acc => acc.platform === 'instagram').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'instagram').length,
          status: 'healthy',
        },
        whatsapp: {
          accounts: accounts.filter(acc => acc.platform === 'whatsapp').length,
          unread: accounts.filter(acc => acc.platform === 'whatsapp').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'whatsapp').length,
          status: 'healthy',
        },
        zalo: {
          accounts: accounts.filter(acc => acc.platform === 'zalo').length,
          unread: accounts.filter(acc => acc.platform === 'zalo').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'zalo').length,
          status: 'healthy',
        },
      },
    };

    return stats;
  }

  // Conversion methods
  private convertEmailAccount = (account: EmailAccount): UnifiedAccount => ({
    id: account.id,
    platform: 'email',
    name: account.name,
    identifier: account.email,
    displayName: account.name,
    status: account.status,
    unreadCount: account.unreadCount,
    totalItems: account.totalEmails,
    lastActivity: account.lastSync,
    metadata: account,
  });

  private convertFacebookPage = (page: FacebookPage): UnifiedAccount => ({
    id: page.id,
    platform: 'facebook',
    name: page.name,
    identifier: page.handle,
    displayName: page.name,
    profilePicture: page.profilePicture,
    status: page.status,
    unreadCount: page.unreadMessages + page.unreadComments,
    totalItems: page.followers,
    lastActivity: page.lastActivity,
    metadata: page,
  });

  private convertInstagramAccount = (account: InstagramAccount): UnifiedAccount => ({
    id: account.id,
    platform: 'instagram',
    name: account.name,
    identifier: account.username,
    displayName: account.name,
    profilePicture: account.profilePicture,
    status: account.status,
    unreadCount: account.unreadMessages + account.unreadStoryReplies,
    totalItems: account.followers,
    lastActivity: account.lastActivity,
    metadata: account,
  });

  private convertWhatsAppAccount = (profile: any): UnifiedAccount => ({
    id: 'whatsapp_business',
    platform: 'whatsapp',
    name: profile.verified_name || 'WhatsApp Business',
    identifier: profile.display_phone_number,
    displayName: profile.verified_name || 'WhatsApp Business',
    status: 'connected',
    unreadCount: 0,
    totalItems: 0,
    lastActivity: new Date().toISOString(),
    metadata: profile,
  });

  private convertZaloAccount = (account: ZaloAccount): UnifiedAccount => ({
    id: account.id,
    platform: 'zalo',
    name: account.name,
    identifier: account.zaloId,
    displayName: account.displayName,
    profilePicture: account.profilePicture,
    status: account.status,
    unreadCount: account.unreadMessages,
    totalItems: account.followerCount,
    lastActivity: account.lastActivity,
    metadata: account,
  });

  private convertEmailsToConversations(emails: Email[]): UnifiedConversation[] {
    const conversationMap = new Map<string, Email[]>();

    // Group emails by sender
    emails.forEach(email => {
      const key = email.from.email;
      if (!conversationMap.has(key)) {
        conversationMap.set(key, []);
      }
      conversationMap.get(key)!.push(email);
    });

    // Convert to conversations
    return Array.from(conversationMap.entries()).map(([senderEmail, emails]) => {
      const latestEmail = emails[emails.length - 1];
      
      return {
        id: `email_conv_${senderEmail}`,
        platform: 'email' as Platform,
        accountId: latestEmail.accountId,
        participantId: senderEmail,
        participantName: latestEmail.from.name,
        participantIdentifier: senderEmail,
        lastMessage: {
          id: latestEmail.id,
          platform: 'email' as Platform,
          conversationId: `email_conv_${senderEmail}`,
          senderId: senderEmail,
          senderName: latestEmail.from.name,
          senderIdentifier: senderEmail,
          type: 'text' as const,
          content: latestEmail.snippet,
          timestamp: latestEmail.timestamp,
          status: 'delivered' as const,
          attachments: latestEmail.attachments.map(att => ({
            id: att.id,
            type: 'file' as const,
            url: att.url || '',
            filename: att.filename,
            size: att.size,
            metadata: att,
          })),
          mentions: [],
          reactions: [],
          isFromBusiness: !latestEmail.isFromCustomer,
          metadata: latestEmail,
        },
        unreadCount: emails.filter(e => !e.isRead).length,
        status: 'active' as const,
        priority: latestEmail.priority,
        tags: latestEmail.labels,
        assignedTo: latestEmail.assignedTo,
        createdAt: emails[0].timestamp,
        updatedAt: latestEmail.timestamp,
        metadata: { emails },
      };
    });
  }

  private convertFacebookConversation = (conv: FacebookConversation): UnifiedConversation => ({
    id: conv.id,
    platform: 'facebook',
    accountId: conv.pageId,
    participantId: conv.participantId,
    participantName: conv.participantName,
    participantIdentifier: conv.participantId,
    participantPicture: conv.participantPicture,
    lastMessage: {
      id: conv.lastMessage.id,
      platform: 'facebook',
      conversationId: conv.id,
      senderId: conv.lastMessage.senderId,
      senderName: conv.lastMessage.senderName,
      senderIdentifier: conv.lastMessage.senderId,
      type: conv.lastMessage.type,
      content: conv.lastMessage.content,
      timestamp: conv.lastMessage.timestamp,
      status: conv.lastMessage.status,
      attachments: conv.lastMessage.attachments.map(att => ({
        id: att.id,
        type: att.type,
        url: att.url,
        filename: att.name,
        size: att.size,
        metadata: att,
      })),
      mentions: conv.lastMessage.mentions,
      reactions: conv.lastMessage.reactions.map(r => ({
        type: r.type,
        count: r.count,
        users: r.users,
        userReacted: r.userReacted,
      })),
      isFromBusiness: conv.lastMessage.isFromPage,
      metadata: conv.lastMessage,
    },
    unreadCount: conv.unreadCount,
    status: conv.status,
    priority: conv.priority,
    tags: conv.tags,
    assignedTo: conv.assignedTo,
    createdAt: conv.createdAt,
    updatedAt: conv.updatedAt,
    metadata: conv,
  });

  private convertInstagramConversation = (conv: InstagramConversation): UnifiedConversation => ({
    id: conv.id,
    platform: 'instagram',
    accountId: conv.accountId,
    participantId: conv.participantId,
    participantName: conv.participantName,
    participantIdentifier: conv.participantUsername,
    participantPicture: conv.participantPicture,
    lastMessage: {
      id: conv.lastMessage.id,
      platform: 'instagram',
      conversationId: conv.id,
      senderId: conv.lastMessage.senderId,
      senderName: conv.lastMessage.senderName,
      senderIdentifier: conv.lastMessage.senderUsername,
      type: conv.lastMessage.type,
      content: conv.lastMessage.content,
      timestamp: conv.lastMessage.timestamp,
      status: conv.lastMessage.status,
      attachments: conv.lastMessage.attachments.map(att => ({
        id: att.id,
        type: att.type,
        url: att.url,
        size: att.size,
        duration: att.duration,
        metadata: att,
      })),
      mentions: conv.lastMessage.mentions,
      reactions: conv.lastMessage.reactions.map(r => ({
        type: r.type,
        count: r.count,
        users: r.users,
        userReacted: r.userReacted,
      })),
      isFromBusiness: conv.lastMessage.isFromBusiness,
      metadata: conv.lastMessage,
    },
    unreadCount: conv.unreadCount,
    status: conv.status,
    priority: conv.priority,
    tags: conv.tags,
    assignedTo: conv.assignedTo,
    createdAt: conv.createdAt,
    updatedAt: conv.updatedAt,
    metadata: conv,
  });

  // Enhanced email account methods
  async addEmailAccount(account: EmailAccount): Promise<void> {
    await enhancedEmailService.addImapAccount(account);
  }

  async removeEmailAccount(accountId: string): Promise<void> {
    await enhancedEmailService.removeImapAccount(accountId);
  }

  async syncEmailAccount(accountId: string): Promise<void> {
    await enhancedEmailService.syncAccount(accountId);
  }

  async syncAllEmailAccounts(): Promise<void> {
    await enhancedEmailService.syncAllAccounts();
  }

  async testEmailConnection(account: EmailAccount): Promise<boolean> {
    return await enhancedEmailService.testImapConnection(account);
  }

  async getEmailFolders(accountId: string): Promise<string[]> {
    return await enhancedEmailService.getImapFolders(accountId);
  }

  async getEmailUnreadCount(accountId: string, folder: string = 'INBOX'): Promise<number> {
    return await enhancedEmailService.getUnreadCount(accountId, folder);
  }

  async getEmailMessages(accountId: string, folder: string = 'INBOX', limit: number = 20): Promise<Email[]> {
    return await enhancedEmailService.getMessages(accountId, limit, folder);
  }

  // Enhanced statistics with multiple email accounts
  async getEnhancedStats(): Promise<OneTalkStats> {
    const accounts = await this.getAllAccounts();
    const conversations = await this.getAllConversations();
    
    // Get detailed email statistics
    const emailAccounts = accounts.filter(acc => acc.platform === 'email');
    const emailStats = {
      totalAccounts: emailAccounts.length,
      connectedAccounts: emailAccounts.filter(acc => acc.status === 'connected').length,
      totalUnread: emailAccounts.reduce((sum, acc) => sum + acc.unreadCount, 0),
      accountBreakdown: emailAccounts.map(acc => ({
        id: acc.id,
        name: acc.name,
        email: acc.identifier,
        unreadCount: acc.unreadCount,
        status: acc.status,
        lastActivity: acc.lastActivity,
      })),
    };

    const stats: OneTalkStats = {
      totalAccounts: accounts.length,
      connectedAccounts: accounts.filter(acc => acc.status === 'connected').length,
      totalUnread: accounts.reduce((sum, acc) => sum + acc.unreadCount, 0),
      totalConversations: conversations.length,
      activeConversations: conversations.filter(conv => conv.status === 'active').length,
      avgResponseTime: await this.calculateAverageResponseTime(conversations),
      platformStats: {
        email: {
          accounts: emailStats.totalAccounts,
          unread: emailStats.totalUnread,
          conversations: conversations.filter(conv => conv.platform === 'email').length,
          status: emailStats.connectedAccounts > 0 ? 'healthy' : 'warning',
          details: emailStats,
        },
        facebook: {
          accounts: accounts.filter(acc => acc.platform === 'facebook').length,
          unread: accounts.filter(acc => acc.platform === 'facebook').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'facebook').length,
          status: 'healthy',
        },
        instagram: {
          accounts: accounts.filter(acc => acc.platform === 'instagram').length,
          unread: accounts.filter(acc => acc.platform === 'instagram').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'instagram').length,
          status: 'healthy',
        },
        whatsapp: {
          accounts: accounts.filter(acc => acc.platform === 'whatsapp').length,
          unread: accounts.filter(acc => acc.platform === 'whatsapp').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'whatsapp').length,
          status: 'healthy',
        },
        zalo: {
          accounts: accounts.filter(acc => acc.platform === 'zalo').length,
          unread: accounts.filter(acc => acc.platform === 'zalo').reduce((sum, acc) => sum + acc.unreadCount, 0),
          conversations: conversations.filter(conv => conv.platform === 'zalo').length,
          status: 'healthy',
        },
      },
    };

    return stats;
  }

  private async calculateAverageResponseTime(conversations: UnifiedConversation[]): Promise<number> {
    // Mock calculation - in production, this would analyze actual response times
    const emailConversations = conversations.filter(conv => conv.platform === 'email');
    const socialConversations = conversations.filter(conv => ['facebook', 'instagram', 'whatsapp', 'zalo'].includes(conv.platform));
    
    // Email typically has longer response times
    const emailAvg = emailConversations.length > 0 ? 45 : 0; // 45 minutes
    const socialAvg = socialConversations.length > 0 ? 8 : 0; // 8 minutes
    
    if (emailConversations.length + socialConversations.length === 0) {
      return 0;
    }
    
    return Math.round(
      (emailAvg * emailConversations.length + socialAvg * socialConversations.length) / 
      (emailConversations.length + socialConversations.length)
    );
  }

  // Platform-specific message sending with enhanced features
  async sendEnhancedMessage(
    platform: Platform,
    accountId: string,
    recipientId: string,
    content: string,
    options?: {
      type?: 'text' | 'image' | 'template' | 'quick_reply' | 'button';
      subject?: string;
      attachments?: string[];
      quickReplies?: Array<{ title: string; payload: string }>;
      buttons?: Array<{ title: string; type: string; payload?: string; url?: string }>;
      template?: any;
      metadata?: any;
    }
  ): Promise<void> {
    const { type = 'text', subject, attachments, quickReplies, buttons, template, metadata } = options || {};
    
    switch (platform) {
      case 'email':
        await this.sendMessage(platform, accountId, recipientId, content, type, { subject, ...metadata });
        break;
      
      case 'facebook':
        if (type === 'quick_reply' && quickReplies) {
          await facebookService.sendQuickReply(accountId, recipientId, content, quickReplies, metadata?.pageAccessToken);
        } else if (type === 'button' && buttons) {
          await facebookService.sendButtonTemplate(accountId, recipientId, content, buttons, metadata?.pageAccessToken);
        } else {
          await this.sendMessage(platform, accountId, recipientId, content, type, metadata);
        }
        break;
      
      case 'zalo':
        if (type === 'quick_reply' && quickReplies) {
          await zaloService.sendQuickReplyMessage(recipientId, content, quickReplies);
        } else if (template) {
          await zaloService.sendInteractiveMessage(recipientId, template.type, template.elements);
        } else {
          await this.sendMessage(platform, accountId, recipientId, content, type, metadata);
        }
        break;
      
      default:
        await this.sendMessage(platform, accountId, recipientId, content, type, metadata);
    }
  }

  // Bulk operations
  async broadcastMessage(
    platforms: Platform[],
    accountIds: string[],
    recipientIds: string[],
    content: string,
    options?: any
  ): Promise<{ success: number; failed: number; errors: string[] }> {
    const results = { success: 0, failed: 0, errors: [] as string[] };
    
    for (const platform of platforms) {
      for (const accountId of accountIds) {
        for (const recipientId of recipientIds) {
          try {
            await this.sendEnhancedMessage(platform, accountId, recipientId, content, options);
            results.success++;
          } catch (error) {
            results.failed++;
            results.errors.push(`${platform}:${accountId}:${recipientId} - ${error instanceof Error ? error.message : String(error)}`);
          }
        }
      }
    }
    
    return results;
  }

  // Search and filtering
  async searchConversations(query: string, platform?: Platform): Promise<UnifiedConversation[]> {
    const conversations = await this.getAllConversations(platform);
    
    return conversations.filter(conv => 
      conv.participantName.toLowerCase().includes(query.toLowerCase()) ||
      conv.participantIdentifier.toLowerCase().includes(query.toLowerCase()) ||
      conv.lastMessage.content.toLowerCase().includes(query.toLowerCase()) ||
      conv.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );
  }

  async getConversationsByPriority(priority: 'low' | 'medium' | 'high' | 'urgent', platform?: Platform): Promise<UnifiedConversation[]> {
    const conversations = await this.getAllConversations(platform);
    return conversations.filter(conv => conv.priority === priority);
  }

  async getUnreadConversations(platform?: Platform): Promise<UnifiedConversation[]> {
    const conversations = await this.getAllConversations(platform);
    return conversations.filter(conv => conv.unreadCount > 0);
  }
}

export const oneTalkService = new OneTalkService();