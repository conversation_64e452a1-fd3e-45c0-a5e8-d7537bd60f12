import { 
  Certification, 
  CertificationCreate, 
  CertificationUpdate, 
  CertificationType, 
  CertificationStatus, 
  CertificationScope 
} from '../../domain/models/Certification';
import { CertificationRepository } from '../../domain/repositories/CertificationRepository';

/**
 * Service layer for certification operations
 * Uses repository to access data but knows nothing about storage details
 */
export class CertificationService {
  constructor(private certificationRepository: CertificationRepository) {}

  async getCertificationById(id: string): Promise<Certification | null> {
    return this.certificationRepository.getCertificationById(id);
  }

  async getCertificationByCertificationId(certificationId: string): Promise<Certification | null> {
    return this.certificationRepository.getCertificationByCertificationId(certificationId);
  }

  async getAllCertifications(): Promise<Certification[]> {
    return this.certificationRepository.getAllCertifications();
  }

  async getActiveCertifications(): Promise<Certification[]> {
    return this.certificationRepository.getActiveCertifications();
  }

  async getExpiredCertifications(): Promise<Certification[]> {
    return this.certificationRepository.getExpiredCertifications();
  }

  async getCertificationsByType(type: CertificationType): Promise<Certification[]> {
    return this.certificationRepository.getCertificationsByType(type);
  }

  async createCertification(certificationData: CertificationCreate): Promise<Certification> {
    // Additional business logic can be added here before creation
    return this.certificationRepository.createCertification(certificationData);
  }

  async updateCertification(id: string, updates: Partial<Omit<Certification, 'id'>>): Promise<Certification | null> {
    const certification = await this.certificationRepository.getCertificationById(id);
    if (!certification) return null;
    
    return this.certificationRepository.updateCertification({
      id,
      ...updates
    });
  }

  async deleteCertification(id: string): Promise<boolean> {
    return this.certificationRepository.deleteCertification(id);
  }

  async getCertificationsForEntity(entityType: string, entityId: string): Promise<Certification[]> {
    return this.certificationRepository.getCertificationsForEntity(entityType, entityId);
  }

  async getCertificationsByStatus(status: CertificationStatus): Promise<Certification[]> {
    return this.certificationRepository.getCertificationsByStatus(status);
  }

  async getCertificationsByScope(scope: CertificationScope): Promise<Certification[]> {
    return this.certificationRepository.getCertificationsByScope(scope);
  }

  async getCertificationsExpiringWithin(days: number): Promise<Certification[]> {
    return this.certificationRepository.getCertificationsExpiringWithin(days);
  }

  async renewCertification(id: string, newExpiryDate: Date): Promise<Certification | null> {
    const certification = await this.certificationRepository.getCertificationById(id);
    if (!certification) return null;

    return this.updateCertification(id, {
      expiryDate: newExpiryDate,
      status: CertificationStatus.ACTIVE
    });
  }

  async suspendCertification(id: string, reason: string): Promise<Certification | null> {
    const certification = await this.certificationRepository.getCertificationById(id);
    if (!certification) return null;

    return this.updateCertification(id, {
      status: CertificationStatus.SUSPENDED,
      notes: `${certification.notes || ''}\nSuspended: ${reason} (${new Date().toISOString()})`
    });
  }

  async revokeCertification(id: string, reason: string): Promise<Certification | null> {
    const certification = await this.certificationRepository.getCertificationById(id);
    if (!certification) return null;

    return this.updateCertification(id, {
      status: CertificationStatus.REVOKED,
      notes: `${certification.notes || ''}\nRevoked: ${reason} (${new Date().toISOString()})`
    });
  }

  async addAuditRecord(certificationId: string, auditData: {
    auditDate: Date;
    auditor: string;
    findings: string;
    recommendations?: string;
    complianceScore?: number;
  }): Promise<Certification | null> {
    const certification = await this.certificationRepository.getCertificationById(certificationId);
    if (!certification) return null;

    const newAuditRecord = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...auditData
    };

    const updatedAuditRecords = [...certification.auditRecords, newAuditRecord];
    
    return this.updateCertification(certificationId, {
      auditRecords: updatedAuditRecords
    });
  }

  async getCertificationSummary(): Promise<{
    total: number;
    active: number;
    expired: number;
    suspended: number;
    revoked: number;
    expiringWithin30Days: number;
    byType: Record<CertificationType, number>;
    byScope: Record<CertificationScope, number>;
  }> {
    const allCertifications = await this.getAllCertifications();
    const expiringWithin30Days = await this.getCertificationsExpiringWithin(30);
    
    const summary = {
      total: allCertifications.length,
      active: 0,
      expired: 0,
      suspended: 0,
      revoked: 0,
      expiringWithin30Days: expiringWithin30Days.length,
      byType: {} as Record<CertificationType, number>,
      byScope: {} as Record<CertificationScope, number>
    };

    allCertifications.forEach(cert => {
      // Count by status
      switch (cert.status) {
        case CertificationStatus.ACTIVE:
          summary.active++;
          break;
        case CertificationStatus.EXPIRED:
          summary.expired++;
          break;
        case CertificationStatus.SUSPENDED:
          summary.suspended++;
          break;
        case CertificationStatus.REVOKED:
          summary.revoked++;
          break;
      }

      // Count by type
      summary.byType[cert.type] = (summary.byType[cert.type] || 0) + 1;
      
      // Count by scope
      summary.byScope[cert.scope] = (summary.byScope[cert.scope] || 0) + 1;
    });

    return summary;
  }

  async validateCertificationChain(entityType: string, entityId: string): Promise<{
    isValid: boolean;
    missingCertifications: CertificationType[];
    expiredCertifications: Certification[];
    warnings: string[];
  }> {
    const certifications = await this.getCertificationsForEntity(entityType, entityId);
    const activeCertifications = certifications.filter(cert => cert.status === CertificationStatus.ACTIVE);
    
    // Define required certifications based on entity type
    const requiredCertifications: Record<string, CertificationType[]> = {
      'farmer': [CertificationType.ORGANIC, CertificationType.FAIR_TRADE],
      'processor': [CertificationType.HACCP, CertificationType.ISO],
      'distributor': [CertificationType.COLD_CHAIN, CertificationType.TRANSPORT]
    };

    const required = requiredCertifications[entityType] || [];
    const activeCertTypes = activeCertifications.map(cert => cert.type);
    const missingCertifications = required.filter(type => !activeCertTypes.includes(type));
    
    const expiredCertifications = certifications.filter(cert => cert.status === CertificationStatus.EXPIRED);
    
    const warnings: string[] = [];
    const expiringWithin30Days = await this.getCertificationsExpiringWithin(30);
    const entityExpiringCerts = expiringWithin30Days.filter(cert => 
      cert.entityType === entityType && cert.entityId === entityId
    );
    
    if (entityExpiringCerts.length > 0) {
      warnings.push(`${entityExpiringCerts.length} certification(s) expiring within 30 days`);
    }

    return {
      isValid: missingCertifications.length === 0 && expiredCertifications.length === 0,
      missingCertifications,
      expiredCertifications,
      warnings
    };
  }
}
