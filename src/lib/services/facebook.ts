import axios from 'axios';

export interface FacebookPage {
  id: string;
  name: string;
  handle: string;
  profilePicture: string;
  followers: number;
  isVerified: boolean;
  status: 'connected' | 'disconnected' | 'limited';
  unreadMessages: number;
  unreadComments: number;
  lastActivity: string;
  accessToken: string;
}

export interface FacebookConversation {
  id: string;
  type: 'message' | 'comment' | 'post_comment' | 'story_reply';
  pageId: string;
  participantId: string;
  participantName: string;
  participantPicture?: string;
  isVerified: boolean;
  lastMessage: FacebookMessage;
  unreadCount: number;
  status: 'active' | 'archived' | 'spam';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    postId?: string;
    storyId?: string;
    source: string;
  };
}

export interface FacebookMessage {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderPicture?: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'sticker' | 'reaction' | 'system';
  content: string;
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  attachments: FacebookAttachment[];
  reactions: FacebookReaction[];
  mentions: string[];
  isEdited: boolean;
  isDeleted: boolean;
  replyTo?: string;
  isFromPage: boolean;
  metadata: any;
}

export interface FacebookAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'file' | 'link';
  url: string;
  thumbnailUrl?: string;
  name?: string;
  size?: number;
  description?: string;
}

export interface FacebookReaction {
  type: 'like' | 'love' | 'haha' | 'wow' | 'sad' | 'angry';
  count: number;
  users: string[];
  userReacted: boolean;
}

export class FacebookService {
  private baseURL = 'https://graph.facebook.com/v23.0';
  private accessToken: string;
  private appId: string;
  private appSecret: string;

  constructor() {
    this.accessToken = process.env.FACEBOOK_ACCESS_TOKEN || '';
    this.appId = process.env.FACEBOOK_APP_ID || '';
    this.appSecret = process.env.FACEBOOK_APP_SECRET || '';
  }

  private async makeRequest(endpoint: string, method: 'GET' | 'POST' | 'DELETE' = 'GET', data?: any, accessToken?: string): Promise<any> {
    try {
      const token = accessToken || this.accessToken;
      const response = await axios({
        method,
        url: `${this.baseURL}${endpoint}`,
        params: method === 'GET' ? { access_token: token, ...data } : { access_token: token },
        data: method === 'POST' ? data : undefined,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 second timeout
      });

      // Check for Facebook API errors
      if (response.data.error) {
        throw new Error(`Facebook API Error (${response.data.error.code}): ${response.data.error.message}`);
      }

      return response.data;
    } catch (error: any) {
      console.error('Facebook API Error:', error.response?.data || error.message);
      
      // Handle specific Facebook error codes
      if (error.response?.data?.error?.code) {
        const errorCode = error.response.data.error.code;
        const errorMessage = error.response.data.error.message;
        
        switch (errorCode) {
          case 190:
            throw new Error('Facebook API: Invalid access token - Please refresh your token');
          case 230:
            throw new Error('Facebook API: User has not granted the required permissions');
          case 80006:
            throw new Error('Facebook API: Too many API calls - Rate limit exceeded');
          case 100:
            throw new Error('Facebook API: Invalid parameter - Check your request parameters');
          case 200:
            throw new Error('Facebook API: Permission denied - Check your app permissions');
          case 80001:
            throw new Error('Facebook API: Messaging feature is not available');
          default:
            throw new Error(`Facebook API Error (${errorCode}): ${errorMessage}`);
        }
      }
      
      throw new Error(`Facebook API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async getPages(): Promise<FacebookPage[]> {
    try {
      const response = await this.makeRequest('/me/accounts', 'GET', {
        fields: 'id,name,username,picture,fan_count,verification_status,access_token',
      });

      return response.data.map((page: any) => ({
        id: page.id,
        name: page.name,
        handle: `@${page.username || page.name.toLowerCase().replace(/\s+/g, '')}`,
        profilePicture: page.picture?.data?.url || '',
        followers: page.fan_count || 0,
        isVerified: page.verification_status === 'blue_verified',
        status: 'connected' as const,
        unreadMessages: 0, // Will be populated separately
        unreadComments: 0, // Will be populated separately
        lastActivity: new Date().toISOString(),
        accessToken: page.access_token,
      }));
    } catch (error) {
      console.error('Error fetching Facebook pages:', error);
      throw error;
    }
  }

  async getConversations(pageId: string, pageAccessToken: string, platform: 'messenger' | 'instagram' = 'messenger', folder: string = 'inbox'): Promise<FacebookConversation[]> {
    try {
      // Get page conversations from Messenger or Instagram
      const conversationsResponse = await this.makeRequest(`/${pageId}/conversations`, 'GET', {
        platform: platform.toUpperCase(),
        folder,
        fields: 'id,participants,updated_time,message_count,unread_count,messages{id,from,to,message,created_time,attachments}',
      }, pageAccessToken);

      const conversations: FacebookConversation[] = [];

      for (const conv of conversationsResponse.data || []) {
        // Get the latest message from the conversation
        const latestMessage = conv.messages?.data?.[0];
        if (!latestMessage) continue;

        // Get participant info (exclude the page itself)
        const participant = conv.participants?.data?.find((p: any) => p.id !== pageId);
        if (!participant) continue;

        let participantInfo;
        try {
          participantInfo = await this.getUserInfo(participant.id, pageAccessToken);
        } catch (error) {
          // If we can't get participant info, use basic info
          participantInfo = {
            name: participant.name || 'Unknown User',
            picture: null,
            verified: false,
          };
        }

        const conversation: FacebookConversation = {
          id: conv.id,
          type: 'message',
          pageId,
          participantId: participant.id,
          participantName: participantInfo.name || participant.name || 'Unknown User',
          participantPicture: participantInfo.picture?.data?.url,
          isVerified: participantInfo.verified || false,
          lastMessage: {
            id: latestMessage.id,
            conversationId: conv.id,
            senderId: latestMessage.from?.id || participant.id,
            senderName: latestMessage.from?.name || participant.name || 'Unknown User',
            type: this.getMessageType(latestMessage),
            content: latestMessage.message || this.getAttachmentContent(latestMessage.attachments),
            timestamp: latestMessage.created_time,
            status: 'delivered',
            attachments: this.parseAttachments(latestMessage.attachments?.data || []),
            reactions: [],
            mentions: this.extractMentions(latestMessage.message || ''),
            isEdited: false,
            isDeleted: false,
            isFromPage: latestMessage.from?.id === pageId,
            metadata: {
              platform,
              folder,
            },
          },
          unreadCount: conv.unread_count || 0,
          status: folder === 'spam' ? 'spam' : 'active',
          priority: this.calculatePriority(conv.unread_count || 0, latestMessage.created_time),
          tags: [platform, folder],
          createdAt: conv.updated_time,
          updatedAt: conv.updated_time,
          metadata: {
            source: platform,
            folder,
            messageCount: conv.message_count || 0,
          },
        };

        conversations.push(conversation);
      }

      return conversations;
    } catch (error) {
      console.error(`Error fetching Facebook ${platform} conversations:`, error);
      throw error;
    }
  }

  async getMessages(conversationId: string, pageAccessToken: string, limit: number = 20): Promise<FacebookMessage[]> {
    try {
      const response = await this.makeRequest(`/${conversationId}/messages`, 'GET', {
        limit,
        fields: 'id,from,to,message,created_time,attachments,reactions,tags',
      }, pageAccessToken);

      return (response.data || []).map((message: any) => ({
        id: message.id,
        conversationId,
        senderId: message.from?.id || 'unknown',
        senderName: message.from?.name || 'Unknown User',
        senderPicture: message.from?.picture?.data?.url,
        type: this.getMessageType(message),
        content: message.message || this.getAttachmentContent(message.attachments),
        timestamp: message.created_time,
        status: 'delivered' as const,
        attachments: this.parseAttachments(message.attachments?.data || []),
        reactions: this.parseReactions(message.reactions?.data || []),
        mentions: this.extractMentions(message.message || ''),
        isEdited: false,
        isDeleted: false,
        replyTo: message.reply_to?.id,
        isFromPage: message.from?.id === message.to?.data?.[0]?.id, // Check if sender is the page
        metadata: {
          tags: message.tags || [],
          messageType: message.type || 'text',
        },
      }));
    } catch (error) {
      console.error('Error fetching Facebook messages:', error);
      throw error;
    }
  }

  async sendMessage(pageId: string, recipientId: string, message: string, pageAccessToken: string, messageType: 'text' | 'image' | 'video' | 'audio' | 'file' = 'text', attachmentUrl?: string): Promise<any> {
    try {
      let payload: any = {
        recipient: { id: recipientId },
        messaging_type: 'RESPONSE', // For responding to user messages
      };

      // Build message payload based on type
      switch (messageType) {
        case 'text':
          payload.message = { text: message };
          break;
        
        case 'image':
          if (!attachmentUrl) {
            throw new Error('Image URL is required for image messages');
          }
          payload.message = {
            attachment: {
              type: 'image',
              payload: {
                url: attachmentUrl,
                is_reusable: true,
              },
            },
          };
          if (message) {
            payload.message.text = message;
          }
          break;
        
        case 'video':
          if (!attachmentUrl) {
            throw new Error('Video URL is required for video messages');
          }
          payload.message = {
            attachment: {
              type: 'video',
              payload: {
                url: attachmentUrl,
                is_reusable: true,
              },
            },
          };
          break;
        
        case 'audio':
          if (!attachmentUrl) {
            throw new Error('Audio URL is required for audio messages');
          }
          payload.message = {
            attachment: {
              type: 'audio',
              payload: {
                url: attachmentUrl,
                is_reusable: true,
              },
            },
          };
          break;
        
        case 'file':
          if (!attachmentUrl) {
            throw new Error('File URL is required for file messages');
          }
          payload.message = {
            attachment: {
              type: 'file',
              payload: {
                url: attachmentUrl,
                is_reusable: true,
              },
            },
          };
          break;
        
        default:
          throw new Error(`Unsupported message type: ${messageType}`);
      }

      const response = await this.makeRequest(`/${pageId}/messages`, 'POST', payload, pageAccessToken);
      
      if (response.message_id) {
        console.log('Facebook message sent successfully:', response.message_id);
        return response;
      } else {
        throw new Error('Failed to send Facebook message - no message ID returned');
      }
    } catch (error) {
      console.error('Error sending Facebook message:', error);
      throw error;
    }
  }

  async getPagePosts(pageId: string, pageAccessToken: string): Promise<any[]> {
    try {
      const response = await axios.get(`${this.baseURL}/${pageId}/posts`, {
        params: {
          access_token: pageAccessToken,
          fields: 'id,message,created_time,comments{id,from,message,created_time}',
          limit: 20,
        },
      });

      return response.data.data;
    } catch (error) {
      console.error('Error fetching Facebook posts:', error);
      throw error;
    }
  }

  async getPostComments(postId: string, pageAccessToken: string): Promise<any[]> {
    try {
      const response = await axios.get(`${this.baseURL}/${postId}/comments`, {
        params: {
          access_token: pageAccessToken,
          fields: 'id,from,message,created_time,parent',
        },
      });

      return response.data.data;
    } catch (error) {
      console.error('Error fetching post comments:', error);
      throw error;
    }
  }

  async replyToComment(commentId: string, message: string, pageAccessToken: string): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/${commentId}/comments`, {
        message,
        access_token: pageAccessToken,
      });
    } catch (error) {
      console.error('Error replying to Facebook comment:', error);
      throw error;
    }
  }

  private async getUserInfo(userId: string, pageAccessToken?: string): Promise<any> {
    try {
      return await this.makeRequest(`/${userId}`, 'GET', {
        fields: 'id,name,picture,verified,username,first_name,last_name,profile_pic',
      }, pageAccessToken);
    } catch (error) {
      console.error('Error fetching user info:', error);
      return { name: 'Unknown User', picture: null, verified: false };
    }
  }

  private parseAttachments(attachments: any[]): FacebookAttachment[] {
    if (!attachments || attachments.length === 0) return [];
    
    return attachments.map((att: any) => ({
      id: att.id || att.payload?.attachment_id || '',
      type: att.type || att.payload?.type || 'file',
      url: att.url || att.payload?.url || '',
      thumbnailUrl: att.payload?.thumbnail_url || att.payload?.preview_url,
      name: att.name || att.payload?.title || att.payload?.filename,
      size: att.payload?.size,
      description: att.payload?.description || att.payload?.caption,
    }));
  }

  private parseReactions(reactions: any[]): FacebookReaction[] {
    if (!reactions || reactions.length === 0) return [];
    
    const reactionMap = new Map<string, FacebookReaction>();

    reactions.forEach((reaction: any) => {
      const type = reaction.type?.toLowerCase() || 'like';
      if (reactionMap.has(type)) {
        const existing = reactionMap.get(type)!;
        existing.count++;
        existing.users.push(reaction.from?.id || 'unknown');
      } else {
        reactionMap.set(type, {
          type: type as any,
          count: 1,
          users: [reaction.from?.id || 'unknown'],
          userReacted: false, // Would need to check if current user reacted
        });
      }
    });

    return Array.from(reactionMap.values());
  }

  private getMessageType(message: any): string {
    if (message.attachments?.data?.length > 0) {
      const attachment = message.attachments.data[0];
      switch (attachment.type) {
        case 'image':
          return 'image';
        case 'video':
          return 'video';
        case 'audio':
          return 'audio';
        case 'file':
          return 'file';
        default:
          return 'file';
      }
    } else if (message.sticker_id) {
      return 'sticker';
    } else if (message.message) {
      return 'text';
    } else {
      return 'system';
    }
  }

  private getAttachmentContent(attachments: any): string {
    if (!attachments?.data?.length) return '';
    
    const attachment = attachments.data[0];
    switch (attachment.type) {
      case 'image':
        return 'Image';
      case 'video':
        return 'Video';
      case 'audio':
        return 'Audio';
      case 'file':
        return attachment.name || 'File';
      default:
        return 'Attachment';
    }
  }

  private extractMentions(text: string): string[] {
    const mentionRegex = /@\[(\d+):(.*?)\]/g;
    const mentions: string[] = [];
    let match;

    while ((match = mentionRegex.exec(text)) !== null) {
      mentions.push(match[1]); // User ID
    }

    return mentions;
  }

  private calculatePriority(unreadCount: number, lastMessageTime: string): 'low' | 'medium' | 'high' | 'urgent' {
    const now = new Date();
    const lastMessage = new Date(lastMessageTime);
    const hoursSinceLastMessage = (now.getTime() - lastMessage.getTime()) / (1000 * 60 * 60);

    if (unreadCount > 10) return 'urgent';
    if (unreadCount > 5) return 'high';
    if (hoursSinceLastMessage < 1) return 'high';
    if (hoursSinceLastMessage < 6) return 'medium';
    return 'low';
  }

  // Webhook verification for Facebook
  static verifyWebhook(mode: string, token: string, challenge: string): string | null {
    const verifyToken = process.env.FACEBOOK_VERIFY_TOKEN;
    
    if (mode === 'subscribe' && token === verifyToken) {
      return challenge;
    }
    
    return null;
  }

  // Process incoming webhook data
  static processWebhook(body: any): any {
    if (body.object === 'page') {
      body.entry.forEach((entry: any) => {
        // Process messaging events
        if (entry.messaging) {
          entry.messaging.forEach((event: any) => {
            if (event.message) {
              // Handle incoming message
              console.log('New Facebook message:', {
                senderId: event.sender.id,
                recipientId: event.recipient.id,
                messageText: event.message.text,
                messageId: event.message.mid,
                timestamp: event.timestamp,
                attachments: event.message.attachments,
              });
              // TODO: Save to database and trigger notifications
              // await saveFacebookMessage(event);
              // await notifyAssignedAgent(event);
            }

            if (event.postback) {
              // Handle postback (button clicks)
              console.log('Facebook postback received:', {
                senderId: event.sender.id,
                payload: event.postback.payload,
                title: event.postback.title,
                timestamp: event.timestamp,
              });
              // TODO: Handle postback actions
              // await handleFacebookPostback(event);
            }

            if (event.read) {
              // Handle message read receipts
              console.log('Facebook message read:', {
                senderId: event.sender.id,
                watermark: event.read.watermark,
                timestamp: event.timestamp,
              });
              // TODO: Update message read status
              // await markFacebookMessageAsRead(event);
            }

            if (event.delivery) {
              // Handle message delivery receipts
              console.log('Facebook message delivered:', {
                senderId: event.sender.id,
                watermark: event.delivery.watermark,
                timestamp: event.timestamp,
              });
              // TODO: Update message delivery status
              // await markFacebookMessageAsDelivered(event);
            }
          });
        }

        // Process feed events (posts, comments)
        if (entry.changes) {
          entry.changes.forEach((change: any) => {
            if (change.field === 'feed') {
              if (change.value.item === 'comment') {
                // Handle new comment
                console.log('New Facebook comment:', {
                  postId: change.value.post_id,
                  commentId: change.value.comment_id,
                  parentId: change.value.parent_id,
                  message: change.value.message,
                  from: change.value.from,
                  createdTime: change.value.created_time,
                });
                // TODO: Handle comment notifications
                // await handleFacebookComment(change.value);
              } else if (change.value.item === 'post') {
                // Handle new post
                console.log('New Facebook post:', change.value);
                // TODO: Handle post notifications
              }
            } else if (change.field === 'conversations') {
              // Handle conversation changes
              console.log('Facebook conversation change:', change.value);
              // TODO: Handle conversation updates
            }
          });
        }
      });
    }

    return { status: 'ok' };
  }

  // Advanced message sending methods
  async sendQuickReply(pageId: string, recipientId: string, text: string, quickReplies: any[], pageAccessToken: string): Promise<any> {
    try {
      const payload = {
        recipient: { id: recipientId },
        message: {
          text,
          quick_replies: quickReplies.map(reply => ({
            content_type: 'text',
            title: reply.title,
            payload: reply.payload,
            image_url: reply.image_url,
          })),
        },
      };

      return await this.makeRequest(`/${pageId}/messages`, 'POST', payload, pageAccessToken);
    } catch (error) {
      console.error('Error sending Facebook quick reply:', error);
      throw error;
    }
  }

  async sendButtonTemplate(pageId: string, recipientId: string, text: string, buttons: any[], pageAccessToken: string): Promise<any> {
    try {
      const payload = {
        recipient: { id: recipientId },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'button',
              text,
              buttons: buttons.map(btn => ({
                type: btn.type || 'postback',
                title: btn.title,
                payload: btn.payload,
                url: btn.url,
              })),
            },
          },
        },
      };

      return await this.makeRequest(`/${pageId}/messages`, 'POST', payload, pageAccessToken);
    } catch (error) {
      console.error('Error sending Facebook button template:', error);
      throw error;
    }
  }

  async sendGenericTemplate(pageId: string, recipientId: string, elements: any[], pageAccessToken: string): Promise<any> {
    try {
      const payload = {
        recipient: { id: recipientId },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'generic',
              elements: elements.map(elem => ({
                title: elem.title,
                subtitle: elem.subtitle,
                image_url: elem.image_url,
                default_action: elem.default_action,
                buttons: elem.buttons?.map((btn: any) => ({
                  type: btn.type || 'postback',
                  title: btn.title,
                  payload: btn.payload,
                  url: btn.url,
                })),
              })),
            },
          },
        },
      };

      return await this.makeRequest(`/${pageId}/messages`, 'POST', payload, pageAccessToken);
    } catch (error) {
      console.error('Error sending Facebook generic template:', error);
      throw error;
    }
  }

  async sendTypingAction(pageId: string, recipientId: string, action: 'typing_on' | 'typing_off' | 'mark_seen', pageAccessToken: string): Promise<any> {
    try {
      const payload = {
        recipient: { id: recipientId },
        sender_action: action,
      };

      return await this.makeRequest(`/${pageId}/messages`, 'POST', payload, pageAccessToken);
    } catch (error) {
      console.error('Error sending Facebook typing action:', error);
      throw error;
    }
  }

  // Message management methods
  async markConversationAsRead(conversationId: string, pageAccessToken: string): Promise<any> {
    try {
      const payload = {
        folder: 'inbox',
        read: true,
      };

      return await this.makeRequest(`/${conversationId}`, 'POST', payload, pageAccessToken);
    } catch (error) {
      console.error('Error marking Facebook conversation as read:', error);
      throw error;
    }
  }

  async moveConversationToFolder(conversationId: string, folder: string, pageAccessToken: string): Promise<any> {
    try {
      const payload = {
        folder,
      };

      return await this.makeRequest(`/${conversationId}`, 'POST', payload, pageAccessToken);
    } catch (error) {
      console.error('Error moving Facebook conversation:', error);
      throw error;
    }
  }

  async tagConversation(conversationId: string, tag: string, pageAccessToken: string): Promise<any> {
    try {
      const payload = {
        tag,
      };

      return await this.makeRequest(`/${conversationId}/tags`, 'POST', payload, pageAccessToken);
    } catch (error) {
      console.error('Error tagging Facebook conversation:', error);
      throw error;
    }
  }

  async untagConversation(conversationId: string, tag: string, pageAccessToken: string): Promise<any> {
    try {
      return await this.makeRequest(`/${conversationId}/tags`, 'DELETE', { tag }, pageAccessToken);
    } catch (error) {
      console.error('Error untagging Facebook conversation:', error);
      throw error;
    }
  }

  // Page management methods
  async getPageInfo(pageId: string, pageAccessToken: string): Promise<any> {
    try {
      return await this.makeRequest(`/${pageId}`, 'GET', {
        fields: 'id,name,username,picture,fan_count,verification_status,category,about,website,phone,location',
      }, pageAccessToken);
    } catch (error) {
      console.error('Error fetching Facebook page info:', error);
      throw error;
    }
  }

  async getPageInsights(pageId: string, pageAccessToken: string, metrics: string[] = ['page_impressions', 'page_engaged_users']): Promise<any> {
    try {
      return await this.makeRequest(`/${pageId}/insights`, 'GET', {
        metric: metrics.join(','),
        period: 'day',
      }, pageAccessToken);
    } catch (error) {
      console.error('Error fetching Facebook page insights:', error);
      throw error;
    }
  }

  // Conversation search and filtering
  async searchConversations(pageId: string, query: string, pageAccessToken: string): Promise<FacebookConversation[]> {
    try {
      const conversations = await this.getConversations(pageId, pageAccessToken);
      
      return conversations.filter(conv => 
        conv.participantName.toLowerCase().includes(query.toLowerCase()) ||
        conv.lastMessage.content.toLowerCase().includes(query.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching Facebook conversations:', error);
      throw error;
    }
  }

  async getConversationsByFolder(pageId: string, pageAccessToken: string, folder: 'inbox' | 'other' | 'spam' | 'page_done'): Promise<FacebookConversation[]> {
    try {
      return await this.getConversations(pageId, pageAccessToken, 'messenger', folder);
    } catch (error) {
      console.error(`Error fetching Facebook conversations from ${folder}:`, error);
      throw error;
    }
  }

  async getUnreadConversations(pageId: string, pageAccessToken: string): Promise<FacebookConversation[]> {
    try {
      const conversations = await this.getConversations(pageId, pageAccessToken);
      return conversations.filter(conv => conv.unreadCount > 0);
    } catch (error) {
      console.error('Error fetching unread Facebook conversations:', error);
      throw error;
    }
  }

  // Convert webhook messages to conversation format
  convertWebhookToMessage(webhookData: any): FacebookMessage | null {
    try {
      const message = webhookData.message;
      if (!message) return null;

      return {
        id: message.mid,
        conversationId: `fb_${webhookData.sender.id}`,
        senderId: webhookData.sender.id,
        senderName: 'User', // Would need separate API call for name
        type: this.getMessageType(message),
        content: message.text || this.getAttachmentContent(message.attachments),
        timestamp: new Date(webhookData.timestamp).toISOString(),
        status: 'delivered',
        attachments: this.parseAttachments(message.attachments || []),
        reactions: [],
        mentions: this.extractMentions(message.text || ''),
        isEdited: false,
        isDeleted: false,
        isFromPage: false,
        metadata: webhookData,
      };
    } catch (error) {
      console.error('Error converting Facebook webhook to message:', error);
      return null;
    }
  }

  // Webhook signature verification
  static verifyWebhookSignature(payload: string, signature: string): boolean {
    try {
      const appSecret = process.env.FACEBOOK_APP_SECRET;
      if (!appSecret) {
        console.error('Facebook app secret not configured');
        return false;
      }

      const crypto = require('crypto');
      const expectedSignature = crypto
        .createHmac('sha256', appSecret)
        .update(payload)
        .digest('hex');

      const signatureHash = signature.replace('sha256=', '');
      return crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(signatureHash, 'hex')
      );
    } catch (error) {
      console.error('Error verifying Facebook webhook signature:', error);
      return false;
    }
  }
}

export const facebookService = new FacebookService();