import { 
  FarmActivity, 
  FarmActivityCreate, 
  FarmActivityUpdate, 
  ActivityType, 
  ActivityStatus 
} from '../../domain/models/FarmActivity';
import { FarmActivityRepository } from '../../domain/repositories/FarmActivityRepository';

/**
 * Service layer for farm activity operations
 * Uses repository to access data but knows nothing about storage details
 */
export class FarmActivityService {
  constructor(private farmActivityRepository: FarmActivityRepository) {}

  async getActivityById(id: string): Promise<FarmActivity | null> {
    return this.farmActivityRepository.getActivityById(id);
  }

  async getActivityByActivityId(activityId: string): Promise<FarmActivity | null> {
    return this.farmActivityRepository.getActivityByActivityId(activityId);
  }

  async getAllActivities(): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getAllActivities();
  }

  async getActivitiesByFarmer(farmerId: string): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getActivitiesByFarmerId(farmerId);
  }

  async getActivitiesByLot(lotId: string): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getActivitiesByLotId(lotId);
  }
  
  async getActivitiesByType(type: ActivityType): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getActivitiesByType(type);
  }

  async createActivity(activityData: FarmActivityCreate): Promise<FarmActivity> {
    // Could add additional business logic here before creation
    return this.farmActivityRepository.createActivity(activityData);
  }

  async updateActivity(id: string, updates: Partial<Omit<FarmActivity, 'id'>>): Promise<FarmActivity | null> {
    const activity = await this.farmActivityRepository.getActivityById(id);
    if (!activity) return null;
    
    return this.farmActivityRepository.updateActivity({
      id,
      ...updates
    });
  }

  async deleteActivity(id: string): Promise<boolean> {
    return this.farmActivityRepository.deleteActivity(id);
  }

  async getActivitiesByStatus(status: ActivityStatus): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getActivitiesByStatus(status);
  }

  async getActivitiesByDateRange(startDate: Date, endDate: Date): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getActivitiesByDateRange(startDate, endDate);
  }

  async getActivitiesForFarmerByDateRange(farmerId: string, startDate: Date, endDate: Date): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getActivitiesForFarmerByDateRange(farmerId, startDate, endDate);
  }

  async getActivitiesForLotByDateRange(lotId: string, startDate: Date, endDate: Date): Promise<FarmActivity[]> {
    return this.farmActivityRepository.getActivitiesForLotByDateRange(lotId, startDate, endDate);
  }

  async updateActivityStatus(id: string, status: ActivityStatus): Promise<FarmActivity | null> {
    return this.updateActivity(id, { status });
  }

  async addInputToActivity(activityId: string, input: { name: string; quantity: number; unit: string; cost: number }): Promise<FarmActivity | null> {
    const activity = await this.farmActivityRepository.getActivityById(activityId);
    if (!activity) return null;

    const updatedInputs = [...activity.inputs, input];
    return this.updateActivity(activityId, { inputs: updatedInputs });
  }

  async addLaborToActivity(activityId: string, labor: { worker: string; hours: number; cost: number }): Promise<FarmActivity | null> {
    const activity = await this.farmActivityRepository.getActivityById(activityId);
    if (!activity) return null;

    const updatedLabor = [...activity.labor, labor];
    return this.updateActivity(activityId, { labor: updatedLabor });
  }

  async calculateTotalCost(activityId: string): Promise<number | null> {
    const activity = await this.farmActivityRepository.getActivityById(activityId);
    if (!activity) return null;

    const inputsCost = activity.inputs.reduce((total, input) => total + input.cost, 0);
    const laborCost = activity.labor.reduce((total, labor) => total + labor.cost, 0);
    
    return inputsCost + laborCost;
  }

  async getActivitiesSummaryByFarmer(farmerId: string): Promise<{
    totalActivities: number;
    activitiesByType: Record<ActivityType, number>;
    activitiesByStatus: Record<ActivityStatus, number>;
    totalCost: number;
  }> {
    const activities = await this.getActivitiesByFarmer(farmerId);
    
    const summary = {
      totalActivities: activities.length,
      activitiesByType: {} as Record<ActivityType, number>,
      activitiesByStatus: {} as Record<ActivityStatus, number>,
      totalCost: 0
    };

    activities.forEach(activity => {
      // Count by type
      summary.activitiesByType[activity.activityType] = 
        (summary.activitiesByType[activity.activityType] || 0) + 1;
      
      // Count by status
      summary.activitiesByStatus[activity.status] = 
        (summary.activitiesByStatus[activity.status] || 0) + 1;
      
      // Sum costs
      const inputsCost = activity.inputs.reduce((total, input) => total + input.cost, 0);
      const laborCost = activity.labor.reduce((total, labor) => total + labor.cost, 0);
      summary.totalCost += inputsCost + laborCost;
    });

    return summary;
  }

  async getUpcomingActivities(farmerId?: string): Promise<FarmActivity[]> {
    const now = new Date();
    const oneWeekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    if (farmerId) {
      return this.getActivitiesForFarmerByDateRange(farmerId, now, oneWeekFromNow);
    } else {
      return this.getActivitiesByDateRange(now, oneWeekFromNow);
    }
  }

  async getOverdueActivities(farmerId?: string): Promise<FarmActivity[]> {
    const activities = farmerId 
      ? await this.getActivitiesByFarmer(farmerId)
      : await this.getAllActivities();
    
    const now = new Date();
    return activities.filter(activity => 
      activity.status !== ActivityStatus.COMPLETED && 
      activity.endDate && 
      new Date(activity.endDate) < now
    );
  }
}
