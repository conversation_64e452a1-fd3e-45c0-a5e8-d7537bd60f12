import axios from 'axios';
import crypto from 'crypto';

export interface ZaloAccount {
  id: string;
  zaloId: string;
  name: string;
  displayName: string;
  profilePicture: string;
  isOfficialAccount: boolean;
  isVerified: boolean;
  followerCount: number;
  status: 'connected' | 'disconnected' | 'limited';
  unreadMessages: number;
  totalChats: number;
  lastActivity: string;
}

export interface ZaloConversation {
  id: string;
  accountId: string;
  userId: string;
  userName: string;
  userDisplayName: string;
  userAvatar?: string;
  isGroup: boolean;
  groupMembers?: number;
  lastMessage: ZaloMessage;
  unreadCount: number;
  status: 'active' | 'archived' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    source: string;
    businessContext?: string;
    userInfo?: {
      isFollowing: boolean;
      relationship: 'new' | 'returning' | 'vip';
    };
  };
}

export interface ZaloMessage {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderDisplayName: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'location' | 'sticker' | 'voice' | 'link' | 'business_card' | 'gif' | 'system';
  content: string;
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  attachments: ZaloAttachment[];
  sticker?: {
    id: string;
    package: string;
    url: string;
  };
  quotedMessage?: {
    id: string;
    content: string;
    senderName: string;
  };
  reactions: ZaloReaction[];
  isFromOA: boolean;
  metadata: any;
}

export interface ZaloAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'file' | 'voice' | 'location' | 'link';
  url: string;
  thumbnailUrl?: string;
  filename?: string;
  size?: number;
  duration?: number;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  link?: {
    title: string;
    description: string;
    image: string;
    domain: string;
  };
}

export interface ZaloReaction {
  type: 'like' | 'love' | 'haha' | 'wow' | 'sad' | 'angry';
  count: number;
  users: string[];
  userReacted: boolean;
}

export interface ZaloTemplate {
  id: string;
  name: string;
  type: 'text' | 'media' | 'list' | 'transaction';
  status: 'ENABLE' | 'DISABLE' | 'PENDING_REVIEW';
  language: 'vi' | 'en';
  content: any;
  createdAt: string;
  updatedAt: string;
}

export class ZaloService {
  private baseURL = 'https://openapi.zalo.me/v3.0/oa';
  private appId: string;
  private appSecret: string;
  private oaId: string;
  private accessToken: string;

  constructor() {
    this.appId = process.env.ZALO_APP_ID || '';
    this.appSecret = process.env.ZALO_APP_SECRET || '';
    this.oaId = process.env.ZALO_OA_ID || '';
    this.accessToken = process.env.ZALO_ACCESS_TOKEN || '';
  }

  private async makeRequest(endpoint: string, method: 'GET' | 'POST' = 'GET', data?: any): Promise<any> {
    try {
      const headers: any = {
        'Content-Type': 'application/json',
      };

      // Add access token to headers instead of params for better security
      if (this.accessToken) {
        headers['access_token'] = this.accessToken;
      }

      const response = await axios({
        method,
        url: `${this.baseURL}${endpoint}`,
        params: method === 'GET' ? data : undefined,
        data: method === 'POST' ? data : undefined,
        headers,
        timeout: 30000, // 30 second timeout
      });

      // Check for Zalo API error response
      if (response.data.error && response.data.error !== 0) {
        throw new Error(`Zalo API Error (${response.data.error}): ${response.data.message || 'Unknown error'}`);
      }

      return response.data;
    } catch (error: any) {
      console.error('Zalo API Error:', error.response?.data || error.message);
      
      // Handle different error types
      if (error.response?.status === 401) {
        throw new Error('Zalo API: Unauthorized - Check your access token');
      } else if (error.response?.status === 403) {
        throw new Error('Zalo API: Forbidden - Check your app permissions');
      } else if (error.response?.status === 429) {
        throw new Error('Zalo API: Rate limit exceeded - Please wait before retrying');
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Zalo API: Request timeout');
      }
      
      throw new Error(`Zalo API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  private generateMac(data: string): string {
    return crypto.createHmac('sha256', this.appSecret).update(data).digest('hex');
  }

  async getProfile(): Promise<ZaloAccount> {
    try {
      const response = await this.makeRequest('/getoa');
      const profile = response.data;

      return {
        id: this.oaId,
        zaloId: this.oaId,
        name: profile.name,
        displayName: profile.name,
        profilePicture: profile.avatar,
        isOfficialAccount: true,
        isVerified: profile.is_verified || false,
        followerCount: profile.num_follower || 0,
        status: 'connected',
        unreadMessages: 0, // Would need separate API call
        totalChats: 0, // Would need separate API call
        lastActivity: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching Zalo profile:', error);
      throw error;
    }
  }

  async sendTextMessage(userId: string, message: string): Promise<any> {
    try {
      const payload = {
        recipient: {
          user_id: userId,
        },
        message: {
          text: message,
        },
      };

      const response = await this.makeRequest('/message', 'POST', payload);
      
      if (response.data && response.data.message_id) {
        console.log('Zalo text message sent successfully:', response.data.message_id);
        return response.data;
      } else {
        throw new Error('Failed to send Zalo text message - no message ID returned');
      }
    } catch (error) {
      console.error('Error sending Zalo text message:', error);
      throw error;
    }
  }

  async sendImageMessage(userId: string, imageUrl: string, message?: string): Promise<any> {
    try {
      const attachmentId = await this.uploadAttachment('image', imageUrl);
      
      const payload = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'media',
              elements: [
                {
                  media_type: 'image',
                  attachment_id: attachmentId,
                  title: message || '',
                },
              ],
            },
          },
        },
      };

      return await this.makeRequest('/message', 'POST', payload);
    } catch (error) {
      console.error('Error sending Zalo image message:', error);
      throw error;
    }
  }

  async sendFileMessage(userId: string, fileUrl: string, filename: string): Promise<any> {
    try {
      const attachmentId = await this.uploadAttachment('file', fileUrl);
      
      const payload = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'file',
            payload: {
              attachment_id: attachmentId,
            },
          },
        },
      };

      return await this.makeRequest('/message', 'POST', payload);
    } catch (error) {
      console.error('Error sending Zalo file message:', error);
      throw error;
    }
  }

  async sendStickerMessage(userId: string, stickerId: string): Promise<any> {
    try {
      const payload = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'media',
              elements: [
                {
                  media_type: 'sticker',
                  attachment_id: stickerId,
                },
              ],
            },
          },
        },
      };

      return await this.makeRequest('/message', 'POST', payload);
    } catch (error) {
      console.error('Error sending Zalo sticker message:', error);
      throw error;
    }
  }

  async sendTemplateMessage(userId: string, templateId: string, templateData?: any): Promise<any> {
    try {
      const payload = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_id: templateId,
              template_data: templateData,
            },
          },
        },
      };

      return await this.makeRequest('/message', 'POST', payload);
    } catch (error) {
      console.error('Error sending Zalo template message:', error);
      throw error;
    }
  }

  async sendQuickReplyMessage(userId: string, message: string, quickReplies: any[]): Promise<any> {
    try {
      // Format quick replies for Zalo API
      const formattedQuickReplies = quickReplies.map(reply => ({
        content_type: 'text',
        title: reply.title || reply.text,
        payload: reply.payload || reply.title || reply.text,
        image_url: reply.image_url,
      }));

      const payload = {
        recipient: {
          user_id: userId,
        },
        message: {
          text: message,
          quick_replies: formattedQuickReplies,
        },
      };

      return await this.makeRequest('/message', 'POST', payload);
    } catch (error) {
      console.error('Lỗi khi gửi tin nhắn phản hồi nhanh Zalo:', error);
      throw error;
    }
  }

  async sendInteractiveMessage(userId: string, interactiveType: string, elements: any[]): Promise<any> {
    try {
      // Format elements for Vietnamese audience
      const formattedElements = elements.map(element => ({
        ...element,
        title: element.title || element.text,
        subtitle: element.subtitle || element.description,
        image_url: element.image_url || element.imageUrl,
        buttons: element.buttons?.map((btn: any) => ({
          type: btn.type || 'postback',
          title: btn.title || btn.text,
          payload: btn.payload || btn.value,
          url: btn.url,
        })),
      }));

      const payload = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: interactiveType,
              elements: formattedElements,
            },
          },
        },
      };

      return await this.makeRequest('/message', 'POST', payload);
    } catch (error) {
      console.error('Lỗi khi gửi tin nhắn tương tác Zalo:', error);
      throw error;
    }
  }

  async uploadAttachment(type: 'image' | 'file', url: string): Promise<string> {
    try {
      const payload = {
        url,
        type,
      };

      const endpoint = type === 'image' ? '/upload/image' : '/upload/file';
      const response = await this.makeRequest(endpoint, 'POST', payload);
      
      if (response.data && response.data.attachment_id) {
        return response.data.attachment_id;
      } else {
        throw new Error(`Failed to upload Zalo ${type} - no attachment ID returned`);
      }
    } catch (error) {
      console.error('Error uploading Zalo attachment:', error);
      throw error;
    }
  }

  async uploadLocalFile(filePath: string, type: 'image' | 'file'): Promise<string> {
    try {
      const FormData = require('form-data');
      const fs = require('fs');
      
      const form = new FormData();
      form.append('file', fs.createReadStream(filePath));
      form.append('type', type);

      const response = await axios({
        method: 'POST',
        url: `${this.baseURL}/upload/${type}`,
        data: form,
        headers: {
          ...form.getHeaders(),
          'access_token': this.accessToken,
        },
        timeout: 60000, // 60 second timeout for file uploads
      });

      if (response.data && response.data.data && response.data.data.attachment_id) {
        return response.data.data.attachment_id;
      } else {
        throw new Error(`Failed to upload local ${type} - no attachment ID returned`);
      }
    } catch (error) {
      console.error('Error uploading local Zalo file:', error);
      throw error;
    }
  }

  async getUserProfile(userId: string): Promise<any> {
    try {
      const response = await this.makeRequest('/getprofile', 'GET', {
        data: JSON.stringify({ user_id: userId }),
      });
      
      // Format the response for Vietnamese context
      if (response.data) {
        return {
          id: response.data.user_id,
          name: response.data.display_name || response.data.name || 'Người dùng',
          displayName: response.data.display_name || response.data.name || 'Người dùng',
          avatar: response.data.avatar || '',
          isFollowing: response.data.is_follower || false,
          gender: response.data.gender || 'unknown',
          birthDate: response.data.birth_date || null,
          sharedInfo: response.data.shared_info || {},
          tags: response.data.tags || [],
          notes: response.data.notes || '',
          lastInteraction: response.data.last_interaction_time || null,
        };
      }
      
      return response;
    } catch (error) {
      console.error('Lỗi khi lấy thông tin người dùng Zalo:', error);
      throw error;
    }
  }

  async getConversation(userId: string, offset: number = 0, count: number = 20): Promise<ZaloMessage[]> {
    try {
      const response = await this.makeRequest('/conversation', 'GET', {
        data: JSON.stringify({
          user_id: userId,
          offset,
          count,
        }),
      });

      return response.data.map((msg: any) => this.convertToZaloMessage(msg, userId));
    } catch (error) {
      console.error('Error fetching Zalo conversation:', error);
      throw error;
    }
  }

  async getTemplates(): Promise<ZaloTemplate[]> {
    try {
      const response = await this.makeRequest('/template');
      
      return response.data?.map((template: any) => ({
        id: template.template_id,
        name: template.template_name,
        type: template.template_type,
        status: template.status,
        language: template.language || 'vi',
        content: template.template_data,
        createdAt: template.created_time,
        updatedAt: template.updated_time,
        // Add Vietnamese-specific fields
        description: template.description || '',
        category: template.category || 'general',
        isPublic: template.is_public || false,
        usageCount: template.usage_count || 0,
        approvalStatus: template.approval_status || 'pending',
      })) || [];
    } catch (error) {
      console.error('Lỗi khi lấy danh sách template Zalo:', error);
      return [];
    }
  }

  async createTemplate(template: Partial<ZaloTemplate>): Promise<any> {
    try {
      const payload = {
        template_name: template.name,
        template_type: template.type || 'text',
        language: template.language || 'vi',
        template_data: template.content,
        // Add Vietnamese-specific fields
        description: template.description || '',
        category: template.category || 'general',
        tags: template.tags || [],
      };

      // Validate template content for Vietnamese
      if (template.language === 'vi' && template.content) {
        // Add Vietnamese text validation if needed
        console.log('Tạo template tiếng Việt:', template.name);
      }

      return await this.makeRequest('/template', 'POST', payload);
    } catch (error) {
      console.error('Lỗi khi tạo template Zalo:', error);
      throw error;
    }
  }

  async getFollowers(offset: number = 0, count: number = 50): Promise<any[]> {
    try {
      const response = await this.makeRequest('/getfollowers', 'GET', {
        data: JSON.stringify({ offset, count }),
      });

      // Format followers data for Vietnamese context
      const followers = response.data.followers || [];
      return followers.map((follower: any) => ({
        id: follower.user_id,
        name: follower.display_name || follower.name || 'Người dùng',
        avatar: follower.avatar || '',
        isFollowing: true, // They are followers
        followTime: follower.follow_time || null,
        lastInteraction: follower.last_interaction_time || null,
        tags: follower.tags || [],
        notes: follower.notes || '',
        location: follower.location || null,
        gender: follower.gender || 'unknown',
        birthDate: follower.birth_date || null,
      }));
    } catch (error) {
      console.error('Lỗi khi lấy danh sách người theo dõi Zalo:', error);
      return [];
    }
  }

  async broadcastMessage(message: string, userIds?: string[], scheduleTime?: string): Promise<any> {
    try {
      const payload: any = {
        recipient: userIds ? { user_ids: userIds } : { target: 'all' },
        message: {
          text: message,
        },
      };

      // Add scheduling if provided
      if (scheduleTime) {
        payload.schedule_time = scheduleTime;
      }

      // Add Vietnamese language hint
      payload.language = 'vi';

      return await this.makeRequest('/broadcast', 'POST', payload);
    } catch (error) {
      console.error('Lỗi khi phát sóng tin nhắn Zalo:', error);
      throw error;
    }
  }

  async tagUser(userId: string, tagName: string, tagColor?: string): Promise<any> {
    try {
      const payload: any = {
        user_id: userId,
        tag_name: tagName,
      };

      // Add color if provided
      if (tagColor) {
        payload.tag_color = tagColor;
      }

      // Add Vietnamese tag suggestions
      const vietnameseTags = {
        'khách_hàng': 'Khách hàng',
        'tiềm_năng': 'Tiềm năng',
        'quan_tâm': 'Quan tâm',
        'thành_viên': 'Thành viên',
        'vip': 'VIP',
        'hỗ_trợ': 'Hỗ trợ',
        'khiếu_nại': 'Khiếu nại',
        'đã_mua': 'Đã mua',
      };

      console.log(`Đánh tag "${tagName}" cho người dùng ${userId}`);
      return await this.makeRequest('/tag', 'POST', payload);
    } catch (error) {
      console.error('Lỗi khi đánh tag người dùng Zalo:', error);
      throw error;
    }
  }

  async removeUserTag(userId: string, tagName: string): Promise<any> {
    try {
      const payload = {
        user_id: userId,
        tag_name: tagName,
      };

      console.log(`Gỡ tag "${tagName}" khỏi người dùng ${userId}`);
      return await this.makeRequest('/untag', 'POST', payload);
    } catch (error) {
      console.error('Lỗi khi gỡ tag người dùng Zalo:', error);
      throw error;
    }
  }

  private convertToZaloMessage(msg: any, conversationId: string): ZaloMessage {
    return {
      id: msg.message_id || msg.msg_id,
      conversationId,
      senderId: msg.from_id || msg.user_id,
      senderName: msg.from_display_name || msg.display_name || 'Unknown',
      senderDisplayName: msg.from_display_name || msg.display_name || 'Unknown',
      type: msg.message?.attachment?.type === 'image' ? 'image' : 
            msg.message?.attachment?.type === 'file' ? 'file' :
            msg.message?.attachment?.type === 'sticker' ? 'sticker' : 'text',
      content: msg.message?.text || msg.message?.attachment?.payload?.url || 'Attachment',
      timestamp: new Date(msg.time * 1000).toISOString(),
      status: 'delivered',
      attachments: this.parseZaloAttachments(msg.message?.attachment),
      sticker: msg.message?.attachment?.type === 'sticker' ? {
        id: msg.message.attachment.payload.id,
        package: msg.message.attachment.payload.package || 'default',
        url: msg.message.attachment.payload.url,
      } : undefined,
      reactions: [],
      isFromOA: msg.from_id === this.oaId,
      metadata: msg,
    };
  }

  private parseZaloAttachments(attachment: any): ZaloAttachment[] {
    if (!attachment) return [];

    const attachments: ZaloAttachment[] = [];

    if (attachment.type === 'image') {
      attachments.push({
        id: attachment.payload.id || '',
        type: 'image',
        url: attachment.payload.url,
        thumbnailUrl: attachment.payload.thumbnail,
      });
    } else if (attachment.type === 'file') {
      attachments.push({
        id: attachment.payload.id || '',
        type: 'file',
        url: attachment.payload.url,
        filename: attachment.payload.name,
        size: attachment.payload.size,
      });
    } else if (attachment.type === 'location') {
      attachments.push({
        id: 'location',
        type: 'location',
        url: '',
        location: {
          latitude: attachment.payload.coordinates?.latitude,
          longitude: attachment.payload.coordinates?.longitude,
          address: attachment.payload.address,
        },
      });
    }

    return attachments;
  }

  // Convert webhook messages to conversation format
  convertWebhookToMessage(webhookData: any): ZaloMessage | null {
    try {
      const message = webhookData.message;
      if (!message) return null;

      return {
        id: message.msg_id,
        conversationId: `zalo_${webhookData.sender.id}`,
        senderId: webhookData.sender.id,
        senderName: webhookData.sender.name || 'Người dùng',
        senderDisplayName: webhookData.sender.display_name || webhookData.sender.name || 'Người dùng',
        type: this.getZaloMessageType(message),
        content: this.extractZaloMessageContent(message),
        timestamp: new Date(webhookData.timestamp).toISOString(),
        status: 'delivered',
        attachments: this.parseZaloAttachments(message.attachment),
        reactions: [],
        isFromOA: false,
        metadata: webhookData,
      };
    } catch (error) {
      console.error('Lỗi khi chuyển đổi webhook Zalo sang tin nhắn:', error);
      return null;
    }
  }

  private getZaloMessageType(message: any): string {
    if (message.text) return 'text';
    if (message.attachment?.type === 'image') return 'image';
    if (message.attachment?.type === 'file') return 'file';
    if (message.attachment?.type === 'sticker') return 'sticker';
    if (message.attachment?.type === 'location') return 'location';
    return 'text';
  }

  private extractZaloMessageContent(message: any): string {
    if (message.text) return message.text;
    if (message.attachment?.type === 'image') return 'Hình ảnh';
    if (message.attachment?.type === 'file') return message.attachment.payload?.name || 'Tệp đính kèm';
    if (message.attachment?.type === 'sticker') return 'Sticker';
    if (message.attachment?.type === 'location') return 'Vị trí';
    if (message.attachment?.type === 'video') return 'Video';
    if (message.attachment?.type === 'audio') return 'Tin nhắn thoại';
    if (message.attachment?.type === 'gif') return 'Ảnh GIF';
    if (message.attachment?.type === 'link') return 'Liên kết';
    return 'Tin nhắn';
  }

  // Webhook verification for Zalo
  static verifyWebhook(data: string, signature: string): boolean {
    try {
      const appSecret = process.env.ZALO_APP_SECRET || '';
      if (!appSecret) {
        console.error('Zalo app secret chưa được cấu hình');
        return false;
      }
      
      const expectedSignature = crypto
        .createHmac('sha256', appSecret)
        .update(data)
        .digest('hex');
      
      const isValid = signature === expectedSignature;
      
      if (!isValid) {
        console.error('Zalo webhook signature không hợp lệ');
      }
      
      return isValid;
    } catch (error) {
      console.error('Lỗi khi xác thực Zalo webhook:', error);
      return false;
    }
  }

  // Process incoming webhook data
  static processWebhook(body: any): any {
    const service = new ZaloService();
    
    try {
      if (body.event_name === 'user_send_text' || body.event_name === 'user_send_image' || body.event_name === 'user_send_file') {
        const message = service.convertWebhookToMessage(body);
        if (message) {
          console.log('Tin nhắn Zalo mới:', message);
          // Here you would typically save to database or trigger handlers
          // await saveZaloMessage(message);
          // await notifyAssignedAgent(message);
        }
      } else if (body.event_name === 'follow') {
        console.log('Người theo dõi Zalo mới:', body.follower);
        // Handle new follower
        // await handleNewZaloFollower(body.follower);
      } else if (body.event_name === 'unfollow') {
        console.log('Người dừng theo dõi Zalo:', body.follower);
        // Handle unfollower
        // await handleZaloUnfollow(body.follower);
      } else if (body.event_name === 'user_submit_info') {
        console.log('Thông tin người dùng Zalo:', body.info);
        // Handle user info submission
      } else if (body.event_name === 'user_click_chatnow') {
        console.log('Người dùng nhấn chat ngay:', body.user_info);
        // Handle chat now click
      }

      return { status: 'ok', message: 'Webhook đã được xử lý thành công' };
    } catch (error) {
      console.error('Lỗi khi xử lý webhook Zalo:', error);
      return { status: 'error', message: 'Lỗi khi xử lý webhook' };
    }
  }

  // Additional Vietnamese-specific methods
  async sendVietnameseGreeting(userId: string): Promise<any> {
    const greetings = [
      'Xin chào! Chúng tôi có thể giúp gì cho bạn?',
      'Chào bạn! Bạn cần hỗ trợ gì không?',
      'Xin chào! Chúng tôi rất vui được hỗ trợ bạn.',
      'Chào bạn! Hãy cho chúng tôi biết bạn cần giúp đỡ gì nhé!',
    ];
    
    const randomGreeting = greetings[Math.floor(Math.random() * greetings.length)];
    return this.sendTextMessage(userId, randomGreeting);
  }

  async sendVietnameseQuickReplies(userId: string, question: string): Promise<any> {
    const quickReplies = [
      { title: 'Thông tin sản phẩm', payload: 'PRODUCT_INFO' },
      { title: 'Báo giá', payload: 'PRICING' },
      { title: 'Liên hệ tư vấn', payload: 'CONTACT_SUPPORT' },
      { title: 'Tìm cửa hàng', payload: 'STORE_LOCATOR' },
      { title: 'Chính sách bảo hành', payload: 'WARRANTY' },
    ];
    
    return this.sendQuickReplyMessage(userId, question, quickReplies);
  }

  async sendVietnameseBusinessCard(userId: string, businessInfo: any): Promise<any> {
    const elements = [{
      title: businessInfo.name || 'Công ty chúng tôi',
      subtitle: businessInfo.description || 'Cam kết chất lượng và dịch vụ tốt nhất',
      image_url: businessInfo.logo || '',
      buttons: [
        {
          type: 'phone_number',
          title: 'Gọi ngay',
          payload: businessInfo.phone || '+***********',
        },
        {
          type: 'web_url',
          title: 'Website',
          url: businessInfo.website || 'https://example.com',
        },
        {
          type: 'postback',
          title: 'Xem thêm',
          payload: 'MORE_INFO',
        },
      ],
    }];
    
    return this.sendInteractiveMessage(userId, 'generic', elements);
  }

  // Helper method for Vietnamese text formatting
  formatVietnameseText(text: string): string {
    // Add Vietnamese-specific text formatting
    return text
      .replace(/\b(anh|chị|em|bạn)\b/gi, (match) => match.toLowerCase())
      .replace(/\b(xin chào|chào|cảm ơn)\b/gi, (match) => match.charAt(0).toUpperCase() + match.slice(1).toLowerCase());
  }

  // Get Vietnamese business hours message
  getVietnameseBusinessHours(): string {
    const now = new Date();
    const hour = now.getHours();
    
    if (hour >= 8 && hour < 17) {
      return 'Chúng tôi đang hoạt động! Hãy gửi tin nhắn, chúng tôi sẽ phản hồi ngay.';
    } else if (hour >= 17 && hour < 22) {
      return 'Chúng tôi đang ngoài giờ làm việc. Tin nhắn của bạn sẽ được phản hồi vào ngày hôm sau.';
    } else {
      return 'Chúng tôi đang nghỉ ngoi. Giờ làm việc: 8:00 - 17:00 (Thứ 2 - Thứ 6). Hãy để lại tin nhắn, chúng tôi sẽ liên hệ lại sau.';
    }
  }
}

export const zaloService = new ZaloService();