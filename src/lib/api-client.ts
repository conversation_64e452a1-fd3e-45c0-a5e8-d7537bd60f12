interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  total?: number;
  metadata?: any;
}

interface ListOptions {
  status?: string;
  category?: string;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

class ApiClient {
  private baseUrl = '/api/suite';

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Requests API
  async getRequests(options: ListOptions = {}): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams();
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    return this.request(`/requests?${params.toString()}`);
  }

  async getRequest(id: string): Promise<ApiResponse<any>> {
    return this.request(`/requests/${id}`);
  }

  async createRequest(request: any): Promise<ApiResponse<any>> {
    return this.request('/requests', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async updateRequest(id: string, updates: any): Promise<ApiResponse<any>> {
    return this.request(`/requests/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteRequest(id: string): Promise<ApiResponse<any>> {
    return this.request(`/requests?id=${id}`, {
      method: 'DELETE',
    });
  }

  async addRequestComment(id: string, comment: any): Promise<ApiResponse<any>> {
    return this.request(`/requests/${id}`, {
      method: 'PATCH',
      body: JSON.stringify({ action: 'add_comment', ...comment }),
    });
  }

  async addRequestApproval(id: string, approval: any): Promise<ApiResponse<any>> {
    return this.request(`/requests/${id}`, {
      method: 'PATCH',
      body: JSON.stringify({ action: 'add_approval', ...approval }),
    });
  }

  // Workflows API
  async getWorkflows(type: 'instances' | 'templates' = 'instances', options: ListOptions = {}): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams({ type, ...options as any });
    return this.request(`/workflows?${params.toString()}`);
  }

  async getWorkflowTemplates(options: ListOptions = {}): Promise<ApiResponse<any[]>> {
    return this.getWorkflows('templates', options);
  }

  async getWorkflowInstances(options: ListOptions = {}): Promise<ApiResponse<any[]>> {
    return this.getWorkflows('instances', options);
  }

  async createWorkflowTemplate(template: any): Promise<ApiResponse<any>> {
    return this.request('/workflows', {
      method: 'POST',
      body: JSON.stringify({ type: 'template', ...template }),
    });
  }

  async createWorkflowInstance(instance: any): Promise<ApiResponse<any>> {
    return this.request('/workflows', {
      method: 'POST',
      body: JSON.stringify({ type: 'instance', ...instance }),
    });
  }

  async updateWorkflowTemplate(id: string, updates: any): Promise<ApiResponse<any>> {
    return this.request('/workflows', {
      method: 'PUT',
      body: JSON.stringify({ type: 'template', id, ...updates }),
    });
  }

  async updateWorkflowInstance(id: string, updates: any): Promise<ApiResponse<any>> {
    return this.request('/workflows', {
      method: 'PUT',
      body: JSON.stringify({ type: 'instance', id, ...updates }),
    });
  }

  async deleteWorkflowTemplate(id: string): Promise<ApiResponse<any>> {
    return this.request(`/workflows?type=template&id=${id}`, {
      method: 'DELETE',
    });
  }

  async deleteWorkflowInstance(id: string): Promise<ApiResponse<any>> {
    return this.request(`/workflows?type=instance&id=${id}`, {
      method: 'DELETE',
    });
  }

  // Users API
  async getUsers(options: ListOptions = {}): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams();
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    return this.request(`/users?${params.toString()}`);
  }

  async createUser(user: any): Promise<ApiResponse<any>> {
    return this.request('/users', {
      method: 'POST',
      body: JSON.stringify(user),
    });
  }

  async updateUser(id: string, updates: any): Promise<ApiResponse<any>> {
    return this.request('/users', {
      method: 'PUT',
      body: JSON.stringify({ id, ...updates }),
    });
  }

  async deleteUser(id: string): Promise<ApiResponse<any>> {
    return this.request(`/users?id=${id}`, {
      method: 'DELETE',
    });
  }

  // Settings API
  async getSettings(section?: string): Promise<ApiResponse<any>> {
    const params = section ? `?section=${section}` : '';
    return this.request(`/settings${params}`);
  }

  async updateSettings(updates: any, section?: string): Promise<ApiResponse<any>> {
    return this.request('/settings', {
      method: 'PUT',
      body: JSON.stringify({ section, ...updates }),
    });
  }

  async resetSettings(): Promise<ApiResponse<any>> {
    return this.request('/settings', {
      method: 'POST',
      body: JSON.stringify({ action: 'reset' }),
    });
  }

  async backupSettings(): Promise<ApiResponse<any>> {
    return this.request('/settings', {
      method: 'POST',
      body: JSON.stringify({ action: 'backup' }),
    });
  }

  async testIntegration(type: string, config: any): Promise<ApiResponse<any>> {
    return this.request('/settings', {
      method: 'POST',
      body: JSON.stringify({ action: 'test_integration', type, config }),
    });
  }

  // Analytics API
  async getAnalytics(
    type: 'overview' | 'requests' | 'workflows' | 'users' | 'performance' = 'overview',
    options: {
      period?: string;
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams({ type, ...options });
    return this.request(`/analytics?${params.toString()}`);
  }

  // Advanced Workflows API
  async evaluateApprovals(templateId: string, instanceData: any): Promise<ApiResponse<any>> {
    return this.request('/workflows/advanced', {
      method: 'POST',
      body: JSON.stringify({
        action: 'evaluate_approvals',
        templateId,
        instanceData,
      }),
    });
  }

  async delegateApproval(instanceId: string, originalApproverId: string, delegateToId: string, reason: string): Promise<ApiResponse<any>> {
    return this.request('/workflows/advanced', {
      method: 'POST',
      body: JSON.stringify({
        action: 'delegate_approval',
        instanceId,
        originalApproverId,
        delegateToId,
        reason,
      }),
    });
  }

  async approveOnBehalf(templateId: string, instanceId: string, approverId: string, originalApproverId: string, instanceData: any): Promise<ApiResponse<any>> {
    return this.request('/workflows/advanced', {
      method: 'POST',
      body: JSON.stringify({
        action: 'approve_on_behalf',
        templateId,
        instanceId,
        approverId,
        originalApproverId,
        instanceData,
      }),
    });
  }

  async escalateApproval(instanceId: string, level: number, approverId: string): Promise<ApiResponse<any>> {
    return this.request('/workflows/advanced', {
      method: 'POST',
      body: JSON.stringify({
        action: 'escalate_approval',
        instanceId,
        level,
        approverId,
      }),
    });
  }

  async checkConditions(conditions: any, instanceData: any): Promise<ApiResponse<any>> {
    return this.request('/workflows/advanced', {
      method: 'POST',
      body: JSON.stringify({
        action: 'check_conditions',
        conditions,
        instanceData,
      }),
    });
  }

  async getApprovalTypes(): Promise<ApiResponse<any[]>> {
    return this.request('/workflows/advanced?action=approval_types');
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
