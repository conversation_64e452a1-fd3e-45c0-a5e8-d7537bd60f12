/**
 * Cache-aware file writing utilities
 * 
 * These utilities make it easy to write JSON files while maintaining cache consistency.
 * Always use these instead of direct fs.writeFile operations.
 */

import { writeFile, readFile, mkdir } from 'fs/promises';
import { join, dirname } from 'path';

export interface WriteOptions {
  updateCache?: boolean;     // Whether to update cache after write (default: true)
  invalidateFirst?: boolean; // Whether to invalidate cache before write (default: true)
  atomic?: boolean;          // Whether to use atomic write (default: true)
  backup?: boolean;          // Whether to create backup before write (default: false)
}

/**
 * Write JSON data to file with cache management
 * 
 * @example
 * ```typescript
 * import { writeJsonFile } from '@/lib/cache/cache-write-utils';
 * 
 * await writeJsonFile('data/users.json', { users: [] });
 * ```
 */
export async function writeJsonFile<T = any>(
  filePath: string, 
  data: T, 
  options: WriteOptions = {}
): Promise<void> {
  const {
    updateCache = true,
    invalidateFirst = true,
    atomic = true,
    backup = false
  } = options;

  const fullPath = join(process.cwd(), filePath);
  
  try {
    // Ensure directory exists
    await mkdir(dirname(fullPath), { recursive: true });

    // Step 1: Create backup if requested
    if (backup) {
      await createBackup(filePath);
    }

    // Step 2: Invalidate cache first
    if (invalidateFirst) {
      await invalidateCache(filePath);
    }

    // Step 3: Write file
    const jsonData = JSON.stringify(data, null, 2);
    
    if (atomic) {
      await writeFileAtomic(fullPath, jsonData);
    } else {
      await writeFile(fullPath, jsonData, 'utf8');
    }

    // Step 4: Update cache
    if (updateCache) {
      await warmupCache(filePath);
    }

    console.log(`✅ Written: ${filePath} (${jsonData.length} bytes)`);
  } catch (error) {
    console.error(`❌ Failed to write ${filePath}:`, error);
    throw error;
  }
}

/**
 * Update existing JSON file by merging new data
 * 
 * @example
 * ```typescript
 * import { updateJsonFile } from '@/lib/cache/cache-write-utils';
 * 
 * await updateJsonFile('data/config.json', { version: '1.1' });
 * ```
 */
export async function updateJsonFile<T = any>(
  filePath: string, 
  updates: Partial<T>, 
  options: WriteOptions = {}
): Promise<T> {
  try {
    // Read current data (from cache if available)
    const currentData = await readJsonFile<T>(filePath);
    
    // Merge updates
    const updatedData = { ...currentData, ...updates } as T;
    
    // Write merged data
    await writeJsonFile(filePath, updatedData, options);
    
    return updatedData;
  } catch (error) {
    // If file doesn't exist, treat updates as new data
    if (error instanceof Error && error.message.includes('ENOENT')) {
      await writeJsonFile(filePath, updates as T, options);
      return updates as T;
    }
    throw error;
  }
}

/**
 * Append item to array in JSON file
 * 
 * @example
 * ```typescript
 * import { appendToJsonFile } from '@/lib/cache/cache-write-utils';
 * 
 * // For direct array files
 * await appendToJsonFile('data/users.json', { id: 1, name: 'John' });
 * 
 * // For arrays within objects
 * await appendToJsonFile('data/data.json', { id: 1, name: 'John' }, 'users');
 * ```
 */
export async function appendToJsonFile<T = any>(
  filePath: string, 
  newItem: T, 
  arrayKey?: string,
  options: WriteOptions = {}
): Promise<T[]> {
  try {
    const currentData = await readJsonFile<any>(filePath);
    
    let targetArray: T[];
    let updatedData: any;

    if (arrayKey) {
      // Append to specific array property
      targetArray = Array.isArray(currentData[arrayKey]) ? currentData[arrayKey] : [];
      targetArray.push(newItem);
      updatedData = { ...currentData, [arrayKey]: targetArray };
    } else {
      // Assume the file contains an array directly
      targetArray = Array.isArray(currentData) ? currentData : [];
      targetArray.push(newItem);
      updatedData = targetArray;
    }

    await writeJsonFile(filePath, updatedData, options);
    return targetArray;
  } catch (error) {
    // If file doesn't exist, create new array
    if (error instanceof Error && error.message.includes('ENOENT')) {
      const newArray = [newItem];
      const newData = arrayKey ? { [arrayKey]: newArray } : newArray;
      await writeJsonFile(filePath, newData, options);
      return newArray;
    }
    throw error;
  }
}

/**
 * Remove items from array in JSON file
 * 
 * @example
 * ```typescript
 * import { removeFromJsonFile } from '@/lib/cache/cache-write-utils';
 * 
 * // Remove by ID
 * await removeFromJsonFile('data/users.json', { id: 123 });
 * 
 * // Remove by index
 * await removeFromJsonFile('data/users.json', { index: 0 });
 * 
 * // Remove from nested array
 * await removeFromJsonFile('data/data.json', { id: 123 }, 'users');
 * ```
 */
export async function removeFromJsonFile<T = any>(
  filePath: string, 
  removeBy: { id?: any; index?: number; predicate?: (item: T) => boolean },
  arrayKey?: string,
  options: WriteOptions = {}
): Promise<T[]> {
  const currentData = await readJsonFile<any>(filePath);
  
  let targetArray: T[];
  let updatedData: any;

  if (arrayKey) {
    targetArray = Array.isArray(currentData[arrayKey]) ? currentData[arrayKey] : [];
  } else {
    targetArray = Array.isArray(currentData) ? currentData : [];
  }

  // Remove items based on criteria
  if (typeof removeBy.index === 'number') {
    // Remove by index
    if (removeBy.index >= 0 && removeBy.index < targetArray.length) {
      targetArray.splice(removeBy.index, 1);
    }
  } else if (removeBy.id !== undefined) {
    // Remove by ID
    targetArray = targetArray.filter((item: any) => {
      const itemId = item.id || item._id || item.uuid;
      return itemId !== removeBy.id;
    });
  } else if (removeBy.predicate) {
    // Remove by custom predicate
    targetArray = targetArray.filter(item => !removeBy.predicate!(item));
  }

  if (arrayKey) {
    updatedData = { ...currentData, [arrayKey]: targetArray };
  } else {
    updatedData = targetArray;
  }

  await writeJsonFile(filePath, updatedData, options);
  return targetArray;
}

/**
 * Read JSON file (cache-aware)
 * Will attempt to read from cache first, fallback to filesystem
 */
export async function readJsonFile<T = any>(filePath: string): Promise<T> {
  try {
    // Try to read from cache API first
    const response = await fetch(`http://localhost:3000/api/cache/${filePath.replace(/\//g, '_')}`);
    if (response.ok) {
      const result = await response.json();
      if (result.success) {
        return result.data;
      }
    }
  } catch (error) {
    // Cache read failed, fallback to file system
  }

  // Fallback to direct file read
  const fullPath = join(process.cwd(), filePath);
  const content = await readFile(fullPath, 'utf8');
  return JSON.parse(content);
}

/**
 * Check if file exists
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    const fullPath = join(process.cwd(), filePath);
    await readFile(fullPath);
    return true;
  } catch {
    return false;
  }
}

// Helper functions

async function createBackup(filePath: string): Promise<void> {
  try {
    const fullPath = join(process.cwd(), filePath);
    const backupPath = `${fullPath}.backup.${new Date().toISOString().replace(/[:.]/g, '-')}`;
    
    const content = await readFile(fullPath, 'utf8');
    await writeFile(backupPath, content, 'utf8');
    
    console.log(`📄 Created backup: ${backupPath}`);
  } catch (error) {
    // File doesn't exist, no backup needed
  }
}

async function writeFileAtomic(fullPath: string, data: string): Promise<void> {
  const tempPath = `${fullPath}.tmp.${Date.now()}`;
  
  try {
    await writeFile(tempPath, data, 'utf8');
    
    // Atomic rename
    const fs = await import('fs/promises');
    await fs.rename(tempPath, fullPath);
  } catch (error) {
    // Clean up temp file
    try {
      const fs = await import('fs/promises');
      await fs.unlink(tempPath);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    throw error;
  }
}

async function invalidateCache(filePath: string): Promise<void> {
  try {
    const fileId = filePath.replace(/\//g, '_');
    await fetch(`http://localhost:3000/api/cache/${fileId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'clear' })
    });
  } catch (error) {
    // Cache invalidation failed, not critical
    console.warn('Cache invalidation failed:', error);
  }
}

async function warmupCache(filePath: string): Promise<void> {
  try {
    const fileId = filePath.replace(/\//g, '_');
    await fetch(`http://localhost:3000/api/cache/${fileId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'warmup' })
    });
  } catch (error) {
    // Cache warmup failed, not critical
    console.warn('Cache warmup failed:', error);
  }
}

// Convenience re-exports for common patterns
export const cacheWrite = {
  /**
   * Write complete file
   */
  write: writeJsonFile,
  
  /**
   * Update existing file
   */
  update: updateJsonFile,
  
  /**
   * Add item to array
   */
  append: appendToJsonFile,
  
  /**
   * Remove item from array
   */
  remove: removeFromJsonFile,
  
  /**
   * Read file (cache-aware)
   */
  read: readJsonFile,
  
  /**
   * Check if file exists
   */
  exists: fileExists
};

// Default export for easy importing
export default cacheWrite; 