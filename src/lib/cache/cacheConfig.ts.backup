import { JsonCacheManager, CacheStrategy, CacheConfig } from './JsonCacheManager';

// Initialize cache manager
const cacheManager = JsonCacheManager.getInstance();

// Define cache configurations for different files
const cacheConfigs: Record<string, CacheConfig> = {
  // Frequently accessed, rarely changed files
  'data/users/users.mass.json': {
    strategy: CacheStrategy.MEMORY,
    ttl: 3600, // 1 hour
    watchChanges: true
  },

  // Large files that need size limits
  'data/dace/database/dace.farmers.json': {
    strategy: CacheStrategy.NODE_CACHE,
    ttl: 1800, // 30 minutes
    maxSize: 50 * 1024 * 1024, // 50MB
    watchChanges: true
  },

  // Files that change frequently
  'data/minirent/payments.json': {
    strategy: CacheStrategy.FILE_WATCHER,
    watchChanges: true
  },

  // Development files that need real-time updates
  'data/real-estate/people/clients.json': {
    strategy: CacheStrategy.FILE_WATCHER,
    watchChanges: true
  }
};

// Register all files with their configurations
export function initializeCache() {
  for (const [filePath, config] of Object.entries(cacheConfigs)) {
    cacheManager.registerFile(filePath, config);
  }
}

// Export the cache manager instance
export { cacheManager }; 