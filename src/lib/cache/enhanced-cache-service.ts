import { JsonCacheManager, CacheStrategy, CacheConfig } from './JsonCacheManager';
import { readdir, stat, access, writeFile as fsWriteFile } from 'fs/promises';
import { join, relative, extname } from 'path';
import glob from 'glob';
import { promisify } from 'util';

const globAsync = promisify(glob);

export interface FileMetadata {
  category: 'core' | 'apps' | 'user-data' | 'large-files' | 'temporary';
  strategy: CacheStrategy;
  size: number;
  lastModified: string;
  cached: boolean;
  hits: number;
  misses: number;
  lastAccessed: string | null;
  ttl: number | null;
  priority: 'high' | 'medium' | 'low';
}

export interface CacheStats {
  hits: number;
  misses: number;
  memoryUsage: number;
  averageResponseTime: number;
  totalFiles: number;
  cachedFiles: number;
  startTime: number;
}

export interface WriteOptions {
  updateCache?: boolean; // Whether to update cache after write (default: true)
  invalidateFirst?: boolean; // Whether to invalidate cache before write (default: true)
  atomic?: boolean; // Whether to use atomic write (write to temp file first)
  backup?: boolean; // Whether to create backup before write
}

export class EnhancedCacheService {
  private static instance: EnhancedCacheService;
  private cacheManager: JsonCacheManager;
  private stats: CacheStats;
  private startTime: number;

  private constructor() {
    this.cacheManager = JsonCacheManager.getInstance();
    this.startTime = Date.now();
    this.stats = {
      hits: 0,
      misses: 0,
      memoryUsage: 0,
      averageResponseTime: 0,
      totalFiles: 0,
      cachedFiles: 0,
      startTime: this.startTime
    };
  }

  public static getInstance(): EnhancedCacheService {
    if (!EnhancedCacheService.instance) {
      EnhancedCacheService.instance = new EnhancedCacheService();
    }
    return EnhancedCacheService.instance;
  }

  /**
   * Write data to a JSON file with cache management
   */
  public async writeFile<T = any>(filePath: string, data: T, options: WriteOptions = {}): Promise<void> {
    const {
      updateCache = true,
      invalidateFirst = true,
      atomic = true,
      backup = false
    } = options;

    const fullPath = join(process.cwd(), filePath);
    const startTime = Date.now();

    try {
      // Step 1: Create backup if requested
      if (backup) {
        await this.createBackup(filePath);
      }

      // Step 2: Invalidate cache first to prevent stale reads during write
      if (invalidateFirst) {
        try {
          await this.invalidate(filePath);
        } catch (error) {
          // Cache might not exist yet, that's okay
        }
      }

      // Step 3: Write file (atomically if requested)
      const jsonData = JSON.stringify(data, null, 2);
      
      if (atomic) {
        await this.writeFileAtomic(fullPath, jsonData);
      } else {
        await fsWriteFile(fullPath, jsonData, 'utf8');
      }

      // Step 4: Update cache with new data
      if (updateCache) {
        try {
          // Register file if not already registered
          await this.ensureFileRegistered(filePath);
          
          // Update cache with new data
          await this.cacheManager.writeFile(fullPath, data);
          
          console.log(`✅ Written and cached: ${filePath} (${jsonData.length} bytes)`);
        } catch (error) {
          console.warn(`⚠️ Failed to update cache for ${filePath}:`, error);
          // File was written successfully, cache update failed - that's acceptable
        }
      }

      // Step 5: Update registry metadata
      await this.updateFileRegistryMetadata(filePath);

    } catch (error) {
      console.error(`❌ Failed to write file ${filePath}:`, error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      console.log(`📝 Write operation completed in ${duration}ms`);
    }
  }

  /**
   * Update existing JSON file (merge with existing data)
   */
  public async updateFile<T = any>(filePath: string, updates: Partial<T>, options: WriteOptions = {}): Promise<T> {
    try {
      // Get current data
      const currentData = await this.get<T>(filePath);
      
      // Merge updates with existing data
      const updatedData = { ...currentData, ...updates } as T;
      
      // Write merged data
      await this.writeFile(filePath, updatedData, options);
      
      return updatedData;
    } catch (error) {
      // If file doesn't exist, treat updates as new data
      if (error instanceof Error && error.message.includes('ENOENT')) {
        await this.writeFile(filePath, updates as T, options);
        return updates as T;
      }
      throw error;
    }
  }

  /**
   * Append data to an array in JSON file
   */
  public async appendToFile<T = any>(filePath: string, newItem: T, arrayKey?: string, options: WriteOptions = {}): Promise<T[]> {
    try {
      const currentData = await this.get<any>(filePath);
      
      let targetArray: T[];
      let updatedData: any;

      if (arrayKey) {
        // Append to specific array property
        targetArray = Array.isArray(currentData[arrayKey]) ? currentData[arrayKey] : [];
        targetArray.push(newItem);
        updatedData = { ...currentData, [arrayKey]: targetArray };
      } else {
        // Assume the file contains an array directly
        targetArray = Array.isArray(currentData) ? currentData : [];
        targetArray.push(newItem);
        updatedData = targetArray;
      }

      await this.writeFile(filePath, updatedData, options);
      return targetArray;
    } catch (error) {
      // If file doesn't exist, create new array
      if (error instanceof Error && error.message.includes('ENOENT')) {
        const newArray = [newItem];
        const newData = arrayKey ? { [arrayKey]: newArray } : newArray;
        await this.writeFile(filePath, newData, options);
        return newArray;
      }
      throw error;
    }
  }

  /**
   * Delete an item from an array in JSON file
   */
  public async deleteFromFile<T = any>(filePath: string, predicate: (item: T) => boolean, arrayKey?: string, options: WriteOptions = {}): Promise<T[]> {
    const currentData = await this.get<any>(filePath);
    
    let targetArray: T[];
    let updatedData: any;

    if (arrayKey) {
      targetArray = Array.isArray(currentData[arrayKey]) ? currentData[arrayKey] : [];
      const filteredArray = targetArray.filter(item => !predicate(item));
      updatedData = { ...currentData, [arrayKey]: filteredArray };
      targetArray = filteredArray;
    } else {
      targetArray = Array.isArray(currentData) ? currentData : [];
      const filteredArray = targetArray.filter(item => !predicate(item));
      updatedData = filteredArray;
      targetArray = filteredArray;
    }

    await this.writeFile(filePath, updatedData, options);
    return targetArray;
  }

  /**
   * Atomic file write (write to temporary file first, then rename)
   */
  private async writeFileAtomic(fullPath: string, data: string): Promise<void> {
    const tempPath = `${fullPath}.tmp.${Date.now()}`;
    
    try {
      // Write to temporary file
      await fsWriteFile(tempPath, data, 'utf8');
      
      // Atomic rename (this is atomic on most filesystems)
      const fs = await import('fs/promises');
      await fs.rename(tempPath, fullPath);
    } catch (error) {
      // Clean up temp file if it exists
      try {
        const fs = await import('fs/promises');
        await fs.unlink(tempPath);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      throw error;
    }
  }

  /**
   * Create backup of file before writing
   */
  private async createBackup(filePath: string): Promise<void> {
    const fullPath = join(process.cwd(), filePath);
    const backupPath = `${fullPath}.backup.${new Date().toISOString().replace(/[:.]/g, '-')}`;
    
    try {
      await access(fullPath);
      const fs = await import('fs/promises');
      await fs.copyFile(fullPath, backupPath);
      console.log(`📄 Created backup: ${backupPath}`);
    } catch (error) {
      // File doesn't exist, no backup needed
    }
  }

  /**
   * Ensure file is registered with cache system
   */
  private async ensureFileRegistered(filePath: string): Promise<void> {
    try {
      const fullPath = join(process.cwd(), filePath);
      if (!this.cacheManager.isFileCached(fullPath)) {
        await this.registerFile(filePath);
      }
    } catch (error) {
      // Registration failed, but that's not critical for write operations
      console.warn(`⚠️ Could not register file ${filePath}:`, error);
    }
  }

  /**
   * Update registry metadata after file write
   */
  private async updateFileRegistryMetadata(filePath: string): Promise<void> {
    try {
      const fullPath = join(process.cwd(), filePath);
      const stats = await stat(fullPath);
      
      // Update the registry with new file metadata
      // This would update the cache-registry.json file
      const registryPath = join(process.cwd(), 'data/cache/cache-registry.json');
      
      try {
        const registryData = await this.get(registryPath);
        if (registryData && registryData.files && registryData.files[filePath]) {
          registryData.files[filePath].size = stats.size;
          registryData.files[filePath].lastModified = stats.mtime.toISOString();
          registryData.lastUpdated = new Date().toISOString();
          
          // Write registry update (without cache to avoid recursion)
          await this.writeFile('data/cache/cache-registry.json', registryData, { 
            updateCache: false 
          });
        }
      } catch (error) {
        // Registry update failed, but that's not critical
        console.warn(`⚠️ Failed to update registry metadata:`, error);
      }
    } catch (error) {
      // Metadata update failed, not critical
    }
  }

  /**
   * Auto-discover all JSON files in the project
   */
  public async discoverJsonFiles(): Promise<Record<string, FileMetadata>> {
    const projectRoot = process.cwd();
    const discoveredFiles: Record<string, FileMetadata> = {};

    try {
      // Use glob to find all JSON files, excluding certain directories
      const jsonFiles = await globAsync('**/*.json', {
        cwd: projectRoot,
        ignore: [
          'node_modules/**',
          '.next/**',
          'dist/**',
          'build/**',
          '**/.git/**',
          '**/cache/**',
          '**/temp/**',
          '**/backup/**',
          '**/*.backup.json',
          '**/*.bak'
        ]
      });

      for (const file of jsonFiles) {
        const fullPath = join(projectRoot, file);
        const relativePath = relative(projectRoot, fullPath);
        
        try {
          const stats = await stat(fullPath);
          const metadata = await this.categorizeFile(relativePath, stats.size);
          
          discoveredFiles[relativePath] = {
            ...metadata,
            size: stats.size,
            lastModified: stats.mtime.toISOString(),
            cached: false,
            hits: 0,
            misses: 0,
            lastAccessed: null
          };
        } catch (error) {
          console.warn(`Error processing file ${file}:`, error);
        }
      }

      console.log(`Discovered ${Object.keys(discoveredFiles).length} JSON files`);
      return discoveredFiles;
    } catch (error) {
      console.error('Error discovering JSON files:', error);
      return {};
    }
  }

  /**
   * Categorize a file and determine appropriate caching strategy
   */
  private async categorizeFile(filePath: string, fileSize: number): Promise<Omit<FileMetadata, 'size' | 'lastModified' | 'cached' | 'hits' | 'misses' | 'lastAccessed'>> {
    const path = filePath.toLowerCase();
    
    // Core system files - high priority, FILE_WATCHER strategy
    if (path.includes('data/blocks.json') || 
        path.includes('data/agent-preferences.json') || 
        path.includes('data/cache/registry.json') ||
        path.includes('src/config/') ||
        path.includes('prisma/')) {
      return {
        category: 'core',
        strategy: CacheStrategy.FILE_WATCHER,
        priority: 'high',
        ttl: null // No TTL for core files
      };
    }

    // Application-specific data - medium priority
    if (path.includes('data/apps/') || 
        path.includes('data/aimarketplace/') ||
        path.includes('data/minirent/') ||
        path.includes('data/contests/') ||
        path.includes('data/games/')) {
      return {
        category: 'apps',
        strategy: fileSize > 100000 ? CacheStrategy.NODE_CACHE : CacheStrategy.MEMORY,
        priority: 'medium',
        ttl: 900000 // 15 minutes
      };
    }

    // User-generated content - medium priority, longer TTL
    if (path.includes('data/users/') || 
        path.includes('data/whoswho/') ||
        path.includes('data/customers/')) {
      return {
        category: 'user-data',
        strategy: fileSize > 200000 ? CacheStrategy.NODE_CACHE : CacheStrategy.FILE_WATCHER,
        priority: 'medium',
        ttl: 1800000 // 30 minutes
      };
    }

    // Large files - low priority, size-based strategy
    if (fileSize > 500000) {
      return {
        category: 'large-files',
        strategy: CacheStrategy.NODE_CACHE,
        priority: 'low',
        ttl: 1800000 // 30 minutes
      };
    }

    // Temporary/backup files - low priority, short TTL
    if (path.includes('backup') || 
        path.includes('temp') || 
        path.includes('.bak') ||
        path.includes('cache/')) {
      return {
        category: 'temporary',
        strategy: CacheStrategy.MEMORY,
        priority: 'low',
        ttl: 300000 // 5 minutes
      };
    }

    // Default categorization for other files
    return {
      category: 'apps',
      strategy: fileSize > 50000 ? CacheStrategy.NODE_CACHE : CacheStrategy.MEMORY,
      priority: 'medium',
      ttl: 900000 // 15 minutes
    };
  }

  /**
   * Register a file with the cache system
   */
  public async registerFile(filePath: string, metadata?: Partial<FileMetadata>): Promise<void> {
    try {
      const fullPath = join(process.cwd(), filePath);
      await access(fullPath);
      
      const stats = await stat(fullPath);
      const defaultMetadata = await this.categorizeFile(filePath, stats.size);
      
      const finalMetadata: FileMetadata = {
        ...defaultMetadata,
        ...metadata,
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        cached: false,
        hits: 0,
        misses: 0,
        lastAccessed: null
      };

      // Register with the cache manager
      const cacheConfig = {
        strategy: finalMetadata.strategy,
        ttl: finalMetadata.ttl,
        watchChanges: finalMetadata.strategy === CacheStrategy.FILE_WATCHER
      };
      this.cacheManager.registerFile(filePath, cacheConfig);
      
      console.log(`Registered file: ${filePath} with strategy: ${finalMetadata.strategy}`);
    } catch (error) {
      console.error(`Error registering file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get a file from cache (main API for file access)
   */
  public async get<T = any>(filePath: string): Promise<T> {
    const startTime = Date.now();
    
    try {
      // Check if file is registered, if not, register it
      const fullPath = join(process.cwd(), filePath);
      
      try {
        const data = await this.cacheManager.getFile<T>(fullPath);
        this.stats.hits++;
        return data;
      } catch (error) {
        // If file not registered, try to register it first
        if (error instanceof Error && error.message.includes('not registered')) {
          await this.registerFile(filePath);
          const data = await this.cacheManager.getFile<T>(fullPath);
          this.stats.hits++;
          return data;
        }
        throw error;
      }
    } catch (error) {
      this.stats.misses++;
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      this.updateAverageResponseTime(duration);
    }
  }

  /**
   * Check if a file is cached
   */
  public async has(filePath: string): Promise<boolean> {
    try {
      const fullPath = join(process.cwd(), filePath);
      // Check if the file is registered and has cached data
      return this.cacheManager.isFileCached(fullPath);
    } catch (error) {
      return false;
    }
  }

  /**
   * Invalidate/clear cache for a specific file
   */
  public async invalidate(filePath: string): Promise<void> {
    const fullPath = join(process.cwd(), filePath);
    await this.cacheManager.clearCache(fullPath);
  }

  /**
   * Clear all cache
   */
  public async clearAll(): Promise<void> {
    await this.cacheManager.clearAllCache();
    this.stats.hits = 0;
    this.stats.misses = 0;
  }

  /**
   * Get cache statistics
   */
  public async getStats(): Promise<CacheStats> {
    const cacheManagerStats = this.cacheManager.getStats();
    
    return {
      ...this.stats,
      memoryUsage: this.calculateMemoryUsage(),
      totalFiles: cacheManagerStats.totalFiles || 0,
      cachedFiles: cacheManagerStats.cachedFiles || 0
    };
  }

  /**
   * Get statistics for a specific file
   */
  public async getFileStats(filePath: string): Promise<any> {
    const fullPath = join(process.cwd(), filePath);
    return this.cacheManager.getFileStats(fullPath);
  }

  /**
   * Health check for the cache system
   */
  public async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      const stats = await this.getStats();
      const isHealthy = stats.averageResponseTime < 100 && // Less than 100ms average
                       stats.memoryUsage < 100 * 1024 * 1024; // Less than 100MB
      
      return {
        status: isHealthy ? 'healthy' : 'warning',
        details: {
          averageResponseTime: stats.averageResponseTime,
          memoryUsage: stats.memoryUsage,
          hitRatio: stats.hits + stats.misses > 0 ? (stats.hits / (stats.hits + stats.misses)) * 100 : 0,
          totalFiles: stats.totalFiles,
          cachedFiles: stats.cachedFiles
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Update average response time
   */
  private updateAverageResponseTime(duration: number): void {
    const totalRequests = this.stats.hits + this.stats.misses;
    if (totalRequests === 1) {
      this.stats.averageResponseTime = duration;
    } else {
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime * (totalRequests - 1) + duration) / totalRequests;
    }
  }

  /**
   * Calculate memory usage
   */
  private calculateMemoryUsage(): number {
    const memUsage = process.memoryUsage();
    return memUsage.heapUsed;
  }

  /**
   * Utility method to create a cache-aware file reader
   */
  public createFileReader() {
    return {
      readJsonFile: async <T>(filePath: string): Promise<T> => {
        return this.get<T>(filePath);
      },
      writeJsonFile: async <T>(filePath: string, data: T, options?: WriteOptions): Promise<void> => {
        return this.writeFile<T>(filePath, data, options);
      },
      updateJsonFile: async <T>(filePath: string, updates: Partial<T>, options?: WriteOptions): Promise<T> => {
        return this.updateFile<T>(filePath, updates, options);
      }
    };
  }
}
