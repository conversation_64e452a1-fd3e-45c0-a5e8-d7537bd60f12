// Error handling utilities for the learning platform

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  public errors: string[];

  constructor(errors: string[]) {
    super('Validation failed', 400);
    this.errors = errors;
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource already exists') {
    super(message, 409);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
  }
}

export class ErrorHandler {
  static handle(error: any): { status: number; message: string; errors?: string[] } {
    // Log error for debugging
    console.error('Error occurred:', {
      message: error.message,
      stack: error.stack,
      statusCode: error.statusCode,
      timestamp: new Date().toISOString()
    });

    // Handle known error types
    if (error instanceof ValidationError) {
      return {
        status: error.statusCode,
        message: error.message,
        errors: error.errors
      };
    }

    if (error instanceof AppError) {
      return {
        status: error.statusCode,
        message: error.message
      };
    }

    // Handle specific error types
    if (error.name === 'JsonWebTokenError') {
      return {
        status: 401,
        message: 'Invalid token'
      };
    }

    if (error.name === 'TokenExpiredError') {
      return {
        status: 401,
        message: 'Token expired'
      };
    }

    if (error.code === 'ENOENT') {
      return {
        status: 500,
        message: 'Data file not found'
      };
    }

    if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
      return {
        status: 500,
        message: 'Data corruption detected'
      };
    }

    // Default error response
    return {
      status: 500,
      message: process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : error.message || 'Unknown error occurred'
    };
  }

  static async withErrorHandling<T>(
    operation: () => Promise<T>,
    context: string = 'Operation'
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      console.error(`Error in ${context}:`, error);
      throw error;
    }
  }

  static validateAndSanitize<T>(
    data: any,
    validator: (data: any) => { isValid: boolean; errors: string[] },
    sanitizer?: (data: any) => T
  ): T {
    const validation = validator(data);
    
    if (!validation.isValid) {
      throw new ValidationError(validation.errors);
    }

    return sanitizer ? sanitizer(data) : data;
  }
}

// Rate limiting utility
export class RateLimiter {
  private static requests: Map<string, { count: number; resetTime: number }> = new Map();

  static check(
    identifier: string, 
    maxRequests: number = 100, 
    windowMs: number = 60000
  ): boolean {
    const now = Date.now();
    const key = identifier;
    const record = this.requests.get(key);

    if (!record || now > record.resetTime) {
      this.requests.set(key, {
        count: 1,
        resetTime: now + windowMs
      });
      return true;
    }

    if (record.count >= maxRequests) {
      return false;
    }

    record.count++;
    return true;
  }

  static cleanup(): void {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

// Data backup utility
export class DataBackup {
  static async createBackup(filePath: string): Promise<string> {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
      
      if (fs.existsSync(filePath)) {
        fs.copyFileSync(filePath, backupPath);
        return backupPath;
      }
      
      return '';
    } catch (error) {
      console.error('Backup creation failed:', error);
      return '';
    }
  }

  static async restoreBackup(backupPath: string, targetPath: string): Promise<boolean> {
    try {
      const fs = require('fs');
      
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, targetPath);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Backup restoration failed:', error);
      return false;
    }
  }
}

// Health check utility
export class HealthCheck {
  static async checkDataFiles(): Promise<{ healthy: boolean; issues: string[] }> {
    const issues: string[] = [];
    const fs = require('fs');
    const path = require('path');

    const requiredFiles = [
      'data/apps/learning/users.json',
      'data/apps/learning/enrollments.json',
      'data/apps/learning/payments.json'
    ];

    for (const filePath of requiredFiles) {
      const fullPath = path.join(process.cwd(), filePath);
      
      try {
        if (!fs.existsSync(fullPath)) {
          issues.push(`Missing data file: ${filePath}`);
          continue;
        }

        const content = fs.readFileSync(fullPath, 'utf8');
        JSON.parse(content); // Validate JSON
      } catch (error) {
        issues.push(`Invalid JSON in file: ${filePath}`);
      }
    }

    return {
      healthy: issues.length === 0,
      issues
    };
  }

  static async checkAPIEndpoints(): Promise<{ healthy: boolean; issues: string[] }> {
    const issues: string[] = [];
    
    // This would typically make actual HTTP requests to test endpoints
    // For now, we'll just return a basic check
    
    return {
      healthy: true,
      issues
    };
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static metrics: Map<string, { count: number; totalTime: number; avgTime: number }> = new Map();

  static startTimer(operation: string): () => void {
    const startTime = Date.now();
    
    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.recordMetric(operation, duration);
    };
  }

  private static recordMetric(operation: string, duration: number): void {
    const existing = this.metrics.get(operation) || { count: 0, totalTime: 0, avgTime: 0 };
    
    existing.count++;
    existing.totalTime += duration;
    existing.avgTime = existing.totalTime / existing.count;
    
    this.metrics.set(operation, existing);
  }

  static getMetrics(): Record<string, { count: number; totalTime: number; avgTime: number }> {
    const result: Record<string, any> = {};
    
    for (const [operation, metrics] of this.metrics.entries()) {
      result[operation] = { ...metrics };
    }
    
    return result;
  }

  static clearMetrics(): void {
    this.metrics.clear();
  }
}
