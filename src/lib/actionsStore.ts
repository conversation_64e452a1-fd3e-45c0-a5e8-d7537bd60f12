import fs from 'fs';
import path from 'path';

// Core action interface
export interface Action {
  key: string;
  description: string;
  category: string;
  tags: string[];
  parameters: ActionParameter[];
  metadata: ActionMetadata;
  run: (payload: Record<string, any>, context?: ActionContext) => Promise<any>;
}

// Action parameter definition
export interface ActionParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
}

// Action metadata
export interface ActionMetadata {
  version: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  deprecated?: boolean;
  deprecationMessage?: string;
  permissions?: string[];
  rateLimit?: {
    maxCalls: number;
    windowMs: number;
  };
}

// Action execution context
export interface ActionContext {
  userId?: string;
  sessionId?: string;
  source: 'chatbot' | 'api' | 'dashboard' | 'system';
  timestamp: string;
  requestId: string;
  permissions?: string[];
}

// Action execution result
export interface ActionResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
  context: ActionContext;
}

// Action execution log
export interface ActionLog {
  id: string;
  actionKey: string;
  context: ActionContext;
  payload: Record<string, any>;
  result: ActionResult;
  timestamp: string;
}

// Action registry class
export class ActionsStore {
  private static instance: ActionsStore;
  private actions: Map<string, Action> = new Map();
  private dataDir: string;

  private constructor() {
    this.dataDir = path.join(process.cwd(), 'data/apps/platforms/actionStore');
    this.ensureDataDir();
    this.loadActions();
  }

  public static getInstance(): ActionsStore {
    if (!ActionsStore.instance) {
      ActionsStore.instance = new ActionsStore();
    }
    return ActionsStore.instance;
  }

  private ensureDataDir(): void {
    const dirs = [
      this.dataDir,
      path.join(this.dataDir, 'actions'),
      path.join(this.dataDir, 'logs'),
      path.join(this.dataDir, 'config')
    ];

    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
  }

  private loadActions(): void {
    // Load built-in actions
    this.loadBuiltInActions();
    
    // Load custom actions from JSON files
    this.loadCustomActions();
  }

  private loadBuiltInActions(): void {
    // This will be populated with sample actions
    // Actions are registered programmatically for built-in functionality
  }

  private loadCustomActions(): void {
    const actionsFile = path.join(this.dataDir, 'actions', 'custom.json');
    if (fs.existsSync(actionsFile)) {
      try {
        const data = JSON.parse(fs.readFileSync(actionsFile, 'utf8'));
        // Custom actions would be loaded here
        // For now, we focus on programmatic registration
      } catch (error) {
        console.error('Error loading custom actions:', error);
      }
    }
  }

  // Register a new action
  public registerAction(action: Action): void {
    // Validate action
    this.validateAction(action);
    
    // Add metadata if missing
    if (!action.metadata.createdAt) {
      action.metadata.createdAt = new Date().toISOString();
    }
    action.metadata.updatedAt = new Date().toISOString();

    // Register action
    this.actions.set(action.key, action);
    
    // Log registration
    console.log(`Action registered: ${action.key}`);
  }

  private validateAction(action: Action): void {
    if (!action.key || typeof action.key !== 'string') {
      throw new Error('Action key is required and must be a string');
    }
    
    if (!action.description || typeof action.description !== 'string') {
      throw new Error('Action description is required and must be a string');
    }
    
    if (typeof action.run !== 'function') {
      throw new Error('Action run method is required and must be a function');
    }
    
    // Validate parameters
    if (action.parameters) {
      for (const param of action.parameters) {
        if (!param.name || !param.type) {
          throw new Error('Parameter name and type are required');
        }
      }
    }
  }

  // Execute an action
  public async callAction(
    actionKey: string, 
    payload: Record<string, any> = {}, 
    context?: Partial<ActionContext>
  ): Promise<ActionResult> {
    const startTime = Date.now();
    const fullContext: ActionContext = {
      userId: context?.userId || 'anonymous',
      sessionId: context?.sessionId || this.generateSessionId(),
      source: context?.source || 'api',
      timestamp: new Date().toISOString(),
      requestId: context?.requestId || this.generateRequestId(),
      permissions: context?.permissions || []
    };

    try {
      // Check if action exists
      const action = this.actions.get(actionKey);
      if (!action) {
        throw new Error(`Action not found: ${actionKey}`);
      }

      // Check if action is deprecated
      if (action.metadata.deprecated) {
        console.warn(`Action ${actionKey} is deprecated: ${action.metadata.deprecationMessage}`);
      }

      // Validate permissions
      if (action.metadata.permissions && action.metadata.permissions.length > 0) {
        const hasPermission = action.metadata.permissions.some(perm => 
          fullContext.permissions?.includes(perm)
        );
        if (!hasPermission) {
          throw new Error(`Insufficient permissions for action: ${actionKey}`);
        }
      }

      // Validate payload against parameters
      this.validatePayload(action, payload);

      // Check rate limiting
      await this.checkRateLimit(actionKey, fullContext);

      // Execute action
      const data = await action.run(payload, fullContext);
      
      const result: ActionResult = {
        success: true,
        data,
        executionTime: Date.now() - startTime,
        context: fullContext
      };

      // Log execution
      await this.logExecution(actionKey, payload, result);
      
      return result;

    } catch (error) {
      const result: ActionResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
        context: fullContext
      };

      // Log execution
      await this.logExecution(actionKey, payload, result);

      return result;
    }
  }

  private validatePayload(action: Action, payload: Record<string, any>): void {
    for (const param of action.parameters) {
      const value = payload[param.name];

      // Check required parameters
      if (param.required && (value === undefined || value === null)) {
        throw new Error(`Required parameter missing: ${param.name}`);
      }

      // Skip validation if value is undefined and not required
      if (value === undefined) continue;

      // Type validation
      if (!this.validateType(value, param.type)) {
        throw new Error(`Invalid type for parameter ${param.name}. Expected ${param.type}`);
      }

      // Additional validation
      if (param.validation) {
        this.validateParameterConstraints(param.name, value, param.validation);
      }
    }
  }

  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array':
        return Array.isArray(value);
      default:
        return false;
    }
  }

  private validateParameterConstraints(
    paramName: string,
    value: any,
    validation: NonNullable<ActionParameter['validation']>
  ): void {
    if (validation.min !== undefined && value < validation.min) {
      throw new Error(`Parameter ${paramName} must be >= ${validation.min}`);
    }

    if (validation.max !== undefined && value > validation.max) {
      throw new Error(`Parameter ${paramName} must be <= ${validation.max}`);
    }

    if (validation.pattern && typeof value === 'string') {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        throw new Error(`Parameter ${paramName} does not match required pattern`);
      }
    }

    if (validation.enum && !validation.enum.includes(value)) {
      throw new Error(`Parameter ${paramName} must be one of: ${validation.enum.join(', ')}`);
    }
  }

  private async checkRateLimit(actionKey: string, context: ActionContext): Promise<void> {
    const action = this.actions.get(actionKey);
    if (!action?.metadata.rateLimit) return;

    const { maxCalls, windowMs } = action.metadata.rateLimit;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Get recent executions for this action and user
    const logs = await this.getExecutionLogs({
      actionKey,
      userId: context.userId,
      since: new Date(windowStart).toISOString()
    });

    if (logs.length >= maxCalls) {
      throw new Error(`Rate limit exceeded for action ${actionKey}. Max ${maxCalls} calls per ${windowMs}ms`);
    }
  }

  private async logExecution(
    actionKey: string,
    payload: Record<string, any>,
    result: ActionResult
  ): Promise<void> {
    const log: ActionLog = {
      id: this.generateLogId(),
      actionKey,
      context: result.context,
      payload,
      result,
      timestamp: new Date().toISOString()
    };

    // Write to log file
    const logFile = path.join(this.dataDir, 'logs', `${new Date().toISOString().split('T')[0]}.json`);

    try {
      // Ensure directory exists
      const logDir = path.dirname(logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      let logs: ActionLog[] = [];

      // Use a simple retry mechanism for file operations
      let retries = 3;
      while (retries > 0) {
        try {
          if (fs.existsSync(logFile)) {
            const content = fs.readFileSync(logFile, 'utf8').trim();
            if (content) {
              try {
                logs = JSON.parse(content);
                if (!Array.isArray(logs)) {
                  logs = [];
                }
              } catch (parseError) {
                console.error('Error parsing log file, starting fresh:', parseError);
                logs = [];
              }
            }
          }

          logs.push(log);

          // Write atomically by writing to temp file first
          const tempFile = logFile + '.tmp';
          fs.writeFileSync(tempFile, JSON.stringify(logs, null, 2));
          fs.renameSync(tempFile, logFile);
          break;

        } catch (error) {
          retries--;
          if (retries === 0) {
            throw error;
          }
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }
    } catch (error) {
      console.error('Error logging action execution:', error);
    }
  }

  // List available actions
  public listAvailableActions(): Array<{ key: string; description: string; category: string; tags: string[] }> {
    return Array.from(this.actions.values()).map(action => ({
      key: action.key,
      description: action.description,
      category: action.category,
      tags: action.tags
    }));
  }

  // Get action details
  public getAction(key: string): Action | undefined {
    return this.actions.get(key);
  }

  // Get execution logs
  public async getExecutionLogs(filters?: {
    actionKey?: string;
    userId?: string;
    since?: string;
    limit?: number;
  }): Promise<ActionLog[]> {
    const logs: ActionLog[] = [];
    const logsDir = path.join(this.dataDir, 'logs');

    if (!fs.existsSync(logsDir)) return logs;

    const logFiles = fs.readdirSync(logsDir).filter(file => file.endsWith('.json'));

    for (const file of logFiles) {
      try {
        const content = fs.readFileSync(path.join(logsDir, file), 'utf8').trim();
        if (content) {
          try {
            const fileLogs: ActionLog[] = JSON.parse(content);
            logs.push(...fileLogs);
          } catch (parseError) {
            console.error(`Error parsing log file ${file}:`, parseError);
          }
        }
      } catch (error) {
        console.error(`Error reading log file ${file}:`, error);
      }
    }

    // Apply filters
    let filteredLogs = logs;

    if (filters?.actionKey) {
      filteredLogs = filteredLogs.filter(log => log.actionKey === filters.actionKey);
    }

    if (filters?.userId) {
      filteredLogs = filteredLogs.filter(log => log.context.userId === filters.userId);
    }

    if (filters?.since) {
      const sinceDate = new Date(filters.since);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= sinceDate);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Apply limit
    if (filters?.limit) {
      filteredLogs = filteredLogs.slice(0, filters.limit);
    }

    return filteredLogs;
  }

  // Utility methods
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Utility functions for easy access
export const actionsStore = ActionsStore.getInstance();

export async function callAction(
  actionKey: string,
  payload: Record<string, any> = {},
  context?: Partial<ActionContext>
): Promise<ActionResult> {
  return actionsStore.callAction(actionKey, payload, context);
}

export function listAvailableActions(): Array<{ key: string; description: string; category: string; tags: string[] }> {
  return actionsStore.listAvailableActions();
}

export function registerAction(action: Action): void {
  actionsStore.registerAction(action);
}
