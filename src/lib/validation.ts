// Data validation utilities for the learning platform

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export class Validator {
  static email(email: string): ValidationResult {
    const errors: string[] = [];
    
    if (!email) {
      errors.push('Email is required');
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        errors.push('Invalid email format');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static password(password: string): ValidationResult {
    const errors: string[] = [];
    
    if (!password) {
      errors.push('Password is required');
    } else {
      if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
      }
      if (!/(?=.*[a-z])/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
      }
      if (!/(?=.*[A-Z])/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
      }
      if (!/(?=.*\d)/.test(password)) {
        errors.push('Password must contain at least one number');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static name(name: string): ValidationResult {
    const errors: string[] = [];
    
    if (!name) {
      errors.push('Name is required');
    } else {
      if (name.trim().length < 2) {
        errors.push('Name must be at least 2 characters long');
      }
      if (name.trim().length > 100) {
        errors.push('Name must be less than 100 characters');
      }
      if (!/^[a-zA-Z\s\-'\.]+$/.test(name)) {
        errors.push('Name can only contain letters, spaces, hyphens, apostrophes, and periods');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static courseData(courseData: any): ValidationResult {
    const errors: string[] = [];
    
    if (!courseData.title) {
      errors.push('Course title is required');
    } else if (courseData.title.length < 5) {
      errors.push('Course title must be at least 5 characters long');
    } else if (courseData.title.length > 200) {
      errors.push('Course title must be less than 200 characters');
    }
    
    if (!courseData.description) {
      errors.push('Course description is required');
    } else if (courseData.description.length < 20) {
      errors.push('Course description must be at least 20 characters long');
    } else if (courseData.description.length > 2000) {
      errors.push('Course description must be less than 2000 characters');
    }
    
    if (!courseData.instructor) {
      errors.push('Course instructor is required');
    }
    
    if (!courseData.category) {
      errors.push('Course category is required');
    }
    
    if (!courseData.level || !['Beginner', 'Intermediate', 'Advanced'].includes(courseData.level)) {
      errors.push('Valid course level is required (Beginner, Intermediate, or Advanced)');
    }
    
    if (courseData.price !== undefined) {
      if (typeof courseData.price !== 'number' || courseData.price < 0) {
        errors.push('Course price must be a non-negative number');
      }
      if (courseData.price > 10000) {
        errors.push('Course price cannot exceed $10,000');
      }
    }
    
    if (courseData.duration !== undefined) {
      if (typeof courseData.duration !== 'number' || courseData.duration <= 0) {
        errors.push('Course duration must be a positive number');
      }
      if (courseData.duration > 10000) {
        errors.push('Course duration cannot exceed 10,000 minutes');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static enrollmentData(enrollmentData: any): ValidationResult {
    const errors: string[] = [];
    
    if (!enrollmentData.userId) {
      errors.push('User ID is required');
    }
    
    if (!enrollmentData.courseId) {
      errors.push('Course ID is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static paymentData(paymentData: any): ValidationResult {
    const errors: string[] = [];
    
    if (!paymentData.type) {
      errors.push('Payment type is required');
    } else if (!['course_purchase', 'subscription', 'edutalk_session'].includes(paymentData.type)) {
      errors.push('Invalid payment type');
    }
    
    if (!paymentData.amount) {
      errors.push('Payment amount is required');
    } else {
      if (typeof paymentData.amount !== 'number' || paymentData.amount <= 0) {
        errors.push('Payment amount must be a positive number');
      }
      if (paymentData.amount > 50000) {
        errors.push('Payment amount cannot exceed $50,000');
      }
    }
    
    if (paymentData.type === 'course_purchase' && !paymentData.courseId) {
      errors.push('Course ID is required for course purchases');
    }
    
    if (paymentData.type === 'edutalk_session') {
      if (!paymentData.sessionId) {
        errors.push('Session ID is required for EduTalk sessions');
      }
      if (!paymentData.coachId) {
        errors.push('Coach ID is required for EduTalk sessions');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static progressData(progressData: any): ValidationResult {
    const errors: string[] = [];
    
    if (!progressData.enrollmentId) {
      errors.push('Enrollment ID is required');
    }
    
    if (!progressData.lessonId) {
      errors.push('Lesson ID is required');
    }
    
    if (!progressData.status) {
      errors.push('Progress status is required');
    } else if (!['not_started', 'in_progress', 'completed'].includes(progressData.status)) {
      errors.push('Invalid progress status');
    }
    
    if (progressData.score !== undefined) {
      if (typeof progressData.score !== 'number' || progressData.score < 0 || progressData.score > 100) {
        errors.push('Score must be a number between 0 and 100');
      }
    }
    
    if (progressData.timeSpent !== undefined) {
      if (typeof progressData.timeSpent !== 'number' || progressData.timeSpent < 0) {
        errors.push('Time spent must be a non-negative number');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static userProfile(profileData: any): ValidationResult {
    const errors: string[] = [];
    
    if (profileData.title && profileData.title.length > 200) {
      errors.push('Title must be less than 200 characters');
    }
    
    if (profileData.bio && profileData.bio.length > 2000) {
      errors.push('Bio must be less than 2000 characters');
    }
    
    if (profileData.hourlyRate !== undefined) {
      if (typeof profileData.hourlyRate !== 'number' || profileData.hourlyRate < 0) {
        errors.push('Hourly rate must be a non-negative number');
      }
      if (profileData.hourlyRate > 1000) {
        errors.push('Hourly rate cannot exceed $1,000');
      }
    }
    
    if (profileData.expertise && Array.isArray(profileData.expertise)) {
      if (profileData.expertise.length > 20) {
        errors.push('Cannot have more than 20 expertise areas');
      }
      for (const expertise of profileData.expertise) {
        if (typeof expertise !== 'string' || expertise.length > 100) {
          errors.push('Each expertise area must be a string less than 100 characters');
          break;
        }
      }
    }
    
    if (profileData.languages && Array.isArray(profileData.languages)) {
      if (profileData.languages.length > 10) {
        errors.push('Cannot have more than 10 languages');
      }
      for (const language of profileData.languages) {
        if (typeof language !== 'string' || language.length > 50) {
          errors.push('Each language must be a string less than 50 characters');
          break;
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static sanitizeString(input: string): string {
    if (typeof input !== 'string') return '';
    
    // Remove HTML tags and trim whitespace
    return input
      .replace(/<[^>]*>/g, '')
      .trim()
      .substring(0, 10000); // Limit length
  }

  static sanitizeEmail(email: string): string {
    if (typeof email !== 'string') return '';
    
    return email
      .toLowerCase()
      .trim()
      .substring(0, 254); // RFC 5321 limit
  }

  static sanitizeNumber(input: any, min: number = 0, max: number = Number.MAX_SAFE_INTEGER): number {
    const num = Number(input);
    if (isNaN(num)) return min;
    return Math.max(min, Math.min(max, num));
  }
}
