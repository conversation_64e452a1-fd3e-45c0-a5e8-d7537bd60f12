interface ValidationError {
  type: 'error' | 'warning' | 'info';
  field: string;
  message: string;
  suggestion?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  score: number; // 0-100 quality score
}

interface WorkflowStep {
  id: string;
  type: string;
  title: string;
  description: string;
  required: boolean;
  config?: any;
  options?: string[];
  dependencies?: string[];
  conditions?: any;
}

interface ApprovalLevel {
  level: number;
  name: string;
  approvers: string[];
  required: boolean;
  type: 'individual' | 'committee' | 'any' | 'majority';
  allowDelegation?: boolean;
  escalationDays?: number;
  escalationTo?: string[];
  minimumApprovals?: number;
  conditions?: any;
}

interface WorkflowTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  steps: WorkflowStep[];
  approvalFlow: {
    enabled: boolean;
    type?: 'sequential' | 'parallel' | 'hybrid';
    levels: ApprovalLevel[];
    parallelApprovals?: any[];
    conditionalApprovals?: any[];
  };
  notifications?: any;
}

export class WorkflowValidator {
  static validate(template: WorkflowTemplate): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    let score = 100;

    // Basic template validation
    this.validateBasicInfo(template, errors, warnings);
    
    // Step validation
    this.validateSteps(template.steps, errors, warnings);
    
    // Approval flow validation
    if (template.approvalFlow.enabled) {
      this.validateApprovalFlow(template.approvalFlow, errors, warnings);
    }
    
    // Business rules validation
    this.validateBusinessRules(template, errors, warnings);
    
    // Calculate quality score
    score = this.calculateQualityScore(template, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score
    };
  }

  private static validateBasicInfo(template: WorkflowTemplate, errors: ValidationError[], warnings: ValidationError[]) {
    if (!template.title || template.title.trim().length === 0) {
      errors.push({
        type: 'error',
        field: 'title',
        message: 'Template title is required',
        suggestion: 'Provide a clear, descriptive title for your workflow'
      });
    } else if (template.title.length < 3) {
      warnings.push({
        type: 'warning',
        field: 'title',
        message: 'Template title is very short',
        suggestion: 'Consider using a more descriptive title'
      });
    }

    if (!template.description || template.description.trim().length === 0) {
      warnings.push({
        type: 'warning',
        field: 'description',
        message: 'Template description is missing',
        suggestion: 'Add a description to help users understand the workflow purpose'
      });
    }

    if (!template.category || template.category.trim().length === 0) {
      warnings.push({
        type: 'warning',
        field: 'category',
        message: 'Template category is missing',
        suggestion: 'Categorize your template for better organization'
      });
    }
  }

  private static validateSteps(steps: WorkflowStep[], errors: ValidationError[], warnings: ValidationError[]) {
    if (!steps || steps.length === 0) {
      errors.push({
        type: 'error',
        field: 'steps',
        message: 'Workflow must have at least one step',
        suggestion: 'Add workflow steps to define the process'
      });
      return;
    }

    const stepIds = new Set<string>();
    const duplicateIds: string[] = [];

    steps.forEach((step, index) => {
      // Check for duplicate IDs
      if (stepIds.has(step.id)) {
        duplicateIds.push(step.id);
      } else {
        stepIds.add(step.id);
      }

      // Validate step basic info
      if (!step.title || step.title.trim().length === 0) {
        errors.push({
          type: 'error',
          field: `steps[${index}].title`,
          message: `Step ${index + 1} title is required`,
          suggestion: 'Provide a clear title for each step'
        });
      }

      if (!step.type) {
        errors.push({
          type: 'error',
          field: `steps[${index}].type`,
          message: `Step ${index + 1} type is required`,
          suggestion: 'Select a step type (input, table, file, etc.)'
        });
      }

      // Validate step-specific configuration
      this.validateStepConfig(step, index, errors, warnings);

      // Check dependencies
      if (step.dependencies) {
        step.dependencies.forEach(depId => {
          if (!stepIds.has(depId)) {
            errors.push({
              type: 'error',
              field: `steps[${index}].dependencies`,
              message: `Step ${index + 1} depends on non-existent step: ${depId}`,
              suggestion: 'Ensure all step dependencies reference valid step IDs'
            });
          }
        });
      }
    });

    // Report duplicate IDs
    duplicateIds.forEach(id => {
      errors.push({
        type: 'error',
        field: 'steps',
        message: `Duplicate step ID found: ${id}`,
        suggestion: 'Ensure all step IDs are unique'
      });
    });

    // Check for circular dependencies
    this.checkCircularDependencies(steps, errors);
  }

  private static validateStepConfig(step: WorkflowStep, index: number, errors: ValidationError[], warnings: ValidationError[]) {
    switch (step.type) {
      case 'input':
        if (step.config?.validation?.maxLength && step.config.validation.maxLength < step.config?.validation?.minLength) {
          errors.push({
            type: 'error',
            field: `steps[${index}].config.validation`,
            message: `Step ${index + 1}: Max length cannot be less than min length`,
            suggestion: 'Ensure max length is greater than or equal to min length'
          });
        }
        break;

      case 'table':
        if (!step.config?.columns || step.config.columns.length === 0) {
          errors.push({
            type: 'error',
            field: `steps[${index}].config.columns`,
            message: `Step ${index + 1}: Table step must have at least one column`,
            suggestion: 'Define table columns with keys, labels, and types'
          });
        } else {
          const columnKeys = new Set();
          step.config.columns.forEach((col: any, colIndex: number) => {
            if (!col.key) {
              errors.push({
                type: 'error',
                field: `steps[${index}].config.columns[${colIndex}].key`,
                message: `Step ${index + 1}, Column ${colIndex + 1}: Column key is required`,
                suggestion: 'Provide a unique key for each column'
              });
            } else if (columnKeys.has(col.key)) {
              errors.push({
                type: 'error',
                field: `steps[${index}].config.columns[${colIndex}].key`,
                message: `Step ${index + 1}: Duplicate column key: ${col.key}`,
                suggestion: 'Ensure all column keys are unique'
              });
            } else {
              columnKeys.add(col.key);
            }

            if (!col.label) {
              warnings.push({
                type: 'warning',
                field: `steps[${index}].config.columns[${colIndex}].label`,
                message: `Step ${index + 1}, Column ${colIndex + 1}: Column label is missing`,
                suggestion: 'Provide user-friendly labels for columns'
              });
            }
          });
        }
        break;

      case 'file':
        if (step.config?.max && step.config.max < 1) {
          errors.push({
            type: 'error',
            field: `steps[${index}].config.max`,
            message: `Step ${index + 1}: Maximum files must be at least 1`,
            suggestion: 'Set a reasonable maximum number of files'
          });
        }
        break;

      case 'select':
      case 'multiselect':
        if (!step.options || step.options.length === 0) {
          errors.push({
            type: 'error',
            field: `steps[${index}].options`,
            message: `Step ${index + 1}: Select step must have at least one option`,
            suggestion: 'Provide options for users to choose from'
          });
        }
        break;
    }
  }

  private static validateApprovalFlow(approvalFlow: any, errors: ValidationError[], warnings: ValidationError[]) {
    if (!approvalFlow.levels || approvalFlow.levels.length === 0) {
      errors.push({
        type: 'error',
        field: 'approvalFlow.levels',
        message: 'Approval flow must have at least one level',
        suggestion: 'Define approval levels with approvers'
      });
      return;
    }

    const levelNumbers = new Set<number>();
    
    approvalFlow.levels.forEach((level: ApprovalLevel, index: number) => {
      // Check for duplicate level numbers
      if (levelNumbers.has(level.level)) {
        errors.push({
          type: 'error',
          field: `approvalFlow.levels[${index}].level`,
          message: `Duplicate approval level number: ${level.level}`,
          suggestion: 'Ensure all approval levels have unique numbers'
        });
      } else {
        levelNumbers.add(level.level);
      }

      // Validate level configuration
      if (!level.name || level.name.trim().length === 0) {
        errors.push({
          type: 'error',
          field: `approvalFlow.levels[${index}].name`,
          message: `Approval level ${level.level} name is required`,
          suggestion: 'Provide a descriptive name for each approval level'
        });
      }

      if (!level.approvers || level.approvers.length === 0) {
        errors.push({
          type: 'error',
          field: `approvalFlow.levels[${index}].approvers`,
          message: `Approval level ${level.level} must have at least one approver`,
          suggestion: 'Assign approvers to each approval level'
        });
      }

      // Validate approval type specific rules
      if (level.type === 'majority' && level.approvers && level.approvers.length < 2) {
        warnings.push({
          type: 'warning',
          field: `approvalFlow.levels[${index}].type`,
          message: `Approval level ${level.level}: Majority approval with less than 2 approvers`,
          suggestion: 'Consider using "individual" or "any" type for single approver'
        });
      }

      if (level.type === 'committee' && level.approvers && level.approvers.length > 5) {
        warnings.push({
          type: 'warning',
          field: `approvalFlow.levels[${index}].approvers`,
          message: `Approval level ${level.level}: Committee approval with many approvers may be slow`,
          suggestion: 'Consider using "majority" type for large groups'
        });
      }

      // Validate escalation configuration
      if (level.escalationDays && level.escalationDays < 1) {
        errors.push({
          type: 'error',
          field: `approvalFlow.levels[${index}].escalationDays`,
          message: `Approval level ${level.level}: Escalation days must be at least 1`,
          suggestion: 'Set a reasonable escalation timeframe'
        });
      }

      if (level.escalationTo && level.escalationTo.length === 0) {
        warnings.push({
          type: 'warning',
          field: `approvalFlow.levels[${index}].escalationTo`,
          message: `Approval level ${level.level}: Escalation configured but no escalation targets`,
          suggestion: 'Specify who to escalate to or remove escalation configuration'
        });
      }
    });
  }

  private static validateBusinessRules(template: WorkflowTemplate, errors: ValidationError[], warnings: ValidationError[]) {
    // Check for reasonable workflow complexity
    if (template.steps.length > 20) {
      warnings.push({
        type: 'warning',
        field: 'steps',
        message: 'Workflow has many steps and may be complex for users',
        suggestion: 'Consider breaking down into smaller workflows or grouping related steps'
      });
    }

    if (template.approvalFlow.enabled && template.approvalFlow.levels.length > 5) {
      warnings.push({
        type: 'warning',
        field: 'approvalFlow.levels',
        message: 'Workflow has many approval levels and may be slow',
        suggestion: 'Consider consolidating approval levels or using parallel approvals'
      });
    }

    // Check for required steps without approval
    const hasRequiredSteps = template.steps.some(step => step.required);
    if (hasRequiredSteps && !template.approvalFlow.enabled) {
      warnings.push({
        type: 'warning',
        field: 'approvalFlow',
        message: 'Workflow has required steps but no approval process',
        suggestion: 'Consider adding approval flow for workflows with required data'
      });
    }
  }

  private static checkCircularDependencies(steps: WorkflowStep[], errors: ValidationError[]) {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (stepId: string): boolean => {
      if (recursionStack.has(stepId)) {
        return true;
      }
      if (visited.has(stepId)) {
        return false;
      }

      visited.add(stepId);
      recursionStack.add(stepId);

      const step = steps.find(s => s.id === stepId);
      if (step?.dependencies) {
        for (const depId of step.dependencies) {
          if (hasCycle(depId)) {
            return true;
          }
        }
      }

      recursionStack.delete(stepId);
      return false;
    };

    for (const step of steps) {
      if (!visited.has(step.id) && hasCycle(step.id)) {
        errors.push({
          type: 'error',
          field: 'steps',
          message: `Circular dependency detected involving step: ${step.id}`,
          suggestion: 'Remove circular dependencies between steps'
        });
        break;
      }
    }
  }

  private static calculateQualityScore(template: WorkflowTemplate, errors: ValidationError[], warnings: ValidationError[]): number {
    let score = 100;

    // Deduct points for errors and warnings
    score -= errors.length * 20;
    score -= warnings.length * 5;

    // Bonus points for good practices
    if (template.description && template.description.length > 50) {
      score += 5;
    }

    if (template.category && template.category.trim().length > 0) {
      score += 5;
    }

    if (template.steps.every(step => step.description && step.description.length > 0)) {
      score += 10;
    }

    if (template.approvalFlow.enabled && template.approvalFlow.levels.length > 0) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }
}
