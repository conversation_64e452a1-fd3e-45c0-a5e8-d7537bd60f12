import { readJsonFileWithCache, writeJsonFile, invalidateCache } from './file-utils';

export interface PaymentMethod {
  id: string;
  userId: string;
  type: 'credit_card' | 'debit_card' | 'paypal' | 'bank_transfer' | 'apple_pay' | 'google_pay';
  isDefault: boolean;
  details: {
    // For cards
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
    holderName?: string;
    // For PayPal
    email?: string;
    // For bank transfer
    bankName?: string;
    accountNumber?: string;
  };
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  createdAt: Date;
  isActive: boolean;
}

export interface PaymentTransaction {
  id: string;
  userId: string;
  bookingId: string;
  paymentMethodId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  paymentIntentId?: string; // For Stripe or other payment processors
  transactionId?: string; // External transaction ID
  failureReason?: string;
  refundAmount?: number;
  refundReason?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  description: string;
  metadata: Record<string, any>;
  clientSecret?: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled';
}

const PAYMENT_METHODS_FILE = 'payment-methods.json';
const PAYMENT_TRANSACTIONS_FILE = 'payment-transactions.json';

export class PaymentService {
  /**
   * Get user's payment methods
   */
  async getUserPaymentMethods(userId: string): Promise<PaymentMethod[]> {
    const paymentMethods = await readJsonFileWithCache<PaymentMethod[]>(PAYMENT_METHODS_FILE);
    return paymentMethods.filter(method => method.userId === userId && method.isActive);
  }

  /**
   * Add a new payment method
   */
  async addPaymentMethod(paymentMethod: Omit<PaymentMethod, 'id' | 'createdAt' | 'isActive'>): Promise<PaymentMethod> {
    const paymentMethods = await readJsonFileWithCache<PaymentMethod[]>(PAYMENT_METHODS_FILE);
    
    const newPaymentMethod: PaymentMethod = {
      ...paymentMethod,
      id: `pm-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      isActive: true
    };

    // If this is set as default, unset other default methods for this user
    if (newPaymentMethod.isDefault) {
      const updatedMethods = paymentMethods.map(method => 
        method.userId === paymentMethod.userId 
          ? { ...method, isDefault: false }
          : method
      );
      paymentMethods.splice(0, paymentMethods.length, ...updatedMethods);
    }

    paymentMethods.push(newPaymentMethod);
    await writeJsonFile(PAYMENT_METHODS_FILE, paymentMethods);
    invalidateCache(PAYMENT_METHODS_FILE);

    return newPaymentMethod;
  }

  /**
   * Update payment method
   */
  async updatePaymentMethod(
    paymentMethodId: string, 
    updates: Partial<Omit<PaymentMethod, 'id' | 'userId' | 'createdAt'>>
  ): Promise<PaymentMethod> {
    const paymentMethods = await readJsonFileWithCache<PaymentMethod[]>(PAYMENT_METHODS_FILE);
    const methodIndex = paymentMethods.findIndex(method => method.id === paymentMethodId);

    if (methodIndex === -1) {
      throw new Error('Payment method not found');
    }

    const method = paymentMethods[methodIndex];

    // If setting as default, unset other default methods for this user
    if (updates.isDefault) {
      const updatedMethods = paymentMethods.map(m => 
        m.userId === method.userId && m.id !== paymentMethodId
          ? { ...m, isDefault: false }
          : m
      );
      paymentMethods.splice(0, paymentMethods.length, ...updatedMethods);
    }

    paymentMethods[methodIndex] = { ...method, ...updates };
    await writeJsonFile(PAYMENT_METHODS_FILE, paymentMethods);
    invalidateCache(PAYMENT_METHODS_FILE);

    return paymentMethods[methodIndex];
  }

  /**
   * Delete payment method
   */
  async deletePaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      await this.updatePaymentMethod(paymentMethodId, { isActive: false });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create payment intent
   */
  async createPaymentIntent(
    userId: string,
    amount: number,
    currency: string = 'USD',
    description: string,
    metadata: Record<string, any> = {}
  ): Promise<PaymentIntent> {
    // In a real implementation, this would call Stripe or another payment processor
    const paymentIntent: PaymentIntent = {
      id: `pi-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      amount,
      currency,
      description,
      metadata: { ...metadata, userId },
      clientSecret: `pi_${Math.random().toString(36).substr(2, 24)}_secret_${Math.random().toString(36).substr(2, 16)}`,
      status: 'requires_payment_method'
    };

    return paymentIntent;
  }

  /**
   * Process payment
   */
  async processPayment(
    userId: string,
    bookingId: string,
    paymentMethodId: string,
    amount: number,
    currency: string = 'USD'
  ): Promise<PaymentTransaction> {
    const transactions = await readJsonFileWithCache<PaymentTransaction[]>(PAYMENT_TRANSACTIONS_FILE);
    
    // Verify payment method exists and belongs to user
    const paymentMethods = await this.getUserPaymentMethods(userId);
    const paymentMethod = paymentMethods.find(method => method.id === paymentMethodId);
    
    if (!paymentMethod) {
      throw new Error('Payment method not found or not accessible');
    }

    const transaction: PaymentTransaction = {
      id: `txn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      userId,
      bookingId,
      paymentMethodId,
      amount,
      currency,
      status: 'processing',
      paymentIntentId: `pi-${Date.now()}`,
      transactionId: `ext-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        paymentMethodType: paymentMethod.type
      }
    };

    // Simulate payment processing
    setTimeout(async () => {
      try {
        // Simulate success/failure (90% success rate)
        const isSuccess = Math.random() > 0.1;
        
        if (isSuccess) {
          transaction.status = 'completed';
          transaction.completedAt = new Date();
        } else {
          transaction.status = 'failed';
          transaction.failureReason = 'Insufficient funds';
        }
        
        transaction.updatedAt = new Date();
        
        // Update transaction in storage
        const currentTransactions = await readJsonFileWithCache<PaymentTransaction[]>(PAYMENT_TRANSACTIONS_FILE);
        const txnIndex = currentTransactions.findIndex(t => t.id === transaction.id);
        if (txnIndex !== -1) {
          currentTransactions[txnIndex] = transaction;
          await writeJsonFile(PAYMENT_TRANSACTIONS_FILE, currentTransactions);
          invalidateCache(PAYMENT_TRANSACTIONS_FILE);
        }
      } catch (error) {
        console.error('Error updating transaction status:', error);
      }
    }, 2000); // Simulate 2-second processing time

    transactions.push(transaction);
    await writeJsonFile(PAYMENT_TRANSACTIONS_FILE, transactions);
    invalidateCache(PAYMENT_TRANSACTIONS_FILE);

    return transaction;
  }

  /**
   * Get transaction by ID
   */
  async getTransaction(transactionId: string): Promise<PaymentTransaction | null> {
    const transactions = await readJsonFileWithCache<PaymentTransaction[]>(PAYMENT_TRANSACTIONS_FILE);
    return transactions.find(transaction => transaction.id === transactionId) || null;
  }

  /**
   * Get user's payment transactions
   */
  async getUserTransactions(userId: string): Promise<PaymentTransaction[]> {
    const transactions = await readJsonFileWithCache<PaymentTransaction[]>(PAYMENT_TRANSACTIONS_FILE);
    return transactions.filter(transaction => transaction.userId === userId);
  }

  /**
   * Refund payment
   */
  async refundPayment(
    transactionId: string,
    refundAmount?: number,
    reason?: string
  ): Promise<PaymentTransaction> {
    const transactions = await readJsonFileWithCache<PaymentTransaction[]>(PAYMENT_TRANSACTIONS_FILE);
    const transactionIndex = transactions.findIndex(t => t.id === transactionId);

    if (transactionIndex === -1) {
      throw new Error('Transaction not found');
    }

    const transaction = transactions[transactionIndex];

    if (transaction.status !== 'completed') {
      throw new Error('Can only refund completed transactions');
    }

    const refundAmountFinal = refundAmount || transaction.amount;

    if (refundAmountFinal > transaction.amount) {
      throw new Error('Refund amount cannot exceed original transaction amount');
    }

    transaction.status = 'refunded';
    transaction.refundAmount = refundAmountFinal;
    transaction.refundReason = reason;
    transaction.updatedAt = new Date();

    await writeJsonFile(PAYMENT_TRANSACTIONS_FILE, transactions);
    invalidateCache(PAYMENT_TRANSACTIONS_FILE);

    return transaction;
  }

  /**
   * Get payment statistics for a user
   */
  async getUserPaymentStats(userId: string): Promise<{
    totalTransactions: number;
    totalSpent: number;
    totalRefunded: number;
    successRate: number;
    averageTransactionAmount: number;
  }> {
    const transactions = await this.getUserTransactions(userId);
    
    const totalTransactions = transactions.length;
    const completedTransactions = transactions.filter(t => t.status === 'completed');
    const totalSpent = completedTransactions.reduce((sum, t) => sum + t.amount, 0);
    const totalRefunded = transactions
      .filter(t => t.status === 'refunded')
      .reduce((sum, t) => sum + (t.refundAmount || 0), 0);
    const successRate = totalTransactions > 0 ? completedTransactions.length / totalTransactions : 0;
    const averageTransactionAmount = completedTransactions.length > 0 
      ? totalSpent / completedTransactions.length 
      : 0;

    return {
      totalTransactions,
      totalSpent,
      totalRefunded,
      successRate,
      averageTransactionAmount
    };
  }
}

// Export a singleton instance
export const paymentService = new PaymentService();
