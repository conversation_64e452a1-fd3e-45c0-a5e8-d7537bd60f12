import { UserPreferences } from '@/types/travel-planner';

export interface NaturalLanguageInput {
  text: string;
  userId?: string;
}

export interface ParsedTripCriteria {
  destination?: string;
  duration?: number;
  budget?: number;
  groupSize?: number;
  travelStyle?: 'budget' | 'comfort' | 'luxury';
  activities?: string[];
  accommodationType?: string[];
  departureDate?: Date;
  returnDate?: Date;
  preferences?: {
    preferredAirlines?: string[];
    preferredAccommodationTypes?: string[];
    preferredActivities?: string[];
  };
}

export interface TripRecommendation {
  destination: string;
  confidence: number;
  reasons: string[];
  suggestedDuration: number;
  estimatedBudget: {
    min: number;
    max: number;
  };
  bestTimeToVisit: string;
  highlights: string[];
  activities: string[];
}

export class AIService {
  /**
   * Parse natural language input to extract trip criteria
   */
  async parseNaturalLanguageInput(input: NaturalLanguageInput): Promise<ParsedTripCriteria> {
    const text = input.text.toLowerCase();
    const criteria: ParsedTripCriteria = {};

    // Extract destination
    const destinations = [
      'paris', 'london', 'tokyo', 'new york', 'bali', 'rome', 'barcelona', 
      'dubai', 'singapore', 'thailand', 'japan', 'italy', 'spain', 'france',
      'greece', 'turkey', 'egypt', 'morocco', 'india', 'china', 'australia',
      'brazil', 'argentina', 'mexico', 'canada', 'iceland', 'norway', 'sweden'
    ];
    
    for (const dest of destinations) {
      if (text.includes(dest)) {
        criteria.destination = dest.charAt(0).toUpperCase() + dest.slice(1);
        break;
      }
    }

    // Extract duration
    const durationMatch = text.match(/(\d+)\s*(day|days|week|weeks|month|months)/i);
    if (durationMatch) {
      const value = parseInt(durationMatch[1]);
      const unit = durationMatch[2].toLowerCase();
      
      if (unit.includes('week')) {
        criteria.duration = value * 7;
      } else if (unit.includes('month')) {
        criteria.duration = value * 30;
      } else {
        criteria.duration = value;
      }
    }

    // Extract budget
    const budgetMatch = text.match(/\$(\d+(?:,\d+)?)/);
    if (budgetMatch) {
      criteria.budget = parseInt(budgetMatch[1].replace(',', ''));
    }

    // Extract group size
    const peopleMatch = text.match(/(\d+)\s*(person|people|guest|guests|traveler|travelers)/i);
    if (peopleMatch) {
      criteria.groupSize = parseInt(peopleMatch[1]);
    }

    // Extract travel style
    if (text.includes('budget') || text.includes('cheap') || text.includes('affordable')) {
      criteria.travelStyle = 'budget';
    } else if (text.includes('luxury') || text.includes('premium') || text.includes('high-end')) {
      criteria.travelStyle = 'luxury';
    } else {
      criteria.travelStyle = 'comfort';
    }

    // Extract activities
    const activityKeywords = {
      'adventure': ['adventure', 'hiking', 'climbing', 'extreme', 'adrenaline'],
      'cultural': ['culture', 'museum', 'history', 'heritage', 'art'],
      'relaxation': ['relax', 'spa', 'beach', 'peaceful', 'calm'],
      'nightlife': ['nightlife', 'party', 'club', 'bar', 'entertainment'],
      'food': ['food', 'cuisine', 'restaurant', 'culinary', 'dining'],
      'shopping': ['shopping', 'market', 'boutique', 'mall'],
      'nature': ['nature', 'wildlife', 'safari', 'national park', 'outdoor']
    };

    criteria.activities = [];
    for (const [activity, keywords] of Object.entries(activityKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        criteria.activities.push(activity);
      }
    }

    // Extract accommodation preferences
    const accommodationTypes = ['hotel', 'resort', 'apartment', 'villa', 'hostel'];
    criteria.accommodationType = accommodationTypes.filter(type => text.includes(type));

    // Extract dates (basic implementation)
    const datePatterns = [
      /(\w+)\s+(\d{1,2}),?\s+(\d{4})/g, // "January 15, 2024"
      /(\d{1,2})\/(\d{1,2})\/(\d{4})/g,  // "01/15/2024"
      /(\d{4})-(\d{1,2})-(\d{1,2})/g     // "2024-01-15"
    ];

    for (const pattern of datePatterns) {
      const matches = [...text.matchAll(pattern)];
      if (matches.length > 0) {
        // Simple date parsing - in a real app, use a proper date parsing library
        try {
          criteria.departureDate = new Date(matches[0][0]);
          if (matches.length > 1) {
            criteria.returnDate = new Date(matches[1][0]);
          }
        } catch (error) {
          // Ignore date parsing errors
        }
        break;
      }
    }

    return criteria;
  }

  /**
   * Get AI-powered destination recommendations based on user preferences and criteria
   */
  async getDestinationRecommendations(
    criteria: ParsedTripCriteria,
    userPreferences?: UserPreferences
  ): Promise<TripRecommendation[]> {
    // This is a mock implementation. In a real app, this would call an AI service
    // like OpenAI, Google's Travel Partner API, or a custom ML model
    
    const recommendations: TripRecommendation[] = [];
    
    // Mock recommendation logic based on criteria
    if (criteria.activities?.includes('adventure')) {
      recommendations.push({
        destination: 'New Zealand',
        confidence: 0.95,
        reasons: ['World-class adventure activities', 'Stunning natural landscapes', 'Safe for travelers'],
        suggestedDuration: 14,
        estimatedBudget: { min: 3000, max: 5000 },
        bestTimeToVisit: 'October to April',
        highlights: ['Bungee jumping', 'Skydiving', 'Hiking', 'Lord of the Rings locations'],
        activities: ['adventure', 'nature', 'cultural']
      });
    }

    if (criteria.activities?.includes('cultural') || criteria.destination?.toLowerCase().includes('europe')) {
      recommendations.push({
        destination: 'Italy',
        confidence: 0.90,
        reasons: ['Rich cultural heritage', 'World-class museums', 'Amazing cuisine'],
        suggestedDuration: 10,
        estimatedBudget: { min: 2000, max: 4000 },
        bestTimeToVisit: 'April to June, September to October',
        highlights: ['Colosseum', 'Vatican Museums', 'Tuscany', 'Venice canals'],
        activities: ['cultural', 'food', 'art']
      });
    }

    if (criteria.activities?.includes('relaxation') || criteria.travelStyle === 'luxury') {
      recommendations.push({
        destination: 'Maldives',
        confidence: 0.88,
        reasons: ['Ultimate relaxation', 'Luxury resorts', 'Crystal clear waters'],
        suggestedDuration: 7,
        estimatedBudget: { min: 4000, max: 8000 },
        bestTimeToVisit: 'November to April',
        highlights: ['Overwater bungalows', 'Spa treatments', 'Snorkeling', 'Private beaches'],
        activities: ['relaxation', 'nature', 'luxury']
      });
    }

    if (criteria.travelStyle === 'budget' || criteria.budget && criteria.budget < 2000) {
      recommendations.push({
        destination: 'Thailand',
        confidence: 0.85,
        reasons: ['Excellent value for money', 'Diverse experiences', 'Friendly locals'],
        suggestedDuration: 12,
        estimatedBudget: { min: 1200, max: 2500 },
        bestTimeToVisit: 'November to March',
        highlights: ['Bangkok temples', 'Island hopping', 'Street food', 'Elephant sanctuaries'],
        activities: ['cultural', 'food', 'nature', 'adventure']
      });
    }

    // If no specific recommendations, provide popular destinations
    if (recommendations.length === 0) {
      recommendations.push(
        {
          destination: 'Japan',
          confidence: 0.80,
          reasons: ['Unique culture', 'Excellent infrastructure', 'Safe for travelers'],
          suggestedDuration: 10,
          estimatedBudget: { min: 2500, max: 4500 },
          bestTimeToVisit: 'March to May, September to November',
          highlights: ['Cherry blossoms', 'Mount Fuji', 'Tokyo', 'Traditional ryokans'],
          activities: ['cultural', 'food', 'nature']
        },
        {
          destination: 'France',
          confidence: 0.75,
          reasons: ['World-class cuisine', 'Rich history', 'Romantic atmosphere'],
          suggestedDuration: 8,
          estimatedBudget: { min: 2200, max: 4200 },
          bestTimeToVisit: 'April to June, September to October',
          highlights: ['Eiffel Tower', 'Louvre Museum', 'French Riviera', 'Wine regions'],
          activities: ['cultural', 'food', 'art']
        }
      );
    }

    // Sort by confidence score
    return recommendations.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Generate personalized trip suggestions based on user history and preferences
   */
  async getPersonalizedSuggestions(
    userId: string,
    userPreferences?: UserPreferences
  ): Promise<TripRecommendation[]> {
    // This would analyze user's booking history, preferences, and behavior
    // For now, return some mock personalized suggestions
    
    const suggestions: TripRecommendation[] = [];
    
    if (userPreferences?.savedDestinations.length) {
      // Suggest similar destinations to saved ones
      suggestions.push({
        destination: 'Portugal',
        confidence: 0.85,
        reasons: ['Similar to your saved destinations', 'Great value', 'Beautiful coastline'],
        suggestedDuration: 8,
        estimatedBudget: { min: 1800, max: 3200 },
        bestTimeToVisit: 'May to September',
        highlights: ['Lisbon', 'Porto', 'Algarve beaches', 'Sintra palaces'],
        activities: ['cultural', 'food', 'nature']
      });
    }

    if (userPreferences?.travelStyle === 'luxury') {
      suggestions.push({
        destination: 'Switzerland',
        confidence: 0.90,
        reasons: ['Luxury travel destination', 'Stunning Alpine scenery', 'Premium experiences'],
        suggestedDuration: 7,
        estimatedBudget: { min: 4000, max: 7000 },
        bestTimeToVisit: 'June to September, December to March',
        highlights: ['Swiss Alps', 'Luxury trains', 'Fine dining', 'Exclusive resorts'],
        activities: ['luxury', 'nature', 'adventure']
      });
    }

    return suggestions;
  }

  /**
   * Enhance trip options with AI insights
   */
  async enhanceTripOptions(
    destination: string,
    criteria: ParsedTripCriteria,
    userPreferences?: UserPreferences
  ): Promise<{
    insights: string[];
    recommendations: string[];
    warnings: string[];
    alternatives: string[];
  }> {
    // Mock AI insights - in a real app, this would use ML models
    return {
      insights: [
        `Best time to visit ${destination} is during shoulder season for better prices`,
        'Consider booking flights 6-8 weeks in advance for optimal pricing',
        'Local transportation passes can save up to 30% on travel costs'
      ],
      recommendations: [
        'Book accommodations in the city center for easy access to attractions',
        'Try local street food for authentic culinary experiences',
        'Download offline maps and translation apps before traveling'
      ],
      warnings: [
        'Peak season prices may be 40-60% higher than estimated',
        'Some attractions require advance booking during busy periods',
        'Weather can be unpredictable - pack layers'
      ],
      alternatives: [
        'Consider nearby destinations for similar experiences at lower cost',
        'Extend your trip by 2-3 days to justify long-haul flight costs',
        'Split time between multiple cities for diverse experiences'
      ]
    };
  }
}

// Export a singleton instance
export const aiService = new AIService();
