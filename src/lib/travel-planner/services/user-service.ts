import { readJsonFileWithCache, writeJsonFile, invalidateCache } from './file-utils';

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  lastLoginAt?: Date;
  isActive: boolean;
  profile: {
    dateOfBirth?: Date;
    nationality?: string;
    passportNumber?: string;
    emergencyContact?: {
      name: string;
      phone: string;
      relationship: string;
    };
    loyaltyPrograms?: {
      airline: string;
      membershipNumber: string;
    }[];
  };
  settings: {
    currency: string;
    language: string;
    timezone: string;
    emailNotifications: boolean;
    smsNotifications: boolean;
    marketingEmails: boolean;
  };
}

export interface UserSession {
  userId: string;
  sessionId: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
  deviceInfo?: string;
  ipAddress?: string;
}

const USERS_FILE = 'users.json';
const SESSIONS_FILE = 'user-sessions.json';

export class UserService {
  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<User | null> {
    const users = await readJsonFileWithCache<User[]>(USERS_FILE);
    return users.find(user => user.id === userId) || null;
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    const users = await readJsonFileWithCache<User[]>(USERS_FILE);
    return users.find(user => user.email.toLowerCase() === email.toLowerCase()) || null;
  }

  /**
   * Create a new user
   */
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'isActive'>): Promise<User> {
    const users = await readJsonFileWithCache<User[]>(USERS_FILE);
    
    // Check if user already exists
    const existingUser = await this.getUserByEmail(userData.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    const newUser: User = {
      ...userData,
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      isActive: true
    };

    users.push(newUser);
    await writeJsonFile(USERS_FILE, users);
    invalidateCache(USERS_FILE);

    return newUser;
  }

  /**
   * Update user profile
   */
  async updateUser(userId: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): Promise<User> {
    const users = await readJsonFileWithCache<User[]>(USERS_FILE);
    const userIndex = users.findIndex(user => user.id === userId);

    if (userIndex === -1) {
      throw new Error('User not found');
    }

    users[userIndex] = { ...users[userIndex], ...updates };
    await writeJsonFile(USERS_FILE, users);
    invalidateCache(USERS_FILE);

    return users[userIndex];
  }

  /**
   * Update last login time
   */
  async updateLastLogin(userId: string): Promise<void> {
    await this.updateUser(userId, { lastLoginAt: new Date() });
  }

  /**
   * Create user session
   */
  async createSession(userId: string, deviceInfo?: string, ipAddress?: string): Promise<UserSession> {
    const sessions = await readJsonFileWithCache<UserSession[]>(SESSIONS_FILE);
    
    const session: UserSession = {
      userId,
      sessionId: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      isActive: true,
      deviceInfo,
      ipAddress
    };

    sessions.push(session);
    await writeJsonFile(SESSIONS_FILE, sessions);
    invalidateCache(SESSIONS_FILE);

    // Update last login
    await this.updateLastLogin(userId);

    return session;
  }

  /**
   * Get active session
   */
  async getSession(sessionId: string): Promise<UserSession | null> {
    const sessions = await readJsonFileWithCache<UserSession[]>(SESSIONS_FILE);
    const session = sessions.find(s => s.sessionId === sessionId && s.isActive);
    
    if (!session) return null;
    
    // Check if session is expired
    if (new Date() > new Date(session.expiresAt)) {
      await this.invalidateSession(sessionId);
      return null;
    }

    return session;
  }

  /**
   * Invalidate session
   */
  async invalidateSession(sessionId: string): Promise<void> {
    const sessions = await readJsonFileWithCache<UserSession[]>(SESSIONS_FILE);
    const sessionIndex = sessions.findIndex(s => s.sessionId === sessionId);

    if (sessionIndex !== -1) {
      sessions[sessionIndex].isActive = false;
      await writeJsonFile(SESSIONS_FILE, sessions);
      invalidateCache(SESSIONS_FILE);
    }
  }

  /**
   * Get user's active sessions
   */
  async getUserSessions(userId: string): Promise<UserSession[]> {
    const sessions = await readJsonFileWithCache<UserSession[]>(SESSIONS_FILE);
    return sessions.filter(s => s.userId === userId && s.isActive && new Date() < new Date(s.expiresAt));
  }

  /**
   * Invalidate all user sessions
   */
  async invalidateAllUserSessions(userId: string): Promise<void> {
    const sessions = await readJsonFileWithCache<UserSession[]>(SESSIONS_FILE);
    const updatedSessions = sessions.map(session => 
      session.userId === userId ? { ...session, isActive: false } : session
    );

    await writeJsonFile(SESSIONS_FILE, updatedSessions);
    invalidateCache(SESSIONS_FILE);
  }

  /**
   * Get all users (admin function)
   */
  async getAllUsers(): Promise<User[]> {
    return await readJsonFileWithCache<User[]>(USERS_FILE);
  }

  /**
   * Delete user (soft delete)
   */
  async deleteUser(userId: string): Promise<boolean> {
    try {
      await this.updateUser(userId, { isActive: false });
      await this.invalidateAllUserSessions(userId);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Search users by name or email
   */
  async searchUsers(query: string): Promise<User[]> {
    const users = await readJsonFileWithCache<User[]>(USERS_FILE);
    const lowercaseQuery = query.toLowerCase();
    
    return users.filter(user => 
      user.isActive && (
        user.name.toLowerCase().includes(lowercaseQuery) ||
        user.email.toLowerCase().includes(lowercaseQuery)
      )
    );
  }

  /**
   * Get user statistics
   */
  async getUserStats(userId: string): Promise<{
    totalTrips: number;
    totalSpent: number;
    favoriteDestinations: string[];
    memberSince: Date;
    lastActivity: Date;
  }> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // This would integrate with booking history and trip plans
    // For now, return mock data
    return {
      totalTrips: 0,
      totalSpent: 0,
      favoriteDestinations: [],
      memberSince: user.createdAt,
      lastActivity: user.lastLoginAt || user.createdAt
    };
  }
}

// Export a singleton instance
export const userService = new UserService();
