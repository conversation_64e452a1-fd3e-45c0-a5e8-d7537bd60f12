import { readJsonFileWithCache, writeJsonFile, invalidateCache } from './file-utils';

export interface PriceAlert {
  id: string;
  userId: string;
  serviceType: 'flight' | 'accommodation' | 'activity';
  serviceId: string;
  serviceName: string;
  destination: string;
  targetPrice: number;
  currentPrice: number;
  priceDropThreshold: number; // Percentage drop to trigger alert
  isActive: boolean;
  createdAt: Date;
  lastCheckedAt: Date;
  triggeredAt?: Date;
  notificationsSent: number;
  maxNotifications: number;
}

export interface PriceHistory {
  id: string;
  serviceType: 'flight' | 'accommodation' | 'activity';
  serviceId: string;
  price: number;
  currency: string;
  recordedAt: Date;
  source: string;
  metadata?: Record<string, any>;
}

export interface PriceInsight {
  serviceId: string;
  serviceType: 'flight' | 'accommodation' | 'activity';
  currentPrice: number;
  averagePrice: number;
  lowestPrice: number;
  highestPrice: number;
  priceChange24h: number;
  priceChange7d: number;
  priceChange30d: number;
  trend: 'rising' | 'falling' | 'stable';
  recommendation: 'buy_now' | 'wait' | 'monitor';
  confidence: number;
  bestTimeToBook?: string;
  seasonalTrends?: {
    month: number;
    averagePrice: number;
    priceIndex: number; // Relative to yearly average
  }[];
}

const PRICE_ALERTS_FILE = 'price-alerts.json';
const PRICE_HISTORY_FILE = 'price-history.json';

export class PriceTrackingService {
  /**
   * Create a price alert
   */
  async createPriceAlert(alert: Omit<PriceAlert, 'id' | 'createdAt' | 'lastCheckedAt' | 'notificationsSent'>): Promise<PriceAlert> {
    const alerts = await readJsonFileWithCache<PriceAlert[]>(PRICE_ALERTS_FILE);
    
    // Check if user already has an alert for this service
    const existingAlert = alerts.find(a => 
      a.userId === alert.userId && 
      a.serviceId === alert.serviceId && 
      a.isActive
    );
    
    if (existingAlert) {
      throw new Error('Price alert already exists for this service');
    }

    const newAlert: PriceAlert = {
      ...alert,
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      lastCheckedAt: new Date(),
      notificationsSent: 0
    };

    alerts.push(newAlert);
    await writeJsonFile(PRICE_ALERTS_FILE, alerts);
    invalidateCache(PRICE_ALERTS_FILE);

    return newAlert;
  }

  /**
   * Get user's price alerts
   */
  async getUserPriceAlerts(userId: string): Promise<PriceAlert[]> {
    const alerts = await readJsonFileWithCache<PriceAlert[]>(PRICE_ALERTS_FILE);
    return alerts.filter(alert => alert.userId === userId);
  }

  /**
   * Update price alert
   */
  async updatePriceAlert(
    alertId: string,
    updates: Partial<Pick<PriceAlert, 'targetPrice' | 'priceDropThreshold' | 'isActive' | 'maxNotifications'>>
  ): Promise<PriceAlert> {
    const alerts = await readJsonFileWithCache<PriceAlert[]>(PRICE_ALERTS_FILE);
    const alertIndex = alerts.findIndex(alert => alert.id === alertId);

    if (alertIndex === -1) {
      throw new Error('Price alert not found');
    }

    alerts[alertIndex] = { ...alerts[alertIndex], ...updates };
    await writeJsonFile(PRICE_ALERTS_FILE, alerts);
    invalidateCache(PRICE_ALERTS_FILE);

    return alerts[alertIndex];
  }

  /**
   * Delete price alert
   */
  async deletePriceAlert(alertId: string, userId: string): Promise<boolean> {
    const alerts = await readJsonFileWithCache<PriceAlert[]>(PRICE_ALERTS_FILE);
    const alertIndex = alerts.findIndex(alert => 
      alert.id === alertId && alert.userId === userId
    );

    if (alertIndex === -1) {
      return false;
    }

    alerts.splice(alertIndex, 1);
    await writeJsonFile(PRICE_ALERTS_FILE, alerts);
    invalidateCache(PRICE_ALERTS_FILE);

    return true;
  }

  /**
   * Record price history
   */
  async recordPrice(
    serviceType: PriceHistory['serviceType'],
    serviceId: string,
    price: number,
    currency: string = 'USD',
    source: string = 'system',
    metadata?: Record<string, any>
  ): Promise<PriceHistory> {
    const history = await readJsonFileWithCache<PriceHistory[]>(PRICE_HISTORY_FILE);
    
    const priceRecord: PriceHistory = {
      id: `price-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      serviceType,
      serviceId,
      price,
      currency,
      recordedAt: new Date(),
      source,
      metadata
    };

    history.push(priceRecord);
    await writeJsonFile(PRICE_HISTORY_FILE, history);
    invalidateCache(PRICE_HISTORY_FILE);

    // Check for triggered alerts
    await this.checkPriceAlerts(serviceId, price);

    return priceRecord;
  }

  /**
   * Get price history for a service
   */
  async getPriceHistory(
    serviceId: string,
    days: number = 30
  ): Promise<PriceHistory[]> {
    const history = await readJsonFileWithCache<PriceHistory[]>(PRICE_HISTORY_FILE);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return history
      .filter(record => 
        record.serviceId === serviceId && 
        new Date(record.recordedAt) >= cutoffDate
      )
      .sort((a, b) => new Date(b.recordedAt).getTime() - new Date(a.recordedAt).getTime());
  }

  /**
   * Get price insights for a service
   */
  async getPriceInsights(serviceId: string): Promise<PriceInsight | null> {
    const history = await this.getPriceHistory(serviceId, 90); // 90 days of history
    
    if (history.length === 0) {
      return null;
    }

    const prices = history.map(h => h.price);
    const currentPrice = prices[0];
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const lowestPrice = Math.min(...prices);
    const highestPrice = Math.max(...prices);

    // Calculate price changes
    const priceChange24h = this.calculatePriceChange(history, 1);
    const priceChange7d = this.calculatePriceChange(history, 7);
    const priceChange30d = this.calculatePriceChange(history, 30);

    // Determine trend
    const recentPrices = history.slice(0, 7).map(h => h.price);
    const olderPrices = history.slice(7, 14).map(h => h.price);
    const recentAvg = recentPrices.reduce((sum, p) => sum + p, 0) / recentPrices.length;
    const olderAvg = olderPrices.length > 0 
      ? olderPrices.reduce((sum, p) => sum + p, 0) / olderPrices.length 
      : recentAvg;

    let trend: PriceInsight['trend'] = 'stable';
    if (recentAvg > olderAvg * 1.05) trend = 'rising';
    else if (recentAvg < olderAvg * 0.95) trend = 'falling';

    // Generate recommendation
    const pricePosition = (currentPrice - lowestPrice) / (highestPrice - lowestPrice);
    let recommendation: PriceInsight['recommendation'] = 'monitor';
    let confidence = 0.5;

    if (pricePosition < 0.3 && trend !== 'rising') {
      recommendation = 'buy_now';
      confidence = 0.8;
    } else if (pricePosition > 0.7 && trend !== 'falling') {
      recommendation = 'wait';
      confidence = 0.7;
    }

    return {
      serviceId,
      serviceType: history[0].serviceType,
      currentPrice,
      averagePrice,
      lowestPrice,
      highestPrice,
      priceChange24h,
      priceChange7d,
      priceChange30d,
      trend,
      recommendation,
      confidence,
      bestTimeToBook: this.getBestTimeToBook(trend, pricePosition),
      seasonalTrends: this.calculateSeasonalTrends(history)
    };
  }

  /**
   * Check price alerts and trigger notifications
   */
  private async checkPriceAlerts(serviceId: string, currentPrice: number): Promise<void> {
    const alerts = await readJsonFileWithCache<PriceAlert[]>(PRICE_ALERTS_FILE);
    const serviceAlerts = alerts.filter(alert => 
      alert.serviceId === serviceId && 
      alert.isActive &&
      alert.notificationsSent < alert.maxNotifications
    );

    const triggeredAlerts: PriceAlert[] = [];

    for (const alert of serviceAlerts) {
      const shouldTrigger = 
        currentPrice <= alert.targetPrice ||
        (alert.currentPrice > 0 && 
         ((alert.currentPrice - currentPrice) / alert.currentPrice) * 100 >= alert.priceDropThreshold);

      if (shouldTrigger) {
        alert.triggeredAt = new Date();
        alert.notificationsSent++;
        alert.currentPrice = currentPrice;
        alert.lastCheckedAt = new Date();
        triggeredAlerts.push(alert);
      } else {
        alert.currentPrice = currentPrice;
        alert.lastCheckedAt = new Date();
      }
    }

    if (triggeredAlerts.length > 0) {
      await writeJsonFile(PRICE_ALERTS_FILE, alerts);
      invalidateCache(PRICE_ALERTS_FILE);

      // In a real app, this would send notifications
      console.log(`Triggered ${triggeredAlerts.length} price alerts for service ${serviceId}`);
    }
  }

  /**
   * Calculate price change over a period
   */
  private calculatePriceChange(history: PriceHistory[], days: number): number {
    if (history.length < 2) return 0;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const currentPrice = history[0].price;
    const oldPrice = history.find(h => new Date(h.recordedAt) <= cutoffDate)?.price;

    if (!oldPrice) return 0;

    return ((currentPrice - oldPrice) / oldPrice) * 100;
  }

  /**
   * Get best time to book recommendation
   */
  private getBestTimeToBook(trend: PriceInsight['trend'], pricePosition: number): string {
    if (trend === 'falling' && pricePosition > 0.5) {
      return 'Wait 1-2 weeks for better prices';
    } else if (trend === 'rising' && pricePosition < 0.5) {
      return 'Book now before prices increase';
    } else if (pricePosition < 0.3) {
      return 'Excellent time to book - prices are near historical low';
    } else if (pricePosition > 0.7) {
      return 'Consider waiting - prices are near historical high';
    } else {
      return 'Prices are moderate - book when convenient';
    }
  }

  /**
   * Calculate seasonal trends
   */
  private calculateSeasonalTrends(history: PriceHistory[]): PriceInsight['seasonalTrends'] {
    const monthlyPrices: { [month: number]: number[] } = {};
    
    history.forEach(record => {
      const month = new Date(record.recordedAt).getMonth();
      if (!monthlyPrices[month]) {
        monthlyPrices[month] = [];
      }
      monthlyPrices[month].push(record.price);
    });

    const yearlyAverage = history.reduce((sum, h) => sum + h.price, 0) / history.length;
    
    return Object.entries(monthlyPrices).map(([month, prices]) => {
      const averagePrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
      return {
        month: parseInt(month),
        averagePrice,
        priceIndex: averagePrice / yearlyAverage
      };
    });
  }

  /**
   * Get all active alerts (for system monitoring)
   */
  async getAllActiveAlerts(): Promise<PriceAlert[]> {
    const alerts = await readJsonFileWithCache<PriceAlert[]>(PRICE_ALERTS_FILE);
    return alerts.filter(alert => alert.isActive);
  }

  /**
   * Bulk update prices (for scheduled price checking)
   */
  async bulkUpdatePrices(updates: {
    serviceId: string;
    serviceType: PriceHistory['serviceType'];
    price: number;
    currency?: string;
    source?: string;
  }[]): Promise<void> {
    for (const update of updates) {
      await this.recordPrice(
        update.serviceType,
        update.serviceId,
        update.price,
        update.currency,
        update.source
      );
    }
  }
}

// Export a singleton instance
export const priceTrackingService = new PriceTrackingService();
