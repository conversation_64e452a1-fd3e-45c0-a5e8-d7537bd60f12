/**
 * Environment Variable Validation Utility
 * Helps debug and validate environment variables in different environments
 */

export interface EnvValidationResult {
  isValid: boolean;
  missing: string[];
  present: string[];
  errors: string[];
}

/**
 * Required environment variables for the application
 */
export const REQUIRED_ENV_VARS = [
  'OPENWEATHER_API_KEY',
  'MONGODB_URI',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
] as const;

/**
 * Optional environment variables that should be checked
 */
export const OPTIONAL_ENV_VARS = [
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'SMTP_HOST',
  'SMTP_USER',
  'SMTP_PASS',
  'NEXT_PUBLIC_SENTRY_DSN',
  'NEXT_PUBLIC_APPSIGNAL_PUSH_API_KEY',
] as const;

/**
 * Validate environment variables
 */
export function validateEnvironment(): EnvValidationResult {
  const missing: string[] = [];
  const present: string[] = [];
  const errors: string[] = [];

  // Check required variables
  for (const envVar of REQUIRED_ENV_VARS) {
    const value = process.env[envVar];
    if (!value || value.trim() === '') {
      missing.push(envVar);
      errors.push(`Required environment variable ${envVar} is missing or empty`);
    } else {
      present.push(envVar);
    }
  }

  // Check optional variables (for informational purposes)
  for (const envVar of OPTIONAL_ENV_VARS) {
    const value = process.env[envVar];
    if (value && value.trim() !== '') {
      present.push(envVar);
    }
  }

  return {
    isValid: missing.length === 0,
    missing,
    present,
    errors,
  };
}

/**
 * Log environment validation results
 */
export function logEnvironmentStatus(): EnvValidationResult {
  const result = validateEnvironment();
  
  console.log('🔍 Environment Variable Status:');
  console.log(`📊 Node Environment: ${process.env.NODE_ENV}`);
  console.log(`✅ Present (${result.present.length}):`, result.present);
  
  if (result.missing.length > 0) {
    console.log(`❌ Missing (${result.missing.length}):`, result.missing);
    console.log('🚨 Errors:', result.errors);
  }
  
  if (result.isValid) {
    console.log('✅ All required environment variables are present');
  } else {
    console.log('❌ Some required environment variables are missing');
  }

  return result;
}

/**
 * Get safe environment variable preview (for logging)
 */
export function getEnvPreview(envVar: string): string {
  const value = process.env[envVar];
  if (!value) return 'undefined';
  if (value.length <= 8) return value;
  return `${value.slice(0, 4)}...${value.slice(-4)}`;
}

/**
 * Validate specific API key
 */
export function validateApiKey(keyName: string, expectedLength?: number): boolean {
  const value = process.env[keyName];
  if (!value) {
    console.error(`❌ ${keyName} is not set`);
    return false;
  }
  
  if (expectedLength && value.length !== expectedLength) {
    console.error(`❌ ${keyName} has unexpected length: ${value.length}, expected: ${expectedLength}`);
    return false;
  }
  
  console.log(`✅ ${keyName} is present (${getEnvPreview(keyName)})`);
  return true;
}
