import fs from 'fs/promises';
import path from 'path';

// Request management types and utilities
export interface Tenant {
  id: string;
  name: string;
  domain: string;
  settings: TenantSettings;
  branding: TenantBranding;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive' | 'suspended';
}

export interface TenantSettings {
  approvalLevels: number;
  autoApprovalThreshold: number;
  currency: string;
  timezone: string;
  workingHours: WorkingHours;
}

export interface WorkingHours {
  start: string;
  end: string;
  days: string[];
}

export interface TenantBranding {
  primaryColor: string;
  logo: string;
  theme: string;
}

export interface RequestType {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  approvalLevels: RequestApprovalLevel[];
  fields: RequestField[];
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive';
}

export interface RequestApprovalLevel {
  level: number;
  name: string;
  description: string;
  required: boolean;
  autoApprove: boolean;
  thresholdAmount?: number;
}

export interface RequestField {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'number' | 'date' | 'select' | 'file' | 'table';
  required: boolean;
  options?: string[];
  validation?: FieldValidation;
  columns?: RequestTableColumn[];
}

export interface FieldValidation {
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  maxFiles?: number;
  allowedTypes?: string[];
  minRows?: number;
  pattern?: string;
}

export interface RequestTableColumn {
  key: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select';
  required: boolean;
  options?: string[];
  calculated?: boolean;
}

export interface Request {
  id: string;
  tenantId: string;
  requestTypeId: string;
  title: string;
  description: string;
  requesterId: string;
  requesterName: string;
  requesterEmail: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  amount?: number;
  currency?: string;
  data: Record<string, any>;
  approvals: RequestApproval[];
  workflow: RequestWorkflow;
  attachments: RequestAttachment[];
  comments: RequestComment[];
  tags: string[];
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  completedAt?: string;
}

export interface RequestApproval {
  level: number;
  approverId: string;
  approverName: string;
  approverEmail: string;
  status: 'pending' | 'approved' | 'rejected';
  comments?: string;
  approvedAt?: string;
}

export interface RequestWorkflow {
  currentLevel: number;
  totalLevels: number;
  nextApprovers: string[];
}

export interface RequestAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: string;
}

export interface RequestComment {
  id: string;
  userId: string;
  userName: string;
  message: string;
  createdAt: string;
}

export interface User {
  id: string;
  tenantId: string;
  email: string;
  name: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: 'employee' | 'manager' | 'finance' | 'executive' | 'admin';
  department: string;
  title: string;
  managerId?: string;
  permissions: string[];
  settings: UserSettings;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive';
}

export interface UserSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  timezone: string;
}

// Data paths
const DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'tools', 'requests');
const TENANTS_FILE = path.join(DATA_DIR, 'tenants.json');
const REQUEST_TYPES_FILE = path.join(DATA_DIR, 'request-types.json');
const REQUESTS_FILE = path.join(DATA_DIR, 'requests.json');
const USERS_FILE = path.join(DATA_DIR, 'users.json');
const WORKFLOWS_FILE = path.join(DATA_DIR, 'workflows.json');
const ANALYTICS_FILE = path.join(DATA_DIR, 'analytics.json');

// Utility functions
export async function ensureRequestDataDirectory() {
  try {
    await fs.mkdir(DATA_DIR, { recursive: true });
  } catch (error) {
    console.error('Error creating request data directory:', error);
  }
}

// Tenant operations
export async function loadTenants(): Promise<Tenant[]> {
  try {
    const data = await fs.readFile(TENANTS_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    return parsed.tenants || [];
  } catch (error) {
    console.error('Error loading tenants:', error);
    return [];
  }
}

export async function saveTenants(tenants: Tenant[]): Promise<void> {
  try {
    await ensureRequestDataDirectory();
    await fs.writeFile(TENANTS_FILE, JSON.stringify({ tenants }, null, 2));
  } catch (error) {
    console.error('Error saving tenants:', error);
    throw error;
  }
}

// Request type operations
export async function loadRequestTypes(): Promise<RequestType[]> {
  try {
    const data = await fs.readFile(REQUEST_TYPES_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    return parsed.requestTypes || [];
  } catch (error) {
    console.error('Error loading request types:', error);
    return [];
  }
}

export async function saveRequestTypes(requestTypes: RequestType[]): Promise<void> {
  try {
    await ensureRequestDataDirectory();
    await fs.writeFile(REQUEST_TYPES_FILE, JSON.stringify({ requestTypes }, null, 2));
  } catch (error) {
    console.error('Error saving request types:', error);
    throw error;
  }
}

// Request operations
export async function loadRequests(): Promise<Request[]> {
  try {
    const data = await fs.readFile(REQUESTS_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    return parsed.requests || [];
  } catch (error) {
    console.error('Error loading requests:', error);
    return [];
  }
}

export async function saveRequests(requests: Request[]): Promise<void> {
  try {
    await ensureRequestDataDirectory();
    await fs.writeFile(REQUESTS_FILE, JSON.stringify({ requests }, null, 2));
  } catch (error) {
    console.error('Error saving requests:', error);
    throw error;
  }
}

// User operations
export async function loadUsers(): Promise<User[]> {
  try {
    const data = await fs.readFile(USERS_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    return parsed.users || [];
  } catch (error) {
    console.error('Error loading users:', error);
    return [];
  }
}

export async function saveUsers(users: User[]): Promise<void> {
  try {
    await ensureRequestDataDirectory();
    await fs.writeFile(USERS_FILE, JSON.stringify({ users }, null, 2));
  } catch (error) {
    console.error('Error saving users:', error);
    throw error;
  }
}

// Helper functions
export async function createRequest(
  tenantId: string,
  requestTypeId: string,
  requesterId: string,
  title: string,
  description: string,
  data: Record<string, any>
): Promise<Request> {
  const requestTypes = await loadRequestTypes();
  const requestType = requestTypes.find(rt => rt.id === requestTypeId && rt.tenantId === tenantId);
  
  if (!requestType) {
    throw new Error(`Request type ${requestTypeId} not found for tenant ${tenantId}`);
  }

  const users = await loadUsers();
  const requester = users.find(u => u.id === requesterId && u.tenantId === tenantId);
  
  if (!requester) {
    throw new Error(`User ${requesterId} not found for tenant ${tenantId}`);
  }

  const request: Request = {
    id: `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    tenantId,
    requestTypeId,
    title,
    description,
    requesterId,
    requesterName: requester.name,
    requesterEmail: requester.email,
    status: 'draft',
    priority: 'medium',
    data,
    approvals: requestType.approvalLevels.map(level => ({
      level: level.level,
      approverId: '', // Will be assigned based on approval rules
      approverName: '',
      approverEmail: '',
      status: 'pending' as const,
    })),
    workflow: {
      currentLevel: 1,
      totalLevels: requestType.approvalLevels.length,
      nextApprovers: []
    },
    attachments: [],
    comments: [],
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const requests = await loadRequests();
  requests.push(request);
  await saveRequests(requests);

  return request;
}

export async function getRequestsByTenant(tenantId: string): Promise<Request[]> {
  const requests = await loadRequests();
  return requests.filter(r => r.tenantId === tenantId);
}

export async function getRequestTypesByTenant(tenantId: string): Promise<RequestType[]> {
  const requestTypes = await loadRequestTypes();
  return requestTypes.filter(rt => rt.tenantId === tenantId);
}

export async function getUsersByTenant(tenantId: string): Promise<User[]> {
  const users = await loadUsers();
  return users.filter(u => u.tenantId === tenantId);
}
