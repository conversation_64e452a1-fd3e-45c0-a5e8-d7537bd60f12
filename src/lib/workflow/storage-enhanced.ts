import fs from 'fs/promises';
import path from 'path';

// Enhanced storage utilities for workflow and request management
export interface WorkflowTemplate {
  id: string;
  tenantId: string;
  title: string;
  description: string;
  icon: string;
  category: string;
  version: string;
  status: 'active' | 'inactive' | 'draft';
  estimatedDuration: string;
  steps: WorkflowStep[];
  approvalFlow?: ApprovalFlow;
  notifications?: NotificationSettings;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface WorkflowInstance {
  id: string;
  tenantId: string;
  templateId: string;
  title: string;
  description: string;
  status: 'draft' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdBy: string;
  createdByName: string;
  assignedTo?: string;
  assignedToName?: string;
  currentStep: number;
  totalSteps: number;
  completionPercentage: number;
  responses: Record<string, any>;
  attachments: Attachment[];
  approvals: Approval[];
  comments: Comment[];
  timeline: TimelineEvent[];
  metadata: WorkflowMetadata;
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  completedAt?: string;
}

export interface WorkflowStep {
  id: string;
  type: 'input' | 'select' | 'multiselect' | 'file' | 'table' | 'date' | 'confirm';
  title: string;
  description: string;
  required: boolean;
  options?: string[];
  config?: StepConfig;
}

export interface StepConfig {
  placeholder?: string;
  validation?: ValidationRules;
  columns?: TableColumn[];
  minRows?: number;
  maxRows?: number;
  allowedTypes?: string[];
  maxSize?: string;
  max?: number;
  min?: number;
  minDate?: string;
  maxDate?: string;
  confirmText?: string;
}

export interface ValidationRules {
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: string;
  required?: boolean;
}

export interface TableColumn {
  key: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select';
  required: boolean;
  options?: string[];
  calculated?: boolean;
}

export interface ApprovalFlow {
  enabled: boolean;
  levels: ApprovalLevel[];
}

export interface ApprovalLevel {
  level: number;
  name: string;
  approvers: string[];
  required: boolean;
}

export interface NotificationSettings {
  onStart: boolean;
  onComplete: boolean;
  onApproval: boolean;
  onRejection: boolean;
}

export interface Attachment {
  id: string;
  stepId?: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: string;
}

export interface Approval {
  level: number;
  approverId: string;
  approverName: string;
  status: 'pending' | 'approved' | 'rejected' | 'not-started';
  requestedAt?: string;
  comments?: string;
  approvedAt?: string;
}

export interface Comment {
  id: string;
  userId: string;
  userName: string;
  message: string;
  createdAt: string;
}

export interface TimelineEvent {
  id: string;
  type: 'created' | 'step_completed' | 'approval_requested' | 'approved' | 'rejected' | 'workflow_completed' | 'comment_added';
  description: string;
  userId: string;
  userName: string;
  timestamp: string;
}

export interface WorkflowMetadata {
  tags: string[];
  estimatedCompletion?: string;
  actualStartDate?: string;
  actualCompletionDate?: string;
  targetCompletionDate?: string;
}

// Data paths
const DATA_DIR = path.join(process.cwd(), 'data', 'apps', 'tools');
const WORKFLOW_TEMPLATES_FILE = path.join(DATA_DIR, 'workflows', 'templates.json');
const WORKFLOW_INSTANCES_FILE = path.join(DATA_DIR, 'workflows', 'instances.json');

// Utility functions
export async function ensureDataDirectory() {
  try {
    await fs.mkdir(path.join(DATA_DIR, 'workflows'), { recursive: true });
    await fs.mkdir(path.join(DATA_DIR, 'requests'), { recursive: true });
  } catch (error) {
    console.error('Error creating data directories:', error);
  }
}

export async function loadWorkflowTemplates(): Promise<WorkflowTemplate[]> {
  try {
    const data = await fs.readFile(WORKFLOW_TEMPLATES_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    return parsed.templates || [];
  } catch (error) {
    console.error('Error loading workflow templates:', error);
    return [];
  }
}

export async function saveWorkflowTemplates(templates: WorkflowTemplate[]): Promise<void> {
  try {
    await ensureDataDirectory();
    await fs.writeFile(WORKFLOW_TEMPLATES_FILE, JSON.stringify({ templates }, null, 2));
  } catch (error) {
    console.error('Error saving workflow templates:', error);
    throw error;
  }
}

export async function loadWorkflowInstances(): Promise<WorkflowInstance[]> {
  try {
    const data = await fs.readFile(WORKFLOW_INSTANCES_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    return parsed.instances || [];
  } catch (error) {
    console.error('Error loading workflow instances:', error);
    return [];
  }
}

export async function saveWorkflowInstances(instances: WorkflowInstance[]): Promise<void> {
  try {
    await ensureDataDirectory();
    await fs.writeFile(WORKFLOW_INSTANCES_FILE, JSON.stringify({ instances }, null, 2));
  } catch (error) {
    console.error('Error saving workflow instances:', error);
    throw error;
  }
}

export async function createWorkflowInstance(
  templateId: string,
  tenantId: string,
  createdBy: string,
  createdByName: string,
  title?: string,
  description?: string
): Promise<WorkflowInstance> {
  const templates = await loadWorkflowTemplates();
  const template = templates.find(t => t.id === templateId && t.tenantId === tenantId);
  
  if (!template) {
    throw new Error(`Template ${templateId} not found for tenant ${tenantId}`);
  }

  const instance: WorkflowInstance = {
    id: `workflow-inst-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    tenantId,
    templateId,
    title: title || template.title,
    description: description || template.description,
    status: 'draft',
    priority: 'medium',
    createdBy,
    createdByName,
    currentStep: 0,
    totalSteps: template.steps.length,
    completionPercentage: 0,
    responses: {},
    attachments: [],
    approvals: template.approvalFlow?.enabled ? template.approvalFlow.levels.map(level => ({
      level: level.level,
      approverId: level.approvers[0], // Default to first approver
      approverName: '', // Will be populated when needed
      status: 'not-started' as const,
    })) : [],
    comments: [],
    timeline: [{
      id: `event-${Date.now()}`,
      type: 'created',
      description: 'Workflow instance created',
      userId: createdBy,
      userName: createdByName,
      timestamp: new Date().toISOString()
    }],
    metadata: {
      tags: [],
      actualStartDate: new Date().toISOString(),
      targetCompletionDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const instances = await loadWorkflowInstances();
  instances.push(instance);
  await saveWorkflowInstances(instances);

  return instance;
}

export async function updateWorkflowInstance(instanceId: string, updates: Partial<WorkflowInstance>): Promise<WorkflowInstance> {
  const instances = await loadWorkflowInstances();
  const index = instances.findIndex(i => i.id === instanceId);
  
  if (index === -1) {
    throw new Error(`Workflow instance ${instanceId} not found`);
  }

  instances[index] = {
    ...instances[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  await saveWorkflowInstances(instances);
  return instances[index];
}

export async function getWorkflowInstancesByTenant(tenantId: string): Promise<WorkflowInstance[]> {
  const instances = await loadWorkflowInstances();
  return instances.filter(i => i.tenantId === tenantId);
}

export async function getWorkflowTemplatesByTenant(tenantId: string): Promise<WorkflowTemplate[]> {
  const templates = await loadWorkflowTemplates();
  return templates.filter(t => t.tenantId === tenantId);
}
