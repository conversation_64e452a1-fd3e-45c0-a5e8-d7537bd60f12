// Main integration exports for Actions Store
export * from './chatbotActions';
export * from './apiServerActions';

// Integration utilities
import { ActionResult } from '../actions';

// Common integration utilities
export class ActionIntegrationUtils {
  // Rate limiting helper
  static createRateLimiter(maxCalls: number, windowMs: number) {
    const calls = new Map<string, number[]>();
    
    return (identifier: string): boolean => {
      const now = Date.now();
      const windowStart = now - windowMs;
      
      if (!calls.has(identifier)) {
        calls.set(identifier, []);
      }
      
      const userCalls = calls.get(identifier)!;
      
      // Remove old calls outside the window
      const recentCalls = userCalls.filter(callTime => callTime > windowStart);
      calls.set(identifier, recentCalls);
      
      // Check if under limit
      if (recentCalls.length >= maxCalls) {
        return false; // Rate limited
      }
      
      // Add current call
      recentCalls.push(now);
      return true; // Allowed
    };
  }

  // Error standardization
  static standardizeError(error: any): {
    message: string;
    type: string;
    code?: string;
  } {
    if (error instanceof Error) {
      return {
        message: error.message,
        type: error.constructor.name,
        code: (error as any).code
      };
    }
    
    if (typeof error === 'string') {
      return {
        message: error,
        type: 'StringError'
      };
    }
    
    return {
      message: 'Unknown error occurred',
      type: 'UnknownError'
    };
  }

  // Result transformation
  static transformResult(
    result: ActionResult,
    format: 'json' | 'text' | 'markdown' = 'json'
  ): any {
    switch (format) {
      case 'text':
        if (result.success) {
          return `Success: ${JSON.stringify(result.data)}`;
        } else {
          return `Error: ${result.error}`;
        }
        
      case 'markdown':
        if (result.success) {
          return `✅ **Success**\n\`\`\`json\n${JSON.stringify(result.data, null, 2)}\n\`\`\``;
        } else {
          return `❌ **Error**\n${result.error}`;
        }
        
      case 'json':
      default:
        return result;
    }
  }

  // Payload validation
  static validatePayload(
    payload: any,
    maxSize: number = 1024 * 1024
  ): { valid: boolean; error?: string; size: number } {
    try {
      const serialized = JSON.stringify(payload);
      const size = new Blob([serialized]).size;
      
      if (size > maxSize) {
        return {
          valid: false,
          error: `Payload size (${size} bytes) exceeds maximum (${maxSize} bytes)`,
          size
        };
      }
      
      return { valid: true, size };
    } catch (error) {
      return {
        valid: false,
        error: 'Payload is not serializable',
        size: 0
      };
    }
  }

  // Context enrichment
  static enrichContext(
    baseContext: any,
    additionalContext: any
  ): any {
    return {
      ...baseContext,
      ...additionalContext,
      timestamp: new Date().toISOString(),
      enrichedAt: Date.now()
    };
  }

  // Security helpers
  static sanitizeForLogging(data: any): any {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sensitiveKeys = [
      'password', 'secret', 'token', 'key', 'auth',
      'credential', 'private', 'confidential'
    ];

    const sanitized = { ...data };
    
    for (const key of Object.keys(sanitized)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeForLogging(sanitized[key]);
      }
    }

    return sanitized;
  }

  // Performance monitoring
  static createPerformanceMonitor() {
    const metrics = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      totalExecutionTime: 0,
      averageExecutionTime: 0,
      lastExecution: null as Date | null
    };

    return {
      recordExecution: (result: ActionResult) => {
        metrics.totalExecutions++;
        metrics.totalExecutionTime += result.executionTime;
        metrics.averageExecutionTime = metrics.totalExecutionTime / metrics.totalExecutions;
        metrics.lastExecution = new Date();
        
        if (result.success) {
          metrics.successfulExecutions++;
        } else {
          metrics.failedExecutions++;
        }
      },
      
      getMetrics: () => ({ ...metrics }),
      
      reset: () => {
        Object.assign(metrics, {
          totalExecutions: 0,
          successfulExecutions: 0,
          failedExecutions: 0,
          totalExecutionTime: 0,
          averageExecutionTime: 0,
          lastExecution: null
        });
      }
    };
  }
}

// Global performance monitor instance
export const globalPerformanceMonitor = ActionIntegrationUtils.createPerformanceMonitor();

// Integration health check
export async function checkIntegrationHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: Array<{
    name: string;
    status: 'pass' | 'fail';
    message?: string;
    duration: number;
  }>;
}> {
  const checks = [];
  let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

  // Check actions store availability
  const actionsStoreCheck = await (async () => {
    const start = Date.now();
    try {
      const { listAvailableActions } = await import('../actions');
      const actions = listAvailableActions();
      return {
        name: 'Actions Store',
        status: 'pass' as const,
        message: `${actions.length} actions available`,
        duration: Date.now() - start
      };
    } catch (error) {
      return {
        name: 'Actions Store',
        status: 'fail' as const,
        message: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - start
      };
    }
  })();
  checks.push(actionsStoreCheck);

  // Check chatbot integration
  const chatbotCheck = await (async () => {
    const start = Date.now();
    try {
      const { getChatbotAvailableActions } = await import('./chatbotActions');
      const actions = getChatbotAvailableActions();
      return {
        name: 'Chatbot Integration',
        status: 'pass' as const,
        message: `${actions.length} actions available for chatbot`,
        duration: Date.now() - start
      };
    } catch (error) {
      return {
        name: 'Chatbot Integration',
        status: 'fail' as const,
        message: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - start
      };
    }
  })();
  checks.push(chatbotCheck);

  // Check API server integration
  const apiCheck = await (async () => {
    const start = Date.now();
    try {
      const { getApiAvailableActions } = await import('./apiServerActions');
      const actions = getApiAvailableActions();
      return {
        name: 'API Server Integration',
        status: 'pass' as const,
        message: `${actions.length} actions available for API`,
        duration: Date.now() - start
      };
    } catch (error) {
      return {
        name: 'API Server Integration',
        status: 'fail' as const,
        message: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - start
      };
    }
  })();
  checks.push(apiCheck);

  // Determine overall status
  const failedChecks = checks.filter(check => check.status === 'fail').length;
  if (failedChecks === 0) {
    overallStatus = 'healthy';
  } else if (failedChecks < checks.length) {
    overallStatus = 'degraded';
  } else {
    overallStatus = 'unhealthy';
  }

  return {
    status: overallStatus,
    checks
  };
}
