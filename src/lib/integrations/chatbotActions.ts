import { callAction, listAvailableActions, ActionResult, ActionContext } from '../actions';

// Chatbot-specific action integration
export class ChatbotActionIntegration {
  private allowedActions: string[];
  private maxPayloadSize: number;

  constructor(config?: {
    allowedActions?: string[];
    maxPayloadSize?: number;
  }) {
    this.allowedActions = config?.allowedActions || [
      'system.getInfo',
      'data.query',
      'notification.send',
      'platform.analytics'
    ];
    this.maxPayloadSize = config?.maxPayloadSize || 1024 * 1024; // 1MB
  }

  // Execute action from chatbot with enhanced context
  async executeAction(
    actionKey: string,
    payload: Record<string, any>,
    chatContext: {
      userId?: string;
      sessionId?: string;
      messageId?: string;
      conversationId?: string;
      userMessage?: string;
    }
  ): Promise<ActionResult> {
    try {
      // Validate action is allowed for chatbot
      if (!this.allowedActions.includes(actionKey) && !this.allowedActions.includes('*')) {
        throw new Error(`Action ${actionKey} is not allowed for chatbot execution`);
      }

      // Validate payload size
      const payloadSize = JSON.stringify(payload).length;
      if (payloadSize > this.maxPayloadSize) {
        throw new Error(`Payload size (${payloadSize} bytes) exceeds maximum allowed (${this.maxPayloadSize} bytes)`);
      }

      // Build execution context
      const context: Partial<ActionContext> = {
        userId: chatContext.userId || 'chatbot-user',
        sessionId: chatContext.sessionId || chatContext.conversationId,
        source: 'chatbot',
        permissions: ['chatbot.execute']
      };

      // Execute action
      const result = await callAction(actionKey, payload, context);

      // Log chatbot-specific information
      if (result.success) {
        console.log(`Chatbot action executed successfully: ${actionKey}`, {
          userId: chatContext.userId,
          messageId: chatContext.messageId,
          executionTime: result.executionTime
        });
      } else {
        console.error(`Chatbot action failed: ${actionKey}`, {
          error: result.error,
          userId: chatContext.userId,
          messageId: chatContext.messageId
        });
      }

      return result;

    } catch (error) {
      const errorResult: ActionResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: 0,
        context: {
          userId: chatContext.userId || 'chatbot-user',
          sessionId: chatContext.sessionId || 'unknown',
          source: 'chatbot',
          timestamp: new Date().toISOString(),
          requestId: `chatbot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }
      };

      console.error('Chatbot action integration error:', error);
      return errorResult;
    }
  }

  // Get actions available for chatbot
  getAvailableActions(): Array<{ key: string; description: string; category: string; tags: string[] }> {
    const allActions = listAvailableActions();
    
    if (this.allowedActions.includes('*')) {
      return allActions;
    }
    
    return allActions.filter(action => this.allowedActions.includes(action.key));
  }

  // Format action result for chatbot response
  formatResultForChat(result: ActionResult, actionKey: string): string {
    if (!result.success) {
      return `❌ Action failed: ${result.error}`;
    }

    // Format based on action type
    switch (actionKey) {
      case 'system.getInfo':
        const info = result.data;
        return `🖥️ **System Information**\n` +
               `Platform: ${info.platform}\n` +
               `Node Version: ${info.nodeVersion}\n` +
               `Memory: ${info.memory?.heapUsed || 'N/A'}\n` +
               `Uptime: ${info.uptime || 'N/A'}`;

      case 'data.query':
        const queryResult = result.data;
        return `📊 **Query Results**\n` +
               `Module: ${queryResult.module}\n` +
               `Entity: ${queryResult.entity}\n` +
               `Found: ${queryResult.count} items\n` +
               `Execution time: ${result.executionTime}ms`;

      case 'notification.send':
        const notifResult = result.data;
        return `📧 **Notification Sent**\n` +
               `Channel: ${notifResult.channel}\n` +
               `Recipient: ${notifResult.recipient}\n` +
               `Status: ${notifResult.status}\n` +
               `ID: ${notifResult.notificationId}`;

      case 'platform.analytics':
        const analytics = result.data;
        return `📈 **Analytics Data**\n` +
               `Metric: ${analytics.metric}\n` +
               `Time Range: ${analytics.timeRange}\n` +
               `Total: ${analytics.summary.total}\n` +
               `Average: ${analytics.summary.average}`;

      default:
        // Generic formatting
        if (typeof result.data === 'object') {
          const keys = Object.keys(result.data).slice(0, 5);
          const preview = keys.map(key => `${key}: ${result.data[key]}`).join('\n');
          return `✅ **Action Completed**\n${preview}${keys.length < Object.keys(result.data).length ? '\n...' : ''}`;
        }
        return `✅ Action completed successfully: ${result.data}`;
    }
  }

  // Suggest actions based on user message
  suggestActions(userMessage: string): string[] {
    const message = userMessage.toLowerCase();
    const suggestions: string[] = [];

    if (message.includes('system') || message.includes('info') || message.includes('status')) {
      suggestions.push('system.getInfo');
    }

    if (message.includes('data') || message.includes('query') || message.includes('search')) {
      suggestions.push('data.query');
    }

    if (message.includes('notify') || message.includes('send') || message.includes('alert')) {
      suggestions.push('notification.send');
    }

    if (message.includes('analytics') || message.includes('metrics') || message.includes('stats')) {
      suggestions.push('platform.analytics');
    }

    return suggestions;
  }

  // Validate and sanitize payload for chatbot use
  sanitizePayload(payload: any): Record<string, any> {
    if (typeof payload !== 'object' || payload === null) {
      return {};
    }

    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(payload)) {
      // Remove potentially dangerous keys
      if (key.startsWith('_') || key.includes('password') || key.includes('secret')) {
        continue;
      }

      // Sanitize values
      if (typeof value === 'string') {
        sanitized[key] = value.slice(0, 1000); // Limit string length
      } else if (typeof value === 'number' && isFinite(value)) {
        sanitized[key] = value;
      } else if (typeof value === 'boolean') {
        sanitized[key] = value;
      } else if (Array.isArray(value)) {
        sanitized[key] = value.slice(0, 100); // Limit array length
      } else if (typeof value === 'object' && value !== null) {
        // Recursively sanitize nested objects (max depth 3)
        sanitized[key] = this.sanitizeNestedObject(value, 3);
      }
    }

    return sanitized;
  }

  private sanitizeNestedObject(obj: any, maxDepth: number): any {
    if (maxDepth <= 0 || typeof obj !== 'object' || obj === null) {
      return {};
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('_')) continue;
      
      if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeNestedObject(value, maxDepth - 1);
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }
}

// Default chatbot integration instance
export const chatbotActions = new ChatbotActionIntegration();

// Convenience functions for easy integration
export async function executeChatbotAction(
  actionKey: string,
  payload: Record<string, any>,
  chatContext: {
    userId?: string;
    sessionId?: string;
    messageId?: string;
    conversationId?: string;
    userMessage?: string;
  }
): Promise<ActionResult> {
  return chatbotActions.executeAction(actionKey, payload, chatContext);
}

export function getChatbotAvailableActions(): Array<{ key: string; description: string; category: string; tags: string[] }> {
  return chatbotActions.getAvailableActions();
}

export function formatChatbotResult(result: ActionResult, actionKey: string): string {
  return chatbotActions.formatResultForChat(result, actionKey);
}
