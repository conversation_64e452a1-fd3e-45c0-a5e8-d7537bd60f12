import { callAction, listAvailableActions, ActionResult, ActionContext } from '../actions';

// API Server-specific action integration
export class ApiServerActionIntegration {
  private allowedActions: string[];
  private maxPayloadSize: number;
  private requireApiKey: boolean;
  private validApiKeys: Set<string>;

  constructor(config?: {
    allowedActions?: string[];
    maxPayloadSize?: number;
    requireApiKey?: boolean;
    validApiKeys?: string[];
  }) {
    this.allowedActions = config?.allowedActions || ['*']; // Allow all by default for API
    this.maxPayloadSize = config?.maxPayloadSize || 5 * 1024 * 1024; // 5MB
    this.requireApiKey = config?.requireApiKey || false;
    this.validApiKeys = new Set(config?.validApiKeys || []);
  }

  // Execute action from API server with enhanced validation
  async executeAction(
    actionKey: string,
    payload: Record<string, any>,
    apiContext: {
      userId?: string;
      sessionId?: string;
      apiKey?: string;
      clientIp?: string;
      userAgent?: string;
      requestId?: string;
      permissions?: string[];
    }
  ): Promise<ActionResult> {
    try {
      // Validate API key if required
      if (this.requireApiKey) {
        if (!apiContext.apiKey) {
          throw new Error('API key is required');
        }
        if (!this.validApiKeys.has(apiContext.apiKey)) {
          throw new Error('Invalid API key');
        }
      }

      // Validate action is allowed for API server
      if (!this.allowedActions.includes(actionKey) && !this.allowedActions.includes('*')) {
        throw new Error(`Action ${actionKey} is not allowed for API server execution`);
      }

      // Validate payload size
      const payloadSize = JSON.stringify(payload).length;
      if (payloadSize > this.maxPayloadSize) {
        throw new Error(`Payload size (${payloadSize} bytes) exceeds maximum allowed (${this.maxPayloadSize} bytes)`);
      }

      // Build execution context
      const context: Partial<ActionContext> = {
        userId: apiContext.userId || 'api-user',
        sessionId: apiContext.sessionId || apiContext.requestId,
        source: 'api',
        permissions: apiContext.permissions || ['api.execute'],
        requestId: apiContext.requestId
      };

      // Execute action
      const result = await callAction(actionKey, payload, context);

      // Log API-specific information
      if (result.success) {
        console.log(`API action executed successfully: ${actionKey}`, {
          userId: apiContext.userId,
          clientIp: apiContext.clientIp,
          executionTime: result.executionTime,
          requestId: apiContext.requestId
        });
      } else {
        console.error(`API action failed: ${actionKey}`, {
          error: result.error,
          userId: apiContext.userId,
          clientIp: apiContext.clientIp,
          requestId: apiContext.requestId
        });
      }

      return result;

    } catch (error) {
      const errorResult: ActionResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: 0,
        context: {
          userId: apiContext.userId || 'api-user',
          sessionId: apiContext.sessionId || 'unknown',
          source: 'api',
          timestamp: new Date().toISOString(),
          requestId: apiContext.requestId || `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }
      };

      console.error('API action integration error:', error);
      return errorResult;
    }
  }

  // Get actions available for API server
  getAvailableActions(): Array<{ key: string; description: string; category: string; tags: string[] }> {
    const allActions = listAvailableActions();
    
    if (this.allowedActions.includes('*')) {
      return allActions;
    }
    
    return allActions.filter(action => this.allowedActions.includes(action.key));
  }

  // Batch execute multiple actions
  async executeBatch(
    actions: Array<{
      actionKey: string;
      payload: Record<string, any>;
      id?: string;
    }>,
    apiContext: {
      userId?: string;
      sessionId?: string;
      apiKey?: string;
      clientIp?: string;
      userAgent?: string;
      requestId?: string;
      permissions?: string[];
    }
  ): Promise<Array<{
    id?: string;
    actionKey: string;
    result: ActionResult;
  }>> {
    const results = [];
    
    // Validate batch size
    if (actions.length > 10) {
      throw new Error('Batch size cannot exceed 10 actions');
    }

    for (const action of actions) {
      try {
        const result = await this.executeAction(action.actionKey, action.payload, {
          ...apiContext,
          requestId: `${apiContext.requestId || 'batch'}_${action.id || Math.random().toString(36).substr(2, 9)}`
        });
        
        results.push({
          id: action.id,
          actionKey: action.actionKey,
          result
        });
      } catch (error) {
        results.push({
          id: action.id,
          actionKey: action.actionKey,
          result: {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            executionTime: 0,
            context: {
              userId: apiContext.userId || 'api-user',
              sessionId: apiContext.sessionId || 'unknown',
              source: 'api',
              timestamp: new Date().toISOString(),
              requestId: apiContext.requestId || 'batch'
            }
          }
        });
      }
    }

    return results;
  }

  // Validate request format
  validateRequest(request: any): {
    valid: boolean;
    errors: string[];
    sanitized?: any;
  } {
    const errors: string[] = [];
    
    if (!request || typeof request !== 'object') {
      errors.push('Request must be an object');
      return { valid: false, errors };
    }

    if (!request.actionKey || typeof request.actionKey !== 'string') {
      errors.push('actionKey is required and must be a string');
    }

    if (request.payload && typeof request.payload !== 'object') {
      errors.push('payload must be an object');
    }

    if (errors.length > 0) {
      return { valid: false, errors };
    }

    // Sanitize request
    const sanitized = {
      actionKey: request.actionKey,
      payload: this.sanitizePayload(request.payload || {}),
      context: this.sanitizeContext(request.context || {})
    };

    return { valid: true, errors: [], sanitized };
  }

  // Sanitize payload for API use
  private sanitizePayload(payload: any): Record<string, any> {
    if (typeof payload !== 'object' || payload === null) {
      return {};
    }

    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(payload)) {
      // Remove potentially dangerous keys
      if (key.startsWith('__') || key.includes('prototype')) {
        continue;
      }

      // Sanitize values based on type
      if (typeof value === 'string') {
        sanitized[key] = value.slice(0, 10000); // Limit string length
      } else if (typeof value === 'number' && isFinite(value)) {
        sanitized[key] = value;
      } else if (typeof value === 'boolean') {
        sanitized[key] = value;
      } else if (Array.isArray(value)) {
        sanitized[key] = value.slice(0, 1000).map(item => 
          typeof item === 'object' ? this.sanitizeNestedObject(item, 2) : item
        );
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeNestedObject(value, 3);
      }
    }

    return sanitized;
  }

  // Sanitize context for API use
  private sanitizeContext(context: any): Record<string, any> {
    if (typeof context !== 'object' || context === null) {
      return {};
    }

    const allowedKeys = ['userId', 'sessionId', 'permissions', 'source'];
    const sanitized: Record<string, any> = {};

    for (const key of allowedKeys) {
      if (context[key] !== undefined) {
        sanitized[key] = context[key];
      }
    }

    return sanitized;
  }

  private sanitizeNestedObject(obj: any, maxDepth: number): any {
    if (maxDepth <= 0 || typeof obj !== 'object' || obj === null) {
      return {};
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('__')) continue;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        sanitized[key] = this.sanitizeNestedObject(value, maxDepth - 1);
      } else if (Array.isArray(value)) {
        sanitized[key] = value.slice(0, 100);
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  // Generate API response format
  formatApiResponse(result: ActionResult, actionKey: string, requestId?: string): any {
    const response = {
      success: result.success,
      actionKey,
      executionTime: result.executionTime,
      timestamp: new Date().toISOString(),
      requestId: requestId || result.context.requestId
    };

    if (result.success) {
      return {
        ...response,
        data: result.data
      };
    } else {
      return {
        ...response,
        error: {
          message: result.error,
          type: 'execution_error'
        }
      };
    }
  }

  // Add API key
  addApiKey(apiKey: string): void {
    this.validApiKeys.add(apiKey);
  }

  // Remove API key
  removeApiKey(apiKey: string): void {
    this.validApiKeys.delete(apiKey);
  }

  // Check if API key is valid
  isValidApiKey(apiKey: string): boolean {
    return !this.requireApiKey || this.validApiKeys.has(apiKey);
  }
}

// Default API server integration instance
export const apiServerActions = new ApiServerActionIntegration();

// Convenience functions for easy integration
export async function executeApiAction(
  actionKey: string,
  payload: Record<string, any>,
  apiContext: {
    userId?: string;
    sessionId?: string;
    apiKey?: string;
    clientIp?: string;
    userAgent?: string;
    requestId?: string;
    permissions?: string[];
  }
): Promise<ActionResult> {
  return apiServerActions.executeAction(actionKey, payload, apiContext);
}

export function getApiAvailableActions(): Array<{ key: string; description: string; category: string; tags: string[] }> {
  return apiServerActions.getAvailableActions();
}

export function validateApiRequest(request: any): {
  valid: boolean;
  errors: string[];
  sanitized?: any;
} {
  return apiServerActions.validateRequest(request);
}
