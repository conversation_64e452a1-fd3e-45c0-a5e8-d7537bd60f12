'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeType, themes, setTheme, getCurrentTheme, initializeTheme } from './theme-config';

interface ThemeContextType {
  currentTheme: ThemeType;
  switchTheme: (theme: ThemeType) => void;
  toggleTheme: () => void;
  isLoading: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeType;
}

export function ThemeProvider({ children, defaultTheme = 'current' }: ThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<ThemeType>(defaultTheme);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize theme from localStorage or use default
    const savedTheme = typeof window !== 'undefined' 
      ? localStorage.getItem('preferred-theme') as ThemeType 
      : null;
    
    const initialTheme = savedTheme && themes[savedTheme] ? savedTheme : defaultTheme;
    setCurrentTheme(initialTheme);
    setTheme(initialTheme);
    setIsLoading(false);
  }, [defaultTheme]);

  const switchTheme = (theme: ThemeType) => {
    if (themes[theme]) {
      setCurrentTheme(theme);
      setTheme(theme);
      
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferred-theme', theme);
      }
    }
  };

  const toggleTheme = () => {
    const newTheme = currentTheme === 'current' ? 'paper-wireframe' : 'current';
    switchTheme(newTheme);
  };

  const value: ThemeContextType = {
    currentTheme,
    switchTheme,
    toggleTheme,
    isLoading,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Theme switching button component
export function ThemeToggleButton({ className = '' }: { className?: string }) {
  const { currentTheme, toggleTheme, isLoading } = useTheme();

  if (isLoading) {
    return (
      <button 
        className={`px-4 py-2 border-2 border-gray-300 rounded-lg bg-gray-100 text-gray-400 cursor-not-allowed ${className}`}
        disabled
      >
        Loading...
      </button>
    );
  }

  const isPaperTheme = currentTheme === 'paper-wireframe';

  return (
    <button
      onClick={toggleTheme}
      className={`
        px-4 py-2 border-2 rounded-lg font-bold text-sm uppercase tracking-wide transition-all duration-300
        ${isPaperTheme 
          ? 'border-black bg-black text-white font-["Comic_Sans_MS",cursive,sans-serif] shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-0.5' 
          : 'border-gray-300 bg-white text-gray-700 shadow-sm hover:shadow-md hover:-translate-y-0.5'
        }
        ${className}
      `}
      title={`Switch to ${isPaperTheme ? 'Current' : 'Paper Wireframe'} Theme`}
    >
      {isPaperTheme ? '📝 Paper' : '🎨 Current'}
    </button>
  );
}

// Theme selector dropdown component
export function ThemeSelector({ className = '' }: { className?: string }) {
  const { currentTheme, switchTheme, isLoading } = useTheme();

  if (isLoading) {
    return (
      <select 
        className={`px-3 py-2 border-2 border-gray-300 rounded-lg bg-gray-100 text-gray-400 cursor-not-allowed ${className}`}
        disabled
      >
        <option>Loading...</option>
      </select>
    );
  }

  const isPaperTheme = currentTheme === 'paper-wireframe';

  return (
    <select
      value={currentTheme}
      onChange={(e) => switchTheme(e.target.value as ThemeType)}
      className={`
        px-3 py-2 border-2 rounded-lg font-bold text-sm transition-all duration-300
        ${isPaperTheme 
          ? 'border-black bg-white text-black font-["Comic_Sans_MS",cursive,sans-serif] shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]' 
          : 'border-gray-300 bg-white text-gray-700 shadow-sm'
        }
        ${className}
      `}
    >
      {Object.entries(themes).map(([key, theme]) => (
        <option key={key} value={key}>
          {theme.name}
        </option>
      ))}
    </select>
  );
}

// Hook to get current theme configuration
export function useThemeConfig() {
  const { currentTheme } = useTheme();
  return themes[currentTheme];
}

// Hook to apply theme-specific styles
export function useThemeStyles() {
  const { currentTheme } = useTheme();
  const config = themes[currentTheme];

  const getButtonClass = (variant: 'primary' | 'secondary' | 'outline' = 'primary') => {
    const baseClass = currentTheme === 'paper-wireframe' 
      ? 'font-["Comic_Sans_MS",cursive,sans-serif] font-bold uppercase tracking-wide border-2 border-black rounded-lg px-6 py-3 transition-all duration-300'
      : 'font-medium border rounded-lg px-6 py-3 transition-all duration-300';

    const variantClass = currentTheme === 'paper-wireframe' 
      ? {
          primary: 'bg-black text-white shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-0.5',
          secondary: 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-0.5 hover:bg-gray-200',
          outline: 'bg-transparent text-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-0.5'
        }
      : {
          primary: 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:border-blue-700',
          secondary: 'bg-gray-200 text-gray-800 border-gray-200 hover:bg-gray-300 hover:border-gray-300',
          outline: 'bg-transparent text-blue-600 border-blue-600 hover:bg-blue-50'
        };

    return `${baseClass} ${variantClass[variant]}`;
  };

  const getCardClass = () => {
    return currentTheme === 'paper-wireframe'
      ? 'bg-white border-2 border-black rounded-lg p-6 shadow-[0_2px_4px_rgba(0,0,0,0.1)] hover:shadow-[0_4px_8px_rgba(0,0,0,0.15)] hover:-translate-y-0.5 transition-all duration-300'
      : 'bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-300';
  };

  const getInputClass = () => {
    return currentTheme === 'paper-wireframe'
      ? 'w-full px-4 py-3 border-2 border-black rounded-lg bg-white font-["Comic_Sans_MS",cursive,sans-serif] focus:outline-none focus:shadow-[0_2px_4px_rgba(0,0,0,0.1)] focus:-translate-y-0.5 transition-all duration-300'
      : 'w-full px-4 py-3 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300';
  };

  const getHeadingClass = (level: 1 | 2 | 3 | 4 | 5 | 6 = 1) => {
    const baseClass = currentTheme === 'paper-wireframe'
      ? 'font-["Comic_Sans_MS",cursive,sans-serif] font-bold uppercase tracking-wide text-black'
      : 'font-semibold text-gray-900';

    const sizeClass = currentTheme === 'paper-wireframe'
      ? {
          1: 'text-3xl md:text-4xl lg:text-5xl',
          2: 'text-2xl md:text-3xl',
          3: 'text-xl md:text-2xl',
          4: 'text-lg md:text-xl',
          5: 'text-base md:text-lg',
          6: 'text-sm md:text-base'
        }
      : {
          1: 'text-3xl md:text-4xl lg:text-5xl',
          2: 'text-2xl md:text-3xl',
          3: 'text-xl md:text-2xl',
          4: 'text-lg md:text-xl',
          5: 'text-base md:text-lg',
          6: 'text-sm md:text-base'
        };

    return `${baseClass} ${sizeClass[level]}`;
  };

  return {
    getButtonClass,
    getCardClass,
    getInputClass,
    getHeadingClass,
    config,
    isPaperTheme: currentTheme === 'paper-wireframe',
  };
}
