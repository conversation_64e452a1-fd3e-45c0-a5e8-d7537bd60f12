# 🎨 Complete Theming Guide

This comprehensive guide covers everything you need to know about implementing, customizing, and extending the paper wireframe theme system.

## 📋 Table of Contents

1. [Quick Start](#quick-start)
2. [Converting Existing Apps](#converting-existing-apps)
3. [Creating New Apps with Themes](#creating-new-apps-with-themes)
4. [Customizing Paper Theme](#customizing-paper-theme)
5. [Adding New Components](#adding-new-components)
6. [Theme Development Prompts](#theme-development-prompts)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

---

## 🚀 Quick Start

### Basic Integration

**For New Apps:**
```typescript
// layout.tsx (Server Component)
import { ThemeProvider } from '@/themes/ThemeProvider';
import AppLayoutClient from './layout-client';

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider defaultTheme="current">
      <AppLayoutClient>{children}</AppLayoutClient>
    </ThemeProvider>
  );
}
```

```typescript
// layout-client.tsx (Client Component)
'use client';
import { useThemeStyles, ThemeToggleButton } from '@/themes/ThemeProvider';

export default function AppLayoutClient({ children }: { children: React.ReactNode }) {
  const { isPaperTheme } = useThemeStyles();
  
  return (
    <div className={`min-h-screen ${isPaperTheme ? 'bg-paper-bg font-paper' : 'bg-white'}`}>
      <nav>
        <ThemeToggleButton />
      </nav>
      {children}
    </div>
  );
}
```

---

## 🔄 Converting Existing Apps

### Step-by-Step Conversion Process

#### 1. **Identify Component Structure**
```bash
# First, analyze your app structure
find src/app/your-app -name "*.tsx" -o -name "*.ts" | head -20
```

#### 2. **Add Theme Provider (Server-Side)**
```typescript
// src/app/your-app/layout.tsx
import { ThemeProvider } from '@/themes/ThemeProvider';
import YourAppClient from './layout-client';

export default function YourAppLayout({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider defaultTheme="current">
      <YourAppClient>{children}</YourAppClient>
    </ThemeProvider>
  );
}
```

#### 3. **Create Client Layout Component**
```typescript
// src/app/your-app/layout-client.tsx
'use client';
import { useThemeStyles, ThemeToggleButton } from '@/themes/ThemeProvider';

export default function YourAppClient({ children }: { children: React.ReactNode }) {
  const { getButtonClass, getCardClass, isPaperTheme } = useThemeStyles();
  
  return (
    <div className={`min-h-screen transition-all duration-300 ${isPaperTheme ? 'bg-paper-bg font-paper' : 'bg-white'}`}>
      {/* Your existing layout with theme-aware classes */}
      {children}
    </div>
  );
}
```

#### 4. **Convert Page Components**
```typescript
// src/app/your-app/page.tsx
'use client';
import { useThemeStyles } from '@/themes/ThemeProvider';

export default function YourAppPage() {
  const { getButtonClass, getCardClass, getHeadingClass, isPaperTheme } = useThemeStyles();
  
  return (
    <div className={isPaperTheme ? 'bg-paper-bg' : 'bg-gray-50'}>
      <h1 className={getHeadingClass(1)}>Your App Title</h1>
      
      <div className={getCardClass()}>
        <p className={isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}>
          Your content here
        </p>
        <button className={getButtonClass('primary')}>
          Action Button
        </button>
      </div>
    </div>
  );
}
```

#### 5. **Update Existing Components**

**Before (Original):**
```typescript
<button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
  Click me
</button>
```

**After (Theme-Aware):**
```typescript
<button className={getButtonClass('primary')}>
  Click me
</button>
```

---

## 🆕 Creating New Apps with Themes

### Development Prompts for AI Assistants

#### **Prompt Template for New Apps:**

```
Create a new [APP_TYPE] app at /src/app/[APP_PATH]/ with the following requirements:

THEMING REQUIREMENTS:
- Use the paper wireframe theme system from /src/themes/
- Implement ThemeProvider in layout.tsx (server component)
- Create layout-client.tsx for theme-aware UI (client component)
- Use theme hooks: useThemeStyles(), useTheme()
- Apply theme-aware styling to all components
- Include ThemeToggleButton in navigation

COMPONENT REQUIREMENTS:
- All buttons should use getButtonClass('primary'|'secondary'|'outline')
- All cards should use getCardClass()
- All headings should use getHeadingClass(1-6)
- All inputs should use getInputClass()
- Use isPaperTheme for conditional styling

STYLING REQUIREMENTS:
- Paper theme: Comic Sans MS font, bold borders, monochromatic colors
- Current theme: Modern gradients, clean typography
- Smooth transitions between themes
- Responsive design for both themes

STRUCTURE:
/src/app/[APP_PATH]/
├── layout.tsx (server component with ThemeProvider)
├── layout-client.tsx (client component with theme logic)
├── page.tsx (main page with theme integration)
├── components/ (theme-aware components)
└── [other-pages]/ (all using theme system)

Please implement [SPECIFIC_FEATURES] with full theme support.
```

#### **Example Usage:**
```
Create a new task management app at /src/app/taskmanager/ with the theming requirements above.

SPECIFIC FEATURES:
- Task list with add/edit/delete functionality
- Priority levels (high, medium, low)
- Due date management
- Search and filter capabilities
- Dashboard with statistics

Ensure all components work perfectly in both current and paper wireframe themes.
```

---

## 🎨 Customizing Paper Theme

### Modifying Theme Variables

#### 1. **Update CSS Variables**
```css
/* src/themes/paper-wireframe/globals.css */
:root {
  /* Modify existing variables */
  --paper-bg: #f5f5f5;           /* Change background */
  --paper-black: #2a2a2a;        /* Softer black */
  --paper-radius: 12px;          /* Rounder corners */
  
  /* Add new variables */
  --paper-accent: #ff6b6b;       /* Add accent color */
  --paper-success: #51cf66;      /* Success color */
  --paper-warning: #ffd43b;      /* Warning color */
}
```

#### 2. **Update Theme Configuration**
```typescript
// src/themes/theme-config.ts
export const themes: Record<ThemeType, ThemeConfig> = {
  'paper-wireframe': {
    name: 'Paper Wireframe',
    description: 'Hand-drawn wireframe style',
    variables: {
      '--paper-bg': '#f5f5f5',
      '--paper-accent': '#ff6b6b',
      '--paper-success': '#51cf66',
      '--paper-warning': '#ffd43b',
      // ... other variables
    }
  }
};
```

#### 3. **Update Tailwind Configuration**
```javascript
// src/themes/paper-wireframe/tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        'paper': {
          'accent': 'var(--paper-accent, #ff6b6b)',
          'success': 'var(--paper-success, #51cf66)',
          'warning': 'var(--paper-warning, #ffd43b)',
        }
      }
    }
  }
};
```

### Creating Theme Variants

#### **Add New Theme Type:**
```typescript
// src/themes/theme-config.ts
export type ThemeType = 'current' | 'paper-wireframe' | 'paper-colorful';

export const themes: Record<ThemeType, ThemeConfig> = {
  // ... existing themes
  'paper-colorful': {
    name: 'Colorful Paper',
    description: 'Paper wireframe with colors',
    cssFiles: {
      globals: '/src/themes/paper-colorful/globals.css',
      styles: '/src/themes/paper-colorful/style.css',
    },
    variables: {
      '--paper-primary': '#3b82f6',
      '--paper-secondary': '#10b981',
      '--paper-accent': '#f59e0b',
    }
  }
};
```

---

## 🧩 Adding New Components

### Component Development Template

#### **Theme-Aware Component Pattern:**
```typescript
// src/themes/components/PaperModal.tsx
'use client';
import { useThemeStyles } from '@/themes/ThemeProvider';

interface PaperModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export function PaperModal({ isOpen, onClose, title, children }: PaperModalProps) {
  const { getCardClass, getHeadingClass, getButtonClass, isPaperTheme } = useThemeStyles();
  
  if (!isOpen) return null;
  
  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${isPaperTheme ? 'bg-black/50' : 'bg-black/50'}`}>
      <div className={`max-w-md w-full mx-4 ${getCardClass()}`}>
        <div className={`flex justify-between items-center mb-4 ${isPaperTheme ? 'border-b-2 border-paper-black pb-4' : 'border-b border-gray-200 pb-4'}`}>
          <h2 className={getHeadingClass(3)}>{title}</h2>
          <button 
            onClick={onClose}
            className={`${isPaperTheme ? 'text-paper-black hover:bg-paper-light-gray' : 'text-gray-500 hover:text-gray-700'} p-2 rounded`}
          >
            ✕
          </button>
        </div>
        <div className="mb-6">
          {children}
        </div>
        <div className="flex justify-end gap-3">
          <button className={getButtonClass('secondary')} onClick={onClose}>
            Cancel
          </button>
          <button className={getButtonClass('primary')}>
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
}
```

#### **Add Component Styles to Theme CSS:**
```css
/* src/themes/paper-wireframe/components.css */
.paper-modal {
  background: var(--paper-white);
  border: 2px solid var(--paper-black);
  border-radius: var(--paper-radius);
  box-shadow: var(--paper-shadow-hover);
  font-family: var(--paper-font);
}

.paper-modal-overlay {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}
```

#### **Update Theme Utilities:**
```typescript
// src/themes/ThemeProvider.tsx - Add to useThemeStyles hook
const getModalClass = () => {
  return currentTheme === 'paper-wireframe'
    ? 'paper-modal bg-paper-white border-2 border-paper-black rounded-lg p-6 shadow-paper-hover'
    : 'bg-white border border-gray-200 rounded-lg p-6 shadow-lg';
};

return {
  // ... existing methods
  getModalClass,
};
```

---

## 💬 Theme Development Prompts

### For Converting Existing Apps

```
Convert the existing [APP_NAME] app at [APP_PATH] to use the paper wireframe theme system:

CONVERSION CHECKLIST:
□ Add ThemeProvider to layout.tsx (server component)
□ Create layout-client.tsx (client component)
□ Convert all pages to use theme hooks
□ Replace hardcoded styles with theme-aware classes
□ Add ThemeToggleButton to navigation
□ Test both themes thoroughly

SPECIFIC REQUIREMENTS:
- Maintain all existing functionality
- Ensure responsive design in both themes
- Use proper server/client component separation
- Apply theme transitions smoothly
- Update [SPECIFIC_COMPONENTS] with theme support

STYLING UPDATES:
- Buttons: Use getButtonClass()
- Cards: Use getCardClass()
- Headings: Use getHeadingClass()
- Forms: Use getInputClass()
- Conditional: Use isPaperTheme for custom styling

Please provide the complete conversion with all files updated.
```

### For New App Development

```
Create a new [APP_TYPE] app with full paper wireframe theme integration:

THEME REQUIREMENTS:
- Implement complete theme system from /src/themes/
- Support both 'current' and 'paper-wireframe' themes
- Include theme toggle in navigation
- Use all theme utilities and hooks
- Ensure smooth transitions

APP FEATURES:
[List specific features here]

COMPONENT REQUIREMENTS:
- All UI components must be theme-aware
- Use theme hooks for styling decisions
- Implement proper server/client component architecture
- Include responsive design for both themes

STYLING SPECIFICATIONS:
- Paper theme: Comic Sans MS, bold borders, monochromatic
- Current theme: Modern, gradients, clean typography
- Smooth 300ms transitions between themes
- Proper hover/focus states in both themes

Please create the complete app structure with theme integration.
```

### For Theme Customization

```
Customize the paper wireframe theme with the following modifications:

VISUAL CHANGES:
- Change font from Comic Sans MS to [NEW_FONT]
- Modify border thickness from 2px to [NEW_THICKNESS]
- Update color palette: [NEW_COLORS]
- Adjust border radius to [NEW_RADIUS]

COMPONENT UPDATES:
- Modify button styles: [BUTTON_CHANGES]
- Update card appearance: [CARD_CHANGES]
- Enhance form elements: [FORM_CHANGES]

FILES TO UPDATE:
- /src/themes/paper-wireframe/globals.css
- /src/themes/paper-wireframe/style.css
- /src/themes/theme-config.ts
- /src/themes/paper-wireframe/tailwind.config.js

Please implement these changes while maintaining theme system compatibility.
```

### For Adding New Components

```
Create a new theme-aware [COMPONENT_NAME] component with the following specifications:

COMPONENT REQUIREMENTS:
- Support both current and paper wireframe themes
- Use theme hooks from /src/themes/ThemeProvider
- Include proper TypeScript interfaces
- Implement responsive design
- Add smooth theme transitions

FUNCTIONALITY:
[Describe component functionality]

STYLING REQUIREMENTS:
- Paper theme: [Paper-specific styling]
- Current theme: [Current theme styling]
- Hover/focus states for both themes
- Accessibility compliance

INTEGRATION:
- Add component to theme system
- Update theme CSS files if needed
- Create usage examples
- Add to theme demo page

Please create the complete component with theme integration.
```

---

## ✅ Best Practices

### 1. **Component Architecture**
- Always separate server and client components
- Use ThemeProvider at the app level
- Keep theme logic in client components only

### 2. **Styling Approach**
- Prefer theme hooks over hardcoded classes
- Use conditional styling with isPaperTheme
- Maintain consistent spacing and sizing

### 3. **Performance**
- Use CSS transitions for smooth theme switching
- Avoid re-rendering entire component trees
- Leverage CSS variables for dynamic theming

### 4. **Accessibility**
- Maintain proper contrast ratios in both themes
- Ensure keyboard navigation works
- Test with screen readers

### 5. **Development Workflow**
- Test components in both themes during development
- Use theme demo page for component showcase
- Document theme-specific behaviors

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### **Server-Side Rendering Errors**
```
Error: useThemeStyles() called from server
```
**Solution:** Move theme hooks to client components only.

#### **Theme Not Switching**
```
Theme toggle button not working
```
**Solution:** Ensure ThemeProvider wraps the component tree.

#### **Styles Not Applied**
```
Paper theme styles not showing
```
**Solution:** Check CSS file imports and Tailwind configuration.

#### **TypeScript Errors**
```
Property 'isPaperTheme' does not exist
```
**Solution:** Import useThemeStyles hook correctly.

### Debug Commands

```bash
# Check theme files
find src/themes -name "*.css" -o -name "*.ts" -o -name "*.tsx"

# Verify imports
grep -r "useThemeStyles" src/app/your-app/

# Check Tailwind config
npx tailwindcss --help
```

---

## 📚 Additional Resources

- **Theme Demo**: `/theme-demo` - See all components in both themes
- **Integration Example**: `/src/themes/integration-example.tsx`
- **ABN Fund Example**: `/web/abnfund` - Complete implementation
- **Theme Configuration**: `/src/themes/theme-config.ts`
- **Component Library**: `/src/themes/components/`

---

## 🤖 AI Assistant Prompt Collection

### **Complete App Creation Prompt**
```
Create a new [APP_TYPE] app at /src/app/[APP_PATH]/ with full paper wireframe theme integration:

REQUIREMENTS:
- Use ThemeProvider architecture from /src/themes/
- Support both 'current' and 'paper-wireframe' themes
- Include ThemeToggleButton in navigation
- Use all theme utilities: getButtonClass, getCardClass, getInputClass, getHeadingClass
- Implement proper server/client component separation
- Add smooth theme transitions (300ms)

FEATURES TO IMPLEMENT:
[List specific app features here]

STYLING SPECIFICATIONS:
- Paper theme: Comic Sans MS font, 2px black borders, monochromatic colors
- Current theme: Modern gradients, clean typography, subtle shadows
- Responsive design for both themes
- Proper hover/focus states

STRUCTURE:
/src/app/[APP_PATH]/
├── layout.tsx (server component with ThemeProvider)
├── layout-client.tsx (client component with theme logic)
├── page.tsx (main page with theme integration)
├── components/ (theme-aware components)
└── [feature-pages]/ (all using theme system)

Please create the complete app with working theme integration.
```

### **App Conversion Prompt**
```
Convert the existing [APP_NAME] app at /src/app/[APP_PATH]/ to use the paper wireframe theme system:

CONVERSION STEPS:
1. Add ThemeProvider to layout.tsx (keep as server component)
2. Create layout-client.tsx for theme-aware UI (client component)
3. Convert all pages to use theme hooks where needed
4. Replace hardcoded styles with theme utilities
5. Add ThemeToggleButton to navigation
6. Update all components to be theme-aware

SPECIFIC UPDATES NEEDED:
- Buttons: Replace with getButtonClass('primary'|'secondary'|'outline')
- Cards: Replace with getCardClass()
- Headings: Replace with getHeadingClass(1-6)
- Forms: Replace with getInputClass()
- Conditional styling: Use isPaperTheme for custom styles

MAINTAIN:
- All existing functionality
- Responsive design
- Performance
- Accessibility

Please provide the complete conversion with all files updated.
```

### **Component Creation Prompt**
```
Create a new theme-aware [COMPONENT_NAME] component with these specifications:

COMPONENT REQUIREMENTS:
- Support both current and paper wireframe themes
- Use useThemeStyles hook from /src/themes/ThemeProvider
- Include proper TypeScript interfaces
- Implement responsive design
- Add smooth theme transitions

FUNCTIONALITY:
[Describe specific component functionality]

STYLING REQUIREMENTS:
- Paper theme: Comic Sans MS font, bold borders, monochromatic colors
- Current theme: Modern styling with gradients and subtle effects
- Hover/focus states for both themes
- Accessibility compliance (WCAG 2.1)

INTEGRATION:
- Add to theme system if needed
- Update theme CSS files if required
- Create usage examples
- Add to theme demo page

Please create the complete component with theme integration and examples.
```

### **Theme Customization Prompt**
```
Customize the paper wireframe theme with these modifications:

VISUAL CHANGES:
- Font: Change from Comic Sans MS to [NEW_FONT]
- Borders: Modify thickness from 2px to [NEW_THICKNESS]px
- Colors: Update palette to [NEW_COLOR_SCHEME]
- Radius: Adjust border radius to [NEW_RADIUS]px
- Shadows: Modify shadow effects to [NEW_SHADOW_STYLE]

COMPONENT UPDATES:
- Buttons: [SPECIFIC_BUTTON_CHANGES]
- Cards: [SPECIFIC_CARD_CHANGES]
- Forms: [SPECIFIC_FORM_CHANGES]
- Navigation: [SPECIFIC_NAV_CHANGES]

FILES TO UPDATE:
- /src/themes/paper-wireframe/globals.css
- /src/themes/paper-wireframe/style.css
- /src/themes/paper-wireframe/components.css
- /src/themes/theme-config.ts
- /src/themes/paper-wireframe/tailwind.config.js

Please implement these changes while maintaining theme system compatibility.
```

---

*This guide covers the complete theming system. For specific implementation questions, refer to the working examples in the codebase or create new issues for additional features.*
