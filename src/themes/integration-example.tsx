/**
 * Integration Example: How to add theme switching to existing components
 * 
 * This file shows how to convert existing components to use the theme system
 */

'use client';

import React from 'react';
import { useTheme, useThemeStyles, ThemeToggleButton } from './ThemeProvider';

// Example 1: Converting an existing header component
function ExampleHeader() {
  const { currentTheme } = useTheme();
  const { getHeadingClass, isPaperTheme } = useThemeStyles();

  return (
    <header className={`
      sticky top-0 z-50 w-full border-b transition-all duration-300
      ${isPaperTheme 
        ? 'bg-white border-black border-b-2 shadow-paper' 
        : 'bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 border-border'
      }
    `}>
      <div className="container flex h-14 items-center justify-between">
        <h1 className={getHeadingClass(2)}>
          My App
        </h1>
        
        <nav className="flex items-center gap-4">
          <a 
            href="#" 
            className={`
              px-3 py-2 rounded transition-all duration-300
              ${isPaperTheme 
                ? 'border-2 border-black bg-white text-black font-bold uppercase tracking-wide hover:bg-gray-200 hover:-translate-y-0.5' 
                : 'text-sm font-medium hover:text-primary'
              }
            `}
          >
            Home
          </a>
          <a 
            href="#" 
            className={`
              px-3 py-2 rounded transition-all duration-300
              ${isPaperTheme 
                ? 'border-2 border-black bg-white text-black font-bold uppercase tracking-wide hover:bg-gray-200 hover:-translate-y-0.5' 
                : 'text-sm font-medium hover:text-primary'
              }
            `}
          >
            About
          </a>
          <ThemeToggleButton />
        </nav>
      </div>
    </header>
  );
}

// Example 2: Converting a card component
interface CardProps {
  title: string;
  description: string;
  children?: React.ReactNode;
}

function ExampleCard({ title, description, children }: CardProps) {
  const { getCardClass, getHeadingClass, isPaperTheme } = useThemeStyles();

  return (
    <div className={getCardClass()}>
      <h3 className={getHeadingClass(3)}>
        {title}
      </h3>
      <p className={`mt-2 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-muted-foreground'}`}>
        {description}
      </p>
      {children && (
        <div className="mt-4">
          {children}
        </div>
      )}
    </div>
  );
}

// Example 3: Converting a form component
function ExampleForm() {
  const { getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();
  const [formData, setFormData] = React.useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label 
          htmlFor="name" 
          className={`
            block text-sm font-medium mb-2
            ${isPaperTheme 
              ? 'text-paper-black font-bold uppercase tracking-wide font-paper' 
              : 'text-foreground'
            }
          `}
        >
          Name
        </label>
        <input
          type="text"
          id="name"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder="Enter your name"
          className={getInputClass()}
        />
      </div>

      <div>
        <label 
          htmlFor="email" 
          className={`
            block text-sm font-medium mb-2
            ${isPaperTheme 
              ? 'text-paper-black font-bold uppercase tracking-wide font-paper' 
              : 'text-foreground'
            }
          `}
        >
          Email
        </label>
        <input
          type="email"
          id="email"
          value={formData.email}
          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
          placeholder="Enter your email"
          className={getInputClass()}
        />
      </div>

      <div>
        <label 
          htmlFor="message" 
          className={`
            block text-sm font-medium mb-2
            ${isPaperTheme 
              ? 'text-paper-black font-bold uppercase tracking-wide font-paper' 
              : 'text-foreground'
            }
          `}
        >
          Message
        </label>
        <textarea
          id="message"
          rows={4}
          value={formData.message}
          onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
          placeholder="Enter your message"
          className={`${getInputClass()} resize-vertical`}
        />
      </div>

      <div className="flex gap-3">
        <button
          type="submit"
          className={getButtonClass('primary')}
        >
          Submit
        </button>
        <button
          type="button"
          onClick={() => setFormData({ name: '', email: '', message: '' })}
          className={getButtonClass('secondary')}
        >
          Reset
        </button>
      </div>
    </form>
  );
}

// Example 4: Converting a data table
interface TableData {
  id: number;
  name: string;
  email: string;
  status: 'active' | 'inactive';
}

function ExampleTable() {
  const { isPaperTheme } = useThemeStyles();
  
  const data: TableData[] = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active' },
  ];

  return (
    <div className="overflow-x-auto">
      <table className={`
        w-full border-collapse
        ${isPaperTheme 
          ? 'border-2 border-black rounded-lg overflow-hidden font-paper' 
          : 'border border-border rounded-lg overflow-hidden'
        }
      `}>
        <thead className={isPaperTheme ? 'bg-paper-light-gray' : 'bg-muted'}>
          <tr>
            <th className={`
              px-4 py-3 text-left font-medium
              ${isPaperTheme 
                ? 'border-b-2 border-black font-bold uppercase tracking-wide text-paper-black' 
                : 'border-b border-border text-muted-foreground'
              }
            `}>
              Name
            </th>
            <th className={`
              px-4 py-3 text-left font-medium
              ${isPaperTheme 
                ? 'border-b-2 border-black font-bold uppercase tracking-wide text-paper-black' 
                : 'border-b border-border text-muted-foreground'
              }
            `}>
              Email
            </th>
            <th className={`
              px-4 py-3 text-left font-medium
              ${isPaperTheme 
                ? 'border-b-2 border-black font-bold uppercase tracking-wide text-paper-black' 
                : 'border-b border-border text-muted-foreground'
              }
            `}>
              Status
            </th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr 
              key={row.id}
              className={`
                transition-colors
                ${isPaperTheme 
                  ? 'hover:bg-paper-light-gray' 
                  : 'hover:bg-muted/50'
                }
              `}
            >
              <td className={`
                px-4 py-3
                ${isPaperTheme 
                  ? index < data.length - 1 ? 'border-b border-black' : ''
                  : index < data.length - 1 ? 'border-b border-border' : ''
                }
              `}>
                {row.name}
              </td>
              <td className={`
                px-4 py-3
                ${isPaperTheme 
                  ? index < data.length - 1 ? 'border-b border-black' : ''
                  : index < data.length - 1 ? 'border-b border-border' : ''
                }
              `}>
                {row.email}
              </td>
              <td className={`
                px-4 py-3
                ${isPaperTheme 
                  ? index < data.length - 1 ? 'border-b border-black' : ''
                  : index < data.length - 1 ? 'border-b border-border' : ''
                }
              `}>
                <span className={`
                  px-2 py-1 text-xs font-medium rounded-full
                  ${row.status === 'active'
                    ? isPaperTheme 
                      ? 'bg-black text-white border-2 border-black uppercase tracking-wide'
                      : 'bg-green-100 text-green-800'
                    : isPaperTheme
                      ? 'bg-white text-black border-2 border-black uppercase tracking-wide'
                      : 'bg-red-100 text-red-800'
                  }
                `}>
                  {row.status}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Example 5: Complete page integration
export default function IntegrationExample() {
  const { isPaperTheme } = useThemeStyles();

  return (
    <div className={`min-h-screen transition-all duration-300 ${isPaperTheme ? 'bg-paper-bg' : 'bg-background'}`}>
      <ExampleHeader />
      
      <main className="container mx-auto px-4 py-8">
        <div className="grid gap-8 md:grid-cols-2">
          <ExampleCard
            title="Contact Form"
            description="Fill out this form to get in touch with us"
          >
            <ExampleForm />
          </ExampleCard>
          
          <ExampleCard
            title="User Data"
            description="Current users in the system"
          >
            <ExampleTable />
          </ExampleCard>
        </div>
      </main>
    </div>
  );
}

// Migration Tips:
// 1. Replace hardcoded colors with theme-aware classes
// 2. Use useThemeStyles() hooks for consistent styling
// 3. Add conditional styling based on isPaperTheme
// 4. Test components in both themes
// 5. Use CSS variables for custom properties
