'use client';

import React, { useState } from 'react';
import { useTheme, useThemeStyles, ThemeToggleButton, ThemeSelector } from './ThemeProvider';

export default function ThemeDemo() {
  const { currentTheme } = useTheme();
  const { getButtonClass, getCardClass, getInputClass, getHeadingClass, isPaperTheme } = useThemeStyles();
  const [inputValue, setInputValue] = useState('');
  const [isChecked, setIsChecked] = useState(false);
  const [selectedOption, setSelectedOption] = useState('option1');

  return (
    <div className={`min-h-screen p-8 transition-all duration-300 ${isPaperTheme ? 'bg-[#f8f8f8] font-["Comic_Sans_MS",cursive,sans-serif]' : 'bg-gray-50'}`}>
      {/* Header */}
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className={getHeadingClass(1)}>
            Theme Switching Demo
          </h1>
          <p className={`mt-4 text-lg ${isPaperTheme ? 'text-[#666666] font-["Comic_Sans_MS",cursive,sans-serif]' : 'text-gray-600'}`}>
            Switch between Current Theme and Paper Wireframe Theme
          </p>
          
          {/* Theme Controls */}
          <div className="flex justify-center items-center gap-4 mt-8">
            <ThemeToggleButton />
            <ThemeSelector />
            <span className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Current: {currentTheme}
            </span>
          </div>
        </div>

        {/* Demo Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          {/* Buttons Demo */}
          <div className={getCardClass()}>
            <h3 className={getHeadingClass(3)}>Buttons</h3>
            <div className="space-y-4">
              <button className={getButtonClass('primary')}>
                Primary Button
              </button>
              <button className={getButtonClass('secondary')}>
                Secondary Button
              </button>
              <button className={getButtonClass('outline')}>
                Outline Button
              </button>
            </div>
          </div>

          {/* Form Elements Demo */}
          <div className={getCardClass()}>
            <h3 className={getHeadingClass(3)}>Form Elements</h3>
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-bold mb-2 ${isPaperTheme ? 'text-black uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'text-gray-700'}`}>
                  Text Input
                </label>
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="Enter some text..."
                  className={getInputClass()}
                />
              </div>
              
              <div>
                <label className={`block text-sm font-bold mb-2 ${isPaperTheme ? 'text-black uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'text-gray-700'}`}>
                  Select Dropdown
                </label>
                <select
                  value={selectedOption}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className={`${getInputClass()} ${isPaperTheme ? 'appearance-none bg-white' : ''}`}
                >
                  <option value="option1">Option 1</option>
                  <option value="option2">Option 2</option>
                  <option value="option3">Option 3</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="demo-checkbox"
                  checked={isChecked}
                  onChange={(e) => setIsChecked(e.target.checked)}
                  className={`mr-3 ${isPaperTheme ? 'w-5 h-5 border-2 border-black rounded' : 'w-4 h-4'}`}
                />
                <label 
                  htmlFor="demo-checkbox"
                  className={`${isPaperTheme ? 'text-black font-bold uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'text-gray-700'}`}
                >
                  Checkbox Option
                </label>
              </div>
            </div>
          </div>

          {/* Typography Demo */}
          <div className={getCardClass()}>
            <h3 className={getHeadingClass(3)}>Typography</h3>
            <div className="space-y-3">
              <h1 className={getHeadingClass(1)}>Heading 1</h1>
              <h2 className={getHeadingClass(2)}>Heading 2</h2>
              <h3 className={getHeadingClass(3)}>Heading 3</h3>
              <p className={`${isPaperTheme ? 'text-[#333333] font-["Comic_Sans_MS",cursive,sans-serif]' : 'text-gray-600'}`}>
                This is a paragraph of text to demonstrate the typography styling in both themes.
              </p>
              <p className={`text-sm ${isPaperTheme ? 'text-[#666666] italic font-["Comic_Sans_MS",cursive,sans-serif]' : 'text-gray-500 italic'}`}>
                This is smaller, italic text.
              </p>
            </div>
          </div>

          {/* Icons Demo */}
          <div className={getCardClass()}>
            <h3 className={getHeadingClass(3)}>Icons & Badges</h3>
            <div className="space-y-4">
              <div className="flex gap-3">
                {isPaperTheme ? (
                  <>
                    <div className="w-6 h-6 border-2 border-black rounded-t relative">
                      <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-b-2 border-transparent border-b-black"></div>
                    </div>
                    <div className="w-6 h-6 border-2 border-black rounded-full"></div>
                    <div className="w-6 h-6 border-2 border-black rounded"></div>
                  </>
                ) : (
                  <>
                    <div className="w-6 h-6 bg-blue-500 rounded"></div>
                    <div className="w-6 h-6 bg-green-500 rounded-full"></div>
                    <div className="w-6 h-6 bg-purple-500 rounded"></div>
                  </>
                )}
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <span className={`px-3 py-1 text-xs font-bold rounded-full ${isPaperTheme ? 'bg-black text-white border-2 border-black uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'bg-blue-100 text-blue-800'}`}>
                  Badge 1
                </span>
                <span className={`px-3 py-1 text-xs font-bold rounded-full ${isPaperTheme ? 'bg-white text-black border-2 border-black uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'bg-green-100 text-green-800'}`}>
                  Badge 2
                </span>
                <span className={`px-3 py-1 text-xs font-bold rounded-full ${isPaperTheme ? 'bg-[#e0e0e0] text-black border-2 border-black uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'bg-purple-100 text-purple-800'}`}>
                  Badge 3
                </span>
              </div>
            </div>
          </div>

          {/* Navigation Demo */}
          <div className={getCardClass()}>
            <h3 className={getHeadingClass(3)}>Navigation</h3>
            <nav className="space-y-2">
              <a href="#" className={`block px-4 py-2 rounded transition-all duration-300 ${isPaperTheme ? 'border-2 border-black bg-black text-white font-bold uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif] hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-0.5' : 'bg-blue-600 text-white hover:bg-blue-700'}`}>
                Active Link
              </a>
              <a href="#" className={`block px-4 py-2 rounded transition-all duration-300 ${isPaperTheme ? 'border-2 border-black bg-white text-black font-bold uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif] hover:bg-[#e0e0e0] hover:-translate-y-0.5' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'}`}>
                Regular Link
              </a>
              <a href="#" className={`block px-4 py-2 rounded transition-all duration-300 ${isPaperTheme ? 'border-2 border-black bg-white text-black font-bold uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif] hover:bg-[#e0e0e0] hover:-translate-y-0.5' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'}`}>
                Another Link
              </a>
            </nav>
          </div>

          {/* Table Demo */}
          <div className={getCardClass()}>
            <h3 className={getHeadingClass(3)}>Table</h3>
            <div className="overflow-x-auto">
              <table className={`w-full ${isPaperTheme ? 'border-2 border-black rounded-lg overflow-hidden' : 'border border-gray-200 rounded-lg overflow-hidden'}`}>
                <thead className={isPaperTheme ? 'bg-[#e0e0e0]' : 'bg-gray-50'}>
                  <tr>
                    <th className={`px-4 py-2 text-left ${isPaperTheme ? 'border-b-2 border-black font-bold uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'border-b border-gray-200 font-medium text-gray-900'}`}>
                      Name
                    </th>
                    <th className={`px-4 py-2 text-left ${isPaperTheme ? 'border-b-2 border-black font-bold uppercase tracking-wide font-["Comic_Sans_MS",cursive,sans-serif]' : 'border-b border-gray-200 font-medium text-gray-900'}`}>
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className={isPaperTheme ? 'hover:bg-[#e0e0e0]' : 'hover:bg-gray-50'}>
                    <td className={`px-4 py-2 ${isPaperTheme ? 'border-b border-black font-["Comic_Sans_MS",cursive,sans-serif]' : 'border-b border-gray-200'}`}>
                      John Doe
                    </td>
                    <td className={`px-4 py-2 ${isPaperTheme ? 'border-b border-black font-["Comic_Sans_MS",cursive,sans-serif]' : 'border-b border-gray-200'}`}>
                      Active
                    </td>
                  </tr>
                  <tr className={isPaperTheme ? 'hover:bg-[#e0e0e0]' : 'hover:bg-gray-50'}>
                    <td className={`px-4 py-2 ${isPaperTheme ? 'font-["Comic_Sans_MS",cursive,sans-serif]' : ''}`}>
                      Jane Smith
                    </td>
                    <td className={`px-4 py-2 ${isPaperTheme ? 'font-["Comic_Sans_MS",cursive,sans-serif]' : ''}`}>
                      Inactive
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-12">
          <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-["Comic_Sans_MS",cursive,sans-serif]' : 'text-gray-500'}`}>
            Theme switching system allows easy toggling between current styles and paper wireframe theme.
          </p>
        </div>
      </div>
    </div>
  );
}
