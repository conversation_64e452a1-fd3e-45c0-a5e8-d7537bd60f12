# Theme Switching System

This directory contains a comprehensive theme switching system that allows easy toggling between the current application styles and a paper wireframe theme.

## Overview

The theme system provides:
- **Current Theme**: The original styling of the application
- **Paper Wireframe Theme**: Hand-drawn wireframe style with Comic Sans font and bold borders

## Directory Structure

```
src/themes/
├── README.md                    # This documentation
├── theme-config.ts             # Theme configuration and utilities
├── ThemeProvider.tsx           # React context and components for theme management
├── css-loader.ts               # Dynamic CSS loading utilities
├── ThemeDemo.tsx               # Demo page showcasing theme switching
├── current-theme/              # Backup of original CSS files
│   ├── globals.css
│   ├── style.css
│   ├── satoshi.css
│   ├── app-globals.css
│   ├── tailwind.config.js
│   ├── tailwind.config.ts
│   ├── portal-theme.ts
│   ├── greenos-styles.module.css
│   ├── eduwise.css
│   ├── gooddeals-tailwind.scss
│   └── shopme-tailwind.scss
└── paper-wireframe/            # Paper wireframe theme files
    ├── globals.css
    ├── style.css
    ├── components.css
    ├── tailwind.config.js
    ├── portal-theme.ts
    ├── greenos-styles.module.css
    ├── eduwise.css
    ├── gooddeals-tailwind.scss
    └── shopme-tailwind.scss
```

## Usage

### 1. Basic Setup

Wrap your app with the ThemeProvider:

```tsx
import { ThemeProvider } from '@/themes/ThemeProvider';

function App() {
  return (
    <ThemeProvider defaultTheme="current">
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

### 2. Theme Toggle Button

Add a theme toggle button anywhere in your app:

```tsx
import { ThemeToggleButton } from '@/themes/ThemeProvider';

function Header() {
  return (
    <header>
      <ThemeToggleButton className="ml-4" />
    </header>
  );
}
```

### 3. Theme Selector Dropdown

For more control, use the theme selector:

```tsx
import { ThemeSelector } from '@/themes/ThemeProvider';

function Settings() {
  return (
    <div>
      <label>Choose Theme:</label>
      <ThemeSelector className="ml-2" />
    </div>
  );
}
```

### 4. Using Theme Hooks

Access theme information and utilities:

```tsx
import { useTheme, useThemeStyles } from '@/themes/ThemeProvider';

function MyComponent() {
  const { currentTheme, switchTheme } = useTheme();
  const { getButtonClass, getCardClass, isPaperTheme } = useThemeStyles();

  return (
    <div className={getCardClass()}>
      <h2>Current Theme: {currentTheme}</h2>
      <button 
        className={getButtonClass('primary')}
        onClick={() => switchTheme('paper-wireframe')}
      >
        Switch to Paper Theme
      </button>
    </div>
  );
}
```

## Theme Configuration

### Adding New Themes

1. Add theme configuration to `theme-config.ts`:

```typescript
export const themes: Record<ThemeType, ThemeConfig> = {
  // ... existing themes
  'new-theme': {
    name: 'New Theme',
    description: 'Description of the new theme',
    cssFiles: {
      globals: '/src/themes/new-theme/globals.css',
      styles: '/src/themes/new-theme/style.css',
    },
    variables: {
      '--primary': '#000000',
      // ... other variables
    }
  }
};
```

2. Create CSS files in the theme directory
3. Update TypeScript types if needed

### CSS Variables

Both themes use CSS variables for easy customization:

**Current Theme Variables:**
- `--primary`: Primary color
- `--secondary`: Secondary color
- `--background`: Background color
- `--foreground`: Text color
- `--border`: Border color

**Paper Wireframe Variables:**
- `--paper-bg`: Background color (#f8f8f8)
- `--paper-white`: White color (#ffffff)
- `--paper-black`: Black color (#333333)
- `--paper-gray`: Gray color (#666666)
- `--paper-light-gray`: Light gray (#e0e0e0)
- `--paper-border`: Border color (#cccccc)
- `--paper-shadow`: Box shadow
- `--paper-radius`: Border radius (8px)
- `--paper-font`: Font family (Comic Sans MS)

## Paper Wireframe Theme Features

The paper wireframe theme includes:

### Typography
- **Font**: Comic Sans MS for hand-drawn feel
- **Weight**: Bold text throughout
- **Transform**: Uppercase headings and buttons
- **Spacing**: Increased letter spacing

### Components
- **Buttons**: Bold borders with drop shadows
- **Cards**: Hand-drawn style borders
- **Inputs**: Thick borders with focus effects
- **Icons**: Simple geometric shapes
- **Tables**: Bold borders and clear structure

### Interactions
- **Hover Effects**: Translate and shadow changes
- **Focus States**: Subtle movements and shadows
- **Active States**: Pressed button effects

### Utility Classes
- `.paper-border`: 2px solid black border
- `.paper-shadow`: Paper-style box shadow
- `.paper-font`: Comic Sans MS font
- `.paper-btn-shadow`: Button drop shadow
- `.paper-hand-drawn`: Hand-drawn border effect

## Tailwind Integration

The theme system is fully integrated with Tailwind CSS:

### Theme-Aware Colors
```css
bg-primary        /* Uses CSS variable --primary */
text-foreground   /* Uses CSS variable --foreground */
border-border     /* Uses CSS variable --border */
```

### Paper-Specific Classes
```css
bg-paper-white    /* Paper wireframe white */
text-paper-black  /* Paper wireframe black */
border-paper-border /* Paper wireframe border */
shadow-paper-btn  /* Paper button shadow */
```

### Font Families
```css
font-paper        /* Comic Sans MS for paper theme */
font-sans         /* Default sans-serif */
font-inter        /* Inter font */
```

## Best Practices

1. **Use Theme Hooks**: Always use `useThemeStyles()` for consistent styling
2. **CSS Variables**: Prefer CSS variables over hardcoded colors
3. **Conditional Styling**: Use `isPaperTheme` for theme-specific logic
4. **Performance**: Themes are loaded dynamically to avoid bundle bloat
5. **Accessibility**: Both themes maintain proper contrast ratios

## Demo

Visit the theme demo page to see all components in both themes:

```tsx
import ThemeDemo from '@/themes/ThemeDemo';

// Use in your routing system
<Route path="/theme-demo" component={ThemeDemo} />
```

## Troubleshooting

### Theme Not Switching
- Check that ThemeProvider wraps your app
- Verify CSS files exist in theme directories
- Check browser console for loading errors

### Styles Not Applied
- Ensure Tailwind includes theme directory in content paths
- Check CSS variable names match theme configuration
- Verify component uses theme hooks correctly

### Performance Issues
- Use `preloadTheme()` for faster switching
- Consider lazy loading theme CSS files
- Monitor bundle size with theme assets

## Contributing

When adding new components or modifying existing ones:

1. Test in both themes
2. Use theme-aware styling
3. Update theme CSS files accordingly
4. Add examples to ThemeDemo if needed
5. Document any new theme variables or utilities
