/* Main container styles */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
}

/* Header styles */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  width: 100%;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.headerTitle {
  font-size: 1.25rem;
  font-weight: bold;
  text-align: center;
  color: #4caf50;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Navigation styles */
.nav {
  position: sticky;
  top: 56px;
  z-index: 40;
  background-color: white;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.25rem;
  overflow-x: auto;
  scrollbar-width: none;
}

.nav::-webkit-scrollbar {
  display: none;
}

.navContent {
  display: flex;
  white-space: nowrap;
  padding: 0 0.5rem;
}

.navLink {
  display: inline-block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  transition: all 0.2s ease;
}

.navLinkActive {
  border-bottom: 2px solid #4caf50;
  color: #4caf50;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.cardMedia {
  position: relative;
  padding-top: 100%;
  background-color: #f0f0f0;
}

.cardIcon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
}

.cardContent {
  padding: 0.5rem;
}

.cardTitle {
  font-weight: 600;
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cardSubtitle {
  font-size: 0.7rem;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Grid layouts */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.5rem;
  padding: 0.5rem;
  width: 100%;
}

.gridCompact {
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
}

/* Dashboard styles */
.dashboard {
  padding: 1rem;
  width: 100%;
}

.dashboardHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.dashboardIcon {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.dashboardTitles {
  flex: 1;
}

.dashboardName {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.dashboardTitle {
  font-size: 1rem;
  color: #666;
}

.dashboardSection {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.dashboardSectionTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.dashboardRow {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.dashboardLabel {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.dashboardValue {
  color: #666;
}

.dashboardHighlight {
  color: #4caf50;
  font-weight: 500;
}

/* Bottom navigation */
.bottomNav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  padding: 0.5rem 0;
  z-index: 50;
}

.bottomNavItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.75rem;
  color: #666;
}

.bottomNavActive {
  color: #4caf50;
}

/* Back button */
.backButton {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4caf50;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;
  margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (min-width: 640px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 0.5rem;
    padding: 0.5rem;
  }
}

@media (min-width: 768px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    padding: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
}

/* Loading skeleton */
.skeleton {
  animation: skeleton-loading 1.5s ease-in-out infinite;
  background-color: #eee;
  border-radius: 0.25rem;
}

@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.skeletonCard {
  height: 200px;
}

.skeletonTitle {
  height: 1rem;
  margin-bottom: 0.5rem;
  width: 80%;
}

.skeletonSubtitle {
  height: 0.75rem;
  width: 60%;
}

/* Base styles */
.container, .mobileContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
}

/* Mobile container specific */
.mobileContainer {
  padding: 1rem;
}

/* Header styles */
.header, .mobileHeader {
  text-align: center;
  margin-bottom: 3rem;
  color: #2c3e50;
}

.mobileHeader {
  margin-bottom: 2rem;
}

.header h1, .mobileHeader h1 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #2ecc71, #27ae60);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mobileHeader h1 {
  font-size: 2rem;
}

.header p, .mobileHeader p {
  font-size: 1.2rem;
  color: #7f8c8d;
}

.mobileHeader p {
  font-size: 1rem;
}

/* Grid and List styles */
.peopleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  padding: 1rem;
}

.mobilePeopleList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem;
}

/* Card styles */
.personCard, .mobilePersonCard {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.mobilePersonCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  text-align: left;
}

.personCard:hover, .mobilePersonCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.personCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2ecc71, #27ae60);
}

/* Icon styles */
.icon, .mobileIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.mobileIcon {
  font-size: 2rem;
  margin-bottom: 0;
}

/* Text styles */
.personCard h3, .mobilePersonCard h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.mobilePersonCard h3 {
  margin-bottom: 0.25rem;
}

.personCard p, .mobilePersonCard p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Dashboard styles */
.dashboard, .mobileDashboard {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mobileDashboard {
  padding: 1rem;
}

/* Back button styles */
.backButton, .mobileBackButton {
  background: none;
  border: none;
  color: #2ecc71;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem 0;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mobileBackButton {
  margin-bottom: 1rem;
}

.backButton:hover, .mobileBackButton:hover {
  color: #27ae60;
}

/* Person header styles */
.personHeader, .mobilePersonHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.mobilePersonHeader {
  margin-bottom: 2rem;
}

.personHeader .icon, .mobilePersonHeader .mobileIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.mobilePersonHeader .mobileIcon {
  font-size: 3rem;
}

.personHeader h2, .mobilePersonHeader h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.mobilePersonHeader h2 {
  font-size: 1.5rem;
}

.personHeader .title, .mobilePersonHeader .mobileTitle {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.mobilePersonHeader .mobileTitle {
  font-size: 1rem;
}

/* Interests grid/list styles */
.interestsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.mobileInterestsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Interest card styles */
.interestCard, .mobileInterestCard {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.mobileInterestCard {
  padding: 1rem;
}

.interestCard:hover, .mobileInterestCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.interestCard h3, .mobileInterestCard h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #2ecc71;
}

.mobileInterestCard h3 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

/* Timeline styles */
.timeline, .mobileTimeline {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.mobileTimeline {
  grid-template-columns: 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.current, .future, .mobileCurrent, .mobileFuture {
  padding: 1rem;
  border-radius: 10px;
}

.current, .mobileCurrent {
  background: #e8f5e9;
}

.future, .mobileFuture {
  background: #c8e6c9;
}

.mobileCurrent, .mobileFuture {
  padding: 0.75rem;
}

/* Timeline text styles */
.timeline h4, .mobileTimeline h4 {
  color: #2c3e50;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.mobileTimeline h4 {
  font-size: 0.9rem;
}

.timeline p, .mobileTimeline p {
  color: #34495e;
  font-size: 0.9rem;
  line-height: 1.4;
}

.mobileTimeline p {
  font-size: 0.8rem;
}

/* Inspiration styles */
.inspiration, .mobileInspiration {
  background: #f1f8e9;
  padding: 1rem;
  border-radius: 10px;
}

.mobileInspiration {
  padding: 0.75rem;
}

.inspiration h4, .mobileInspiration h4 {
  color: #2c3e50;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.mobileInspiration h4 {
  font-size: 0.9rem;
}

.inspiration p, .mobileInspiration p {
  color: #34495e;
  font-size: 0.9rem;
  line-height: 1.4;
}

.mobileInspiration p {
  font-size: 0.8rem;
}

/* Loading styles */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  font-size: 1.2rem;
  color: #2c3e50;
} 