// Theme configuration for Green Portal

interface ThemeColors {
  primary: string;
  secondary: string;
  text: string;
  accent: string;
  accent2: string;
  border: string;
  background: string;
  sale: string;
  new: string;
  hot: string;
  shadow: string;
}

export interface Theme {
  colors: ThemeColors;
  fontFamily: string;
  borderRadius: string;
  boxShadow: string;
}

// Mobile themes
export const mobileTheme: Theme = {
  colors: { 
    primary: '#ffffff',
    secondary: '#f5f5f5',
    text: '#333333',
    accent: '#4caf50',
    accent2: '#2e7d32',
    border: '#e0e0e0',
    background: '#f9f9f9',
    sale: '#ff3d71',
    new: '#4caf50',
    hot: '#ff9800',
    shadow: 'rgba(0, 0, 0, 0.1)'
  },
  fontFamily: "'Segoe UI', 'Roboto', sans-serif",
  borderRadius: '0.5rem',
  boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)'
};

export const mobileDarkTheme: Theme = {
  colors: { 
    primary: '#1a1a1a',
    secondary: '#252525',
    text: '#f5f5f5',
    accent: '#4caf50',
    accent2: '#388e3c',
    border: '#404040',
    background: '#121212',
    sale: '#ff3d71',
    new: '#4caf50',
    hot: '#ff9800',
    shadow: 'rgba(0, 0, 0, 0.3)'
  },
  fontFamily: "'Segoe UI', 'Roboto', sans-serif",
  borderRadius: '0.5rem',
  boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)'
};

// Desktop themes - SHEIN inspired
export const desktopTheme: Theme = {
  colors: { 
    primary: '#ffffff',
    secondary: '#f8f8f8',
    text: '#222222',
    accent: '#009975', // SHEIN's green color
    accent2: '#007c5e',
    border: '#e8e8e8',
    background: '#ffffff',
    sale: '#e73949', // SHEIN's red color
    new: '#009975',
    hot: '#ff6000',
    shadow: 'rgba(0, 0, 0, 0.08)'
  },
  fontFamily: "'Helvetica Neue', Arial, sans-serif", // SHEIN uses a clean sans-serif
  borderRadius: '0.25rem',
  boxShadow: '0 1px 4px rgba(0, 0, 0, 0.08)'
};

export const desktopDarkTheme: Theme = {
  colors: { 
    primary: '#1a1a1a',
    secondary: '#252525',
    text: '#f0f0f0',
    accent: '#00b589', // Brighter for dark mode
    accent2: '#00967a',
    border: '#333333',
    background: '#121212',
    sale: '#f04553', // Brighter red for dark mode
    new: '#00b589',
    hot: '#ff7a2f',
    shadow: 'rgba(0, 0, 0, 0.25)'
  },
  fontFamily: "'Helvetica Neue', Arial, sans-serif",
  borderRadius: '0.25rem',
  boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)'
};

// For backward compatibility
export const lightTheme = desktopTheme;
export const darkTheme = desktopDarkTheme; 