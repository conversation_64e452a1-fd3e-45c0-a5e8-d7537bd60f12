@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* WISE Vietnam Color Palette */
  --wise-primary: #7C3AED;        /* Purple - Main brand color */
  --wise-primary-dark: #5B21B6;   /* Darker purple for hover states */
  --wise-primary-light: #A855F7;  /* Lighter purple for backgrounds */
  --wise-secondary: #F97316;      /* Orange - Accent color */
  --wise-secondary-dark: #EA580C; /* Darker orange for hover */
  --wise-secondary-light: #FB923C; /* Lighter orange */
  --wise-accent: #EC4899;         /* Pink - Supporting accent */
  --wise-accent-light: #F472B6;   /* Light pink */
  --wise-neutral-50: #FAFAF9;     /* Very light gray */
  --wise-neutral-100: #F5F5F4;    /* Light gray */
  --wise-neutral-200: #E7E5E4;    /* Medium light gray */
  --wise-neutral-300: #D6D3D1;    /* Medium gray */
  --wise-neutral-400: #A8A29E;    /* Medium dark gray */
  --wise-neutral-500: #78716C;    /* Dark gray */
  --wise-neutral-600: #57534E;    /* Darker gray */
  --wise-neutral-700: #44403C;    /* Very dark gray */
  --wise-neutral-800: #292524;    /* Almost black */
  --wise-neutral-900: #1C1917;    /* Black */
  --wise-success: #10B981;        /* Green for success states */
  --wise-warning: #F59E0B;        /* Amber for warnings */
  --wise-error: #EF4444;          /* Red for errors */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* WISE Vietnam Color Palette for Tailwind */
  --color-wise-primary: var(--wise-primary);
  --color-wise-primary-dark: var(--wise-primary-dark);
  --color-wise-primary-light: var(--wise-primary-light);
  --color-wise-secondary: var(--wise-secondary);
  --color-wise-secondary-dark: var(--wise-secondary-dark);
  --color-wise-secondary-light: var(--wise-secondary-light);
  --color-wise-accent: var(--wise-accent);
  --color-wise-accent-light: var(--wise-accent-light);
  --color-wise-neutral-50: var(--wise-neutral-50);
  --color-wise-neutral-100: var(--wise-neutral-100);
  --color-wise-neutral-200: var(--wise-neutral-200);
  --color-wise-neutral-300: var(--wise-neutral-300);
  --color-wise-neutral-400: var(--wise-neutral-400);
  --color-wise-neutral-500: var(--wise-neutral-500);
  --color-wise-neutral-600: var(--wise-neutral-600);
  --color-wise-neutral-700: var(--wise-neutral-700);
  --color-wise-neutral-800: var(--wise-neutral-800);
  --color-wise-neutral-900: var(--wise-neutral-900);
  --color-wise-success: var(--wise-success);
  --color-wise-warning: var(--wise-warning);
  --color-wise-error: var(--wise-error);

  /* Override default Tailwind colors with WISE theme */
  --color-blue-600: var(--wise-primary);
  --color-blue-700: var(--wise-primary-dark);
  --color-blue-500: var(--wise-primary-light);
  --color-purple-600: var(--wise-primary);
  --color-purple-700: var(--wise-primary-dark);
  --color-purple-500: var(--wise-primary-light);
  --color-orange-500: var(--wise-secondary);
  --color-orange-600: var(--wise-secondary-dark);
  --color-pink-500: var(--wise-accent);
  --color-pink-600: var(--wise-accent-light);
  --color-gray-50: var(--wise-neutral-50);
  --color-gray-100: var(--wise-neutral-100);
  --color-gray-200: var(--wise-neutral-200);
  --color-gray-300: var(--wise-neutral-300);
  --color-gray-400: var(--wise-neutral-400);
  --color-gray-500: var(--wise-neutral-500);
  --color-gray-600: var(--wise-neutral-600);
  --color-gray-700: var(--wise-neutral-700);
  --color-gray-800: var(--wise-neutral-800);
  --color-gray-900: var(--wise-neutral-900);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Reset and Base Styles */
body {
    background-color: var(--wise-neutral-50);
    color: var(--wise-neutral-800);
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    height: 100%;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: var(--wise-primary);
    transition: all 0.3s ease;
}

a:hover {
    color: var(--wise-primary-dark);
}

input, button, a {
    transition: all 0.4s ease;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--wise-neutral-800);
    font-weight: 600;
}

.custom-select:focus, :focus, [contenteditable].form-control:focus, [type=email].form-control:focus, [type=password].form-control:focus, [type=tel].form-control:focus, [type=text].form-control:focus, button:focus, input.form-control:focus, input[type=email]:focus, input[type=number]:focus, input[type=password]:focus, input[type=text]:focus, select:focus, textarea.form-control:focus, textarea:focus {
    outline: none !important;
    box-shadow: none;
}

/* Header Styles */
.header,
.header-fixed {
    background-color: #273044;
}

.header-fixed {
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 999;
}

.navbar-header img {
    max-height: 85px;
}

a.navbar-brand.logo {
    margin-right: 60px;
}

.header-navbar-rht {
    margin: 0 0 0 25px;
    padding: 0;
}

.header-navbar-rht li {
    align-items: center;
    display: inline-flex;
    padding-right: 20px;
    justify-content: center;
    position: relative;
}

.header-navbar-rht > li > a {
    font-size: 16px;
    font-weight: 500;
    color: #26292c;
}

.header-navbar-rht > li > a.header-sign,
.header-navbar-rht > li > a.header-login {
    background: #fff;
    border-radius: 99px;
    padding-left: 30px;
    padding-right: 30px;
}

.header-navbar-rht > li > a.header-sign:hover,
.header-navbar-rht > li > a.header-login:hover {
    color: #fff !important;
    background: #a50f34 !important;
}

.header-navbar-rht li:last-child {
    padding-right: 0;
}

/* Menu Styles */
.main-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu-header {
    align-items: center;
    background-color: #fff;
    height: 60px;
    padding: 0 20px;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    display: none;
}

.menu-logo img {
    width: 130px;
}

.main-nav li a {
    display: block;
    font-size: 15px;
    font-weight: 500;
    color: #fff;
}

@media (min-width: 992px) {
    .main-nav > li {
        margin-right: 15px;
    }
}

@media (min-width: 1200px) {
    .main-nav > li {
        margin-right: 30px;
    }
}

/* Main Content Styles */
.master-skill {
    padding: 80px 0px 0px;
}

.blur-border {
    padding: 3px;
    border-radius: 10px;
}

.section-header {
    margin-bottom: 18px;
    position: relative;
    display: flex;
}

.section-sub-head h2 {
    font-size: 36px;
    margin-bottom: 0;
    font-weight: 700;
    color: #0B0B0B;
    letter-spacing: 0.9px;
}

.section-sub-head span {
    color: #F66962;
    font-size: 18px;
    font-weight: 700;
    padding-bottom: 18px;
    display: block;
    letter-spacing: 0.9px;
}

.section-text {
    max-width: 700px;
    color: #685F78;
    font-size: 16px;
    font-weight: 500;
}

.career-group {
    margin: 60px 0px;
}

.trend-course {
    padding: 80px 0px;
    background-color: #f8f9fa;
}

.certified-group {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    width: 100%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.certified-group:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.get-certified {
    width: 100%;
}

.blur-box {
    margin-right: 15px;
}

.certified-img {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.certified-img img {
    max-width: 100%;
    height: auto;
}

.get-certified p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

/* Course Card Styles */
.course-box {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.course-box:hover {
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
}

.product-img {
    position: relative;
    overflow: hidden;
}

.product-img img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-box:hover .product-img img {
    transform: scale(1.05);
}

.product-content {
    padding: 20px;
}

.course-group {
    margin-bottom: 15px;
}

.course-group-img {
    align-items: center;
}

.course-group-img img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

.course-name h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.course-name h4 a {
    color: #333;
    text-decoration: none;
}

.course-name h4 a:hover {
    color: #a50f34;
}

.course-name p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.course-share {
    margin-left: auto;
}

.course-share a {
    color: #ccc;
    font-size: 18px;
    transition: all 0.3s ease;
}

.course-share a:hover {
    color: #a50f34;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin: 15px 0;
    color: #333;
}

.title a {
    color: #333;
    text-decoration: none;
}

.title a:hover {
    color: #a50f34;
}

.course-info {
    margin: 15px 0;
}

.rating-img, .course-view {
    margin-right: 20px;
}

.rating-img img, .course-view img {
    width: 16px;
    height: 16px;
    margin-right: 5px;
}

.rating-img p, .course-view p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.rating {
    margin: 15px 0;
}

.rating .fas.fa-star.filled {
    color: #ffc107;
}

.rating .fas.fa-star {
    color: #e0e0e0;
}

.average-rating {
    font-size: 14px;
    color: #666;
    margin-left: 10px;
}

.all-btn {
    margin-top: 15px;
}

.btn-primary {
    background-color: #a50f34;
    border-color: #a50f34;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #8a0d2a;
    border-color: #8a0d2a;
    color: #fff;
}

/* Custom utilities for course platform */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Footer Styles */
.footer {
    background: #273044;
    color: #fff;
}

.footer-top {
    padding-top: 60px;
    padding-bottom: 60px;
    position: relative;
    z-index: 9;
    font-size: 14px;
}

.footer-title {
    color: #fff;
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.footer-widget.footer-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
    outline: none;
}

.footer-widget .footer-logo {
    margin-bottom: 15px;
}

.footer-widget .footer-logo img {
    width: 160px;
    max-width: 100%;
}

.footer-widget .footer-about-content p {
    color: #fff;
}

.footer-widget .footer-about-content p:last-child {
    margin-bottom: 0;
}

.footer-menu ul li {
    margin-bottom: 20px;
    position: relative;
}

.footer-menu ul li:last-child {
    margin-bottom: 0;
}

.footer-menu ul li a {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-menu ul li a:hover {
    color: #a50f34;
}

.social-icon-three ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.social-icon-three ul li {
    margin-right: 15px;
}

.social-icon-three ul li:last-child {
    margin-right: 0;
}

.social-icon-three ul li a {
    color: #fff;
    font-size: 18px;
    transition: all 0.3s ease;
}

.social-icon-three ul li a:hover {
    color: #a50f34;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    padding-bottom: 20px;
}

.copyright-text p {
    color: #fff;
    font-size: 14px;
    margin: 0;
}

/* Testimonial Styles */
.testimonial-three {
    padding: 80px 0;
    background: #fff;
    position: relative;
}

.testimonial-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.testimonial-pattern .pattern-left {
    position: absolute;
    left: 50px;
    top: 50px;
    opacity: 0.3;
}

.testimonial-pattern .pattern-right {
    position: absolute;
    right: 50px;
    bottom: 50px;
    opacity: 0.3;
}

.testimonial-three-content {
    position: relative;
    z-index: 2;
}

.become-content h2 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
}

.become-content h4 {
    font-size: 18px;
    font-weight: 500;
    color: #666;
    margin-bottom: 0;
}

.testimonial-item-five {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.testimonial-quote {
    margin-bottom: 20px;
}

.testimonial-content p {
    font-size: 16px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 20px;
}

.testimonial-ratings {
    margin-bottom: 20px;
}

.testimonial-users {
    display: flex;
    align-items: center;
}

.testimonial-users .imgbx {
    margin-right: 15px;
}

.testimonial-users .imgbx img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-users h6 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.testimonial-users p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* Form Styles */
.form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    border-radius: 5px;
    padding: 10px 15px;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: #a50f34;
    box-shadow: 0 0 0 0.2rem rgba(165, 15, 52, 0.25);
    color: #fff;
}

.btn-submit {
    background-color: #a50f34;
    border: 1px solid #a50f34;
    color: #fff;
    padding: 12px 30px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    background-color: #8a0d2a;
    border-color: #8a0d2a;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Prose styles for course content */
.prose {
  max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: #1f2937;
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.prose h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.prose h4 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.prose p {
  margin-top: 1em;
  margin-bottom: 1em;
  line-height: 1.75;
}

.prose ul,
.prose ol {
  margin-top: 1em;
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose strong {
  font-weight: 600;
  color: #1f2937;
}

.prose em {
  font-style: italic;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 1.5em 0;
  font-style: italic;
  color: #6b7280;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.prose pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5em 0;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

/* Animation classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0;
}

.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-end {
    justify-content: end;
}

.flex-column {
    flex-direction: column;
}

.d-none {
    display: none;
}

.d-block {
    display: block;
}

@media (min-width: 768px) {
    .d-md-none {
        display: none;
    }

    .d-md-flex {
        display: flex;
    }

    .d-md-block {
        display: block;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-sub-head h2 {
        font-size: 28px;
    }

    .master-skill {
        padding: 40px 0px 0px;
    }

    .career-group {
        margin: 40px 0px;
    }

    .trend-course {
        padding: 40px 0px;
    }

    .footer-top {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}

/* Mobile responsive improvements */
@media (max-width: 640px) {
  .prose h1 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .prose h2 {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .prose h3 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}
