/**
 * CSS Loader Utility for Theme Switching
 * Dynamically loads and unloads CSS files based on the active theme
 */

import { ThemeType, themes } from './theme-config';

interface LoadedStylesheet {
  id: string;
  element: HTMLLinkElement;
  theme: ThemeType;
}

class CSSLoader {
  private loadedStylesheets: Map<string, LoadedStylesheet> = new Map();
  private currentTheme: ThemeType = 'current';

  /**
   * Load CSS file dynamically
   */
  private loadCSS(href: string, id: string, theme: ThemeType): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (this.loadedStylesheets.has(id)) {
        resolve();
        return;
      }

      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = href;
      link.id = id;

      link.onload = () => {
        this.loadedStylesheets.set(id, {
          id,
          element: link,
          theme
        });
        resolve();
      };

      link.onerror = () => {
        reject(new Error(`Failed to load CSS: ${href}`));
      };

      document.head.appendChild(link);
    });
  }

  /**
   * Remove CSS file
   */
  private removeCSS(id: string): void {
    const stylesheet = this.loadedStylesheets.get(id);
    if (stylesheet) {
      document.head.removeChild(stylesheet.element);
      this.loadedStylesheets.delete(id);
    }
  }

  /**
   * Remove all CSS files for a specific theme
   */
  private removeThemeCSS(theme: ThemeType): void {
    const toRemove: string[] = [];
    
    this.loadedStylesheets.forEach((stylesheet, id) => {
      if (stylesheet.theme === theme) {
        toRemove.push(id);
      }
    });

    toRemove.forEach(id => this.removeCSS(id));
  }

  /**
   * Load all CSS files for a theme
   */
  async loadThemeCSS(theme: ThemeType): Promise<void> {
    const themeConfig = themes[theme];
    if (!themeConfig) {
      throw new Error(`Theme not found: ${theme}`);
    }

    const loadPromises: Promise<void>[] = [];

    // Load main CSS files
    if (themeConfig.cssFiles.globals) {
      loadPromises.push(
        this.loadCSS(
          themeConfig.cssFiles.globals,
          `theme-${theme}-globals`,
          theme
        )
      );
    }

    if (themeConfig.cssFiles.styles) {
      loadPromises.push(
        this.loadCSS(
          themeConfig.cssFiles.styles,
          `theme-${theme}-styles`,
          theme
        )
      );
    }

    if (themeConfig.cssFiles.components) {
      loadPromises.push(
        this.loadCSS(
          themeConfig.cssFiles.components,
          `theme-${theme}-components`,
          theme
        )
      );
    }

    try {
      await Promise.all(loadPromises);
      console.log(`Successfully loaded CSS for theme: ${theme}`);
    } catch (error) {
      console.error(`Failed to load CSS for theme: ${theme}`, error);
      throw error;
    }
  }

  /**
   * Switch to a new theme
   */
  async switchTheme(newTheme: ThemeType): Promise<void> {
    if (newTheme === this.currentTheme) {
      return; // Already on this theme
    }

    try {
      // Load new theme CSS first
      await this.loadThemeCSS(newTheme);
      
      // Remove old theme CSS
      if (this.currentTheme !== newTheme) {
        this.removeThemeCSS(this.currentTheme);
      }

      this.currentTheme = newTheme;
      
      // Apply theme variables to CSS custom properties
      this.applyThemeVariables(newTheme);
      
      console.log(`Successfully switched to theme: ${newTheme}`);
    } catch (error) {
      console.error(`Failed to switch to theme: ${newTheme}`, error);
      throw error;
    }
  }

  /**
   * Apply theme variables to CSS custom properties
   */
  private applyThemeVariables(theme: ThemeType): void {
    const themeConfig = themes[theme];
    const root = document.documentElement;

    // Apply theme variables
    Object.entries(themeConfig.variables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Add theme class to body for CSS targeting
    document.body.classList.remove('theme-current', 'theme-paper-wireframe');
    document.body.classList.add(`theme-${theme}`);
  }

  /**
   * Get currently loaded theme
   */
  getCurrentTheme(): ThemeType {
    return this.currentTheme;
  }

  /**
   * Get list of loaded stylesheets
   */
  getLoadedStylesheets(): LoadedStylesheet[] {
    return Array.from(this.loadedStylesheets.values());
  }

  /**
   * Preload theme CSS (useful for faster switching)
   */
  async preloadTheme(theme: ThemeType): Promise<void> {
    if (theme === this.currentTheme) {
      return; // Already loaded
    }

    try {
      await this.loadThemeCSS(theme);
      console.log(`Successfully preloaded theme: ${theme}`);
    } catch (error) {
      console.error(`Failed to preload theme: ${theme}`, error);
    }
  }

  /**
   * Initialize with a specific theme
   */
  async initialize(theme: ThemeType = 'current'): Promise<void> {
    try {
      await this.loadThemeCSS(theme);
      this.currentTheme = theme;
      this.applyThemeVariables(theme);
      console.log(`CSS Loader initialized with theme: ${theme}`);
    } catch (error) {
      console.error(`Failed to initialize CSS Loader with theme: ${theme}`, error);
      throw error;
    }
  }

  /**
   * Clean up all loaded stylesheets
   */
  cleanup(): void {
    this.loadedStylesheets.forEach((stylesheet) => {
      document.head.removeChild(stylesheet.element);
    });
    this.loadedStylesheets.clear();
    
    // Remove theme classes
    document.body.classList.remove('theme-current', 'theme-paper-wireframe');
  }
}

// Create singleton instance
const cssLoader = new CSSLoader();

export default cssLoader;

// Export utility functions
export const loadThemeCSS = (theme: ThemeType) => cssLoader.loadThemeCSS(theme);
export const switchTheme = (theme: ThemeType) => cssLoader.switchTheme(theme);
export const preloadTheme = (theme: ThemeType) => cssLoader.preloadTheme(theme);
export const initializeCSSLoader = (theme: ThemeType) => cssLoader.initialize(theme);
export const getCurrentTheme = () => cssLoader.getCurrentTheme();
export const getLoadedStylesheets = () => cssLoader.getLoadedStylesheets();
export const cleanupCSSLoader = () => cssLoader.cleanup();

// React hook for CSS loading
export function useCSSLoader() {
  const switchThemeWithCSS = async (theme: ThemeType) => {
    try {
      await cssLoader.switchTheme(theme);
    } catch (error) {
      console.error('Failed to switch theme CSS:', error);
    }
  };

  const preloadThemeCSS = async (theme: ThemeType) => {
    try {
      await cssLoader.preloadTheme(theme);
    } catch (error) {
      console.error('Failed to preload theme CSS:', error);
    }
  };

  return {
    switchTheme: switchThemeWithCSS,
    preloadTheme: preloadThemeCSS,
    getCurrentTheme: () => cssLoader.getCurrentTheme(),
    getLoadedStylesheets: () => cssLoader.getLoadedStylesheets(),
  };
}
