// Paper Wireframe Theme configuration for Green Portal

interface ThemeColors {
  primary: string;
  secondary: string;
  text: string;
  accent: string;
  accent2: string;
  border: string;
  background: string;
  sale: string;
  new: string;
  hot: string;
  shadow: string;
}

export interface Theme {
  colors: ThemeColors;
  fontFamily: string;
  borderRadius: string;
  boxShadow: string;
}

// Paper wireframe mobile theme
export const mobileTheme: Theme = {
  colors: { 
    primary: '#ffffff',
    secondary: '#e0e0e0',
    text: '#333333',
    accent: '#333333',
    accent2: '#666666',
    border: '#cccccc',
    background: '#f8f8f8',
    sale: '#333333',
    new: '#333333',
    hot: '#333333',
    shadow: 'rgba(0, 0, 0, 0.1)'
  },
  fontFamily: "'Comic Sans MS', cursive, sans-serif",
  borderRadius: '8px',
  boxShadow: '4px 4px 0px 0px rgba(0, 0, 0, 0.9)'
};

export const mobileDarkTheme: Theme = {
  colors: { 
    primary: '#3a3a3a',
    secondary: '#4a4a4a',
    text: '#ffffff',
    accent: '#ffffff',
    accent2: '#cccccc',
    border: '#555555',
    background: '#2a2a2a',
    sale: '#ffffff',
    new: '#ffffff',
    hot: '#ffffff',
    shadow: 'rgba(0, 0, 0, 0.25)'
  },
  fontFamily: "'Comic Sans MS', cursive, sans-serif",
  borderRadius: '8px',
  boxShadow: '4px 4px 0px 0px rgba(255, 255, 255, 0.3)'
};

// Paper wireframe desktop theme
export const desktopTheme: Theme = {
  colors: { 
    primary: '#ffffff',
    secondary: '#e0e0e0',
    text: '#333333',
    accent: '#333333', // Paper wireframe black
    accent2: '#666666',
    border: '#cccccc',
    background: '#f8f8f8',
    sale: '#333333', // All accent colors are black in wireframe
    new: '#333333',
    hot: '#333333',
    shadow: 'rgba(0, 0, 0, 0.1)'
  },
  fontFamily: "'Comic Sans MS', cursive, sans-serif", // Paper wireframe font
  borderRadius: '8px',
  boxShadow: '4px 4px 0px 0px rgba(0, 0, 0, 0.9)' // Paper wireframe shadow
};

export const desktopDarkTheme: Theme = {
  colors: { 
    primary: '#3a3a3a',
    secondary: '#4a4a4a',
    text: '#ffffff',
    accent: '#ffffff', // White for dark mode wireframe
    accent2: '#cccccc',
    border: '#555555',
    background: '#2a2a2a',
    sale: '#ffffff', // White accents for dark mode
    new: '#ffffff',
    hot: '#ffffff',
    shadow: 'rgba(0, 0, 0, 0.25)'
  },
  fontFamily: "'Comic Sans MS', cursive, sans-serif",
  borderRadius: '8px',
  boxShadow: '4px 4px 0px 0px rgba(255, 255, 255, 0.3)'
};

// For backward compatibility
export const lightTheme = desktopTheme;
export const darkTheme = desktopDarkTheme;

// Paper wireframe specific theme utilities
export const paperWireframeUtils = {
  // Button styles
  buttonPrimary: {
    backgroundColor: '#333333',
    color: '#ffffff',
    border: '2px solid #333333',
    borderRadius: '8px',
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
    fontWeight: 'bold',
    textTransform: 'uppercase' as const,
    letterSpacing: '1px',
    boxShadow: '4px 4px 0px 0px rgba(0, 0, 0, 0.9)',
    transition: 'all 0.3s ease',
  },
  
  buttonSecondary: {
    backgroundColor: '#ffffff',
    color: '#333333',
    border: '2px solid #333333',
    borderRadius: '8px',
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
    fontWeight: 'bold',
    textTransform: 'uppercase' as const,
    letterSpacing: '1px',
    boxShadow: '4px 4px 0px 0px rgba(0, 0, 0, 0.9)',
    transition: 'all 0.3s ease',
  },
  
  // Card styles
  card: {
    backgroundColor: '#ffffff',
    border: '2px solid #333333',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
    transition: 'all 0.3s ease',
  },
  
  // Input styles
  input: {
    border: '2px solid #333333',
    borderRadius: '8px',
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
    backgroundColor: '#ffffff',
    color: '#333333',
    padding: '12px 16px',
    fontSize: '16px',
  },
  
  // Typography
  heading: {
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
    fontWeight: 'bold',
    color: '#333333',
    textTransform: 'uppercase' as const,
    letterSpacing: '1px',
  },
  
  body: {
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
    color: '#333333',
    lineHeight: '1.6',
  },
  
  // Icon styles
  icon: {
    border: '2px solid #333333',
    borderRadius: '4px',
    backgroundColor: '#ffffff',
    color: '#333333',
  },
  
  // Tag styles
  tag: {
    backgroundColor: '#ffffff',
    border: '2px solid #333333',
    borderRadius: '20px',
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
    fontWeight: 'bold',
    textTransform: 'uppercase' as const,
    letterSpacing: '0.5px',
    padding: '6px 12px',
    fontSize: '14px',
  },
  
  // Navigation styles
  nav: {
    backgroundColor: '#ffffff',
    borderBottom: '2px solid #333333',
    fontFamily: "'Comic Sans MS', cursive, sans-serif",
  },
  
  navItem: {
    backgroundColor: '#ffffff',
    border: '2px solid #333333',
    borderRadius: '8px',
    color: '#333333',
    fontWeight: 'bold',
    textTransform: 'uppercase' as const,
    letterSpacing: '0.5px',
    padding: '8px 16px',
    textDecoration: 'none',
    transition: 'all 0.3s ease',
  },
  
  navItemActive: {
    backgroundColor: '#333333',
    color: '#ffffff',
  },
};

// Export all themes and utilities
export {
  mobileTheme as paperMobileTheme,
  mobileDarkTheme as paperMobileDarkTheme,
  desktopTheme as paperDesktopTheme,
  desktopDarkTheme as paperDesktopDarkTheme,
  paperWireframeUtils as paperUtils,
};
