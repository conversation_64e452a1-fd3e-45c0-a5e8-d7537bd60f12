/* Paper Wireframe EduWise Theme */

:root {
  /* Paper Wireframe EduWise Color Palette */
  --wise-primary: #333333;
  --wise-primary-dark: #222222;
  --wise-primary-light: #555555;
  --wise-secondary: #333333;
  --wise-secondary-dark: #222222;
  --wise-accent: #333333;
  --wise-accent-light: #555555;
  --wise-success: #333333;
  --wise-warning: #333333;
  --wise-error: #333333;
  --wise-info: #333333;
  
  /* Paper Wireframe Neutral Colors */
  --wise-neutral-50: #f8f8f8;
  --wise-neutral-100: #f0f0f0;
  --wise-neutral-200: #e0e0e0;
  --wise-neutral-300: #cccccc;
  --wise-neutral-400: #999999;
  --wise-neutral-500: #666666;
  --wise-neutral-600: #555555;
  --wise-neutral-700: #444444;
  --wise-neutral-800: #333333;
  --wise-neutral-900: #222222;
  
  /* Paper Wireframe Background Colors */
  --wise-bg-primary: #f8f8f8;
  --wise-bg-secondary: #ffffff;
  --wise-bg-tertiary: #e0e0e0;
  --wise-bg-accent: #cccccc;
  
  /* Paper Wireframe Text Colors */
  --wise-text-primary: #333333;
  --wise-text-secondary: #666666;
  --wise-text-tertiary: #999999;
  --wise-text-inverse: #ffffff;
  
  /* Paper Wireframe Border and Shadow */
  --wise-border-color: #333333;
  --wise-border-light: #cccccc;
  --wise-shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --wise-shadow-md: 0 4px 8px rgba(0,0,0,0.15);
  --wise-shadow-lg: 4px 4px 0px 0px rgba(0,0,0,0.9);
  
  /* Paper Wireframe Typography */
  --wise-font-family: 'Comic Sans MS', cursive, sans-serif;
  --wise-font-size-xs: 12px;
  --wise-font-size-sm: 14px;
  --wise-font-size-base: 16px;
  --wise-font-size-lg: 18px;
  --wise-font-size-xl: 20px;
  --wise-font-size-2xl: 24px;
  --wise-font-size-3xl: 30px;
  
  /* Paper Wireframe Spacing */
  --wise-spacing-xs: 4px;
  --wise-spacing-sm: 8px;
  --wise-spacing-md: 16px;
  --wise-spacing-lg: 24px;
  --wise-spacing-xl: 32px;
  --wise-spacing-2xl: 48px;
  
  /* Paper Wireframe Border Radius */
  --wise-radius-sm: 4px;
  --wise-radius-md: 8px;
  --wise-radius-lg: 12px;
  --wise-radius-xl: 16px;
  --wise-radius-full: 9999px;

  /* Override default Tailwind colors with WISE paper wireframe theme */
  --color-blue-600: var(--wise-primary);
  --color-blue-700: var(--wise-primary-dark);
  --color-blue-500: var(--wise-primary-light);
  --color-purple-600: var(--wise-primary);
  --color-purple-700: var(--wise-primary-dark);
  --color-purple-500: var(--wise-primary-light);
  --color-orange-500: var(--wise-secondary);
  --color-orange-600: var(--wise-secondary-dark);
  --color-pink-500: var(--wise-accent);
  --color-pink-600: var(--wise-accent-light);
  --color-gray-50: var(--wise-neutral-50);
  --color-gray-100: var(--wise-neutral-100);
  --color-gray-200: var(--wise-neutral-200);
  --color-gray-300: var(--wise-neutral-300);
  --color-gray-400: var(--wise-neutral-400);
  --color-gray-500: var(--wise-neutral-500);
  --color-gray-600: var(--wise-neutral-600);
  --color-gray-700: var(--wise-neutral-700);
  --color-gray-800: var(--wise-neutral-800);
  --color-gray-900: var(--wise-neutral-900);
}

/* Base styles with paper wireframe theme */
* {
  font-family: var(--wise-font-family);
}

body {
  font-family: var(--wise-font-family);
  background-color: var(--wise-bg-primary);
  color: var(--wise-text-primary);
  line-height: 1.6;
}

/* Paper wireframe headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--wise-font-family);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--wise-text-primary);
}

h1 {
  font-size: var(--wise-font-size-3xl);
  margin-bottom: var(--wise-spacing-lg);
}

h2 {
  font-size: var(--wise-font-size-2xl);
  margin-bottom: var(--wise-spacing-md);
}

h3 {
  font-size: var(--wise-font-size-xl);
  margin-bottom: var(--wise-spacing-md);
}

/* Paper wireframe buttons */
.wise-btn {
  display: inline-block;
  padding: var(--wise-spacing-md) var(--wise-spacing-lg);
  margin: var(--wise-spacing-xs);
  background-color: var(--wise-bg-secondary);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  font-family: var(--wise-font-family);
  font-size: var(--wise-font-size-base);
  font-weight: bold;
  text-decoration: none;
  color: var(--wise-text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--wise-shadow-lg);
}

.wise-btn:hover {
  box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
  transform: translateY(-2px);
}

.wise-btn:active {
  box-shadow: 2px 2px 0px 0px rgba(0,0,0,0.9);
  transform: translateY(0);
}

.wise-btn-primary {
  background-color: var(--wise-primary);
  color: var(--wise-text-inverse);
}

.wise-btn-secondary {
  background-color: var(--wise-bg-tertiary);
}

/* Paper wireframe cards */
.wise-card {
  background-color: var(--wise-bg-secondary);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  padding: var(--wise-spacing-lg);
  margin-bottom: var(--wise-spacing-md);
  box-shadow: var(--wise-shadow-sm);
  transition: all 0.3s ease;
}

.wise-card:hover {
  box-shadow: var(--wise-shadow-md);
  transform: translateY(-2px);
}

.wise-card-header {
  border-bottom: 2px solid var(--wise-border-color);
  padding-bottom: var(--wise-spacing-md);
  margin-bottom: var(--wise-spacing-md);
}

.wise-card-title {
  font-size: var(--wise-font-size-lg);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--wise-text-primary);
  margin: 0;
}

/* Paper wireframe forms */
.wise-form-group {
  margin-bottom: var(--wise-spacing-md);
}

.wise-label {
  display: block;
  margin-bottom: var(--wise-spacing-xs);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: var(--wise-font-size-sm);
  color: var(--wise-text-primary);
}

.wise-input {
  width: 100%;
  padding: var(--wise-spacing-md);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  font-family: var(--wise-font-family);
  font-size: var(--wise-font-size-base);
  background-color: var(--wise-bg-secondary);
  color: var(--wise-text-primary);
  transition: all 0.3s ease;
}

.wise-input:focus {
  outline: none;
  box-shadow: var(--wise-shadow-sm);
  transform: translateY(-1px);
}

.wise-input::placeholder {
  color: var(--wise-text-secondary);
  font-style: italic;
}

.wise-textarea {
  resize: vertical;
  min-height: 120px;
}

.wise-select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--wise-spacing-md) center;
  background-size: 16px;
  padding-right: calc(var(--wise-spacing-lg) + 16px);
}

/* Paper wireframe navigation */
.wise-nav {
  background-color: var(--wise-bg-secondary);
  border-bottom: 2px solid var(--wise-border-color);
  padding: var(--wise-spacing-md);
}

.wise-nav-item {
  display: inline-block;
  padding: var(--wise-spacing-sm) var(--wise-spacing-md);
  margin: 0 var(--wise-spacing-xs);
  background-color: var(--wise-bg-secondary);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  text-decoration: none;
  color: var(--wise-text-primary);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.wise-nav-item:hover {
  background-color: var(--wise-bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--wise-shadow-md);
}

.wise-nav-item-active {
  background-color: var(--wise-primary);
  color: var(--wise-text-inverse);
}

/* Paper wireframe tables */
.wise-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  background-color: var(--wise-bg-secondary);
  overflow: hidden;
}

.wise-table th,
.wise-table td {
  padding: var(--wise-spacing-md);
  text-align: left;
  border-bottom: 2px solid var(--wise-border-color);
  border-right: 2px solid var(--wise-border-color);
}

.wise-table th {
  background-color: var(--wise-bg-tertiary);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--wise-text-primary);
}

.wise-table th:last-child,
.wise-table td:last-child {
  border-right: none;
}

.wise-table tr:last-child td {
  border-bottom: none;
}

.wise-table tr:hover {
  background-color: var(--wise-bg-tertiary);
}

/* Paper wireframe badges */
.wise-badge {
  display: inline-block;
  padding: var(--wise-spacing-xs) var(--wise-spacing-sm);
  background-color: var(--wise-bg-secondary);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-full);
  font-size: var(--wise-font-size-xs);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--wise-text-primary);
}

.wise-badge-primary {
  background-color: var(--wise-primary);
  color: var(--wise-text-inverse);
}

.wise-badge-success {
  background-color: var(--wise-success);
  color: var(--wise-text-inverse);
}

/* Paper wireframe alerts */
.wise-alert {
  padding: var(--wise-spacing-md);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  margin-bottom: var(--wise-spacing-md);
  background-color: var(--wise-bg-secondary);
  font-weight: bold;
}

.wise-alert-info {
  border-color: var(--wise-info);
  background-color: var(--wise-bg-tertiary);
}

.wise-alert-success {
  border-color: var(--wise-success);
  background-color: var(--wise-bg-tertiary);
}

.wise-alert-warning {
  border-color: var(--wise-warning);
  background-color: var(--wise-bg-tertiary);
}

.wise-alert-error {
  border-color: var(--wise-error);
  background-color: var(--wise-bg-tertiary);
}

/* Paper wireframe course components */
.wise-course-card {
  background-color: var(--wise-bg-secondary);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  padding: var(--wise-spacing-lg);
  margin-bottom: var(--wise-spacing-md);
  box-shadow: var(--wise-shadow-sm);
  transition: all 0.3s ease;
}

.wise-course-card:hover {
  box-shadow: var(--wise-shadow-md);
  transform: translateY(-2px);
}

.wise-course-thumbnail {
  width: 100%;
  height: 200px;
  background-color: var(--wise-bg-tertiary);
  border: 2px solid var(--wise-border-color);
  border-radius: var(--wise-radius-md);
  margin-bottom: var(--wise-spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.wise-course-title {
  font-size: var(--wise-font-size-lg);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--wise-text-primary);
  margin-bottom: var(--wise-spacing-sm);
}

.wise-course-description {
  color: var(--wise-text-secondary);
  margin-bottom: var(--wise-spacing-md);
  line-height: 1.6;
}

.wise-course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--wise-font-size-sm);
  color: var(--wise-text-secondary);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive design */
@media (max-width: 768px) {
  .wise-nav-item {
    display: block;
    margin: var(--wise-spacing-xs) 0;
  }
  
  .wise-table {
    font-size: var(--wise-font-size-sm);
  }
  
  .wise-table th,
  .wise-table td {
    padding: var(--wise-spacing-sm);
  }
}
