/* Paper Wireframe UI Components */

/* Speech Bubbles */
.paper-bubble {
    position: relative;
    background: var(--paper-white);
    border: 2px solid var(--paper-black);
    border-radius: var(--paper-radius);
    padding: 15px;
    margin: 10px;
    max-width: 250px;
    font-weight: bold;
    font-family: var(--paper-font);
}

.paper-bubble-left::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 20px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 12px solid var(--paper-black);
}

.paper-bubble-left::after {
    content: '';
    position: absolute;
    left: -8px;
    top: 22px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 10px solid var(--paper-white);
}

.paper-bubble-right::before {
    content: '';
    position: absolute;
    right: -12px;
    top: 20px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 12px solid var(--paper-black);
}

.paper-bubble-right::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 22px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 10px solid var(--paper-white);
}

/* Form Fields */
.paper-field {
    margin: 10px 0;
    font-family: var(--paper-font);
}

.paper-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
    color: var(--paper-black);
}

.paper-field .hint {
    font-size: 12px;
    color: var(--paper-gray);
    font-style: italic;
    margin-top: 5px;
}

/* Search Input */
.paper-search {
    position: relative;
    margin: 5px 0;
}

.paper-search input {
    padding-left: 40px;
}

.paper-search::before {
    content: '🔍';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    z-index: 1;
}

/* Dropdown */
.paper-dropdown {
    position: relative;
    display: inline-block;
}

.paper-dropdown select {
    padding: 12px 40px 12px 16px;
    border: 2px solid var(--paper-black);
    border-radius: var(--paper-radius);
    font-family: var(--paper-font);
    font-size: 16px;
    background: var(--paper-white);
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.paper-dropdown::after {
    content: '▼';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--paper-black);
}

/* Filter Buttons */
.paper-filter {
    display: inline-block;
    padding: 8px 16px;
    margin: 3px;
    background: var(--paper-white);
    border: 2px solid var(--paper-black);
    border-radius: 20px;
    font-family: var(--paper-font);
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.paper-filter-active {
    background: var(--paper-black);
    color: var(--paper-white);
}

.paper-filter::before {
    content: '⚙ ';
    margin-right: 5px;
}

/* Keyboard */
.paper-keyboard {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 5px;
    max-width: 400px;
    margin: 10px 0;
    font-family: var(--paper-font);
}

.paper-key {
    padding: 8px;
    border: 2px solid var(--paper-black);
    border-radius: 4px;
    background: var(--paper-white);
    text-align: center;
    font-family: var(--paper-font);
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.paper-key:hover {
    background: var(--paper-light-gray);
    transform: translateY(-1px);
}

.paper-key-space {
    grid-column: span 4;
}

.paper-key-number {
    grid-column: span 2;
}

/* Header */
.paper-header {
    text-align: center;
    margin-bottom: 40px;
    font-family: var(--paper-font);
}

.paper-header h1 {
    font-size: 48px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 3px;
    margin: 20px 0;
    color: var(--paper-black);
    text-shadow: 2px 2px 0px var(--paper-light-gray);
}

.paper-header .subtitle {
    font-size: 18px;
    color: var(--paper-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Navigation */
.paper-nav {
    background: var(--paper-white);
    border-bottom: 2px solid var(--paper-black);
    padding: 1rem;
    font-family: var(--paper-font);
}

.paper-nav-item {
    display: inline-block;
    padding: 8px 16px;
    margin: 0 5px;
    background: var(--paper-white);
    border: 2px solid var(--paper-black);
    border-radius: var(--paper-radius);
    text-decoration: none;
    color: var(--paper-black);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.paper-nav-item:hover {
    background: var(--paper-light-gray);
    transform: translateY(-2px);
    box-shadow: var(--paper-shadow-hover);
}

.paper-nav-item-active {
    background: var(--paper-black);
    color: var(--paper-white);
}

/* Tables */
.paper-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: 2px solid var(--paper-black);
    border-radius: var(--paper-radius);
    font-family: var(--paper-font);
    background: var(--paper-white);
}

.paper-table th,
.paper-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 2px solid var(--paper-black);
    border-right: 2px solid var(--paper-black);
}

.paper-table th {
    background: var(--paper-light-gray);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.paper-table th:last-child,
.paper-table td:last-child {
    border-right: none;
}

.paper-table tr:last-child td {
    border-bottom: none;
}

.paper-table tr:hover {
    background: var(--paper-light-gray);
}

/* Modal */
.paper-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.paper-modal-content {
    background: var(--paper-white);
    border: 2px solid var(--paper-black);
    border-radius: var(--paper-radius);
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    font-family: var(--paper-font);
    box-shadow: var(--paper-shadow-hover);
}

.paper-modal-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--paper-black);
}

.paper-modal-title {
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--paper-black);
}

.paper-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--paper-black);
}

/* Responsive */
@media (max-width: 768px) {
    .paper-header h1 {
        font-size: 36px;
    }
    
    .paper-keyboard {
        grid-template-columns: repeat(5, 1fr);
    }
    
    .paper-modal-content {
        margin: 1rem;
        padding: 1rem;
    }
}
