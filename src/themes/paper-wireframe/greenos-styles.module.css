/* Paper Wireframe GreenOS Styles */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
}

/* Header styles */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: white;
  border-bottom: 2px solid #333333;
  width: 100%;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  width: 100%;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.headerTitle {
  font-size: 1.25rem;
  font-weight: bold;
  text-align: center;
  color: #333333;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.headerButton {
  padding: 0.5rem;
  background-color: white;
  border: 2px solid #333333;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  font-weight: bold;
}

.headerButton:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
}

/* Main content */
.main {
  padding: 1rem;
  min-height: calc(100vh - 80px);
}

/* Grid layout */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
  padding: 0.5rem;
}

.gridItem {
  background-color: white;
  border: 2px solid #333333;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.gridItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  background-color: #e0e0e0;
}

.gridItemIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
  color: #333333;
}

.gridItemTitle {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.gridItemSubtitle {
  font-size: 0.75rem;
  color: #666666;
  font-style: italic;
}

/* Search and filters */
.searchContainer {
  margin-bottom: 1rem;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.searchInput {
  flex: 1;
  min-width: 200px;
  padding: 12px 16px;
  border: 2px solid #333333;
  border-radius: 8px;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  font-size: 16px;
  background: white;
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.searchInput::placeholder {
  color: #666666;
  font-style: italic;
}

.filterButton {
  padding: 8px 16px;
  background: white;
  border: 2px solid #333333;
  border-radius: 20px;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filterButton:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
}

.filterButtonActive {
  background: #333333;
  color: white;
}

/* Categories */
.categorySection {
  margin-bottom: 2rem;
}

.categoryTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333333;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 2px solid #333333;
  padding-bottom: 0.5rem;
}

.categoryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.categoryCard {
  background: white;
  border: 2px solid #333333;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.categoryCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.1);
  background: #e0e0e0;
}

.categoryCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: #333333;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
    padding: 0.25rem;
  }
  
  .gridItem {
    padding: 0.75rem;
  }
  
  .gridItemIcon {
    font-size: 1.5rem;
  }
  
  .gridItemTitle {
    font-size: 0.75rem;
  }
  
  .gridItemSubtitle {
    font-size: 0.625rem;
  }
  
  .searchContainer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .searchInput {
    min-width: auto;
  }
  
  .categoryGrid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  
  .categoryCard {
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
}

/* Loading skeleton - paper wireframe style */
.skeleton {
  animation: skeleton-loading 1.5s ease-in-out infinite;
  background-color: #e0e0e0;
  border: 2px solid #333333;
  border-radius: 8px;
}

@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
    transform: translateX(0);
  }
  50% {
    opacity: 1;
    transform: translateX(2px);
  }
  100% {
    opacity: 0.6;
    transform: translateX(0);
  }
}

.skeletonCard {
  height: 200px;
}

.skeletonTitle {
  height: 1rem;
  margin-bottom: 0.5rem;
  width: 80%;
}

.skeletonSubtitle {
  height: 0.75rem;
  width: 60%;
}

/* Base styles */
.container, .mobileContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background: #f8f8f8;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

/* Mobile container specific */
.mobileContainer {
  padding: 1rem;
}

/* Header styles */
.header, .mobileHeader {
  text-align: center;
  margin-bottom: 3rem;
  color: #333333;
}

.mobileHeader {
  margin-bottom: 2rem;
}

.header h1, .mobileHeader h1 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: #333333;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 2px 2px 0px #e0e0e0;
}

.mobileHeader h1 {
  font-size: 2rem;
}

.header p, .mobileHeader p {
  font-size: 1.2rem;
  color: #666666;
  font-style: italic;
}

.mobileHeader p {
  font-size: 1rem;
}
