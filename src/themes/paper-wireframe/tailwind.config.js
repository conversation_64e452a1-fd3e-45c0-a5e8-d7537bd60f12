/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    // Exclude node_modules to avoid performance issues
    '!./src/app/**/node_modules/**',
    '!./node_modules/**',
  ],
  theme: {
    extend: {
      fontFamily: {
        'paper': ['Comic Sans MS', 'Comic Neue', 'cursive', 'sans-serif'],
        'sans': ['Comic Sans MS', 'Comic Neue', 'cursive', 'sans-serif'],
        'inter': ['Comic Sans MS', 'Comic Neue', 'cursive', 'sans-serif'],
        'satoshi': ['Comic Sans MS', 'Comic Neue', 'cursive', 'sans-serif'],
      },
      colors: {
        // Paper wireframe color palette
        'paper': {
          'bg': '#f8f8f8',
          'white': '#ffffff',
          'black': '#333333',
          'gray': '#666666',
          'light-gray': '#e0e0e0',
          'border': '#cccccc',
        },
        // Override default colors with paper theme
        'primary': '#333333',
        'secondary': '#e0e0e0',
        'background': '#f8f8f8',
        'foreground': '#333333',
        'muted': '#e0e0e0',
        'muted-foreground': '#666666',
        'accent': '#e0e0e0',
        'accent-foreground': '#333333',
        'destructive': '#dc2626',
        'destructive-foreground': '#ffffff',
        'border': '#cccccc',
        'input': '#cccccc',
        'ring': '#333333',
        'card': '#ffffff',
        'card-foreground': '#333333',
        'popover': '#ffffff',
        'popover-foreground': '#333333',
      },
      borderColor: {
        border: '#cccccc',
        DEFAULT: '#333333',
      },
      borderWidth: {
        DEFAULT: '2px',
        '0': '0',
        '2': '2px',
        '3': '3px',
        '4': '4px',
      },
      borderRadius: {
        DEFAULT: '8px',
        'none': '0',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        '2xl': '20px',
        'full': '9999px',
      },
      boxShadow: {
        'paper': '0 2px 4px rgba(0,0,0,0.1)',
        'paper-hover': '0 4px 8px rgba(0,0,0,0.15)',
        'paper-btn': '4px 4px 0px 0px rgba(0,0,0,0.9)',
        'paper-btn-hover': '6px 6px 0px 0px rgba(0,0,0,0.9)',
        'paper-btn-active': '2px 2px 0px 0px rgba(0,0,0,0.9)',
        'hand-drawn': '3px 3px 0px rgba(0,0,0,0.3)',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out forwards',
        'fade-out': 'fadeOut 0.3s ease-in-out forwards',
        'bounce-gentle': 'bounceGentle 2s infinite',
        'wiggle': 'wiggle 1s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          'from': { opacity: '0', transform: 'translateY(10px)' },
          'to': { opacity: '1', transform: 'translateY(0)' }
        },
        fadeOut: {
          'from': { opacity: '1', transform: 'translateY(0)' },
          'to': { opacity: '0', transform: 'translateY(-10px)' }
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' }
        },
        wiggle: {
          '0%, 100%': { transform: 'rotate(-1deg)' },
          '50%': { transform: 'rotate(1deg)' }
        }
      },
      spacing: {
        '7.5': '1.875rem',
        '15': '3.75rem',
        '18': '4.5rem',
        '22': '5.5rem',
      },
      fontSize: {
        'xs': ['12px', '16px'],
        'sm': ['14px', '18px'],
        'base': ['16px', '20px'],
        'lg': ['18px', '22px'],
        'xl': ['20px', '24px'],
        '2xl': ['24px', '28px'],
        '3xl': ['30px', '36px'],
        '4xl': ['36px', '40px'],
        '5xl': ['48px', '52px'],
      },
      letterSpacing: {
        'tighter': '-0.05em',
        'tight': '-0.025em',
        'normal': '0',
        'wide': '0.025em',
        'wider': '0.05em',
        'widest': '0.1em',
        'paper': '0.5px',
        'paper-wide': '1px',
      },
      textTransform: {
        'uppercase': 'uppercase',
        'lowercase': 'lowercase',
        'capitalize': 'capitalize',
        'normal-case': 'none',
      },
    },
  },
  plugins: [
    function({ addUtilities, addComponents, theme }) {
      addUtilities({
        '.paper-border': {
          border: '2px solid #333333',
        },
        '.paper-shadow': {
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        },
        '.paper-shadow-hover': {
          boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
        },
        '.paper-font': {
          fontFamily: "'Comic Sans MS', 'Comic Neue', cursive, sans-serif",
        },
        '.paper-btn-shadow': {
          boxShadow: '4px 4px 0px 0px rgba(0,0,0,0.9)',
        },
        '.paper-btn-shadow-hover': {
          boxShadow: '6px 6px 0px 0px rgba(0,0,0,0.9)',
        },
        '.paper-btn-shadow-active': {
          boxShadow: '2px 2px 0px 0px rgba(0,0,0,0.9)',
        },
        '.paper-hand-drawn': {
          position: 'relative',
        },
        '.paper-hand-drawn::before': {
          content: '""',
          position: 'absolute',
          top: '-2px',
          left: '-2px',
          right: '-2px',
          bottom: '-2px',
          border: '2px solid #333333',
          borderRadius: '8px',
          transform: 'rotate(-0.5deg)',
          zIndex: '-1',
        },
        '.paper-hand-drawn::after': {
          content: '""',
          position: 'absolute',
          top: '-1px',
          left: '-1px',
          right: '-1px',
          bottom: '-1px',
          border: '2px solid #333333',
          borderRadius: '8px',
          transform: 'rotate(0.3deg)',
          zIndex: '-2',
        },
      });
      
      addComponents({
        '.paper-btn': {
          display: 'inline-block',
          padding: '12px 24px',
          margin: '5px',
          backgroundColor: '#ffffff',
          border: '2px solid #333333',
          borderRadius: '8px',
          fontFamily: "'Comic Sans MS', cursive, sans-serif",
          fontSize: '16px',
          fontWeight: 'bold',
          textDecoration: 'none',
          color: '#333333',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          textTransform: 'uppercase',
          letterSpacing: '1px',
          boxShadow: '4px 4px 0px 0px rgba(0,0,0,0.9)',
          '&:hover': {
            boxShadow: '6px 6px 0px 0px rgba(0,0,0,0.9)',
            transform: 'translateY(-2px)',
          },
          '&:active': {
            boxShadow: '2px 2px 0px 0px rgba(0,0,0,0.9)',
            transform: 'translateY(0)',
          },
        },
        '.paper-input': {
          width: '100%',
          padding: '12px 16px',
          margin: '5px 0',
          border: '2px solid #333333',
          borderRadius: '8px',
          fontFamily: "'Comic Sans MS', cursive, sans-serif",
          fontSize: '16px',
          backgroundColor: '#ffffff',
          transition: 'all 0.3s ease',
          '&:focus': {
            outline: 'none',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            transform: 'translateY(-1px)',
          },
          '&::placeholder': {
            color: '#666666',
            fontStyle: 'italic',
          },
        },
      });
    },
  ],
};
