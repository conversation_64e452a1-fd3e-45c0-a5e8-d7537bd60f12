@import url("https://fonts.googleapis.com/css2?family=Comic+Neue:wght@300;400;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    body {
        @apply relative z-1 bg-[#f8f8f8] font-['Comic_Sans_MS',cursive,sans-serif] text-base font-normal text-[#333333];
        font-family: 'Comic Sans MS', 'Comic Neue', cursive, sans-serif;
    }
}

@layer components {
    /* Paper Wireframe Buttons */
    .paper-btn {
        @apply inline-block px-6 py-3 m-1 bg-white border-2 border-black rounded-lg font-bold text-black cursor-pointer transition-all duration-200 text-sm uppercase tracking-wide;
        font-family: 'Comic Sans MS', cursive, sans-serif;
        box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
        transform: translateY(0);
    }
    
    .paper-btn:hover {
        box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
        transform: translateY(-2px);
    }
    
    .paper-btn:active {
        box-shadow: 2px 2px 0px 0px rgba(0,0,0,0.9);
        transform: translateY(0);
    }
    
    .paper-btn-primary {
        @apply bg-black text-white;
    }
    
    .paper-btn-secondary {
        @apply bg-[#e0e0e0];
    }
    
    .paper-btn-outline {
        @apply bg-transparent;
    }
    
    /* Paper Wireframe Input Fields */
    .paper-input {
        @apply w-full px-4 py-3 m-1 border-2 border-black rounded-lg bg-white transition-all duration-300 text-base;
        font-family: 'Comic Sans MS', cursive, sans-serif;
    }
    
    .paper-input:focus {
        @apply outline-none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transform: translateY(-1px);
    }
    
    .paper-input::placeholder {
        @apply text-[#666666] italic;
    }
    
    /* Paper Wireframe Cards */
    .paper-card {
        @apply bg-white p-5 rounded-lg border-2 border-black transition-all duration-300;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        font-family: 'Comic Sans MS', cursive, sans-serif;
    }
    
    .paper-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    /* Paper Wireframe Icons */
    .paper-icon {
        @apply w-6 h-6 inline-block border-2 border-black m-1 relative;
    }
    
    .paper-icon-home {
        @apply rounded-t;
    }
    
    .paper-icon-home::after {
        content: '';
        @apply absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #333333;
    }
    
    .paper-icon-cart {
        @apply rounded-sm;
    }
    
    .paper-icon-cart::after {
        content: '';
        @apply absolute -bottom-1.5 left-0.5 w-1.5 h-1.5 rounded-full bg-black;
    }
    
    .paper-icon-cart::before {
        content: '';
        @apply absolute -bottom-1.5 right-0.5 w-1.5 h-1.5 rounded-full bg-black;
    }
    
    .paper-icon-heart {
        @apply rounded-full transform -rotate-45;
        border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    }
    
    .paper-icon-star {
        @apply rounded-full transform rotate-45;
    }
    
    .paper-icon-user {
        @apply rounded-full;
    }
    
    .paper-icon-settings {
        @apply rounded;
    }
    
    .paper-icon-mail {
        @apply rounded;
    }
    
    .paper-icon-phone {
        @apply rounded-2xl;
    }
    
    /* Paper Wireframe Tags */
    .paper-tag {
        @apply inline-block px-3 py-1.5 m-1 bg-white border-2 border-black rounded-full text-sm font-bold uppercase tracking-wide;
        font-family: 'Comic Sans MS', cursive, sans-serif;
    }
    
    .paper-tag-filled {
        @apply bg-black text-white;
    }
    
    .paper-tag-removable {
        @apply pr-6 relative;
    }
    
    .paper-tag-removable::after {
        content: '×';
        @apply absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer text-base;
    }
    
    /* Paper Wireframe Toggles and Checkboxes */
    .paper-toggle {
        @apply inline-block w-15 h-7.5 bg-[#e0e0e0] border-2 border-black rounded-full relative cursor-pointer m-1 transition-all duration-300;
    }
    
    .paper-toggle::after {
        content: '';
        @apply absolute top-0.5 left-0.5 w-5.5 h-5.5 bg-white border-2 border-black rounded-full transition-all duration-300;
    }
    
    .paper-toggle-active {
        @apply bg-black;
    }
    
    .paper-toggle-active::after {
        @apply left-7.5 bg-white;
    }
    
    .paper-checkbox {
        @apply w-5 h-5 border-2 border-black rounded inline-block relative cursor-pointer m-1 bg-white;
    }
    
    .paper-checkbox-checked {
        @apply bg-black;
    }
    
    .paper-checkbox-checked::after {
        content: '✓';
        @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white font-bold text-sm;
    }
    
    .paper-radio {
        @apply w-5 h-5 border-2 border-black rounded-full inline-block relative cursor-pointer m-1 bg-white;
    }
    
    .paper-radio-checked::after {
        content: '';
        @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-black rounded-full;
    }
}

@layer utilities {
    /* Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .chat-height {
        @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
    }

    .inbox-height {
        @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
    }
    
    /* Paper wireframe specific utilities */
    .paper-hand-drawn-border {
        position: relative;
    }
    
    .paper-hand-drawn-border::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid #333333;
        border-radius: 8px;
        transform: rotate(-0.3deg);
        z-index: -1;
    }
    
    .paper-sketch-shadow {
        filter: drop-shadow(3px 3px 0px rgba(0,0,0,0.3));
    }
}

/* third-party libraries CSS - Paper wireframe style */

.tableCheckbox:checked ~ div span {
    @apply opacity-100;
}

.tableCheckbox:checked ~ div {
    @apply border-black bg-black;
}

.apexcharts-legend-text {
    @apply !text-[#333333];
    font-family: 'Comic Sans MS', cursive, sans-serif !important;
}

.apexcharts-text {
    @apply !fill-[#333333];
    font-family: 'Comic Sans MS', cursive, sans-serif !important;
}

.apexcharts-yaxis-label {
    @apply text-xs;
    font-family: 'Comic Sans MS', cursive, sans-serif !important;
}

.apexcharts-xaxis-label {
    @apply text-sm font-medium;
    font-family: 'Comic Sans MS', cursive, sans-serif !important;
}
