@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Paper Wireframe Variables for Good Deals Shop */
  --primary: #333333;
  --secondary: #e0e0e0;
  --background: #f8f8f8;
  --foreground: #333333;
  --section: #ffffff;
  --inactive: #666666;
  --tabIndicator: #333333;
  --subtitle: #666666;
  --danger: #333333;
  --skeleton: #e0e0e0;
  
  /* Paper wireframe specific variables */
  --paper-bg: #f8f8f8;
  --paper-white: #ffffff;
  --paper-black: #333333;
  --paper-gray: #666666;
  --paper-light-gray: #e0e0e0;
  --paper-border: #cccccc;
  --paper-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --paper-shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
  --paper-radius: 8px;
  --paper-font: 'Comic Sans MS', cursive, sans-serif;
}

@layer base {
  * {
    font-family: var(--paper-font);
  }
  
  body {
    font-family: var(--paper-font);
    background-color: var(--paper-bg);
    color: var(--paper-black);
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--paper-font);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--paper-black);
  }
  
  button {
    font-family: var(--paper-font);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  input, textarea, select {
    font-family: var(--paper-font);
  }
}

@layer components {
  /* Paper wireframe buttons */
  .btn-primary {
    @apply bg-black text-white border-2 border-black rounded-lg px-6 py-3 font-bold uppercase tracking-wide transition-all duration-300;
    font-family: var(--paper-font);
    box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
  }
  
  .btn-primary:hover {
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
    transform: translateY(-2px);
  }
  
  .btn-primary:active {
    box-shadow: 2px 2px 0px 0px rgba(0,0,0,0.9);
    transform: translateY(0);
  }
  
  .btn-secondary {
    @apply bg-white text-black border-2 border-black rounded-lg px-6 py-3 font-bold uppercase tracking-wide transition-all duration-300;
    font-family: var(--paper-font);
    box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
  }
  
  .btn-secondary:hover {
    @apply bg-gray-200;
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
    transform: translateY(-2px);
  }
  
  /* Paper wireframe cards */
  .product-card {
    @apply bg-white border-2 border-black rounded-lg p-4 transition-all duration-300;
    font-family: var(--paper-font);
    box-shadow: var(--paper-shadow);
  }
  
  .product-card:hover {
    box-shadow: var(--paper-shadow-hover);
    transform: translateY(-2px);
  }
  
  .product-title {
    @apply font-bold text-black uppercase tracking-wide text-sm;
    font-family: var(--paper-font);
  }
  
  .product-price {
    @apply font-bold text-black text-lg;
    font-family: var(--paper-font);
  }
  
  .product-description {
    @apply text-gray-600 text-sm;
    font-family: var(--paper-font);
  }
  
  /* Paper wireframe inputs */
  .form-input {
    @apply w-full px-4 py-3 border-2 border-black rounded-lg bg-white transition-all duration-300;
    font-family: var(--paper-font);
  }
  
  .form-input:focus {
    @apply outline-none;
    box-shadow: var(--paper-shadow);
    transform: translateY(-1px);
  }
  
  .form-input::placeholder {
    @apply text-gray-500 italic;
  }
  
  /* Paper wireframe navigation */
  .nav-item {
    @apply px-4 py-2 border-2 border-black rounded-lg bg-white text-black font-bold uppercase tracking-wide transition-all duration-300;
    font-family: var(--paper-font);
  }
  
  .nav-item:hover {
    @apply bg-gray-200;
    transform: translateY(-1px);
  }
  
  .nav-item-active {
    @apply bg-black text-white;
  }
  
  /* Paper wireframe badges */
  .badge {
    @apply inline-block px-3 py-1 border-2 border-black rounded-full text-xs font-bold uppercase tracking-wide;
    font-family: var(--paper-font);
  }
  
  .badge-sale {
    @apply bg-black text-white;
  }
  
  .badge-new {
    @apply bg-white text-black;
  }
  
  .badge-hot {
    @apply bg-gray-200 text-black;
  }
  
  /* Paper wireframe search */
  .search-container {
    @apply relative;
  }
  
  .search-input {
    @apply w-full pl-12 pr-4 py-3 border-2 border-black rounded-lg bg-white;
    font-family: var(--paper-font);
  }
  
  .search-icon {
    @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-black;
  }
  
  /* Paper wireframe filters */
  .filter-button {
    @apply px-4 py-2 border-2 border-black rounded-full bg-white text-black font-bold text-sm uppercase tracking-wide transition-all duration-300;
    font-family: var(--paper-font);
  }
  
  .filter-button:hover {
    @apply bg-gray-200;
  }
  
  .filter-button-active {
    @apply bg-black text-white;
  }
  
  /* Paper wireframe cart */
  .cart-item {
    @apply bg-white border-2 border-black rounded-lg p-4 mb-4;
    font-family: var(--paper-font);
  }
  
  .cart-quantity {
    @apply border-2 border-black rounded px-3 py-1 text-center font-bold;
    font-family: var(--paper-font);
  }
  
  /* Paper wireframe modals */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  }
  
  .modal-content {
    @apply bg-white border-2 border-black rounded-lg p-6 max-w-md w-full mx-4;
    font-family: var(--paper-font);
    box-shadow: var(--paper-shadow-hover);
  }
  
  .modal-title {
    @apply text-xl font-bold text-black uppercase tracking-wide mb-4;
    font-family: var(--paper-font);
  }
  
  /* Paper wireframe tables */
  .table {
    @apply w-full border-2 border-black rounded-lg overflow-hidden;
    font-family: var(--paper-font);
  }
  
  .table th {
    @apply bg-gray-200 border-b-2 border-black px-4 py-3 text-left font-bold uppercase tracking-wide;
    font-family: var(--paper-font);
  }
  
  .table td {
    @apply border-b border-gray-300 px-4 py-3;
    font-family: var(--paper-font);
  }
  
  .table tr:hover {
    @apply bg-gray-100;
  }
}

@layer utilities {
  .paper-border {
    border: 2px solid var(--paper-black);
  }
  
  .paper-shadow {
    box-shadow: var(--paper-shadow);
  }
  
  .paper-shadow-hover {
    box-shadow: var(--paper-shadow-hover);
  }
  
  .paper-font {
    font-family: var(--paper-font);
  }
  
  .paper-btn-shadow {
    box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
  }
  
  .paper-btn-shadow-hover {
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
  }
  
  .paper-btn-shadow-active {
    box-shadow: 2px 2px 0px 0px rgba(0,0,0,0.9);
  }
  
  .paper-hand-drawn {
    position: relative;
  }
  
  .paper-hand-drawn::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid var(--paper-black);
    border-radius: var(--paper-radius);
    transform: rotate(-0.5deg);
    z-index: -1;
  }
  
  .paper-uppercase {
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: bold;
  }
  
  .paper-sketch {
    filter: drop-shadow(3px 3px 0px rgba(0,0,0,0.3));
  }
}
