@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Paper Wireframe Variables for ShopMe */
  --primary: #333333;
  --secondary: #e0e0e0;
  --background: #f8f8f8;
  --foreground: #333333;
  --section: #ffffff;
  --inactive: #666666;
  --tabIndicator: #333333;
  --subtitle: #666666;
  --danger: #333333;
  --skeleton: #e0e0e0;
  
  /* Paper wireframe specific variables */
  --paper-bg: #f8f8f8;
  --paper-white: #ffffff;
  --paper-black: #333333;
  --paper-gray: #666666;
  --paper-light-gray: #e0e0e0;
  --paper-border: #cccccc;
  --paper-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --paper-shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
  --paper-radius: 8px;
  --paper-font: 'Comic Sans MS', cursive, sans-serif;
}

@layer base {
  * {
    font-family: var(--paper-font);
  }
  
  body {
    font-family: var(--paper-font);
    background-color: var(--paper-bg);
    color: var(--paper-black);
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--paper-font);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--paper-black);
  }
  
  button {
    font-family: var(--paper-font);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  input, textarea, select {
    font-family: var(--paper-font);
  }
}

@layer components {
  /* ShopMe specific paper wireframe components */
  .shopme-header {
    @apply bg-white border-b-2 border-black p-4;
    font-family: var(--paper-font);
  }
  
  .shopme-logo {
    @apply text-2xl font-bold text-black uppercase tracking-wide;
    font-family: var(--paper-font);
  }
  
  .shopme-nav {
    @apply flex gap-4;
  }
  
  .shopme-nav-item {
    @apply px-4 py-2 border-2 border-black rounded-lg bg-white text-black font-bold uppercase tracking-wide transition-all duration-300;
    font-family: var(--paper-font);
  }
  
  .shopme-nav-item:hover {
    @apply bg-gray-200;
    transform: translateY(-1px);
  }
  
  .shopme-nav-item-active {
    @apply bg-black text-white;
  }
  
  /* Product grid */
  .shopme-product-grid {
    @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4;
  }
  
  .shopme-product-card {
    @apply bg-white border-2 border-black rounded-lg p-4 transition-all duration-300;
    font-family: var(--paper-font);
    box-shadow: var(--paper-shadow);
  }
  
  .shopme-product-card:hover {
    box-shadow: var(--paper-shadow-hover);
    transform: translateY(-2px);
  }
  
  .shopme-product-image {
    @apply w-full h-48 bg-gray-200 border-2 border-black rounded mb-3 flex items-center justify-center;
  }
  
  .shopme-product-image::before {
    content: '📷';
    font-size: 2rem;
  }
  
  .shopme-product-title {
    @apply font-bold text-black uppercase tracking-wide text-sm mb-2;
    font-family: var(--paper-font);
  }
  
  .shopme-product-price {
    @apply font-bold text-black text-lg mb-2;
    font-family: var(--paper-font);
  }
  
  .shopme-product-description {
    @apply text-gray-600 text-sm mb-3;
    font-family: var(--paper-font);
  }
  
  .shopme-add-to-cart {
    @apply w-full bg-black text-white border-2 border-black rounded-lg py-2 font-bold uppercase tracking-wide transition-all duration-300;
    font-family: var(--paper-font);
    box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
  }
  
  .shopme-add-to-cart:hover {
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
    transform: translateY(-2px);
  }
  
  /* Category sidebar */
  .shopme-sidebar {
    @apply bg-white border-r-2 border-black p-4;
    font-family: var(--paper-font);
  }
  
  .shopme-category-title {
    @apply text-lg font-bold text-black uppercase tracking-wide mb-4 border-b-2 border-black pb-2;
    font-family: var(--paper-font);
  }
  
  .shopme-category-item {
    @apply block py-2 px-3 border-2 border-black rounded mb-2 bg-white text-black font-bold uppercase tracking-wide transition-all duration-300;
    font-family: var(--paper-font);
  }
  
  .shopme-category-item:hover {
    @apply bg-gray-200;
  }
  
  .shopme-category-item-active {
    @apply bg-black text-white;
  }
  
  /* Search and filters */
  .shopme-search-bar {
    @apply w-full px-4 py-3 border-2 border-black rounded-lg bg-white mb-4;
    font-family: var(--paper-font);
  }
  
  .shopme-search-bar:focus {
    @apply outline-none;
    box-shadow: var(--paper-shadow);
    transform: translateY(-1px);
  }
  
  .shopme-filter-section {
    @apply mb-6;
  }
  
  .shopme-filter-title {
    @apply text-md font-bold text-black uppercase tracking-wide mb-3;
    font-family: var(--paper-font);
  }
  
  .shopme-filter-option {
    @apply flex items-center mb-2;
  }
  
  .shopme-checkbox {
    @apply w-5 h-5 border-2 border-black rounded mr-3 bg-white;
  }
  
  .shopme-checkbox:checked {
    @apply bg-black;
  }
  
  .shopme-filter-label {
    @apply text-black font-bold uppercase tracking-wide text-sm;
    font-family: var(--paper-font);
  }
  
  /* Shopping cart */
  .shopme-cart {
    @apply bg-white border-2 border-black rounded-lg p-4;
    font-family: var(--paper-font);
  }
  
  .shopme-cart-title {
    @apply text-xl font-bold text-black uppercase tracking-wide mb-4 border-b-2 border-black pb-2;
    font-family: var(--paper-font);
  }
  
  .shopme-cart-item {
    @apply flex items-center justify-between border-b border-gray-300 py-3;
  }
  
  .shopme-cart-item-info {
    @apply flex-1;
  }
  
  .shopme-cart-item-name {
    @apply font-bold text-black uppercase tracking-wide text-sm;
    font-family: var(--paper-font);
  }
  
  .shopme-cart-item-price {
    @apply text-gray-600;
    font-family: var(--paper-font);
  }
  
  .shopme-quantity-controls {
    @apply flex items-center gap-2;
  }
  
  .shopme-quantity-btn {
    @apply w-8 h-8 border-2 border-black rounded bg-white text-black font-bold flex items-center justify-center;
    font-family: var(--paper-font);
  }
  
  .shopme-quantity-btn:hover {
    @apply bg-gray-200;
  }
  
  .shopme-quantity-display {
    @apply px-3 py-1 border-2 border-black rounded bg-white text-black font-bold;
    font-family: var(--paper-font);
  }
  
  .shopme-cart-total {
    @apply text-xl font-bold text-black uppercase tracking-wide mt-4 pt-4 border-t-2 border-black;
    font-family: var(--paper-font);
  }
  
  .shopme-checkout-btn {
    @apply w-full bg-black text-white border-2 border-black rounded-lg py-3 font-bold uppercase tracking-wide mt-4 transition-all duration-300;
    font-family: var(--paper-font);
    box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
  }
  
  .shopme-checkout-btn:hover {
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
    transform: translateY(-2px);
  }
  
  /* Product details page */
  .shopme-product-detail {
    @apply bg-white border-2 border-black rounded-lg p-6;
    font-family: var(--paper-font);
  }
  
  .shopme-product-gallery {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6 mb-6;
  }
  
  .shopme-main-image {
    @apply w-full h-96 bg-gray-200 border-2 border-black rounded flex items-center justify-center;
  }
  
  .shopme-main-image::before {
    content: '🖼️';
    font-size: 4rem;
  }
  
  .shopme-product-info {
    @apply space-y-4;
  }
  
  .shopme-product-detail-title {
    @apply text-2xl font-bold text-black uppercase tracking-wide;
    font-family: var(--paper-font);
  }
  
  .shopme-product-detail-price {
    @apply text-3xl font-bold text-black;
    font-family: var(--paper-font);
  }
  
  .shopme-product-detail-description {
    @apply text-gray-600 leading-relaxed;
    font-family: var(--paper-font);
  }
  
  /* Responsive design */
  @media (max-width: 768px) {
    .shopme-product-grid {
      @apply grid-cols-1;
    }
    
    .shopme-nav {
      @apply flex-col gap-2;
    }
    
    .shopme-product-gallery {
      @apply grid-cols-1;
    }
  }
}
