@tailwind base;
@tailwind components;
@tailwind utilities;

@import "react-day-picker/dist/style.css";

/* Paper Wireframe Theme - Base Variables */
:root {
  --paper-bg: #f8f8f8;
  --paper-white: #ffffff;
  --paper-black: #333333;
  --paper-gray: #666666;
  --paper-light-gray: #e0e0e0;
  --paper-border: #cccccc;
  --paper-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --paper-shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
  --paper-radius: 8px;
  --paper-font: 'Comic Sans MS', cursive, sans-serif;
  
  /* Override Tailwind CSS variables with paper theme */
  --background: 248 248 248; /* #f8f8f8 */
  --foreground: 51 51 51; /* #333333 */
  --card: 255 255 255; /* #ffffff */
  --card-foreground: 51 51 51; /* #333333 */
  --popover: 255 255 255; /* #ffffff */
  --popover-foreground: 51 51 51; /* #333333 */
  --primary: 51 51 51; /* #333333 - black for primary */
  --primary-foreground: 255 255 255; /* #ffffff */
  --secondary: 224 224 224; /* #e0e0e0 - light gray */
  --secondary-foreground: 51 51 51; /* #333333 */
  --muted: 224 224 224; /* #e0e0e0 */
  --muted-foreground: 102 102 102; /* #666666 */
  --accent: 224 224 224; /* #e0e0e0 */
  --accent-foreground: 51 51 51; /* #333333 */
  --destructive: 220 38 38; /* Red for destructive actions */
  --destructive-foreground: 255 255 255; /* #ffffff */
  --border: 204 204 204; /* #cccccc */
  --input: 204 204 204; /* #cccccc */
  --ring: 51 51 51; /* #333333 */
  --radius: 8px;
}

.dark {
  /* Dark mode for paper wireframe - slightly different but still hand-drawn feel */
  --paper-bg: #2a2a2a;
  --paper-white: #3a3a3a;
  --paper-black: #ffffff;
  --paper-gray: #cccccc;
  --paper-light-gray: #4a4a4a;
  --paper-border: #555555;
  
  --background: 42 42 42; /* #2a2a2a */
  --foreground: 255 255 255; /* #ffffff */
  --card: 58 58 58; /* #3a3a3a */
  --card-foreground: 255 255 255; /* #ffffff */
  --popover: 58 58 58; /* #3a3a3a */
  --popover-foreground: 255 255 255; /* #ffffff */
  --primary: 255 255 255; /* #ffffff - white for primary in dark */
  --primary-foreground: 42 42 42; /* #2a2a2a */
  --secondary: 74 74 74; /* #4a4a4a */
  --secondary-foreground: 255 255 255; /* #ffffff */
  --muted: 74 74 74; /* #4a4a4a */
  --muted-foreground: 204 204 204; /* #cccccc */
  --accent: 74 74 74; /* #4a4a4a */
  --accent-foreground: 255 255 255; /* #ffffff */
  --destructive: 239 68 68; /* Red for destructive actions */
  --destructive-foreground: 255 255 255; /* #ffffff */
  --border: 85 85 85; /* #555555 */
  --input: 85 85 85; /* #555555 */
  --ring: 255 255 255; /* #ffffff */
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--paper-font);
  background-color: var(--paper-bg);
  color: var(--paper-black);
  line-height: 1.6;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--paper-font);
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out forwards;
  }
  
  .animate-fade-out {
    animation: fadeOut 0.3s ease-in-out forwards;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  
  /* Paper wireframe specific utilities */
  .paper-border {
    border: 2px solid var(--paper-black);
  }
  
  .paper-shadow {
    box-shadow: var(--paper-shadow);
  }
  
  .paper-shadow-hover {
    box-shadow: var(--paper-shadow-hover);
  }
  
  .paper-font {
    font-family: var(--paper-font);
  }
  
  .paper-btn-shadow {
    box-shadow: 4px 4px 0px 0px rgba(0,0,0,0.9);
  }
  
  .paper-btn-shadow-hover {
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,0.9);
  }
  
  .paper-btn-shadow-active {
    box-shadow: 2px 2px 0px 0px rgba(0,0,0,0.9);
  }
}

@import 'leaflet/dist/leaflet.css';

/* Center pin animation */
.center-pin-container {
  pointer-events: none;
  transition: transform 0.1s ease-out;
}

/* Paper wireframe animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-10px); }
}

/* Hand-drawn effect for borders */
.paper-hand-drawn {
  position: relative;
}

.paper-hand-drawn::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--paper-black);
  border-radius: var(--paper-radius);
  transform: rotate(-0.5deg);
  z-index: -1;
}

.paper-hand-drawn::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 2px solid var(--paper-black);
  border-radius: var(--paper-radius);
  transform: rotate(0.3deg);
  z-index: -2;
}
