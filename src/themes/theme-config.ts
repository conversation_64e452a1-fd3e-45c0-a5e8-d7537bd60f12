/**
 * Theme Configuration System
 * Allows easy switching between current theme and paper wireframe theme
 */

export type ThemeType = 'current' | 'paper-wireframe';

export interface ThemeConfig {
  name: string;
  description: string;
  cssFiles: {
    globals: string;
    styles: string;
    components?: string;
    tailwindConfig?: string;
  };
  variables: Record<string, string>;
}

export const themes: Record<ThemeType, ThemeConfig> = {
  current: {
    name: 'Current Theme',
    description: 'The original styling of the application',
    cssFiles: {
      globals: '/src/themes/current-theme/globals.css',
      styles: '/src/themes/current-theme/style.css',
      components: '/src/themes/current-theme/components.css',
      tailwindConfig: '/src/themes/current-theme/tailwind.config.js'
    },
    variables: {
      '--primary': '142 76% 36%',
      '--secondary': '142 70% 29%',
      '--background': '0 0% 100%',
      '--foreground': '222.2 84% 4.9%',
      '--border': '214.3 31.8% 91.4%',
      '--radius': '0.5rem'
    }
  },
  'paper-wireframe': {
    name: 'Paper Wireframe',
    description: 'Hand-drawn wireframe style with Comic Sans and bold borders',
    cssFiles: {
      globals: '/src/themes/paper-wireframe/globals.css',
      styles: '/src/themes/paper-wireframe/style.css',
      components: '/src/themes/paper-wireframe/components.css',
      tailwindConfig: '/src/themes/paper-wireframe/tailwind.config.js'
    },
    variables: {
      '--paper-bg': '#f8f8f8',
      '--paper-white': '#ffffff',
      '--paper-black': '#333333',
      '--paper-gray': '#666666',
      '--paper-light-gray': '#e0e0e0',
      '--paper-border': '#cccccc',
      '--paper-shadow': '0 2px 4px rgba(0,0,0,0.1)',
      '--paper-shadow-hover': '0 4px 8px rgba(0,0,0,0.15)',
      '--paper-radius': '8px',
      '--paper-font': "'Comic Sans MS', cursive, sans-serif"
    }
  }
};

// Current active theme
let currentTheme: ThemeType = 'current';

export const getCurrentTheme = (): ThemeType => currentTheme;

export const setTheme = (theme: ThemeType): void => {
  currentTheme = theme;
  
  // Apply theme variables to CSS custom properties
  const root = document.documentElement;
  const themeConfig = themes[theme];
  
  Object.entries(themeConfig.variables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
  
  // Store theme preference
  if (typeof window !== 'undefined') {
    localStorage.setItem('preferred-theme', theme);
  }
};

export const getThemeConfig = (theme: ThemeType): ThemeConfig => themes[theme];

// Initialize theme from localStorage or default to current
export const initializeTheme = (): void => {
  if (typeof window !== 'undefined') {
    const savedTheme = localStorage.getItem('preferred-theme') as ThemeType;
    if (savedTheme && themes[savedTheme]) {
      setTheme(savedTheme);
    }
  }
};

// Theme switching utility
export const toggleTheme = (): void => {
  const newTheme = currentTheme === 'current' ? 'paper-wireframe' : 'current';
  setTheme(newTheme);
};
