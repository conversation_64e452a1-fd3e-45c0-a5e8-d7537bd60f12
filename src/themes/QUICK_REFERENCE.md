# 🚀 Theme System Quick Reference

## 📋 Essential Imports
```typescript
import { ThemeProvider, useThemeStyles, ThemeToggleButton } from '@/themes/ThemeProvider';
```

## 🏗️ Basic App Structure

### Server Layout (layout.tsx)
```typescript
import { ThemeProvider } from '@/themes/ThemeProvider';
import AppClient from './layout-client';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider defaultTheme="current">
      <AppClient>{children}</AppClient>
    </ThemeProvider>
  );
}
```

### Client Layout (layout-client.tsx)
```typescript
'use client';
import { useThemeStyles, ThemeToggleButton } from '@/themes/ThemeProvider';

export default function AppClient({ children }: { children: React.ReactNode }) {
  const { isPaperTheme } = useThemeStyles();
  
  return (
    <div className={`min-h-screen ${isPaperTheme ? 'bg-paper-bg font-paper' : 'bg-white'}`}>
      <nav>
        <ThemeToggleButton />
      </nav>
      {children}
    </div>
  );
}
```

## 🎨 Theme Hooks & Utilities

### useThemeStyles Hook
```typescript
const {
  getButtonClass,     // Button styling
  getCardClass,       // Card styling  
  getInputClass,      // Input styling
  getHeadingClass,    // Heading styling
  isPaperTheme,       // Boolean check
  config              // Theme config
} = useThemeStyles();
```

### Usage Examples
```typescript
// Buttons
<button className={getButtonClass('primary')}>Primary</button>
<button className={getButtonClass('secondary')}>Secondary</button>
<button className={getButtonClass('outline')}>Outline</button>

// Cards
<div className={getCardClass()}>Card content</div>

// Headings
<h1 className={getHeadingClass(1)}>Main Title</h1>
<h2 className={getHeadingClass(2)}>Subtitle</h2>

// Inputs
<input className={getInputClass()} placeholder="Enter text" />

// Conditional Styling
<p className={isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}>
  Theme-aware text
</p>
```

## 🎯 AI Development Prompts

### Convert Existing App
```
Convert [APP_NAME] at [PATH] to use paper wireframe theme:
- Add ThemeProvider to layout.tsx
- Create layout-client.tsx with theme hooks
- Replace hardcoded styles with theme utilities
- Add ThemeToggleButton to navigation
- Test both themes thoroughly
```

### Create New App
```
Create new [APP_TYPE] at [PATH] with paper theme integration:
- Use ThemeProvider architecture
- Implement all theme hooks and utilities
- Include ThemeToggleButton
- Support both current and paper wireframe themes
- Ensure responsive design for both themes
```

### Add New Component
```
Create theme-aware [COMPONENT] with:
- useThemeStyles hook integration
- Support for both themes
- Proper TypeScript interfaces
- Smooth theme transitions
- Accessibility compliance
```

## 🎨 Paper Theme Classes

### Tailwind Classes
```css
/* Backgrounds */
bg-paper-bg          /* #f8f8f8 */
bg-paper-white       /* #ffffff */
bg-paper-light-gray  /* #e0e0e0 */

/* Text Colors */
text-paper-black     /* #333333 */
text-paper-gray      /* #666666 */

/* Borders */
border-paper-black   /* #333333 */
border-paper-border  /* #cccccc */

/* Fonts */
font-paper           /* Comic Sans MS */

/* Shadows */
shadow-paper         /* Paper shadow */
shadow-paper-hover   /* Paper hover shadow */
shadow-paper-btn     /* Button shadow */
```

### CSS Variables
```css
--paper-bg: #f8f8f8
--paper-white: #ffffff
--paper-black: #333333
--paper-gray: #666666
--paper-light-gray: #e0e0e0
--paper-border: #cccccc
--paper-radius: 8px
--paper-font: 'Comic Sans MS', cursive, sans-serif
```

## 🔧 Common Patterns

### Theme-Aware Navigation
```typescript
<nav className={`${isPaperTheme ? 'bg-paper-white border-b-2 border-paper-black' : 'bg-white border-b border-gray-200'}`}>
  <ThemeToggleButton />
</nav>
```

### Theme-Aware Cards
```typescript
<div className={getCardClass()}>
  <h3 className={getHeadingClass(3)}>Card Title</h3>
  <p className={isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}>
    Card content
  </p>
</div>
```

### Theme-Aware Forms
```typescript
<form>
  <label className={isPaperTheme ? 'text-paper-black font-paper font-bold uppercase' : 'text-gray-700'}>
    Label
  </label>
  <input className={getInputClass()} />
  <button className={getButtonClass('primary')}>Submit</button>
</form>
```

## 🚨 Common Mistakes

❌ **Don't:** Use theme hooks in server components
```typescript
// layout.tsx (Server Component)
const { isPaperTheme } = useThemeStyles(); // ERROR!
```

✅ **Do:** Use theme hooks in client components only
```typescript
// layout-client.tsx (Client Component)
'use client';
const { isPaperTheme } = useThemeStyles(); // CORRECT!
```

❌ **Don't:** Hardcode theme-specific styles
```typescript
<button className="bg-blue-600 text-white border-2 border-black">
```

✅ **Do:** Use theme utilities
```typescript
<button className={getButtonClass('primary')}>
```

## 📁 File Structure
```
src/themes/
├── theme-config.ts          # Theme configuration
├── ThemeProvider.tsx        # React context & hooks
├── css-loader.ts           # Dynamic CSS loading
├── current-theme/          # Original styles backup
├── paper-wireframe/        # Paper theme styles
└── components/             # Theme-aware components
```

## 🔗 Quick Links
- **Demo**: `/theme-demo`
- **Example**: `/web/abnfund`
- **Guide**: `/src/themes/THEMING_GUIDE.md`
- **Integration**: `/src/themes/integration-example.tsx`
