/**
 * Theme-Aware App Template
 * 
 * Use this template to create new apps with full theme integration.
 * Replace [APP_NAME] and [APP_PATH] with your actual values.
 */

// ===== LAYOUT.TSX (Server Component) =====
/*
import type { Metadata } from 'next';
import { ThemeProvider } from '@/themes/ThemeProvider';
import [APP_NAME]LayoutClient from './layout-client';

export const metadata: Metadata = {
  title: '[APP_TITLE]',
  description: '[APP_DESCRIPTION]',
};

export default function [APP_NAME]Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider defaultTheme="current">
      <[APP_NAME]LayoutClient>{children}</[APP_NAME]LayoutClient>
    </ThemeProvider>
  );
}
*/

// ===== LAYOUT-CLIENT.TSX (Client Component) =====
/**
 * 'use client';
 *
 * import React from 'react';
 * import Link from 'next/link';
 * import { useThemeStyles, ThemeToggleButton } from '@/themes/ThemeProvider';
 *
 * export default function [APP_NAME]LayoutClient({
 *   children,
 * }: {
 *   children: React.ReactNode;
 * }) {
 *   const { isPaperTheme } = useThemeStyles();
 *
 *   return (
 *     <div className={`min-h-screen transition-all duration-300 ${isPaperTheme ? 'bg-paper-bg font-paper' : 'bg-white font-sans'}`}>
 *       // Navigation
 *       <nav className={`sticky top-0 z-50 transition-all duration-300 ${isPaperTheme ? 'bg-paper-white border-b-2 border-paper-black' : 'bg-white/95 backdrop-blur-sm border-b border-gray-200'}`}>
 *         <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
 *           <div className="flex justify-between h-16 items-center">
 *             <div className="flex items-center space-x-3">
 *               <h1 className={`text-xl font-bold transition-all duration-300 ${isPaperTheme ? 'text-paper-black font-paper uppercase tracking-wide' : 'text-gray-900'}`}>
 *                 [APP_NAME]
 *               </h1>
 *             </div>
 *
 *             <div className="flex items-center space-x-4">
 *               // Add your navigation links here
 *               <Link
 *                 href="/[APP_PATH]"
 *                 className={`px-3 py-2 rounded-lg transition-all duration-300 ${isPaperTheme ? 'text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'}`}
 *               >
 *                 Home
 *               </Link>
 *
 *               <ThemeToggleButton />
 *             </div>
 *           </div>
 *         </div>
 *       </nav>
 *
 *       // Main Content
 *       <main className="flex-1">
 *         {children}
 *       </main>
 *
 *       // Footer
 *       <footer className={`mt-auto py-8 transition-all duration-300 ${isPaperTheme ? 'bg-paper-white border-t-2 border-paper-black' : 'bg-gray-50 border-t border-gray-200'}`}>
 *         <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
 *           <p className={`transition-all duration-300 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}`}>
 *             © {new Date().getFullYear()} [APP_NAME]. All rights reserved.
 *           </p>
 *         </div>
 *       </footer>
 *     </div>
 *   );
 * }
 */

// ===== PAGE.TSX (Main Page Component) =====
/**
 * 'use client';
 *
 * import React, { useState } from 'react';
 * import { useThemeStyles } from '@/themes/ThemeProvider';
 *
 * export default function [APP_NAME]Page() {
 *   const { getButtonClass, getCardClass, getInputClass, getHeadingClass, isPaperTheme } = useThemeStyles();
 *   const [inputValue, setInputValue] = useState('');
 *
 *   return (
 *     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
 *       // Hero Section
 *       <div className="text-center mb-12">
 *         <h1 className={getHeadingClass(1)}>
 *           Welcome to [APP_NAME]
 *         </h1>
 *         <p className={`mt-4 text-lg max-w-2xl mx-auto ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}`}>
 *           [APP_DESCRIPTION]
 *         </p>
 *       </div>
 *
 *       // Main Content Grid
 *       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
 *         // Feature Card 1
 *         <div className={getCardClass()}>
 *           <h3 className={getHeadingClass(3)}>Feature One</h3>
 *           <p className={`mt-2 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}`}>
 *             Description of your first feature.
 *           </p>
 *           <button className={`mt-4 ${getButtonClass('primary')}`}>
 *             Learn More
 *           </button>
 *         </div>
 *
 *         // Feature Card 2
 *         <div className={getCardClass()}>
 *           <h3 className={getHeadingClass(3)}>Feature Two</h3>
 *           <p className={`mt-2 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}`}>
 *             Description of your second feature.
 *           </p>
 *           <button className={`mt-4 ${getButtonClass('secondary')}`}>
 *             Get Started
 *           </button>
 *         </div>
 *
 *         // Feature Card 3
 *         <div className={getCardClass()}>
 *           <h3 className={getHeadingClass(3)}>Feature Three</h3>
 *           <p className={`mt-2 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}`}>
 *             Description of your third feature.
 *           </p>
 *           <button className={`mt-4 ${getButtonClass('outline')}`}>
 *             Explore
 *           </button>
 *         </div>
 *       </div>
 *
 *       // Interactive Section
 *       <div className={getCardClass()}>
 *         <h2 className={getHeadingClass(2)}>Try It Out</h2>
 *         <div className="mt-6 space-y-4">
 *           <div>
 *             <label className={`block text-sm font-medium mb-2 ${isPaperTheme ? 'text-paper-black font-paper font-bold uppercase tracking-wide' : 'text-gray-700'}`}>
 *               Sample Input
 *             </label>
 *             <input
 *               type="text"
 *               value={inputValue}
 *               onChange={(e) => setInputValue(e.target.value)}
 *               placeholder="Enter something..."
 *               className={getInputClass()}
 *             />
 *           </div>
 *
 *           <div className="flex gap-3">
 *             <button className={getButtonClass('primary')}>
 *               Submit
 *             </button>
 *             <button
 *               className={getButtonClass('secondary')}
 *               onClick={() => setInputValue('')}
 *             >
 *               Clear
 *             </button>
 *           </div>
 *         </div>
 *       </div>
 *     </div>
 *   );
 * }
 */

// ===== COMPONENT EXAMPLE =====
/**
 * 'use client';
 *
 * import React from 'react';
 * import { useThemeStyles } from '@/themes/ThemeProvider';
 *
 * interface [COMPONENT_NAME]Props {
 *   title: string;
 *   description: string;
 *   onAction?: () => void;
 * }
 *
 * export function [COMPONENT_NAME]({ title, description, onAction }: [COMPONENT_NAME]Props) {
 *   const { getCardClass, getHeadingClass, getButtonClass, isPaperTheme } = useThemeStyles();
 *
 *   return (
 *     <div className={getCardClass()}>
 *       <h3 className={getHeadingClass(3)}>{title}</h3>
 *       <p className={`mt-2 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-gray-600'}`}>
 *         {description}
 *       </p>
 *       {onAction && (
 *         <button
 *           className={`mt-4 ${getButtonClass('primary')}`}
 *           onClick={onAction}
 *         >
 *           Take Action
 *         </button>
 *       )}
 *     </div>
 *   );
 * }
 */

// ===== USAGE INSTRUCTIONS =====
/**
 * 1. Copy the template sections above
 * 2. Replace [APP_NAME] with your app name (e.g., TaskManager)
 * 3. Replace [APP_PATH] with your app path (e.g., taskmanager)
 * 4. Replace [APP_TITLE] and [APP_DESCRIPTION] with appropriate values
 * 5. Replace [COMPONENT_NAME] with your component name
 * 6. Create the file structure:
 *    - src/app/[APP_PATH]/layout.tsx
 *    - src/app/[APP_PATH]/layout-client.tsx
 *    - src/app/[APP_PATH]/page.tsx
 *    - src/app/[APP_PATH]/components/[ComponentName].tsx
 * 7. Customize the content and add your specific features
 * 8. Test both themes thoroughly
 *
 * EXAMPLE FILE STRUCTURE:
 * src/app/taskmanager/
 * ├── layout.tsx
 * ├── layout-client.tsx
 * ├── page.tsx
 * ├── components/
 * │   ├── TaskCard.tsx
 * │   ├── TaskForm.tsx
 * │   └── TaskList.tsx
 * └── types.ts
 */

export default function AppTemplate() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Theme-Aware App Template</h1>
      <p className="text-gray-600">
        This file contains templates for creating new theme-aware apps.
        See the comments above for complete code templates and usage instructions.
      </p>
    </div>
  );
}
