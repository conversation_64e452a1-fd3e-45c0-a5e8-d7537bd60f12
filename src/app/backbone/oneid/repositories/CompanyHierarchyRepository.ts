import { Company, CompanyHierarchy, CompanyHierarchyNode } from '../core/types';
import { CompanyRepository } from './CompanyRepository';

export class CompanyHierarchyRepository {
  private companyRepo: CompanyRepository;

  constructor() {
    this.companyRepo = new CompanyRepository();
  }

  /**
   * Find all companies by hierarchy level
   */
  async findByHierarchyLevel(level: number): Promise<Company[]> {
    try {
      const allCompanies = await this.companyRepo.findAll();
      return allCompanies.filter(company => company.hierarchyLevel === level);
    } catch (error) {
      console.error('Error finding companies by hierarchy level:', error);
      return [];
    }
  }

  /**
   * Find all root companies (level 0)
   */
  async findRootCompanies(): Promise<Company[]> {
    return this.findByHierarchyLevel(0);
  }

  /**
   * Find direct children of a company
   */
  async findDirectChildren(parentId: string): Promise<Company[]> {
    try {
      const allCompanies = await this.companyRepo.findAll();
      return allCompanies.filter(company => company.parentCompanyId === parentId);
    } catch (error) {
      console.error('Error finding direct children:', error);
      return [];
    }
  }

  /**
   * Find all descendants of a company (recursive)
   */
  async findAllDescendants(companyId: string): Promise<Company[]> {
    try {
      const descendants: Company[] = [];
      const directChildren = await this.findDirectChildren(companyId);
      
      for (const child of directChildren) {
        descendants.push(child);
        const childDescendants = await this.findAllDescendants(child.id);
        descendants.push(...childDescendants);
      }

      return descendants;
    } catch (error) {
      console.error('Error finding all descendants:', error);
      return [];
    }
  }

  /**
   * Find all ancestors of a company
   */
  async findAllAncestors(companyId: string): Promise<Company[]> {
    try {
      const company = await this.companyRepo.findById(companyId);
      if (!company || !company.parentCompanyId) {
        return [];
      }

      const ancestors: Company[] = [];
      let currentParentId = company.parentCompanyId;

      while (currentParentId) {
        const parent = await this.companyRepo.findById(currentParentId);
        if (parent) {
          ancestors.push(parent);
          currentParentId = parent.parentCompanyId;
        } else {
          break;
        }
      }

      return ancestors;
    } catch (error) {
      console.error('Error finding all ancestors:', error);
      return [];
    }
  }

  /**
   * Find companies by hierarchy path
   */
  async findByHierarchyPath(path: string[]): Promise<Company[]> {
    try {
      const companies = await Promise.all(
        path.map(id => this.companyRepo.findById(id))
      );
      return companies.filter(company => company !== null) as Company[];
    } catch (error) {
      console.error('Error finding companies by hierarchy path:', error);
      return [];
    }
  }

  /**
   * Check if moving a company would create a circular reference
   */
  async wouldCreateCircularReference(companyId: string, newParentId: string): Promise<boolean> {
    try {
      // If trying to make a company its own parent
      if (companyId === newParentId) {
        return true;
      }

      // Check if the new parent is a descendant of the company
      const descendants = await this.findAllDescendants(companyId);
      return descendants.some(desc => desc.id === newParentId);
    } catch (error) {
      console.error('Error checking circular reference:', error);
      return true; // Err on the side of caution
    }
  }

  /**
   * Get the maximum hierarchy depth
   */
  async getMaxHierarchyDepth(): Promise<number> {
    try {
      const allCompanies = await this.companyRepo.findAll();
      return Math.max(...allCompanies.map(company => company.hierarchyLevel), 0);
    } catch (error) {
      console.error('Error getting max hierarchy depth:', error);
      return 0;
    }
  }

  /**
   * Count companies by hierarchy level
   */
  async countCompaniesByLevel(): Promise<Record<number, number>> {
    try {
      const allCompanies = await this.companyRepo.findAll();
      const counts: Record<number, number> = {};

      allCompanies.forEach(company => {
        const level = company.hierarchyLevel;
        counts[level] = (counts[level] || 0) + 1;
      });

      return counts;
    } catch (error) {
      console.error('Error counting companies by level:', error);
      return {};
    }
  }

  /**
   * Find companies within a specific hierarchy branch
   */
  async findCompaniesinBranch(rootCompanyId: string): Promise<Company[]> {
    try {
      const rootCompany = await this.companyRepo.findById(rootCompanyId);
      if (!rootCompany) {
        return [];
      }

      const branchCompanies = [rootCompany];
      const descendants = await this.findAllDescendants(rootCompanyId);
      branchCompanies.push(...descendants);

      return branchCompanies;
    } catch (error) {
      console.error('Error finding companies in branch:', error);
      return [];
    }
  }

  /**
   * Find sibling companies (companies with the same parent)
   */
  async findSiblingCompanies(companyId: string): Promise<Company[]> {
    try {
      const company = await this.companyRepo.findById(companyId);
      if (!company) {
        return [];
      }

      if (!company.parentCompanyId) {
        // Root company - siblings are other root companies
        const rootCompanies = await this.findRootCompanies();
        return rootCompanies.filter(root => root.id !== companyId);
      }

      // Find all children of the parent, excluding the current company
      const siblings = await this.findDirectChildren(company.parentCompanyId);
      return siblings.filter(sibling => sibling.id !== companyId);
    } catch (error) {
      console.error('Error finding sibling companies:', error);
      return [];
    }
  }

  /**
   * Update hierarchy information for a company and its descendants
   */
  async updateHierarchyInfo(companyId: string, newParentId?: string): Promise<boolean> {
    try {
      const company = await this.companyRepo.findById(companyId);
      if (!company) {
        return false;
      }

      let newHierarchyPath: string[];
      let newHierarchyLevel: number;

      if (newParentId) {
        const newParent = await this.companyRepo.findById(newParentId);
        if (!newParent) {
          return false;
        }
        newHierarchyPath = [...newParent.hierarchyPath, companyId];
        newHierarchyLevel = newParent.hierarchyLevel + 1;
      } else {
        // Making it a root company
        newHierarchyPath = [companyId];
        newHierarchyLevel = 0;
      }

      // Update the company
      const updated = await this.companyRepo.update(companyId, {
        parentCompanyId: newParentId || null,
        hierarchyPath: newHierarchyPath,
        hierarchyLevel: newHierarchyLevel,
        updatedAt: new Date().toISOString()
      });

      if (!updated) {
        return false;
      }

      // Update all descendants
      await this.updateDescendantHierarchy(companyId, newHierarchyPath, newHierarchyLevel);

      return true;
    } catch (error) {
      console.error('Error updating hierarchy info:', error);
      return false;
    }
  }

  /**
   * Recursively update hierarchy information for all descendants
   */
  private async updateDescendantHierarchy(
    parentId: string, 
    parentPath: string[], 
    parentLevel: number
  ): Promise<void> {
    try {
      const children = await this.findDirectChildren(parentId);
      
      for (const child of children) {
        const newPath = [...parentPath, child.id];
        const newLevel = parentLevel + 1;
        
        await this.companyRepo.update(child.id, {
          hierarchyPath: newPath,
          hierarchyLevel: newLevel,
          updatedAt: new Date().toISOString()
        });

        // Recursively update descendants
        await this.updateDescendantHierarchy(child.id, newPath, newLevel);
      }
    } catch (error) {
      console.error('Error updating descendant hierarchy:', error);
    }
  }

  /**
   * Validate hierarchy integrity
   */
  async validateHierarchyIntegrity(): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    try {
      const errors: string[] = [];
      const allCompanies = await this.companyRepo.findAll();

      for (const company of allCompanies) {
        // Check if parent exists (if specified)
        if (company.parentCompanyId) {
          const parent = await this.companyRepo.findById(company.parentCompanyId);
          if (!parent) {
            errors.push(`Company ${company.id} has non-existent parent ${company.parentCompanyId}`);
          } else {
            // Check if parent's child list includes this company
            if (!parent.childCompanyIds.includes(company.id)) {
              errors.push(`Parent ${parent.id} doesn't include child ${company.id} in its child list`);
            }
          }
        }

        // Check if all children exist
        for (const childId of company.childCompanyIds) {
          const child = await this.companyRepo.findById(childId);
          if (!child) {
            errors.push(`Company ${company.id} has non-existent child ${childId}`);
          } else {
            // Check if child's parent is this company
            if (child.parentCompanyId !== company.id) {
              errors.push(`Child ${childId} doesn't have ${company.id} as its parent`);
            }
          }
        }

        // Check hierarchy path consistency
        if (company.hierarchyPath.length !== company.hierarchyLevel + 1) {
          errors.push(`Company ${company.id} has inconsistent hierarchy path length`);
        }

        if (company.hierarchyPath[company.hierarchyPath.length - 1] !== company.id) {
          errors.push(`Company ${company.id} is not the last element in its hierarchy path`);
        }
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      console.error('Error validating hierarchy integrity:', error);
      return {
        isValid: false,
        errors: ['Error during validation']
      };
    }
  }
}
