// OneIDClientService - Client-side wrapper for OneID System
// This provides a client-safe interface that doesn't import server-side dependencies

import { User, Company, Session } from '../core/types';

export class OneIDClientService {
  private static instance: OneIDClientService | null = null;

  private constructor() {}

  static getInstance(): OneIDClientService {
    if (!OneIDClientService.instance) {
      OneIDClientService.instance = new OneIDClientService();
    }
    return OneIDClientService.instance;
  }

  // Authentication methods - these make API calls instead of direct DB access
  async authenticate(username: string, password: string): Promise<{ success: boolean; user?: User; session?: Session; error?: string }> {
    try {
      const response = await fetch('/api/auth/oneid/authenticate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  async validateSession(token: string): Promise<{ valid: boolean; user?: User; session?: Session }> {
    try {
      const response = await fetch('/api/auth/oneid/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ token }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return { valid: false };
    }
  }

  async logout(token: string): Promise<{ success: boolean }> {
    try {
      const response = await fetch('/api/auth/oneid/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ token }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return { success: false };
    }
  }

  // User methods
  async getUserById(userId: string): Promise<User | null> {
    try {
      const response = await fetch(`/api/oneid/users/${userId}`);
      if (!response.ok) return null;
      
      const result = await response.json();
      return result.user || null;
    } catch (error) {
      return null;
    }
  }

  async getUserByUsername(username: string): Promise<User | null> {
    try {
      const response = await fetch(`/api/oneid/users?username=${encodeURIComponent(username)}`);
      if (!response.ok) return null;
      
      const result = await response.json();
      return result.user || null;
    } catch (error) {
      return null;
    }
  }

  async createUser(userData: Partial<User>): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const response = await fetch('/api/oneid/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create user'
      };
    }
  }

  // Company methods
  async getCompanyById(companyId: string): Promise<Company | null> {
    try {
      const response = await fetch(`/api/oneid/companies/${companyId}`);
      if (!response.ok) return null;
      
      const result = await response.json();
      return result.company || null;
    } catch (error) {
      return null;
    }
  }

  // Magic Link methods
  async generateMagicLink(email: string, returnUrl?: string): Promise<{ success: boolean; token?: string; error?: string }> {
    try {
      const response = await fetch('/api/auth/oneid/magic-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, returnUrl }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate magic link'
      };
    }
  }

  async validateMagicLink(token: string): Promise<{ valid: boolean; user?: User; session?: Session }> {
    try {
      const response = await fetch('/api/auth/oneid/magic-link/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return { valid: false };
    }
  }
}
