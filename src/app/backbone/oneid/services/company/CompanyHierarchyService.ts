import { 
  Company, 
  CompanyHierarchy, 
  CompanyHierarchyNode, 
  CreateChildCompanyRequest,
  CompanyHierarchyStats,
  MoveCompanyRequest,
  CompanyAccessPermissions,
  ApiResponse,
  CompanyStatus
} from '../../core/types';
import { CompanyRepository } from '../../repositories/CompanyRepository';
import { UserRepository } from '../../repositories/UserRepository';
import { generateId } from '../../utils/helpers';

export class CompanyHierarchyService {
  private companyRepo: CompanyRepository;
  private userRepo: UserRepository;

  constructor() {
    this.companyRepo = new CompanyRepository();
    this.userRepo = new UserRepository();
  }

  /**
   * Create a child company under a parent company
   */
  async createChildCompany(request: CreateChildCompanyRequest): Promise<ApiResponse<Company>> {
    try {
      // Validate parent company exists and allows child companies
      const parentCompany = await this.companyRepo.findById(request.parentCompanyId);
      if (!parentCompany) {
        return {
          success: false,
          error: { code: 'PARENT_NOT_FOUND', message: 'Parent company not found' }
        };
      }

      if (!parentCompany.settings.allowChildCompanies) {
        return {
          success: false,
          error: { code: 'CHILD_COMPANIES_NOT_ALLOWED', message: 'Parent company does not allow child companies' }
        };
      }

      // Check max child companies limit
      if (parentCompany.settings.maxChildCompanies && 
          parentCompany.childCompanyIds.length >= parentCompany.settings.maxChildCompanies) {
        return {
          success: false,
          error: { code: 'MAX_CHILDREN_EXCEEDED', message: 'Maximum child companies limit exceeded' }
        };
      }

      // Check for circular references
      if (await this.wouldCreateCircularReference(request.parentCompanyId, request.companyInfo.name)) {
        return {
          success: false,
          error: { code: 'CIRCULAR_REFERENCE', message: 'Creating this relationship would create a circular reference' }
        };
      }

      // Create the child company
      const childCompanyId = generateId();
      const now = new Date().toISOString();
      
      // Build hierarchy path
      const hierarchyPath = [...parentCompany.hierarchyPath, childCompanyId];
      const hierarchyLevel = parentCompany.hierarchyLevel + 1;

      // Inherit settings if requested
      const inheritedSettings = this.buildInheritedSettings(parentCompany, request.inheritSettings);

      const childCompany: Company = {
        id: childCompanyId,
        name: request.companyInfo.name,
        displayName: request.companyInfo.displayName || request.companyInfo.name,
        description: request.companyInfo.description,
        industry: request.companyInfo.industry || parentCompany.industry,
        website: request.companyInfo.website,
        email: request.companyInfo.email,
        phone: request.companyInfo.phone,
        address: request.companyInfo.address,
        logo: request.companyInfo.logo,
        status: 'active' as CompanyStatus,
        createdAt: now,
        updatedAt: now,
        createdBy: '', // Will be set after admin user creation
        
        // Hierarchy fields
        parentCompanyId: request.parentCompanyId,
        childCompanyIds: [],
        hierarchyLevel,
        hierarchyPath,
        
        settings: {
          ...inheritedSettings,
          ...request.customSettings,
          inheritFromParent: request.inheritSettings || {
            passwordPolicy: false,
            sessionTimeout: false,
            employeeRegistration: false,
            emailVerification: false
          },
          allowChildCompanies: request.customSettings?.allowChildCompanies ?? true,
          maxChildCompanies: request.customSettings?.maxChildCompanies ?? 5,
          childCompanyPermissions: request.customSettings?.childCompanyPermissions ?? {
            canCreateSubsidiaries: false,
            canManageEmployees: true,
            canViewParentData: false,
            canInheritSettings: true
          }
        },
        metadata: {}
      };

      // Save the child company
      const savedCompany = await this.companyRepo.create(childCompany);
      if (!savedCompany) {
        return {
          success: false,
          error: { code: 'CREATION_FAILED', message: 'Failed to create child company' }
        };
      }

      // Update parent company's child list
      await this.companyRepo.update(request.parentCompanyId, {
        childCompanyIds: [...parentCompany.childCompanyIds, childCompanyId],
        updatedAt: now
      });

      // Create admin user for the child company
      // This would integrate with the existing user creation logic
      // For now, we'll just update the createdBy field
      await this.companyRepo.update(childCompanyId, {
        createdBy: 'system', // This should be the actual admin user ID
        updatedAt: now
      });

      return {
        success: true,
        data: savedCompany
      };

    } catch (error) {
      console.error('Error creating child company:', error);
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
      };
    }
  }

  /**
   * Get the complete hierarchy tree for a company
   */
  async getCompanyHierarchy(companyId: string): Promise<CompanyHierarchy | null> {
    try {
      const company = await this.companyRepo.findById(companyId);
      if (!company) return null;

      return await this.buildHierarchyTree(company);
    } catch (error) {
      console.error('Error getting company hierarchy:', error);
      return null;
    }
  }

  /**
   * Get all root companies (companies without parents)
   */
  async getRootCompanies(): Promise<Company[]> {
    try {
      const allCompanies = await this.companyRepo.findAll();
      return allCompanies.filter(company => !company.parentCompanyId);
    } catch (error) {
      console.error('Error getting root companies:', error);
      return [];
    }
  }

  /**
   * Get direct children of a company
   */
  async getDirectChildren(companyId: string): Promise<Company[]> {
    try {
      const company = await this.companyRepo.findById(companyId);
      if (!company) return [];

      const children = await Promise.all(
        company.childCompanyIds.map(childId => this.companyRepo.findById(childId))
      );

      return children.filter(child => child !== null) as Company[];
    } catch (error) {
      console.error('Error getting direct children:', error);
      return [];
    }
  }

  /**
   * Get all descendants of a company (children, grandchildren, etc.)
   */
  async getAllDescendants(companyId: string): Promise<Company[]> {
    try {
      const descendants: Company[] = [];
      const directChildren = await this.getDirectChildren(companyId);
      
      for (const child of directChildren) {
        descendants.push(child);
        const childDescendants = await this.getAllDescendants(child.id);
        descendants.push(...childDescendants);
      }

      return descendants;
    } catch (error) {
      console.error('Error getting all descendants:', error);
      return [];
    }
  }

  /**
   * Get the path from root to a specific company
   */
  async getCompanyPath(companyId: string): Promise<Company[]> {
    try {
      const company = await this.companyRepo.findById(companyId);
      if (!company) return [];

      const path = await Promise.all(
        company.hierarchyPath.map(id => this.companyRepo.findById(id))
      );

      return path.filter(comp => comp !== null) as Company[];
    } catch (error) {
      console.error('Error getting company path:', error);
      return [];
    }
  }

  /**
   * Move a company to a new parent (or make it a root company)
   */
  async moveCompany(request: MoveCompanyRequest): Promise<ApiResponse<boolean>> {
    try {
      const company = await this.companyRepo.findById(request.companyId);
      if (!company) {
        return {
          success: false,
          error: { code: 'COMPANY_NOT_FOUND', message: 'Company not found' }
        };
      }

      // Validate new parent if specified
      if (request.newParentId) {
        const newParent = await this.companyRepo.findById(request.newParentId);
        if (!newParent) {
          return {
            success: false,
            error: { code: 'NEW_PARENT_NOT_FOUND', message: 'New parent company not found' }
          };
        }

        // Check for circular references
        if (await this.wouldCreateCircularReference(request.newParentId, company.name, request.companyId)) {
          return {
            success: false,
            error: { code: 'CIRCULAR_REFERENCE', message: 'Moving would create a circular reference' }
          };
        }
      }

      // Remove from old parent
      if (company.parentCompanyId) {
        const oldParent = await this.companyRepo.findById(company.parentCompanyId);
        if (oldParent) {
          await this.companyRepo.update(company.parentCompanyId, {
            childCompanyIds: oldParent.childCompanyIds.filter(id => id !== request.companyId),
            updatedAt: new Date().toISOString()
          });
        }
      }

      // Add to new parent or make root
      let newHierarchyPath: string[];
      let newHierarchyLevel: number;

      if (request.newParentId) {
        const newParent = await this.companyRepo.findById(request.newParentId);
        if (newParent) {
          await this.companyRepo.update(request.newParentId, {
            childCompanyIds: [...newParent.childCompanyIds, request.companyId],
            updatedAt: new Date().toISOString()
          });
          newHierarchyPath = [...newParent.hierarchyPath, request.companyId];
          newHierarchyLevel = newParent.hierarchyLevel + 1;
        } else {
          throw new Error('New parent not found');
        }
      } else {
        // Making it a root company
        newHierarchyPath = [request.companyId];
        newHierarchyLevel = 0;
      }

      // Update the company
      await this.companyRepo.update(request.companyId, {
        parentCompanyId: request.newParentId || null,
        hierarchyPath: newHierarchyPath,
        hierarchyLevel: newHierarchyLevel,
        updatedAt: new Date().toISOString()
      });

      // Update all descendants if preserving children
      if (request.preserveChildren !== false) {
        await this.updateDescendantHierarchy(request.companyId);
      }

      return { success: true, data: true };

    } catch (error) {
      console.error('Error moving company:', error);
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
      };
    }
  }

  /**
   * Get hierarchy statistics
   */
  async getHierarchyStats(): Promise<CompanyHierarchyStats> {
    try {
      const allCompanies = await this.companyRepo.findAll();
      
      const stats: CompanyHierarchyStats = {
        totalCompanies: allCompanies.length,
        maxDepth: 0,
        companiesByLevel: {},
        rootCompanies: 0,
        activeCompanies: allCompanies.filter(c => c.status === 'active').length,
        inactiveCompanies: allCompanies.filter(c => c.status !== 'active').length
      };

      allCompanies.forEach(company => {
        const level = company.hierarchyLevel;
        stats.companiesByLevel[level] = (stats.companiesByLevel[level] || 0) + 1;
        stats.maxDepth = Math.max(stats.maxDepth, level);
        
        if (level === 0) {
          stats.rootCompanies++;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error getting hierarchy stats:', error);
      return {
        totalCompanies: 0,
        maxDepth: 0,
        companiesByLevel: {},
        rootCompanies: 0,
        activeCompanies: 0,
        inactiveCompanies: 0
      };
    }
  }

  // Private helper methods
  private async buildHierarchyTree(company: Company): Promise<CompanyHierarchy> {
    const children = await this.getDirectChildren(company.id);
    const childHierarchies = await Promise.all(
      children.map(child => this.buildHierarchyTree(child))
    );

    return {
      companyId: company.id,
      parentId: company.parentCompanyId,
      children: childHierarchies,
      level: company.hierarchyLevel,
      path: company.hierarchyPath,
      company
    };
  }

  private buildInheritedSettings(parentCompany: Company, inheritSettings?: CreateChildCompanyRequest['inheritSettings']) {
    const settings = {
      allowEmployeeRegistration: parentCompany.settings.allowEmployeeRegistration,
      requireEmailVerification: parentCompany.settings.requireEmailVerification,
      passwordPolicy: parentCompany.settings.passwordPolicy,
      sessionTimeout: parentCompany.settings.sessionTimeout
    };

    if (inheritSettings?.passwordPolicy) {
      settings.passwordPolicy = parentCompany.settings.passwordPolicy;
    }
    if (inheritSettings?.sessionTimeout) {
      settings.sessionTimeout = parentCompany.settings.sessionTimeout;
    }
    if (inheritSettings?.employeeRegistration) {
      settings.allowEmployeeRegistration = parentCompany.settings.allowEmployeeRegistration;
    }
    if (inheritSettings?.emailVerification) {
      settings.requireEmailVerification = parentCompany.settings.requireEmailVerification;
    }

    return settings;
  }

  private async wouldCreateCircularReference(parentId: string, companyName: string, companyId?: string): Promise<boolean> {
    // Check if the parent is actually a descendant of the company being moved/created
    if (companyId) {
      const descendants = await this.getAllDescendants(companyId);
      return descendants.some(desc => desc.id === parentId);
    }
    return false;
  }

  private async updateDescendantHierarchy(companyId: string): Promise<void> {
    const company = await this.companyRepo.findById(companyId);
    if (!company) return;

    const children = await this.getDirectChildren(companyId);
    
    for (const child of children) {
      const newPath = [...company.hierarchyPath, child.id];
      const newLevel = company.hierarchyLevel + 1;
      
      await this.companyRepo.update(child.id, {
        hierarchyPath: newPath,
        hierarchyLevel: newLevel,
        updatedAt: new Date().toISOString()
      });

      // Recursively update descendants
      await this.updateDescendantHierarchy(child.id);
    }
  }
}
