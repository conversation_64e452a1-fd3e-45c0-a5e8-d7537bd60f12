import { 
  User, 
  Company, 
  CompanyAccessPermissions,
  EmployeeUser,
  CompanyAdminUser,
  ApiResponse
} from '../../core/types';
import { UserRepository } from '../../repositories/UserRepository';
import { CompanyRepository } from '../../repositories/CompanyRepository';
import { CompanyHierarchyRepository } from '../../repositories/CompanyHierarchyRepository';

export interface UserCompanyAssociation {
  userId: string;
  companyId: string;
  associationType: 'direct' | 'inherited_parent' | 'inherited_child';
  permissions: string[];
  canView: boolean;
  canEdit: boolean;
  canManageEmployees: boolean;
  canCreateChildren: boolean;
  canViewChildren: boolean;
  canViewParent: boolean;
  inheritanceLevel: number; // 0 = direct, 1 = parent, 2 = grandparent, etc.
}

export interface MultiLevelCompanyAccess {
  directCompanies: Company[];
  parentCompanies: Company[];
  childCompanies: Company[];
  allAccessibleCompanies: Company[];
  permissions: Record<string, CompanyAccessPermissions>;
}

export class UserCompanyAssociationService {
  private userRepo: UserRepository;
  private companyRepo: CompanyRepository;
  private hierarchyRepo: CompanyHierarchyRepository;

  constructor() {
    this.userRepo = new UserRepository();
    this.companyRepo = new CompanyRepository();
    this.hierarchyRepo = new CompanyHierarchyRepository();
  }

  /**
   * Get all companies a user has access to (including through hierarchy)
   */
  async getUserCompanyAccess(userId: string): Promise<MultiLevelCompanyAccess | null> {
    try {
      const user = await this.userRepo.findById(userId);
      if (!user) return null;

      const access: MultiLevelCompanyAccess = {
        directCompanies: [],
        parentCompanies: [],
        childCompanies: [],
        allAccessibleCompanies: [],
        permissions: {}
      };

      // Get direct company association
      if (user.userType === 'employee' || user.userType === 'company_admin') {
        const userWithCompany = user as EmployeeUser | CompanyAdminUser;
        const directCompany = await this.companyRepo.findById(userWithCompany.companyId);
        
        if (directCompany) {
          access.directCompanies.push(directCompany);
          access.allAccessibleCompanies.push(directCompany);
          
          // Calculate permissions for direct company
          access.permissions[directCompany.id] = await this.calculateCompanyPermissions(user, directCompany);

          // Get parent companies (if user is admin and has parent access)
          if (user.userType === 'company_admin') {
            const parentCompanies = await this.getAccessibleParentCompanies(directCompany);
            access.parentCompanies.push(...parentCompanies);
            access.allAccessibleCompanies.push(...parentCompanies);

            // Calculate permissions for parent companies
            for (const parent of parentCompanies) {
              access.permissions[parent.id] = await this.calculateCompanyPermissions(user, parent);
            }
          }

          // Get child companies (if user is admin and has child access)
          if (user.userType === 'company_admin' && directCompany.settings.allowChildCompanies) {
            const childCompanies = await this.getAccessibleChildCompanies(directCompany);
            access.childCompanies.push(...childCompanies);
            access.allAccessibleCompanies.push(...childCompanies);

            // Calculate permissions for child companies
            for (const child of childCompanies) {
              access.permissions[child.id] = await this.calculateCompanyPermissions(user, child);
            }
          }
        }
      }

      return access;
    } catch (error) {
      console.error('Error getting user company access:', error);
      return null;
    }
  }

  /**
   * Check if a user has specific permission for a company
   */
  async hasCompanyPermission(
    userId: string, 
    companyId: string, 
    permission: string
  ): Promise<boolean> {
    try {
      const access = await this.getUserCompanyAccess(userId);
      if (!access) return false;

      const companyPermissions = access.permissions[companyId];
      if (!companyPermissions) return false;

      return companyPermissions.directPermissions.includes(permission) ||
             companyPermissions.inheritedPermissions.includes(permission);
    } catch (error) {
      console.error('Error checking company permission:', error);
      return false;
    }
  }

  /**
   * Get all users associated with a company (including through hierarchy)
   */
  async getCompanyUsers(
    companyId: string, 
    includeHierarchy: boolean = false
  ): Promise<{
    directUsers: User[];
    hierarchyUsers: User[];
    allUsers: User[];
  }> {
    try {
      const directUsers = await this.userRepo.findByCompanyId(companyId);
      let hierarchyUsers: User[] = [];

      if (includeHierarchy) {
        const company = await this.companyRepo.findById(companyId);
        if (company) {
          // Get users from parent companies (who might have access to this company)
          const parentCompanies = await this.hierarchyRepo.findAllAncestors(companyId);
          for (const parent of parentCompanies) {
            const parentUsers = await this.userRepo.findByCompanyId(parent.id);
            // Only include admin users who have child access permissions
            const adminUsers = parentUsers.filter(user => 
              user.userType === 'company_admin' && 
              parent.settings.childCompanyPermissions.canViewParentData
            );
            hierarchyUsers.push(...adminUsers);
          }

          // Get users from child companies (if they have parent access)
          const childCompanies = await this.hierarchyRepo.findAllDescendants(companyId);
          for (const child of childCompanies) {
            if (child.settings.childCompanyPermissions.canViewParentData) {
              const childUsers = await this.userRepo.findByCompanyId(child.id);
              const adminUsers = childUsers.filter(user => user.userType === 'company_admin');
              hierarchyUsers.push(...adminUsers);
            }
          }
        }
      }

      return {
        directUsers,
        hierarchyUsers,
        allUsers: [...directUsers, ...hierarchyUsers]
      };
    } catch (error) {
      console.error('Error getting company users:', error);
      return {
        directUsers: [],
        hierarchyUsers: [],
        allUsers: []
      };
    }
  }

  /**
   * Associate a user with a company (with hierarchy considerations)
   */
  async associateUserWithCompany(
    userId: string,
    companyId: string,
    userType: 'employee' | 'company_admin',
    permissions: string[] = []
  ): Promise<ApiResponse<boolean>> {
    try {
      const user = await this.userRepo.findById(userId);
      const company = await this.companyRepo.findById(companyId);

      if (!user || !company) {
        return {
          success: false,
          error: { code: 'NOT_FOUND', message: 'User or company not found' }
        };
      }

      // Check if company allows employee registration
      if (userType === 'employee' && !company.settings.allowEmployeeRegistration) {
        return {
          success: false,
          error: { code: 'REGISTRATION_NOT_ALLOWED', message: 'Company does not allow employee registration' }
        };
      }

      // Update user with company association
      const updates: Partial<User> = {
        userType,
        updatedAt: new Date().toISOString()
      };

      if (userType === 'employee') {
        (updates as Partial<EmployeeUser>).companyId = companyId;
        (updates as Partial<EmployeeUser>).permissions = permissions;
      } else if (userType === 'company_admin') {
        (updates as Partial<CompanyAdminUser>).companyId = companyId;
        (updates as Partial<CompanyAdminUser>).permissions = permissions;
      }

      const updatedUser = await this.userRepo.update(userId, updates);
      
      return {
        success: !!updatedUser,
        data: !!updatedUser
      };
    } catch (error) {
      console.error('Error associating user with company:', error);
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
      };
    }
  }

  /**
   * Remove user association from a company
   */
  async removeUserFromCompany(userId: string): Promise<ApiResponse<boolean>> {
    try {
      const user = await this.userRepo.findById(userId);
      if (!user) {
        return {
          success: false,
          error: { code: 'USER_NOT_FOUND', message: 'User not found' }
        };
      }

      // Convert to individual user
      const updates: Partial<User> = {
        userType: 'individual',
        updatedAt: new Date().toISOString()
      };

      // Remove company-specific fields
      if ('companyId' in user) {
        delete (updates as any).companyId;
      }
      if ('permissions' in user) {
        delete (updates as any).permissions;
      }

      const updatedUser = await this.userRepo.update(userId, updates);
      
      return {
        success: !!updatedUser,
        data: !!updatedUser
      };
    } catch (error) {
      console.error('Error removing user from company:', error);
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
      };
    }
  }

  // Private helper methods

  private async getAccessibleParentCompanies(company: Company): Promise<Company[]> {
    const accessibleParents: Company[] = [];
    
    if (company.parentCompanyId && company.settings.childCompanyPermissions.canViewParentData) {
      const parent = await this.companyRepo.findById(company.parentCompanyId);
      if (parent) {
        accessibleParents.push(parent);
        // Recursively get grandparents if allowed
        const grandparents = await this.getAccessibleParentCompanies(parent);
        accessibleParents.push(...grandparents);
      }
    }
    
    return accessibleParents;
  }

  private async getAccessibleChildCompanies(company: Company): Promise<Company[]> {
    const accessibleChildren: Company[] = [];
    
    if (company.settings.childCompanyPermissions.canCreateSubsidiaries) {
      const directChildren = await this.hierarchyRepo.findDirectChildren(company.id);
      accessibleChildren.push(...directChildren);
      
      // Recursively get grandchildren if allowed
      for (const child of directChildren) {
        if (child.settings.childCompanyPermissions.canViewParentData) {
          const grandchildren = await this.getAccessibleChildCompanies(child);
          accessibleChildren.push(...grandchildren);
        }
      }
    }
    
    return accessibleChildren;
  }

  private async calculateCompanyPermissions(user: User, company: Company): Promise<CompanyAccessPermissions> {
    const permissions: CompanyAccessPermissions = {
      companyId: company.id,
      canView: false,
      canEdit: false,
      canManageEmployees: false,
      canCreateChildren: false,
      canViewChildren: false,
      canViewParent: false,
      inheritedPermissions: [],
      directPermissions: []
    };

    // Direct company association
    if ((user.userType === 'employee' || user.userType === 'company_admin') && 
        'companyId' in user && user.companyId === company.id) {
      
      permissions.canView = true;
      permissions.directPermissions.push('view');

      if (user.userType === 'company_admin') {
        permissions.canEdit = true;
        permissions.canManageEmployees = true;
        permissions.canViewChildren = company.settings.allowChildCompanies;
        permissions.canCreateChildren = company.settings.allowChildCompanies;
        permissions.directPermissions.push('edit', 'manage_employees');
        
        if (company.settings.allowChildCompanies) {
          permissions.directPermissions.push('view_children', 'create_children');
        }
      }
    }

    // Inherited permissions from parent company
    if (user.userType === 'company_admin' && 'companyId' in user && company.parentCompanyId === user.companyId) {
      permissions.canView = true;
      permissions.canViewChildren = true;
      permissions.inheritedPermissions.push('view_from_parent', 'view_children_from_parent');
      
      if (company.settings.childCompanyPermissions.canViewParentData) {
        permissions.canEdit = true;
        permissions.inheritedPermissions.push('edit_from_parent');
      }
    }

    // Inherited permissions from child company
    if (user.userType === 'company_admin' && 'companyId' in user && 
        company.childCompanyIds.includes(user.companyId)) {
      const userCompany = await this.companyRepo.findById(user.companyId);
      if (userCompany?.settings.childCompanyPermissions.canViewParentData) {
        permissions.canView = true;
        permissions.canViewParent = true;
        permissions.inheritedPermissions.push('view_from_child', 'view_parent_from_child');
      }
    }

    return permissions;
  }
}
