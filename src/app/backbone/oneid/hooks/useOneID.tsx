'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { OneIDClientService } from '../services/OneIDClientService';

interface User {
  id: string;
  username: string;
  email?: string;
  firstName?: string;
  lastName?: string;
}

interface OneIDContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: () => void;
  logout: () => void;
}

const OneIDContext = createContext<OneIDContextType | undefined>(undefined);

export function OneIDProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('oneid_token');
        if (token) {
          const oneIDService = OneIDClientService.getInstance();
          const result = await oneIDService.validateSession(token);
          
          if (result.valid && result.user) {
            setUser(result.user);
          } else {
            localStorage.removeItem('oneid_token');
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (typeof window !== 'undefined') {
          localStorage.removeItem('oneid_token');
        }
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, [mounted]);

  const login = () => {
    // Redirect to OneID login page
    if (typeof window !== 'undefined') {
      window.location.href = '/api/auth/oneid/login';
    }
  };

  const logout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('oneid_token');
    }
    setUser(null);
    if (typeof window !== 'undefined') {
      window.location.href = '/platforms/travel/start';
    }
  };

  const value: OneIDContextType = {
    user,
    isAuthenticated: !!user,
    loading,
    login,
    logout,
  };

  return (
    <OneIDContext.Provider value={value}>
      {children}
    </OneIDContext.Provider>
  );
}

export function useOneID() {
  const context = useContext(OneIDContext);
  if (context === undefined) {
    throw new Error('useOneID must be used within a OneIDProvider');
  }
  return context;
}