import { NextRequest, NextResponse } from 'next/server';
import { 
  CreateChildCompanyRequest,
  MoveCompanyRequest,
  ApiResponse,
  Company,
  CompanyHierarchy,
  CompanyHierarchyNode,
  CompanyHierarchyStats
} from '../core/types';
import { CompanyService } from '../services/company/CompanyService';
import { UserCompanyAssociationService } from '../services/user/UserCompanyAssociationService';

const companyService = new CompanyService();
const associationService = new UserCompanyAssociationService();

/**
 * GET /api/backbone/oneid/company-hierarchy/tree
 * Get the complete company hierarchy tree
 */
export async function getHierarchyTree(request: NextRequest): Promise<NextResponse> {
  try {
    const hierarchyTree = await companyService.getCompanyHierarchyTree();
    
    return NextResponse.json({
      success: true,
      data: hierarchyTree,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting hierarchy tree:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/stats
 * Get hierarchy statistics
 */
export async function getHierarchyStats(request: NextRequest): Promise<NextResponse> {
  try {
    const stats = await companyService.getHierarchyStats();
    
    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting hierarchy stats:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/company/{companyId}
 * Get hierarchy for a specific company
 */
export async function getCompanyHierarchy(
  request: NextRequest,
  { params }: { params: { companyId: string } }
): Promise<NextResponse> {
  try {
    const { companyId } = params;
    
    if (!companyId) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_COMPANY_ID', message: 'Company ID is required' }
      }, { status: 400 });
    }

    const hierarchy = await companyService.getCompanyHierarchy(companyId);
    
    if (!hierarchy) {
      return NextResponse.json({
        success: false,
        error: { code: 'COMPANY_NOT_FOUND', message: 'Company not found' }
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: hierarchy,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting company hierarchy:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/company/{companyId}/children
 * Get direct children of a company
 */
export async function getCompanyChildren(
  request: NextRequest,
  { params }: { params: { companyId: string } }
): Promise<NextResponse> {
  try {
    const { companyId } = params;
    
    const children = await companyService.getDirectChildren(companyId);
    
    return NextResponse.json({
      success: true,
      data: children,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting company children:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/company/{companyId}/descendants
 * Get all descendants of a company
 */
export async function getCompanyDescendants(
  request: NextRequest,
  { params }: { params: { companyId: string } }
): Promise<NextResponse> {
  try {
    const { companyId } = params;
    
    const descendants = await companyService.getAllDescendants(companyId);
    
    return NextResponse.json({
      success: true,
      data: descendants,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting company descendants:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/company/{companyId}/path
 * Get the path from root to a specific company
 */
export async function getCompanyPath(
  request: NextRequest,
  { params }: { params: { companyId: string } }
): Promise<NextResponse> {
  try {
    const { companyId } = params;
    
    const path = await companyService.getCompanyPath(companyId);
    
    return NextResponse.json({
      success: true,
      data: path,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting company path:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/roots
 * Get all root companies
 */
export async function getRootCompanies(request: NextRequest): Promise<NextResponse> {
  try {
    const rootCompanies = await companyService.getRootCompanies();
    
    return NextResponse.json({
      success: true,
      data: rootCompanies,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting root companies:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * POST /api/backbone/oneid/company-hierarchy/create-child
 * Create a child company
 */
export async function createChildCompany(request: NextRequest): Promise<NextResponse> {
  try {
    const body: CreateChildCompanyRequest = await request.json();
    
    // Validate required fields
    if (!body.parentCompanyId || !body.companyInfo?.name || !body.adminUser) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_REQUIRED_FIELDS', message: 'Missing required fields' }
      }, { status: 400 });
    }

    const result = await companyService.createChildCompany(body);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 400 });
    }

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating child company:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * PUT /api/backbone/oneid/company-hierarchy/move
 * Move a company to a new parent
 */
export async function moveCompany(request: NextRequest): Promise<NextResponse> {
  try {
    const body: MoveCompanyRequest = await request.json();
    
    if (!body.companyId) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_COMPANY_ID', message: 'Company ID is required' }
      }, { status: 400 });
    }

    const result = await companyService.moveCompany(body);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 400 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error moving company:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/user/{userId}/access
 * Get user's company access across hierarchy
 */
export async function getUserCompanyAccess(
  request: NextRequest,
  { params }: { params: { userId: string } }
): Promise<NextResponse> {
  try {
    const { userId } = params;
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_USER_ID', message: 'User ID is required' }
      }, { status: 400 });
    }

    const access = await associationService.getUserCompanyAccess(userId);
    
    if (!access) {
      return NextResponse.json({
        success: false,
        error: { code: 'USER_NOT_FOUND', message: 'User not found' }
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: access,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting user company access:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/company-hierarchy/company/{companyId}/users
 * Get all users associated with a company (including hierarchy)
 */
export async function getCompanyUsers(
  request: NextRequest,
  { params }: { params: { companyId: string } }
): Promise<NextResponse> {
  try {
    const { companyId } = params;
    const { searchParams } = new URL(request.url);
    const includeHierarchy = searchParams.get('includeHierarchy') === 'true';
    
    const users = await associationService.getCompanyUsers(companyId, includeHierarchy);
    
    return NextResponse.json({
      success: true,
      data: users,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting company users:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * POST /api/backbone/oneid/company-hierarchy/associate-user
 * Associate a user with a company
 */
export async function associateUserWithCompany(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { userId, companyId, userType, permissions } = body;
    
    if (!userId || !companyId || !userType) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_REQUIRED_FIELDS', message: 'Missing required fields' }
      }, { status: 400 });
    }

    const result = await associationService.associateUserWithCompany(
      userId, 
      companyId, 
      userType, 
      permissions || []
    );
    
    if (!result.success) {
      return NextResponse.json(result, { status: 400 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error associating user with company:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * DELETE /api/backbone/oneid/company-hierarchy/user/{userId}/association
 * Remove user association from company
 */
export async function removeUserFromCompany(
  request: NextRequest,
  { params }: { params: { userId: string } }
): Promise<NextResponse> {
  try {
    const { userId } = params;
    
    const result = await associationService.removeUserFromCompany(userId);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 400 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error removing user from company:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}
