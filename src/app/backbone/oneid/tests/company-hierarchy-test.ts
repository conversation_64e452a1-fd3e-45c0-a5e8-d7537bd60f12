/**
 * Company Hierarchy Test Script
 * Tests the multi-tenant company hierarchy functionality
 */

import { initializeOneID } from '../index';
import { 
  CreateChildCompanyRequest,
  MoveCompanyRequest,
  Company,
  CompanyHierarchy,
  CompanyHierarchyStats
} from '../core/types';

interface TestResult {
  testName: string;
  success: boolean;
  message: string;
  data?: any;
}

class CompanyHierarchyTester {
  private oneID: any;
  private results: TestResult[] = [];

  async initialize() {
    console.log('🚀 Initializing OneID System for Company Hierarchy Testing...\n');
    this.oneID = await initializeOneID();
  }

  private logTest(testName: string, success: boolean, message: string, data?: any) {
    const result: TestResult = { testName, success, message, data };
    this.results.push(result);
    
    const icon = success ? '✅' : '❌';
    console.log(`${icon} ${testName}: ${message}`);
    if (data && typeof data === 'object') {
      console.log(`   Data:`, JSON.stringify(data, null, 2));
    }
    console.log('');
  }

  async testGetHierarchyStats() {
    try {
      const stats = await this.oneID.company.getHierarchyStats();
      
      this.logTest(
        'Get Hierarchy Statistics',
        true,
        `Found ${stats.totalCompanies} companies with max depth ${stats.maxDepth}`,
        stats
      );
    } catch (error) {
      this.logTest('Get Hierarchy Statistics', false, `Error: ${error}`);
    }
  }

  async testGetRootCompanies() {
    try {
      const rootCompanies = await this.oneID.company.getRootCompanies();
      
      this.logTest(
        'Get Root Companies',
        rootCompanies.length > 0,
        `Found ${rootCompanies.length} root companies`,
        rootCompanies.map((c: Company) => ({ id: c.id, name: c.name }))
      );
    } catch (error) {
      this.logTest('Get Root Companies', false, `Error: ${error}`);
    }
  }

  async testGetCompanyHierarchy() {
    try {
      // Test with TechCorp (has children)
      const hierarchy = await this.oneID.company.getCompanyHierarchy('comp_001_techcorp');
      
      const hasChildren = hierarchy && hierarchy.children && hierarchy.children.length > 0;
      
      this.logTest(
        'Get Company Hierarchy',
        hasChildren,
        `TechCorp hierarchy has ${hierarchy?.children?.length || 0} direct children`,
        {
          companyId: hierarchy?.companyId,
          level: hierarchy?.level,
          childrenCount: hierarchy?.children?.length,
          children: hierarchy?.children?.map((c: any) => ({ id: c.companyId, name: c.company.name }))
        }
      );
    } catch (error) {
      this.logTest('Get Company Hierarchy', false, `Error: ${error}`);
    }
  }

  async testGetDirectChildren() {
    try {
      const children = await this.oneID.company.getDirectChildren('comp_001_techcorp');
      
      this.logTest(
        'Get Direct Children',
        children.length > 0,
        `TechCorp has ${children.length} direct children`,
        children.map((c: Company) => ({ id: c.id, name: c.name, level: c.hierarchyLevel }))
      );
    } catch (error) {
      this.logTest('Get Direct Children', false, `Error: ${error}`);
    }
  }

  async testGetCompanyPath() {
    try {
      // Test with a child company
      const path = await this.oneID.company.getCompanyPath('comp_001_techcorp_ai');
      
      this.logTest(
        'Get Company Path',
        path.length > 1,
        `AI Division path has ${path.length} companies`,
        path.map((c: Company) => ({ id: c.id, name: c.name, level: c.hierarchyLevel }))
      );
    } catch (error) {
      this.logTest('Get Company Path', false, `Error: ${error}`);
    }
  }

  async testGetHierarchyTree() {
    try {
      const tree = await this.oneID.company.getCompanyHierarchyTree();
      
      const totalNodes = this.countTreeNodes(tree);
      
      this.logTest(
        'Get Hierarchy Tree',
        tree.length > 0,
        `Hierarchy tree has ${tree.length} root nodes and ${totalNodes} total nodes`,
        {
          rootCount: tree.length,
          totalNodes,
          roots: tree.map((node: any) => ({
            id: node.id,
            name: node.name,
            childrenCount: node.children.length
          }))
        }
      );
    } catch (error) {
      this.logTest('Get Hierarchy Tree', false, `Error: ${error}`);
    }
  }

  async testCreateChildCompany() {
    try {
      const request: CreateChildCompanyRequest = {
        parentCompanyId: 'comp_004_edulearn',
        companyInfo: {
          name: 'EduLearn Online Academy',
          displayName: 'EduLearn Online',
          description: 'Online learning platform division of EduLearn Academy',
          industry: 'Education',
          email: '<EMAIL>'
        },
        adminUser: {
          username: 'edulearn_online_admin',
          email: '<EMAIL>',
          firstName: 'Online',
          lastName: 'Admin',
          temporaryPassword: true
        },
        inheritSettings: {
          passwordPolicy: true,
          sessionTimeout: true,
          employeeRegistration: false,
          emailVerification: true
        }
      };

      const result = await this.oneID.company.createChildCompany(request);
      
      this.logTest(
        'Create Child Company',
        result.success,
        result.success ? 
          `Successfully created child company: ${result.data?.name}` : 
          `Failed: ${result.error?.message}`,
        result.success ? {
          id: result.data?.id,
          name: result.data?.name,
          parentId: result.data?.parentCompanyId,
          level: result.data?.hierarchyLevel
        } : result.error
      );
    } catch (error) {
      this.logTest('Create Child Company', false, `Error: ${error}`);
    }
  }

  async testMoveCompany() {
    try {
      // First, let's try to move a company to a different parent
      const request: MoveCompanyRequest = {
        companyId: 'comp_002_healthplus_cardio',
        newParentId: 'comp_003_greenfinance', // Move cardio center to green finance (for testing)
        preserveChildren: true
      };

      const result = await this.oneID.company.moveCompany(request);
      
      this.logTest(
        'Move Company',
        result.success,
        result.success ? 
          'Successfully moved company to new parent' : 
          `Failed: ${result.error?.message}`,
        result.success ? { moved: true } : result.error
      );

      // Move it back to original parent
      if (result.success) {
        const moveBackRequest: MoveCompanyRequest = {
          companyId: 'comp_002_healthplus_cardio',
          newParentId: 'comp_002_healthplus',
          preserveChildren: true
        };
        
        await this.oneID.company.moveCompany(moveBackRequest);
        console.log('   ↩️  Moved company back to original parent');
      }
    } catch (error) {
      this.logTest('Move Company', false, `Error: ${error}`);
    }
  }

  async testUserCompanyAccess() {
    try {
      // This would require actual user data, so we'll simulate
      console.log('🔍 Testing User Company Access (simulated)...');
      
      // In a real test, you would:
      // const access = await this.oneID.association.getUserCompanyAccess('user_001_john_admin');
      
      this.logTest(
        'User Company Access',
        true,
        'User company access test simulated (requires actual user data)',
        { note: 'This test requires actual user data to be meaningful' }
      );
    } catch (error) {
      this.logTest('User Company Access', false, `Error: ${error}`);
    }
  }

  async testHierarchyValidation() {
    try {
      // Test circular reference prevention
      const circularRequest: MoveCompanyRequest = {
        companyId: 'comp_001_techcorp',
        newParentId: 'comp_001_techcorp_ai', // Try to make parent a child of its own child
        preserveChildren: true
      };

      const result = await this.oneID.company.moveCompany(circularRequest);
      
      this.logTest(
        'Circular Reference Prevention',
        !result.success,
        result.success ? 
          'ERROR: Circular reference was allowed!' : 
          `Correctly prevented circular reference: ${result.error?.message}`,
        result.error
      );
    } catch (error) {
      this.logTest('Circular Reference Prevention', false, `Error: ${error}`);
    }
  }

  private countTreeNodes(nodes: any[]): number {
    let count = nodes.length;
    for (const node of nodes) {
      if (node.children && node.children.length > 0) {
        count += this.countTreeNodes(node.children);
      }
    }
    return count;
  }

  async runAllTests() {
    console.log('🧪 Starting Company Hierarchy Tests\n');
    console.log('=' .repeat(60));
    
    await this.testGetHierarchyStats();
    await this.testGetRootCompanies();
    await this.testGetCompanyHierarchy();
    await this.testGetDirectChildren();
    await this.testGetCompanyPath();
    await this.testGetHierarchyTree();
    await this.testCreateChildCompany();
    await this.testMoveCompany();
    await this.testUserCompanyAccess();
    await this.testHierarchyValidation();
    
    this.printSummary();
  }

  private printSummary() {
    console.log('=' .repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('=' .repeat(60));
    
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    const total = this.results.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`   - ${result.testName}: ${result.message}`);
      });
    }
    
    console.log('\n🎉 Company Hierarchy Testing Complete!');
  }
}

// Export for use in other test files
export { CompanyHierarchyTester };

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new CompanyHierarchyTester();
  tester.initialize().then(() => {
    return tester.runAllTests();
  }).catch(error => {
    console.error('❌ Test execution failed:', error);
  });
}
