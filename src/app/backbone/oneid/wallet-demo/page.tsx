'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Wallet, 
  DollarSign, 
  ArrowUpCircle, 
  ArrowDownCircle, 
  Send, 
  History,
  CreditCard,
  Shield,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

interface WalletData {
  id: string;
  userId: string;
  balance: number;
  currency: string;
  status: string;
  autoTopUp: {
    enabled: boolean;
    threshold: number;
    amount: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface WalletTransaction {
  id: string;
  type: string;
  amount: number;
  currency: string;
  description: string;
  status: string;
  createdAt: string;
}

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
}

export default function WalletDemoPage() {
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form states
  const [topUpAmount, setTopUpAmount] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [transferAmount, setTransferAmount] = useState('');
  const [transferRecipient, setTransferRecipient] = useState('');
  
  // Demo session token (in real app, this would come from authentication)
  const [sessionToken] = useState('demo-session-token-12345');
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Auto-load wallet data on component mount
    if (sessionToken) {
      loadWalletData();
    }
  }, [sessionToken]);

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  const loadWalletData = async () => {
    setLoading(true);
    clearMessages();
    
    try {
      const response = await fetch('/api/backbone/oneid/wallet', {
        headers: {
          'Authorization': `Bearer ${sessionToken}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setWallet(data.data.wallet);
        setUser(data.data.user);
        setIsAuthenticated(true);
        setSuccess('Wallet data loaded successfully');
        
        // Also load transaction history
        loadTransactionHistory();
      } else {
        setError(data.error || 'Failed to load wallet data');
        setIsAuthenticated(false);
      }
    } catch (error) {
      setError('Network error: Unable to connect to wallet service');
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  const loadTransactionHistory = async () => {
    try {
      const response = await fetch('/api/backbone/oneid/wallet/transactions?limit=10', {
        headers: {
          'Authorization': `Bearer ${sessionToken}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setTransactions(data.data);
      }
    } catch (error) {
      console.error('Failed to load transaction history:', error);
    }
  };

  const handleTopUp = async () => {
    if (!topUpAmount || parseFloat(topUpAmount) <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    setLoading(true);
    clearMessages();

    try {
      const response = await fetch('/api/backbone/oneid/wallet', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${sessionToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: parseFloat(topUpAmount),
          paymentMethod: 'credit_card',
          description: 'Wallet top-up via demo'
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setWallet(data.data.wallet);
        setSuccess(data.message);
        setTopUpAmount('');
        loadTransactionHistory();
      } else {
        setError(data.error || 'Top-up failed');
      }
    } catch (err) {
      setError('Network error: Unable to process top-up');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <ArrowUpCircle className="h-4 w-4 text-green-600" />;
      case 'withdrawal':
        return <ArrowDownCircle className="h-4 w-4 text-red-600" />;
      case 'transfer':
        return <Send className="h-4 w-4 text-blue-600" />;
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      pending: { variant: 'secondary' as const, color: 'bg-yellow-100 text-yellow-800' },
      failed: { variant: 'destructive' as const, color: 'bg-red-100 text-red-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Badge className={config.color}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
          <Wallet className="h-8 w-8 text-blue-600" />
          OneID Wallet Demo
        </h1>
        <p className="text-gray-600 mt-2">
          Demonstration of the OneID-Wallet Integration System
        </p>
      </div>

      {/* Connection Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Connection Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isAuthenticated ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-green-600 font-medium">Connected to OneID Wallet Service</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <span className="text-red-600 font-medium">Not Connected</span>
                </>
              )}
            </div>
            <Button 
              onClick={loadWalletData} 
              disabled={loading}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
          
          {user && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>User:</strong> {user.firstName} {user.lastName} ({user.username})
              </p>
              <p className="text-sm text-gray-600">
                <strong>Session Token:</strong> {sessionToken.substring(0, 20)}...
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Messages */}
      {error && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-6 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      {/* Wallet Overview */}
      {wallet && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(wallet.balance, wallet.currency)}
              </div>
              <p className="text-xs text-muted-foreground">
                {wallet.currency} • {wallet.status}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Auto Top-Up</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {wallet.autoTopUp.enabled ? 'Enabled' : 'Disabled'}
              </div>
              {wallet.autoTopUp.enabled && (
                <p className="text-xs text-muted-foreground">
                  Threshold: {formatCurrency(wallet.autoTopUp.threshold)}
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transactions</CardTitle>
              <History className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{transactions.length}</div>
              <p className="text-xs text-muted-foreground">
                Recent transactions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Wallet ID</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-mono">{wallet.id.substring(0, 8)}...</div>
              <p className="text-xs text-muted-foreground">
                Created {formatDate(wallet.createdAt)}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Wallet Operations */}
      <Tabs defaultValue="topup" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="topup">Top Up</TabsTrigger>
          <TabsTrigger value="withdraw">Withdraw</TabsTrigger>
          <TabsTrigger value="transfer">Transfer</TabsTrigger>
        </TabsList>

        <TabsContent value="topup">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowUpCircle className="h-5 w-5 text-green-600" />
                Add Funds to Wallet
              </CardTitle>
              <CardDescription>
                Add money to your wallet using various payment methods
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="topup-amount">Amount</Label>
                <Input
                  id="topup-amount"
                  type="number"
                  placeholder="Enter amount"
                  value={topUpAmount}
                  onChange={(e) => setTopUpAmount(e.target.value)}
                  min="0"
                  step="0.01"
                />
              </div>
              <Button 
                onClick={handleTopUp} 
                disabled={loading || !isAuthenticated}
                className="w-full"
              >
                {loading ? 'Processing...' : 'Add Funds'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="withdraw">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowDownCircle className="h-5 w-5 text-red-600" />
                Withdraw Funds
              </CardTitle>
              <CardDescription>
                Withdraw money from your wallet to external accounts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="withdraw-amount">Amount</Label>
                <Input
                  id="withdraw-amount"
                  type="number"
                  placeholder="Enter amount"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  min="0"
                  step="0.01"
                />
              </div>
              <Button 
                variant="destructive"
                disabled={loading || !isAuthenticated}
                className="w-full"
              >
                {loading ? 'Processing...' : 'Withdraw Funds'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transfer">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5 text-blue-600" />
                Transfer to Another User
              </CardTitle>
              <CardDescription>
                Send money to another OneID user&apos;s wallet
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="transfer-recipient">Recipient User ID</Label>
                <Input
                  id="transfer-recipient"
                  placeholder="Enter recipient user ID"
                  value={transferRecipient}
                  onChange={(e) => setTransferRecipient(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="transfer-amount">Amount</Label>
                <Input
                  id="transfer-amount"
                  type="number"
                  placeholder="Enter amount"
                  value={transferAmount}
                  onChange={(e) => setTransferAmount(e.target.value)}
                  min="0"
                  step="0.01"
                />
              </div>
              <Button 
                disabled={loading || !isAuthenticated}
                className="w-full"
              >
                {loading ? 'Processing...' : 'Send Transfer'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Transaction History */}
      {transactions.length > 0 && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Recent Transactions
            </CardTitle>
            <CardDescription>
              Your latest wallet transactions and activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(transaction.createdAt)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">
                      {transaction.type === 'withdrawal' ? '-' : '+'}
                      {formatCurrency(transaction.amount, transaction.currency)}
                    </p>
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* API Information */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>API Integration Information</CardTitle>
          <CardDescription>
            Technical details about the OneID-Wallet integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-semibold mb-2">Available Endpoints</h4>
              <div className="space-y-1 text-sm">
                <div><code className="bg-gray-100 px-2 py-1 rounded">GET /api/backbone/oneid/wallet</code></div>
                <div><code className="bg-gray-100 px-2 py-1 rounded">POST /api/backbone/oneid/wallet</code></div>
                <div><code className="bg-gray-100 px-2 py-1 rounded">GET /api/backbone/oneid/wallet/transactions</code></div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Authentication</h4>
              <div className="space-y-1 text-sm">
                <p>• Bearer token in Authorization header</p>
                <p>• Session-based authentication</p>
                <p>• Automatic wallet creation on user registration</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}