# Company Hierarchy Management

The OneID system now supports comprehensive multi-tenant company hierarchies with parent-child relationships, inherited settings, and multi-level user access control.

## 🏢 Features

### ✅ **Implemented Features:**

- **Parent-Child Company Relationships** - Companies can have parent and child companies
- **Hierarchy Management** - Create, move, and manage company hierarchies
- **Settings Inheritance** - Child companies can inherit settings from parent companies
- **Multi-Level User Access** - Users can access companies through hierarchy relationships
- **Circular Reference Prevention** - System prevents invalid hierarchy structures
- **Hierarchy Statistics** - Get insights into company structure and depth

## 📊 Data Model

### Company Hierarchy Fields

Each company now includes hierarchy-specific fields:

```typescript
interface Company {
  // ... existing fields ...
  
  // Company Hierarchy Support
  parentCompanyId?: string;     // Reference to parent company
  childCompanyIds: string[];    // Array of child company IDs
  hierarchyLevel: number;       // 0 for root, 1 for first level children, etc.
  hierarchyPath: string[];      // Array of company IDs from root to current
  
  settings: {
    // ... existing settings ...
    
    // Hierarchy-specific settings
    inheritFromParent: {
      passwordPolicy: boolean;
      sessionTimeout: boolean;
      employeeRegistration: boolean;
      emailVerification: boolean;
    };
    allowChildCompanies: boolean;
    maxChildCompanies?: number;
    childCompanyPermissions: {
      canCreateSubsidiaries: boolean;
      canManageEmployees: boolean;
      canViewParentData: boolean;
      canInheritSettings: boolean;
    };
  };
}
```

## 🚀 Usage Examples

### Initialize the System

```typescript
import { initializeOneID } from '@/app/backbone/oneid';

const oneID = await initializeOneID();
```

### Create a Child Company

```typescript
const request: CreateChildCompanyRequest = {
  parentCompanyId: 'comp_001_techcorp',
  companyInfo: {
    name: 'TechCorp AI Division',
    displayName: 'TechCorp AI',
    description: 'AI and ML solutions division',
    industry: 'Technology',
    email: '<EMAIL>'
  },
  adminUser: {
    username: 'ai_admin',
    email: '<EMAIL>',
    firstName: 'AI',
    lastName: 'Admin'
  },
  inheritSettings: {
    passwordPolicy: true,
    sessionTimeout: true,
    employeeRegistration: false,
    emailVerification: true
  }
};

const result = await oneID.company.createChildCompany(request);
```

### Get Company Hierarchy

```typescript
// Get complete hierarchy for a company
const hierarchy = await oneID.company.getCompanyHierarchy('comp_001_techcorp');

// Get direct children only
const children = await oneID.company.getDirectChildren('comp_001_techcorp');

// Get all descendants (children, grandchildren, etc.)
const descendants = await oneID.company.getAllDescendants('comp_001_techcorp');

// Get path from root to company
const path = await oneID.company.getCompanyPath('comp_001_techcorp_ai');
```

### Move Company in Hierarchy

```typescript
const moveRequest: MoveCompanyRequest = {
  companyId: 'comp_subsidiary',
  newParentId: 'comp_new_parent', // or null to make it a root company
  preserveChildren: true
};

const result = await oneID.company.moveCompany(moveRequest);
```

### Get User Company Access

```typescript
// Get all companies a user has access to
const access = await oneID.association.getUserCompanyAccess('user_id');

console.log('Direct companies:', access.directCompanies);
console.log('Parent companies:', access.parentCompanies);
console.log('Child companies:', access.childCompanies);
console.log('All accessible:', access.allAccessibleCompanies);
```

### Get Hierarchy Statistics

```typescript
const stats = await oneID.company.getHierarchyStats();

console.log(`Total companies: ${stats.totalCompanies}`);
console.log(`Maximum depth: ${stats.maxDepth}`);
console.log(`Root companies: ${stats.rootCompanies}`);
console.log('Companies by level:', stats.companiesByLevel);
```

## 🔗 API Endpoints

### Company Hierarchy Management

- `GET /api/backbone/oneid/company-hierarchy/tree` - Get complete hierarchy tree
- `GET /api/backbone/oneid/company-hierarchy/stats` - Get hierarchy statistics
- `GET /api/backbone/oneid/company-hierarchy/roots` - Get all root companies
- `POST /api/backbone/oneid/company-hierarchy/create-child` - Create child company
- `PUT /api/backbone/oneid/company-hierarchy/move` - Move company in hierarchy

### Company-Specific Endpoints

- `GET /api/backbone/oneid/company-hierarchy/company/{id}` - Get company hierarchy
- `GET /api/backbone/oneid/company-hierarchy/company/{id}/children` - Get direct children
- `GET /api/backbone/oneid/company-hierarchy/company/{id}/descendants` - Get all descendants
- `GET /api/backbone/oneid/company-hierarchy/company/{id}/path` - Get company path
- `GET /api/backbone/oneid/company-hierarchy/company/{id}/users` - Get company users

### User Association Endpoints

- `GET /api/backbone/oneid/company-hierarchy/user/{id}/access` - Get user company access
- `POST /api/backbone/oneid/company-hierarchy/associate-user` - Associate user with company
- `DELETE /api/backbone/oneid/company-hierarchy/user/{id}/association` - Remove user association

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run hierarchy tests
npm run test:hierarchy

# Or run the test script directly
node src/app/backbone/oneid/tests/company-hierarchy-test.ts
```

The test suite covers:
- Hierarchy statistics and tree structure
- Company creation and movement
- User access permissions
- Circular reference prevention
- Settings inheritance

## 📋 Example Hierarchy Structure

```
TechCorp Solutions (Root)
├── TechCorp AI Division (Level 1)
└── TechCorp Cloud Services (Level 1)

HealthPlus Medical Group (Root)
└── HealthPlus Cardiology Center (Level 1)

RetailMax Corporation (Root)
├── RetailMax E-commerce (Level 1)
└── RetailMax Physical Stores (Level 1)

Green Finance Solutions (Root)
EduLearn Academy (Root)
```

## 🔒 Security & Permissions

### Access Control

- **Direct Access**: Users have full access to their assigned company
- **Parent Access**: Company admins can view/manage child companies (if permitted)
- **Child Access**: Child company admins can view parent data (if permitted)
- **Inherited Permissions**: Permissions flow down the hierarchy based on settings

### Settings Inheritance

Child companies can inherit settings from their parent:
- Password policies
- Session timeouts
- Employee registration rules
- Email verification requirements

### Circular Reference Prevention

The system automatically prevents:
- Companies becoming their own parent
- Creating circular dependency chains
- Invalid hierarchy structures

## 🚨 Important Notes

1. **Data Migration**: Existing companies are automatically migrated to support hierarchy
2. **Backward Compatibility**: All existing functionality remains unchanged
3. **Performance**: Hierarchy operations are optimized for large company structures
4. **Validation**: All hierarchy changes are validated for integrity
5. **Audit Trail**: All hierarchy changes are logged for compliance

## 📚 Related Documentation

- [OneID Authentication Guide](./AUTHENTICATION_GUIDE.md)
- [Tenant Management](./TENANT_MANAGEMENT.md)
- [Implementation Summary](./IMPLEMENTATION_SUMMARY.md)
- [API Documentation](./API_DOCUMENTATION.md)
