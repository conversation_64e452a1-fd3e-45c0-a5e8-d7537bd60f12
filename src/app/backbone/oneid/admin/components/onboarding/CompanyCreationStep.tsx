'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  Globe,
  Mail,
  Phone,
  MapPin,
  Upload,
  Activity
} from 'lucide-react';

interface CompanyCreationStepProps {
  data: any;
  onChange: (data: any) => void;
}

export default function CompanyCreationStep({ data, onChange }: CompanyCreationStepProps) {
  const [availableParents, setAvailableParents] = useState([]);
  const [selectedParent, setSelectedParent] = useState(data.parentCompanyId || '');

  useEffect(() => {
    // Fetch available parent companies
    fetchParentCompanies();
  }, []);

  const fetchParentCompanies = async () => {
    try {
      const response = await fetch('/api/backbone/oneid/company-hierarchy/roots');
      if (response.ok) {
        const result = await response.json();
        setAvailableParents(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching parent companies:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      onChange({
        ...data,
        [parent]: {
          ...data[parent],
          [child]: value
        }
      });
    } else {
      onChange({
        ...data,
        [field]: value
      });
    }
  };

  const industries = [
    'Technology',
    'Healthcare',
    'Finance',
    'Retail',
    'Education',
    'Manufacturing',
    'Real Estate',
    'Consulting',
    'Media',
    'Transportation',
    'Energy',
    'Agriculture',
    'Other'
  ];

  const countries = [
    'United States',
    'Canada',
    'United Kingdom',
    'Germany',
    'France',
    'Australia',
    'Japan',
    'Singapore',
    'Other'
  ];

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>Basic Information</span>
          </CardTitle>
          <CardDescription>
            Enter the basic details for your new company
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Company Name *</Label>
              <Input
                id="name"
                value={data.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter company name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                value={data.displayName}
                onChange={(e) => handleInputChange('displayName', e.target.value)}
                placeholder="Friendly display name"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={data.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Brief description of the company"
              rows={3}
            />
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="industry">Industry *</Label>
              <Select value={data.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent>
                  {industries.map((industry) => (
                    <SelectItem key={industry} value={industry.toLowerCase()}>
                      {industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <div className="relative">
                <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="website"
                  value={data.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://company.com"
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>Contact Information</span>
          </CardTitle>
          <CardDescription>
            Primary contact details for the company
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  value={data.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  value={data.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+****************"
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Address Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5" />
            <span>Address Information</span>
          </CardTitle>
          <CardDescription>
            Physical address of the company
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="street">Street Address</Label>
            <Input
              id="street"
              value={data.address?.street || ''}
              onChange={(e) => handleInputChange('address.street', e.target.value)}
              placeholder="123 Business Street"
            />
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={data.address?.city || ''}
                onChange={(e) => handleInputChange('address.city', e.target.value)}
                placeholder="City"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="state">State/Province</Label>
              <Input
                id="state"
                value={data.address?.state || ''}
                onChange={(e) => handleInputChange('address.state', e.target.value)}
                placeholder="State"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code</Label>
              <Input
                id="postalCode"
                value={data.address?.postalCode || ''}
                onChange={(e) => handleInputChange('address.postalCode', e.target.value)}
                placeholder="12345"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Select 
                value={data.address?.country || ''} 
                onValueChange={(value) => handleInputChange('address.country', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country} value={country}>
                      {country}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hierarchy Placement */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Hierarchy Placement</span>
          </CardTitle>
          <CardDescription>
            Choose where this company fits in your organization hierarchy
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="parentCompany">Parent Company</Label>
            <Select 
              value={selectedParent} 
              onValueChange={(value) => {
                setSelectedParent(value);
                handleInputChange('parentCompanyId', value === 'root' ? null : value);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select parent company or create as root" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="root">
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4" />
                    <span>Create as Root Company</span>
                  </div>
                </SelectItem>
                {availableParents.map((parent: any) => (
                  <SelectItem key={parent.id} value={parent.id}>
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4" />
                      <span>{parent.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {parent.childCompanyIds?.length || 0} children
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500">
              {selectedParent === 'root' || !selectedParent
                ? 'This will be an independent root company'
                : 'This company will be a subsidiary of the selected parent company'
              }
            </p>
          </div>

          {selectedParent && selectedParent !== 'root' && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Hierarchy Information</h4>
              <p className="text-sm text-blue-800">
                This company will inherit certain settings from its parent company and 
                will be managed within the parent company's organizational structure.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Company Logo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>Company Logo</span>
          </CardTitle>
          <CardDescription>
            Upload a logo for your company (optional)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600 mb-2">
              Drag and drop your logo here, or click to browse
            </p>
            <Button variant="outline" size="sm" disabled>
              Choose File
            </Button>
            <p className="text-xs text-gray-500 mt-2">
              Supported formats: PNG, JPG, SVG (max 2MB)
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
