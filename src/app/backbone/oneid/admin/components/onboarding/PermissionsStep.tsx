'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Shield, Users, Settings, Building2, Lock, Eye } from 'lucide-react';

interface PermissionsStepProps {
  data: any;
  parentCompany?: string;
  onChange: (data: any) => void;
}

export default function PermissionsStep({ data, parentCompany, onChange }: PermissionsStepProps) {
  const handleInheritanceChange = (field: string, value: boolean) => {
    onChange({
      ...data,
      inheritFromParent: {
        ...data.inheritFromParent,
        [field]: value
      }
    });
  };

  const handlePermissionChange = (field: string, value: boolean | number) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      onChange({
        ...data,
        [parent]: {
          ...data[parent],
          [child]: value
        }
      });
    } else {
      onChange({
        ...data,
        [field]: value
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Settings Inheritance */}
      {parentCompany && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="h-5 w-5" />
              <span>Settings Inheritance</span>
            </CardTitle>
            <CardDescription>
              Choose which settings to inherit from the parent company
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="inheritPasswordPolicy"
                  checked={data.inheritFromParent.passwordPolicy}
                  onCheckedChange={(checked) => handleInheritanceChange('passwordPolicy', checked)}
                />
                <Label htmlFor="inheritPasswordPolicy">Password Policy</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="inheritSessionTimeout"
                  checked={data.inheritFromParent.sessionTimeout}
                  onCheckedChange={(checked) => handleInheritanceChange('sessionTimeout', checked)}
                />
                <Label htmlFor="inheritSessionTimeout">Session Timeout</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="inheritEmployeeRegistration"
                  checked={data.inheritFromParent.employeeRegistration}
                  onCheckedChange={(checked) => handleInheritanceChange('employeeRegistration', checked)}
                />
                <Label htmlFor="inheritEmployeeRegistration">Employee Registration</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="inheritEmailVerification"
                  checked={data.inheritFromParent.emailVerification}
                  onCheckedChange={(checked) => handleInheritanceChange('emailVerification', checked)}
                />
                <Label htmlFor="inheritEmailVerification">Email Verification</Label>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Company Hierarchy Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Hierarchy Permissions</span>
          </CardTitle>
          <CardDescription>
            Configure permissions for managing child companies and hierarchy
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="allowChildCompanies"
              checked={data.allowChildCompanies}
              onCheckedChange={(checked) => handlePermissionChange('allowChildCompanies', checked)}
            />
            <Label htmlFor="allowChildCompanies">Allow Child Companies</Label>
          </div>

          {data.allowChildCompanies && (
            <div className="ml-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="maxChildCompanies">Maximum Child Companies</Label>
                <Input
                  id="maxChildCompanies"
                  type="number"
                  value={data.maxChildCompanies}
                  onChange={(e) => handlePermissionChange('maxChildCompanies', parseInt(e.target.value) || 0)}
                  min="0"
                  max="100"
                  className="w-32"
                />
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Child Company Permissions</h4>
                <div className="grid gap-3 md:grid-cols-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canCreateSubsidiaries"
                      checked={data.childCompanyPermissions.canCreateSubsidiaries}
                      onCheckedChange={(checked) => handlePermissionChange('childCompanyPermissions.canCreateSubsidiaries', checked)}
                    />
                    <Label htmlFor="canCreateSubsidiaries">Can Create Subsidiaries</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canManageEmployees"
                      checked={data.childCompanyPermissions.canManageEmployees}
                      onCheckedChange={(checked) => handlePermissionChange('childCompanyPermissions.canManageEmployees', checked)}
                    />
                    <Label htmlFor="canManageEmployees">Can Manage Employees</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewParentData"
                      checked={data.childCompanyPermissions.canViewParentData}
                      onCheckedChange={(checked) => handlePermissionChange('childCompanyPermissions.canViewParentData', checked)}
                    />
                    <Label htmlFor="canViewParentData">Can View Parent Data</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canInheritSettings"
                      checked={data.childCompanyPermissions.canInheritSettings}
                      onCheckedChange={(checked) => handlePermissionChange('childCompanyPermissions.canInheritSettings', checked)}
                    />
                    <Label htmlFor="canInheritSettings">Can Inherit Settings</Label>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Access Control Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>Access Control</span>
          </CardTitle>
          <CardDescription>
            Configure user access and security settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="allowEmployeeRegistration"
                checked={data.customSettings.allowEmployeeRegistration ?? true}
                onCheckedChange={(checked) => handlePermissionChange('customSettings.allowEmployeeRegistration', checked)}
                disabled={data.inheritFromParent.employeeRegistration}
              />
              <Label htmlFor="allowEmployeeRegistration">
                Allow Employee Registration
                {data.inheritFromParent.employeeRegistration && (
                  <Badge variant="outline" className="ml-2">Inherited</Badge>
                )}
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="requireEmailVerification"
                checked={data.customSettings.requireEmailVerification ?? true}
                onCheckedChange={(checked) => handlePermissionChange('customSettings.requireEmailVerification', checked)}
                disabled={data.inheritFromParent.emailVerification}
              />
              <Label htmlFor="requireEmailVerification">
                Require Email Verification
                {data.inheritFromParent.emailVerification && (
                  <Badge variant="outline" className="ml-2">Inherited</Badge>
                )}
              </Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">
                Session Timeout (minutes)
                {data.inheritFromParent.sessionTimeout && (
                  <Badge variant="outline" className="ml-2">Inherited</Badge>
                )}
              </Label>
              <Input
                id="sessionTimeout"
                type="number"
                value={data.customSettings.sessionTimeout ?? 480}
                onChange={(e) => handlePermissionChange('customSettings.sessionTimeout', parseInt(e.target.value) || 480)}
                min="15"
                max="1440"
                className="w-32"
                disabled={data.inheritFromParent.sessionTimeout}
              />
              <p className="text-sm text-gray-500">
                How long users can stay logged in without activity
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resource Limits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Resource Limits</span>
          </CardTitle>
          <CardDescription>
            Set limits on company resource usage
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="maxUsers">Maximum Users</Label>
              <Input
                id="maxUsers"
                type="number"
                defaultValue="100"
                min="1"
                max="10000"
                className="w-32"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="storageLimit">Storage Limit (GB)</Label>
              <Input
                id="storageLimit"
                type="number"
                defaultValue="10"
                min="1"
                max="1000"
                className="w-32"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="apiCallLimit">API Calls per Month</Label>
              <Input
                id="apiCallLimit"
                type="number"
                defaultValue="100000"
                min="1000"
                max="10000000"
                className="w-40"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bandwidthLimit">Bandwidth Limit (GB/month)</Label>
              <Input
                id="bandwidthLimit"
                type="number"
                defaultValue="100"
                min="1"
                max="10000"
                className="w-40"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permission Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>Permission Summary</span>
          </CardTitle>
          <CardDescription>
            Overview of the permissions and settings configured for this company
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Hierarchy Permissions</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center space-x-2">
                  <Badge variant={data.allowChildCompanies ? "default" : "secondary"}>
                    {data.allowChildCompanies ? "Enabled" : "Disabled"}
                  </Badge>
                  <span>Child Companies</span>
                </div>
                {data.allowChildCompanies && (
                  <p className="text-gray-600 ml-2">
                    Max: {data.maxChildCompanies} companies
                  </p>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Access Control</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center space-x-2">
                  <Badge variant={data.customSettings.allowEmployeeRegistration !== false ? "default" : "secondary"}>
                    {data.customSettings.allowEmployeeRegistration !== false ? "Enabled" : "Disabled"}
                  </Badge>
                  <span>Employee Registration</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={data.customSettings.requireEmailVerification !== false ? "default" : "secondary"}>
                    {data.customSettings.requireEmailVerification !== false ? "Required" : "Optional"}
                  </Badge>
                  <span>Email Verification</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
