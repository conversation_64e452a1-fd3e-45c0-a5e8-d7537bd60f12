'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  User,
  Mail,
  Phone,
  Shield,
  Key,
  Send
} from 'lucide-react';

interface AdminUserStepProps {
  data: any;
  onChange: (data: any) => void;
}

export default function AdminUserStep({ data, onChange }: AdminUserStepProps) {
  const handleInputChange = (field: string, value: string | boolean) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  return (
    <div className="space-y-6">
      {/* Admin User Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Administrator Account</span>
          </CardTitle>
          <CardDescription>
            Create the primary administrator account for this company
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                value={data.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                placeholder="John"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                value={data.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                placeholder="Smith"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="username">Username *</Label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="username"
                value={data.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder="admin_username"
                className="pl-10"
              />
            </div>
            <p className="text-sm text-gray-500">
              This will be used for login. Must be unique across the system.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address *</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                value={data.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className="pl-10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="phone"
                value={data.phone || ''}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+****************"
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Password Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5" />
            <span>Password Settings</span>
          </CardTitle>
          <CardDescription>
            Configure how the administrator will access their account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="temporaryPassword"
              checked={data.temporaryPassword}
              onCheckedChange={(checked) => handleInputChange('temporaryPassword', checked)}
            />
            <Label htmlFor="temporaryPassword" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Generate temporary password
            </Label>
          </div>
          <p className="text-sm text-gray-500 ml-6">
            {data.temporaryPassword 
              ? 'A secure temporary password will be generated and sent to the admin user. They will be required to change it on first login.'
              : 'The admin user will need to set their password during account activation.'
            }
          </p>

          {data.temporaryPassword && (
            <div className="ml-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Shield className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-900">Security Information</span>
              </div>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Password will meet company security requirements</li>
                <li>• User must change password on first login</li>
                <li>• Password expires in 24 hours if not used</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Send className="h-5 w-5" />
            <span>Notification Settings</span>
          </CardTitle>
          <CardDescription>
            Configure how the administrator will be notified about their account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sendWelcomeEmail"
              checked={data.sendWelcomeEmail}
              onCheckedChange={(checked) => handleInputChange('sendWelcomeEmail', checked)}
            />
            <Label htmlFor="sendWelcomeEmail" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Send welcome email
            </Label>
          </div>
          <p className="text-sm text-gray-500 ml-6">
            Send an email with account details and getting started information.
          </p>

          {data.sendWelcomeEmail && (
            <div className="ml-6 space-y-3">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Welcome Email Will Include:</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• Account login credentials</li>
                  <li>• Company portal access link</li>
                  <li>• Getting started guide</li>
                  <li>• Support contact information</li>
                  {data.temporaryPassword && <li>• Temporary password (secure)</li>}
                </ul>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Admin Privileges */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Administrator Privileges</span>
          </CardTitle>
          <CardDescription>
            This user will have the following administrative privileges
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            <div className="flex items-center space-x-2">
              <Badge variant="default" className="bg-blue-100 text-blue-800">
                <Shield className="h-3 w-3 mr-1" />
                Company Admin
              </Badge>
              <span className="text-sm">Full company management access</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="default" className="bg-green-100 text-green-800">
                <User className="h-3 w-3 mr-1" />
                User Management
              </Badge>
              <span className="text-sm">Create and manage employees</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="default" className="bg-purple-100 text-purple-800">
                <Key className="h-3 w-3 mr-1" />
                Access Control
              </Badge>
              <span className="text-sm">Manage permissions and roles</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="default" className="bg-orange-100 text-orange-800">
                <Send className="h-3 w-3 mr-1" />
                Service Access
              </Badge>
              <span className="text-sm">Configure company services</span>
            </div>
          </div>

          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-900 mb-2">Important Notes:</h4>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• This user will have full administrative control over the company</li>
              <li>• Additional administrators can be added later</li>
              <li>• Admin privileges can be modified after account creation</li>
              <li>• All administrative actions are logged for security</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
