'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  Users,
  Activity,
  TrendingUp,
  DollarSign,
  Zap,
  Database,
  Shield,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

interface StatisticsOverviewProps {
  stats: any;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

export default function StatisticsOverview({ stats }: StatisticsOverviewProps) {
  if (!stats) return null;

  // Mock data for charts
  const companyGrowthData = [
    { month: 'Jan', companies: 5, users: 25 },
    { month: 'Feb', companies: 6, users: 28 },
    { month: 'Mar', companies: 7, users: 32 },
    { month: 'Apr', companies: 8, users: 38 },
    { month: 'May', companies: 9, users: 42 },
    { month: 'Jun', companies: 10, users: 45 },
  ];

  const userTypeData = [
    { name: 'Individual', value: stats.users.byType.individual, color: '#3B82F6' },
    { name: 'Employee', value: stats.users.byType.employee, color: '#10B981' },
    { name: 'Company Admin', value: stats.users.byType.company_admin, color: '#F59E0B' },
  ];

  const revenueData = [
    { month: 'Jan', revenue: 85000, subscriptions: 6 },
    { month: 'Feb', revenue: 92000, subscriptions: 7 },
    { month: 'Mar', revenue: 98000, subscriptions: 7 },
    { month: 'Apr', revenue: 105000, subscriptions: 8 },
    { month: 'May', revenue: 118000, subscriptions: 8 },
    { month: 'Jun', revenue: 125000, subscriptions: 8 },
  ];

  const usageData = [
    { day: 'Mon', apiCalls: 180000, storage: 2.1 },
    { day: 'Tue', apiCalls: 195000, storage: 2.2 },
    { day: 'Wed', apiCalls: 210000, storage: 2.3 },
    { day: 'Thu', apiCalls: 225000, storage: 2.3 },
    { day: 'Fri', apiCalls: 240000, storage: 2.4 },
    { day: 'Sat', apiCalls: 165000, storage: 2.4 },
    { day: 'Sun', apiCalls: 145000, storage: 2.4 },
  ];

  return (
    <div className="grid gap-6">
      {/* Top Row - Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Company Hierarchy</CardTitle>
            <Activity className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.companies.rootCompanies}</div>
            <p className="text-xs text-muted-foreground">
              Root companies, max depth: {stats.companies.maxHierarchyDepth}
            </p>
            <div className="mt-2">
              <Badge variant="secondary">
                {stats.companies.recentlyCreated} new this month
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">User Activity</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.users.activeToday}</div>
            <p className="text-xs text-muted-foreground">
              Active today out of {stats.users.total} total
            </p>
            <div className="flex items-center mt-2 text-xs text-green-600">
              <ArrowUpRight className="h-3 w-3 mr-1" />
              {stats.growth.usersGrowth}% growth
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Service Adoption</CardTitle>
            <Zap className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.services.averageUsage}%</div>
            <p className="text-xs text-muted-foreground">
              Average service utilization
            </p>
            <div className="mt-2">
              <Badge variant="outline">
                {stats.services.totalServices} services available
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Shield className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">99.8%</div>
            <p className="text-xs text-muted-foreground">
              Uptime this month
            </p>
            <div className="mt-2">
              <Badge variant="outline" className="text-green-600">
                All systems operational
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Growth Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Growth Trends</CardTitle>
            <CardDescription>
              Company and user growth over the last 6 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={companyGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey="companies"
                  stackId="1"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="users"
                  stackId="2"
                  stroke="#10B981"
                  fill="#10B981"
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* User Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>User Distribution</CardTitle>
            <CardDescription>
              Breakdown of users by type across all companies
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={userTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {userTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Row - Revenue and Usage */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Revenue Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trends</CardTitle>
            <CardDescription>
              Monthly revenue and subscription growth
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'revenue' ? `$${value.toLocaleString()}` : value,
                    name === 'revenue' ? 'Revenue' : 'Subscriptions'
                  ]}
                />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={{ fill: '#10B981' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Usage Patterns */}
        <Card>
          <CardHeader>
            <CardTitle>Usage Patterns</CardTitle>
            <CardDescription>
              Daily API calls and storage usage this week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={usageData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'apiCalls' ? `${(value / 1000).toFixed(0)}K` : `${value}GB`,
                    name === 'apiCalls' ? 'API Calls' : 'Storage'
                  ]}
                />
                <Bar dataKey="apiCalls" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
