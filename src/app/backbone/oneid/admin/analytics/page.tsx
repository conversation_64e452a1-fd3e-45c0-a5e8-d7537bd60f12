'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  Database,
  PieChart,
  Download,
  RefreshCw,
  Calendar,
  Filter,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Users,
  Building2,
  Activity
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';

interface AnalyticsData {
  overview: any;
  consumption: any;
  growth: any;
  reports: any;
  alerts: any;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

export default function AnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, this would fetch from the API
      // For now, we'll use the JSON data we created
      const mockData: AnalyticsData = {
        overview: {
          totalCompanies: 10,
          totalUsers: 45,
          totalRevenue: 125000,
          totalApiCalls: 1250000,
          growthRate: 18.5,
          activeRate: 93.3
        },
        consumption: {
          daily: [
            { date: '2024-03-08', apiCalls: 180000, storage: 2.1, bandwidth: 22.5, connections: 320 },
            { date: '2024-03-09', apiCalls: 195000, storage: 2.2, bandwidth: 24.8, connections: 342 },
            { date: '2024-03-10', apiCalls: 210000, storage: 2.3, bandwidth: 26.2, connections: 358 },
            { date: '2024-03-11', apiCalls: 225000, storage: 2.3, bandwidth: 28.1, connections: 375 },
            { date: '2024-03-12', apiCalls: 240000, storage: 2.4, bandwidth: 30.5, connections: 392 },
            { date: '2024-03-13', apiCalls: 165000, storage: 2.4, bandwidth: 20.8, connections: 285 },
            { date: '2024-03-14', apiCalls: 145000, storage: 2.4, bandwidth: 18.3, connections: 258 }
          ],
          byCompany: [
            { name: 'RetailMax Corporation', apiCalls: 450000, storage: 0.8, bandwidth: 65.2, cost: 1250, percentage: 36 },
            { name: 'TechCorp Solutions', apiCalls: 380000, storage: 0.6, bandwidth: 48.5, cost: 980, percentage: 30.4 },
            { name: 'HealthPlus Medical', apiCalls: 220000, storage: 0.4, bandwidth: 28.3, cost: 650, percentage: 17.6 },
            { name: 'Green Finance', apiCalls: 125000, storage: 0.3, bandwidth: 18.7, cost: 420, percentage: 10 },
            { name: 'EduLearn Academy', apiCalls: 75000, storage: 0.3, bandwidth: 12.0, cost: 280, percentage: 6 }
          ]
        },
        growth: {
          companies: [
            { month: '2024-01', total: 5, new: 1, growth: 25 },
            { month: '2024-02', total: 6, new: 1, growth: 20 },
            { month: '2024-03', total: 10, new: 4, growth: 66.7 }
          ],
          users: [
            { month: '2024-01', total: 25, new: 8, growth: 47.1 },
            { month: '2024-02', total: 28, new: 3, growth: 12 },
            { month: '2024-03', total: 45, new: 17, growth: 60.7 }
          ],
          revenue: [
            { month: '2024-01', total: 85000, new: 15000, growth: 21.4 },
            { month: '2024-02', total: 92000, new: 7000, growth: 8.2 },
            { month: '2024-03', total: 125000, new: 33000, growth: 35.9 }
          ]
        },
        reports: {
          available: [
            { id: 'company-performance', name: 'Company Performance Report', lastGenerated: '2024-03-14T09:00:00.000Z', format: 'PDF' },
            { id: 'user-activity', name: 'User Activity Report', lastGenerated: '2024-03-14T09:00:00.000Z', format: 'Excel' },
            { id: 'resource-consumption', name: 'Resource Consumption Report', lastGenerated: '2024-03-14T09:00:00.000Z', format: 'PDF' },
            { id: 'financial-summary', name: 'Financial Summary Report', lastGenerated: '2024-03-14T09:00:00.000Z', format: 'Excel' }
          ]
        },
        alerts: {
          active: [
            { id: 'high-api-usage', type: 'warning', message: 'API usage approaching 80% of monthly limit', company: 'RetailMax Corp', timestamp: '2024-03-14T14:30:00.000Z' },
            { id: 'service-degraded', type: 'error', message: 'Analytics Suite experiencing performance issues', service: 'analytics-suite', timestamp: '2024-03-14T12:15:00.000Z' }
          ]
        }
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
    return num.toString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">
            Comprehensive analytics and insights for your organization
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalyticsData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Companies</CardTitle>
            <Building2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData?.overview.totalCompanies}</div>
            <p className="text-xs text-muted-foreground">
              {analyticsData?.overview.activeRate}% active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData?.overview.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              +{analyticsData?.overview.growthRate}% growth
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${analyticsData?.overview.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Monthly recurring revenue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Calls</CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(analyticsData?.overview.totalApiCalls)}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="consumption">Consumption</TabsTrigger>
          <TabsTrigger value="growth">Growth</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Daily API Calls */}
            <Card>
              <CardHeader>
                <CardTitle>Daily API Calls</CardTitle>
                <CardDescription>API usage over the last week</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={analyticsData?.consumption.daily}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                    <YAxis tickFormatter={formatNumber} />
                    <Tooltip formatter={(value) => [formatNumber(value), 'API Calls']} />
                    <Area
                      type="monotone"
                      dataKey="apiCalls"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.6}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Company Usage Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Usage by Company</CardTitle>
                <CardDescription>Resource consumption distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={analyticsData?.consumption.byCompany}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="percentage"
                    >
                      {analyticsData?.consumption.byCompany.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Active Alerts */}
          {analyticsData?.alerts.active.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  <span>Active Alerts</span>
                </CardTitle>
                <CardDescription>Issues requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.alerts.active.map((alert: any) => (
                    <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          alert.type === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                        }`} />
                        <div>
                          <p className="font-medium">{alert.message}</p>
                          <p className="text-sm text-gray-500">
                            {alert.company || alert.service} • {new Date(alert.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Acknowledge
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="consumption" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Resource Consumption by Company</CardTitle>
              <CardDescription>Detailed breakdown of resource usage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData?.consumption.byCompany.map((company: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                        <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-semibold">{company.name}</h4>
                        <p className="text-sm text-gray-600">
                          {formatNumber(company.apiCalls)} API calls • {company.storage}GB storage
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">${company.cost}</div>
                      <div className="text-sm text-gray-500">{company.percentage}% of total</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="growth" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Company Growth</CardTitle>
                <CardDescription>New companies over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={analyticsData?.growth.companies}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="new" fill="#3B82F6" name="New Companies" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>New user registrations</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={analyticsData?.growth.users}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="new" stroke="#10B981" strokeWidth={2} name="New Users" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Reports</CardTitle>
              <CardDescription>Generate and download detailed reports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {analyticsData?.reports.available.map((report: any) => (
                  <div key={report.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">{report.name}</h4>
                      <Badge variant="outline">{report.format}</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{report.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        Last generated: {new Date(report.lastGenerated).toLocaleDateString()}
                      </span>
                      <Button size="sm">
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
