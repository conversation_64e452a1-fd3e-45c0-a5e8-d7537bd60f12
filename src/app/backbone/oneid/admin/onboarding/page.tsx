'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Building2,
  User,
  Shield,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Zap,
  Users,
  Settings
} from 'lucide-react';
import OnboardingWizard from '../components/onboarding/OnboardingWizard';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  completed: boolean;
  current: boolean;
}

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(0);
  const [showWizard, setShowWizard] = useState(false);

  const steps: OnboardingStep[] = [
    {
      id: 'company',
      title: 'Company Information',
      description: 'Basic company details and hierarchy placement',
      icon: Building2,
      completed: false,
      current: currentStep === 0
    },
    {
      id: 'admin',
      title: 'Admin User Setup',
      description: 'Create the primary administrator account',
      icon: User,
      completed: false,
      current: currentStep === 1
    },
    {
      id: 'permissions',
      title: 'Access & Permissions',
      description: 'Configure access rights and limitations',
      icon: Shield,
      completed: false,
      current: currentStep === 2
    },
    {
      id: 'services',
      title: 'Service Selection',
      description: 'Choose available services and features',
      icon: Zap,
      completed: false,
      current: currentStep === 3
    },
    {
      id: 'review',
      title: 'Review & Activate',
      description: 'Final review and company activation',
      icon: CheckCircle,
      completed: false,
      current: currentStep === 4
    }
  ];

  const progressPercentage = (currentStep / (steps.length - 1)) * 100;

  if (showWizard) {
    return (
      <OnboardingWizard 
        onComplete={() => setShowWizard(false)}
        onCancel={() => setShowWizard(false)}
      />
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">Company Onboarding</h1>
        <p className="text-lg text-gray-600">
          Welcome to the company onboarding process. We'll guide you through setting up 
          a new company in just a few simple steps.
        </p>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>Onboarding Progress</span>
          </CardTitle>
          <CardDescription>
            Complete all steps to activate your new company
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progressPercentage)}% Complete</span>
            </div>
            <Progress value={progressPercentage} className="w-full" />
          </div>

          {/* Steps List */}
          <div className="space-y-4">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center space-x-4 p-4 rounded-lg border ${
                  step.current
                    ? 'border-blue-200 bg-blue-50'
                    : step.completed
                    ? 'border-green-200 bg-green-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full ${
                    step.completed
                      ? 'bg-green-600 text-white'
                      : step.current
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-300 text-gray-600'
                  }`}
                >
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <step.icon className="h-5 w-5" />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{step.title}</h3>
                  <p className="text-sm text-gray-600">{step.description}</p>
                </div>
                <div>
                  {step.completed && (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      Completed
                    </Badge>
                  )}
                  {step.current && (
                    <Badge variant="default" className="bg-blue-100 text-blue-800">
                      Current
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Start Options */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Start New Onboarding */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="h-5 w-5 text-blue-600" />
              <span>Create New Company</span>
            </CardTitle>
            <CardDescription>
              Start the guided onboarding process to create a new company from scratch
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-semibold">What you'll need:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Company basic information</li>
                <li>• Admin user details</li>
                <li>• Hierarchy placement (if applicable)</li>
                <li>• Service requirements</li>
              </ul>
            </div>
            <Button 
              onClick={() => setShowWizard(true)}
              className="w-full"
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              Start Onboarding
            </Button>
          </CardContent>
        </Card>

        {/* Import from Template */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-green-600" />
              <span>Use Company Template</span>
            </CardTitle>
            <CardDescription>
              Create a company based on an existing template or configuration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Available templates:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Technology Company</li>
                <li>• Healthcare Organization</li>
                <li>• Retail Business</li>
                <li>• Financial Services</li>
              </ul>
            </div>
            <Button variant="outline" className="w-full" disabled>
              <Settings className="h-4 w-4 mr-2" />
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Onboarding Activity</CardTitle>
          <CardDescription>
            Latest companies that have been onboarded
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              {
                name: 'TechCorp AI Division',
                parent: 'TechCorp Solutions',
                date: '2 days ago',
                status: 'completed'
              },
              {
                name: 'RetailMax Online',
                parent: 'RetailMax Corporation',
                date: '1 week ago',
                status: 'completed'
              },
              {
                name: 'HealthPlus Cardiology',
                parent: 'HealthPlus Medical Group',
                date: '2 weeks ago',
                status: 'completed'
              }
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">{activity.name}</h4>
                    <p className="text-sm text-gray-600">
                      Child of {activity.parent}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    {activity.status}
                  </Badge>
                  <p className="text-sm text-gray-500 mt-1">
                    {activity.date}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Help & Support */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
          <CardDescription>
            Resources and support for the onboarding process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <h4 className="font-semibold">Documentation</h4>
              <p className="text-sm text-gray-600">
                Comprehensive guides and tutorials
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <h4 className="font-semibold">Support Chat</h4>
              <p className="text-sm text-gray-600">
                Get help from our support team
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
              <h4 className="font-semibold">Video Tutorials</h4>
              <p className="text-sm text-gray-600">
                Step-by-step video guides
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
