'use client';

import React, { useState } from 'react';
import { usePathname } from 'next/navigation';
import PortalSidebar from './components/navigation/PortalSidebar';
import PortalHeader from './components/navigation/PortalHeader';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <PortalSidebar 
        open={sidebarOpen} 
        onClose={() => setSidebarOpen(false)}
        currentPath={pathname}
      />

      {/* Main content area */}
      <div className="lg:pl-64">
        {/* Header */}
        <PortalHeader 
          onMenuClick={() => setSidebarOpen(true)}
          currentPath={pathname}
        />

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
