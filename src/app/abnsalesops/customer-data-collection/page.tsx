'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  Database,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Users,
  Mail,
  Phone,
  Building,
  Calendar,
  MapPin,
  Globe,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';

interface CustomerData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  position: string;
  industry: string;
  location: string;
  website: string;
  source: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  notes: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  lastContact: string;
  score: number;
}

interface CollectionForm {
  id: string;
  name: string;
  description: string;
  fields: FormField[];
  isActive: boolean;
  submissions: number;
  createdAt: string;
}

interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'textarea' | 'checkbox' | 'date';
  required: boolean;
  options?: string[];
  placeholder?: string;
}

export default function CustomerDataCollectionPage() {
  const [customers, setCustomers] = useState<CustomerData[]>([]);
  const [forms, setForms] = useState<CollectionForm[]>([]);
  const [activeTab, setActiveTab] = useState<'customers' | 'forms' | 'analytics'>('customers');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerData | null>(null);
  const [loading, setLoading] = useState(true);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [customersRes, formsRes] = await Promise.all([
        fetch('/api/abnsalesops/customer-data-collection/customers'),
        fetch('/api/abnsalesops/customer-data-collection/forms')
      ]);

      if (customersRes.ok) {
        const customersData = await customersRes.json();
        setCustomers(customersData);
      }

      if (formsRes.ok) {
        const formsData = await formsRes.json();
        setForms(formsData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCustomer = async (customerData: Partial<CustomerData>) => {
    try {
      const response = await fetch('/api/abnsalesops/customer-data-collection/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData)
      });

      if (response.ok) {
        const newCustomer = await response.json();
        setCustomers(prev => [newCustomer, ...prev]);
        setShowAddCustomer(false);
      }
    } catch (error) {
      console.error('Error adding customer:', error);
    }
  };

  const handleCreateForm = async (formData: Partial<CollectionForm>) => {
    try {
      const response = await fetch('/api/abnsalesops/customer-data-collection/forms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const newForm = await response.json();
        setForms(prev => [newForm, ...prev]);
        setShowCreateForm(false);
      }
    } catch (error) {
      console.error('Error creating form:', error);
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || customer.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'contacted': return <Mail className="h-4 w-4 text-yellow-500" />;
      case 'qualified': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'converted': return <CheckCircle className="h-4 w-4 text-emerald-500" />;
      case 'lost': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'qualified': return 'bg-green-100 text-green-800';
      case 'converted': return 'bg-emerald-100 text-emerald-800';
      case 'lost': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              Customer Data Collection
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Never miss potential customers with comprehensive data collection
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button
              onClick={() => setShowAddCustomer(true)}
              className={getButtonClass()}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Customer
            </Button>
            <Button
              onClick={() => setShowCreateForm(true)}
              className={getButtonClass()}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Form
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'customers', label: 'Customers', icon: Users },
          { id: 'forms', label: 'Collection Forms', icon: Database },
          { id: 'analytics', label: 'Analytics', icon: Globe }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? isPaperTheme
                  ? 'bg-black text-white border-2 border-black'
                  : 'bg-indigo-600 text-white'
                : isPaperTheme
                  ? 'bg-white text-black border-2 border-black hover:bg-[#f0f0f0]'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Customers Tab */}
      {activeTab === 'customers' && (
        <div>
          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 ${isPaperTheme ? 'text-black' : 'text-gray-400'}`} />
              <input
                type="text"
                placeholder="Search customers..."
                className={`${getInputClass()} pl-10`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className={getInputClass()}
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="contacted">Contacted</option>
              <option value="qualified">Qualified</option>
              <option value="converted">Converted</option>
              <option value="lost">Lost</option>
            </select>
          </div>

          {/* Customers Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCustomers.map((customer) => (
              <Card key={customer.id} className={getCardClass()}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className={getHeadingClass(4)}>
                        {customer.firstName} {customer.lastName}
                      </h3>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                        {customer.position} at {customer.company}
                      </p>
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(customer.status)}`}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(customer.status)}
                        {customer.status}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-600'}>{customer.email}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-600'}>{customer.phone}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-600'}>{customer.location}</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setSelectedCustomer(customer)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className={`text-xs ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-500'}`}>
                      Score: {customer.score}/100
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Forms Tab */}
      {activeTab === 'forms' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {forms.map((form) => (
            <Card key={form.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{form.name}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {form.description}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    form.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {form.isActive ? 'Active' : 'Inactive'}
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Fields:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{form.fields.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Submissions:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{form.submissions}</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Customers
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{customers.length}</h2>
                </div>
                <Users className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Active Forms
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{forms.filter(f => f.isActive).length}</h2>
                </div>
                <Database className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Conversion Rate
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {customers.length > 0 ? Math.round((customers.filter(c => c.status === 'converted').length / customers.length) * 100) : 0}%
                  </h2>
                </div>
                <CheckCircle className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-emerald-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Score
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {customers.length > 0 ? Math.round(customers.reduce((sum, c) => sum + c.score, 0) / customers.length) : 0}
                  </h2>
                </div>
                <Globe className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-purple-600'}`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
