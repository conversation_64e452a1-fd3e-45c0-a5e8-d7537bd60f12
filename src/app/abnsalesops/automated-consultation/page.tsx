'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  Bot,
  MessageSquare,
  Send,
  Settings,
  BarChart3,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Phone,
  Mail,
  Calendar,
  Star,
  TrendingUp,
  Activity,
  Zap,
  Target,
  MessageCircle,
  User,
  Mic,
  MicOff,
  Volume2,
  VolumeX
} from 'lucide-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: string;
  metadata?: {
    intent?: string;
    confidence?: number;
    suggestedActions?: string[];
  };
}

interface ConsultationSession {
  id: string;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  status: 'active' | 'completed' | 'transferred' | 'abandoned';
  startTime: string;
  endTime?: string;
  duration?: number;
  messages: ChatMessage[];
  leadScore: number;
  intent: string;
  outcome: 'qualified' | 'not-qualified' | 'follow-up' | 'demo-requested' | 'pricing-requested';
  nextAction?: string;
  assignedAgent?: string;
  satisfaction?: number;
  tags: string[];
}

interface BotConfiguration {
  name: string;
  personality: string;
  responseStyle: 'formal' | 'casual' | 'professional';
  language: 'en' | 'vi' | 'both';
  maxResponseTime: number;
  escalationTriggers: string[];
  workingHours: {
    enabled: boolean;
    start: string;
    end: string;
    timezone: string;
  };
  knowledgeBase: {
    products: string[];
    services: string[];
    pricing: boolean;
    technical: boolean;
  };
}

export default function AutomatedConsultationPage() {
  const [activeTab, setActiveTab] = useState<'chat' | 'sessions' | 'analytics' | 'settings'>('chat');
  const [sessions, setSessions] = useState<ConsultationSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ConsultationSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [botConfig, setBotConfig] = useState<BotConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    fetchData();
    scrollToBottom();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [sessionsRes, configRes] = await Promise.all([
        fetch('/api/abnsalesops/automated-consultation/sessions'),
        fetch('/api/abnsalesops/automated-consultation/config')
      ]);

      if (sessionsRes.ok) {
        const sessionsData = await sessionsRes.json();
        setSessions(sessionsData);
      }

      if (configRes.ok) {
        const configData = await configRes.json();
        setBotConfig(configData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const startNewSession = async () => {
    try {
      const response = await fetch('/api/abnsalesops/automated-consultation/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          customerName: 'Anonymous User',
          customerEmail: '',
          intent: 'general_inquiry'
        })
      });

      if (response.ok) {
        const newSession = await response.json();
        setCurrentSession(newSession);
        setMessages(newSession.messages || []);
        
        // Add welcome message
        const welcomeMessage: ChatMessage = {
          id: 'welcome_' + Date.now(),
          type: 'bot',
          content: 'Xin chào! Tôi là trợ lý AI của ABN Sales Ops. Tôi có thể giúp bạn tìm hiểu về sản phẩm và dịch vụ của chúng tôi 24/7. Bạn cần hỗ trợ gì hôm nay?',
          timestamp: new Date().toISOString(),
          metadata: {
            intent: 'greeting',
            confidence: 1.0,
            suggestedActions: ['product_info', 'pricing', 'demo_request']
          }
        };
        
        setMessages([welcomeMessage]);
      }
    } catch (error) {
      console.error('Error starting session:', error);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !currentSession) return;

    const userMessage: ChatMessage = {
      id: 'msg_' + Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    try {
      const response = await fetch('/api/abnsalesops/automated-consultation/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: currentSession.id,
          message: inputMessage
        })
      });

      if (response.ok) {
        const botResponse = await response.json();
        
        setTimeout(() => {
          const botMessage: ChatMessage = {
            id: 'bot_' + Date.now(),
            type: 'bot',
            content: botResponse.content,
            timestamp: new Date().toISOString(),
            metadata: botResponse.metadata
          };
          
          setMessages(prev => [...prev, botMessage]);
          setIsTyping(false);
          
          // Text-to-speech if enabled
          if (botConfig?.language && botResponse.content) {
            speakText(botResponse.content);
          }
        }, 1000 + Math.random() * 2000); // Simulate thinking time
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setIsTyping(false);
    }
  };

  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      setIsSpeaking(true);
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = botConfig?.language === 'vi' ? 'vi-VN' : 'en-US';
      utterance.onend = () => setIsSpeaking(false);
      speechSynthesis.speak(utterance);
    }
  };

  const startListening = () => {
    if ('webkitSpeechRecognition' in window) {
      const recognition = new (window as any).webkitSpeechRecognition();
      recognition.lang = botConfig?.language === 'vi' ? 'vi-VN' : 'en-US';
      recognition.onstart = () => setIsListening(true);
      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setInputMessage(transcript);
      };
      recognition.onend = () => setIsListening(false);
      recognition.start();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'transferred': return 'bg-yellow-100 text-yellow-800';
      case 'abandoned': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'qualified': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'demo-requested': return <Calendar className="h-4 w-4 text-blue-500" />;
      case 'pricing-requested': return <Target className="h-4 w-4 text-purple-500" />;
      case 'follow-up': return <Clock className="h-4 w-4 text-yellow-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              24/7 Automated Product Consultation
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Quick, proactive sales closing with AI-powered consultation
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button
              onClick={startNewSession}
              className={getButtonClass()}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              New Session
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'chat', label: 'Live Chat', icon: MessageSquare },
          { id: 'sessions', label: 'Sessions', icon: Users },
          { id: 'analytics', label: 'Analytics', icon: BarChart3 },
          { id: 'settings', label: 'Bot Settings', icon: Settings }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? isPaperTheme
                  ? 'bg-black text-white border-2 border-black'
                  : 'bg-indigo-600 text-white'
                : isPaperTheme
                  ? 'bg-white text-black border-2 border-black hover:bg-[#f0f0f0]'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Chat Tab */}
      {activeTab === 'chat' && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <Card className={getCardClass()}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className={`h-5 w-5 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
                  AI Consultation Chat
                  {currentSession && (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(currentSession.status)}`}>
                      {currentSession.status}
                    </span>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Messages */}
                <div className="h-96 overflow-y-auto mb-4 p-4 border rounded-lg">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`mb-4 flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.type === 'user'
                            ? isPaperTheme
                              ? 'bg-black text-white border-2 border-black'
                              : 'bg-indigo-600 text-white'
                            : isPaperTheme
                              ? 'bg-[#f0f0f0] text-black border-2 border-black'
                              : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          {message.type === 'bot' ? (
                            <Bot className="h-4 w-4" />
                          ) : (
                            <User className="h-4 w-4" />
                          )}
                          <span className="text-xs opacity-75">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <p>{message.content}</p>
                        {message.metadata?.suggestedActions && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {message.metadata.suggestedActions.map((action, index) => (
                              <button
                                key={index}
                                className="text-xs px-2 py-1 rounded border opacity-75 hover:opacity-100"
                                onClick={() => setInputMessage(action)}
                              >
                                {action}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  {isTyping && (
                    <div className="flex justify-start mb-4">
                      <div className={`px-4 py-2 rounded-lg ${isPaperTheme ? 'bg-[#f0f0f0] border-2 border-black' : 'bg-gray-100'}`}>
                        <div className="flex items-center gap-2">
                          <Bot className="h-4 w-4" />
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input */}
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Type your message..."
                    className={`${getInputClass()} flex-1`}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                    disabled={!currentSession}
                  />
                  <Button
                    onClick={startListening}
                    variant="outline"
                    size="sm"
                    disabled={!currentSession || isListening}
                  >
                    {isListening ? <Mic className="h-4 w-4 text-red-500" /> : <MicOff className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => speechSynthesis.cancel()}
                    variant="outline"
                    size="sm"
                    disabled={!isSpeaking}
                  >
                    {isSpeaking ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={sendMessage}
                    disabled={!inputMessage.trim() || !currentSession}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Session Info */}
          <div>
            <Card className={getCardClass()}>
              <CardHeader>
                <CardTitle>Current Session</CardTitle>
              </CardHeader>
              <CardContent>
                {currentSession ? (
                  <div className="space-y-4">
                    <div>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>Customer</p>
                      <p className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{currentSession.customerName}</p>
                    </div>
                    <div>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>Duration</p>
                      <p className={isPaperTheme ? 'text-black' : 'text-gray-900'}>
                        {Math.round((Date.now() - new Date(currentSession.startTime).getTime()) / 60000)} minutes
                      </p>
                    </div>
                    <div>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>Lead Score</p>
                      <p className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{currentSession.leadScore}/100</p>
                    </div>
                    <div>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>Intent</p>
                      <p className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{currentSession.intent}</p>
                    </div>
                  </div>
                ) : (
                  <p className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-500'}>
                    No active session. Start a new consultation to begin.
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Sessions Tab */}
      {activeTab === 'sessions' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sessions.map((session) => (
            <Card key={session.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{session.customerName}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {session.customerEmail}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                    {session.status}
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Duration:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>
                      {session.duration ? `${Math.round(session.duration / 60)} min` : 'Ongoing'}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Lead Score:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{session.leadScore}/100</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Messages:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{session.messages.length}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {getOutcomeIcon(session.outcome)}
                    <span className="text-sm">{session.outcome}</span>
                  </div>
                  {session.satisfaction && (
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">{session.satisfaction}/5</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Sessions
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{sessions.length}</h2>
                </div>
                <MessageCircle className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Qualified Leads
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {sessions.filter(s => s.outcome === 'qualified').length}
                  </h2>
                </div>
                <Target className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Lead Score
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {sessions.length > 0 ? Math.round(sessions.reduce((sum, s) => sum + s.leadScore, 0) / sessions.length) : 0}
                  </h2>
                </div>
                <TrendingUp className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-purple-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Satisfaction
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {sessions.filter(s => s.satisfaction).length > 0 
                      ? (sessions.reduce((sum, s) => sum + (s.satisfaction || 0), 0) / sessions.filter(s => s.satisfaction).length).toFixed(1)
                      : 'N/A'
                    }
                  </h2>
                </div>
                <Star className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-yellow-600'}`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && botConfig && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle>Bot Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  Bot Name
                </label>
                <input
                  type="text"
                  className={getInputClass()}
                  value={botConfig.name}
                  onChange={(e) => setBotConfig({...botConfig, name: e.target.value})}
                />
              </div>
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  Response Style
                </label>
                <select
                  className={getInputClass()}
                  value={botConfig.responseStyle}
                  onChange={(e) => setBotConfig({...botConfig, responseStyle: e.target.value as any})}
                >
                  <option value="formal">Formal</option>
                  <option value="casual">Casual</option>
                  <option value="professional">Professional</option>
                </select>
              </div>
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  Language
                </label>
                <select
                  className={getInputClass()}
                  value={botConfig.language}
                  onChange={(e) => setBotConfig({...botConfig, language: e.target.value as any})}
                >
                  <option value="en">English</option>
                  <option value="vi">Vietnamese</option>
                  <option value="both">Both</option>
                </select>
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle>Working Hours</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={botConfig.workingHours.enabled}
                  onChange={(e) => setBotConfig({
                    ...botConfig,
                    workingHours: {...botConfig.workingHours, enabled: e.target.checked}
                  })}
                />
                <label className={`text-sm ${isPaperTheme ? 'text-black' : 'text-gray-700'}`}>
                  Enable working hours restriction
                </label>
              </div>
              {botConfig.workingHours.enabled && (
                <>
                  <div>
                    <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                      Start Time
                    </label>
                    <input
                      type="time"
                      className={getInputClass()}
                      value={botConfig.workingHours.start}
                      onChange={(e) => setBotConfig({
                        ...botConfig,
                        workingHours: {...botConfig.workingHours, start: e.target.value}
                      })}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                      End Time
                    </label>
                    <input
                      type="time"
                      className={getInputClass()}
                      value={botConfig.workingHours.end}
                      onChange={(e) => setBotConfig({
                        ...botConfig,
                        workingHours: {...botConfig.workingHours, end: e.target.value}
                      })}
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
