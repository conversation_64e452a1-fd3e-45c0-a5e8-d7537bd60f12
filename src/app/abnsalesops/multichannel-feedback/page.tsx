'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  BarChart,
  Plus,
  MessageSquare,
  Mail,
  Phone,
  Globe,
  MessageCircle,
  Star,
  Users,
  TrendingUp,
  Activity,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  Filter,
  Download,
  Share,
  Settings,
  Smartphone,
  Monitor,
  Headphones
} from 'lucide-react';

interface FeedbackChannel {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'web' | 'mobile' | 'social' | 'phone' | 'chat' | 'instore';
  status: 'active' | 'inactive' | 'maintenance';
  totalFeedback: number;
  todayFeedback: number;
  averageRating: number;
  responseRate: number;
  lastActivity: string;
  configuration: ChannelConfig;
}

interface ChannelConfig {
  autoResponse: boolean;
  escalationRules: string[];
  integrations: string[];
  customFields: string[];
}

interface CustomerFeedback {
  id: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  channel: string;
  touchpoint: string;
  category: 'complaint' | 'suggestion' | 'compliment' | 'question' | 'feature-request';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  rating?: number;
  subject: string;
  content: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  status: 'new' | 'acknowledged' | 'in-progress' | 'resolved' | 'closed';
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  tags: string[];
  attachments: string[];
  followUpRequired: boolean;
}

interface CustomerJourney {
  customerId: string;
  touchpoints: JourneyTouchpoint[];
  totalInteractions: number;
  feedbackCount: number;
  averageSatisfaction: number;
  lastInteraction: string;
}

interface JourneyTouchpoint {
  id: string;
  channel: string;
  type: 'awareness' | 'consideration' | 'purchase' | 'onboarding' | 'support' | 'retention';
  timestamp: string;
  satisfaction?: number;
  feedback?: string;
}

export default function MultichannelFeedbackPage() {
  const [channels, setChannels] = useState<FeedbackChannel[]>([]);
  const [feedback, setFeedback] = useState<CustomerFeedback[]>([]);
  const [journeys, setJourneys] = useState<CustomerJourney[]>([]);
  const [activeTab, setActiveTab] = useState<'channels' | 'feedback' | 'journeys' | 'analytics'>('channels');
  const [filterChannel, setFilterChannel] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      setChannels([
        {
          id: 'channel_001',
          name: 'Email Support',
          type: 'email',
          status: 'active',
          totalFeedback: 1250,
          todayFeedback: 45,
          averageRating: 4.2,
          responseRate: 85,
          lastActivity: '2024-01-25T16:30:00Z',
          configuration: {
            autoResponse: true,
            escalationRules: ['urgent_keywords', 'negative_sentiment'],
            integrations: ['crm', 'helpdesk'],
            customFields: ['product_category', 'purchase_date']
          }
        },
        {
          id: 'channel_002',
          name: 'Live Chat',
          type: 'chat',
          status: 'active',
          totalFeedback: 890,
          todayFeedback: 67,
          averageRating: 4.5,
          responseRate: 92,
          lastActivity: '2024-01-25T16:25:00Z',
          configuration: {
            autoResponse: false,
            escalationRules: ['wait_time_exceeded'],
            integrations: ['crm'],
            customFields: ['session_duration']
          }
        },
        {
          id: 'channel_003',
          name: 'Mobile App',
          type: 'mobile',
          status: 'active',
          totalFeedback: 567,
          todayFeedback: 23,
          averageRating: 4.1,
          responseRate: 78,
          lastActivity: '2024-01-25T15:45:00Z',
          configuration: {
            autoResponse: true,
            escalationRules: ['app_crash', 'payment_issue'],
            integrations: ['analytics', 'push_notifications'],
            customFields: ['app_version', 'device_type']
          }
        },
        {
          id: 'channel_004',
          name: 'Social Media',
          type: 'social',
          status: 'active',
          totalFeedback: 234,
          todayFeedback: 12,
          averageRating: 3.8,
          responseRate: 65,
          lastActivity: '2024-01-25T14:20:00Z',
          configuration: {
            autoResponse: false,
            escalationRules: ['public_complaint', 'viral_risk'],
            integrations: ['social_monitoring'],
            customFields: ['platform', 'follower_count']
          }
        }
      ]);

      setFeedback([
        {
          id: 'feedback_001',
          customerId: 'cust_001',
          customerName: 'Nguyen Van An',
          customerEmail: '<EMAIL>',
          channel: 'email',
          touchpoint: 'post_purchase',
          category: 'compliment',
          priority: 'low',
          rating: 5,
          subject: 'Excellent service experience',
          content: 'I am very satisfied with the quick delivery and product quality. Thank you!',
          sentiment: 'positive',
          status: 'acknowledged',
          createdAt: '2024-01-25T14:30:00Z',
          updatedAt: '2024-01-25T15:00:00Z',
          tags: ['delivery', 'quality', 'satisfaction'],
          attachments: [],
          followUpRequired: false
        },
        {
          id: 'feedback_002',
          customerId: 'cust_002',
          customerName: 'Tran Thi Mai',
          customerEmail: '<EMAIL>',
          channel: 'chat',
          touchpoint: 'support',
          category: 'complaint',
          priority: 'high',
          rating: 2,
          subject: 'Payment processing issue',
          content: 'I have been trying to complete my payment for 2 hours but the system keeps failing.',
          sentiment: 'negative',
          status: 'in-progress',
          assignedTo: 'agent_001',
          createdAt: '2024-01-25T13:15:00Z',
          updatedAt: '2024-01-25T16:00:00Z',
          tags: ['payment', 'technical_issue', 'urgent'],
          attachments: ['screenshot.png'],
          followUpRequired: true
        }
      ]);

      setJourneys([
        {
          customerId: 'cust_001',
          touchpoints: [
            {
              id: 'tp_001',
              channel: 'web',
              type: 'awareness',
              timestamp: '2024-01-20T10:00:00Z',
              satisfaction: 4
            },
            {
              id: 'tp_002',
              channel: 'email',
              type: 'consideration',
              timestamp: '2024-01-22T14:30:00Z',
              satisfaction: 5
            },
            {
              id: 'tp_003',
              channel: 'web',
              type: 'purchase',
              timestamp: '2024-01-23T16:45:00Z',
              satisfaction: 5
            },
            {
              id: 'tp_004',
              channel: 'email',
              type: 'support',
              timestamp: '2024-01-25T14:30:00Z',
              satisfaction: 5,
              feedback: 'Excellent service experience'
            }
          ],
          totalInteractions: 4,
          feedbackCount: 1,
          averageSatisfaction: 4.75,
          lastInteraction: '2024-01-25T14:30:00Z'
        }
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="h-5 w-5" />;
      case 'chat': return <MessageSquare className="h-5 w-5" />;
      case 'phone': return <Phone className="h-5 w-5" />;
      case 'web': return <Globe className="h-5 w-5" />;
      case 'mobile': return <Smartphone className="h-5 w-5" />;
      case 'social': return <MessageCircle className="h-5 w-5" />;
      case 'instore': return <Monitor className="h-5 w-5" />;
      default: return <MessageSquare className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'resolved': return 'bg-green-100 text-green-800';
      case 'inactive': case 'new': return 'bg-red-100 text-red-800';
      case 'maintenance': case 'in-progress': return 'bg-yellow-100 text-yellow-800';
      case 'acknowledged': case 'closed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      case 'neutral': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredFeedback = feedback.filter(item => {
    const matchesChannel = filterChannel === 'all' || item.channel === filterChannel;
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
    return matchesChannel && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              Multichannel Feedback Collection
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Collect customer feedback throughout the journey across all touchpoints and channels
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button className={getButtonClass()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Channel
            </Button>
            <Button className={getButtonClass()}>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'channels', label: 'Channels', icon: BarChart },
          { id: 'feedback', label: 'Feedback', icon: MessageSquare },
          { id: 'journeys', label: 'Customer Journeys', icon: Users },
          { id: 'analytics', label: 'Analytics', icon: TrendingUp }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? isPaperTheme
                  ? 'bg-black text-white border-2 border-black'
                  : 'bg-indigo-600 text-white'
                : isPaperTheme
                  ? 'bg-white text-black border-2 border-black hover:bg-[#f0f0f0]'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Channels Tab */}
      {activeTab === 'channels' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {channels.map((channel) => (
            <Card key={channel.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded ${isPaperTheme ? 'bg-[#e0e0e0] border border-black' : 'bg-gray-100'}`}>
                      {getChannelIcon(channel.type)}
                    </div>
                    <div>
                      <h3 className={getHeadingClass(4)}>{channel.name}</h3>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                        {channel.type}
                      </p>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(channel.status)}`}>
                    {channel.status}
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Total Feedback:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{channel.totalFeedback}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Today:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{channel.todayFeedback}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Response Rate:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{channel.responseRate}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{channel.averageRating}/5</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Feedback Tab */}
      {activeTab === 'feedback' && (
        <div>
          {/* Filters */}
          <div className="flex gap-4 mb-6">
            <select
              className={getInputClass()}
              value={filterChannel}
              onChange={(e) => setFilterChannel(e.target.value)}
            >
              <option value="all">All Channels</option>
              <option value="email">Email</option>
              <option value="chat">Chat</option>
              <option value="phone">Phone</option>
              <option value="web">Web</option>
              <option value="mobile">Mobile</option>
              <option value="social">Social</option>
            </select>
            <select
              className={getInputClass()}
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="acknowledged">Acknowledged</option>
              <option value="in-progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
          </div>

          {/* Feedback List */}
          <div className="space-y-4">
            {filteredFeedback.map((item) => (
              <Card key={item.id} className={getCardClass()}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className={getHeadingClass(4)}>{item.subject}</h3>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                        {item.customerName} • {item.customerEmail}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <div className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(item.status)}`}>
                        {item.status}
                      </div>
                      <div className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(item.priority)}`}>
                        {item.priority}
                      </div>
                      <div className={`px-2 py-1 rounded text-xs font-medium ${getSentimentColor(item.sentiment)}`}>
                        {item.sentiment}
                      </div>
                    </div>
                  </div>

                  <p className={`text-sm ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-4`}>
                    {item.content}
                  </p>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-4 text-sm">
                      <span className={`flex items-center gap-1 ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                        {getChannelIcon(item.channel)}
                        {item.channel}
                      </span>
                      {item.rating && (
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span>{item.rating}/5</span>
                        </div>
                      )}
                      <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-500'}>
                        {new Date(item.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Channels
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{channels.length}</h2>
                </div>
                <BarChart className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Feedback
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {channels.reduce((sum, c) => sum + c.totalFeedback, 0)}
                  </h2>
                </div>
                <MessageSquare className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Response Rate
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {channels.length > 0 ? Math.round(channels.reduce((sum, c) => sum + c.responseRate, 0) / channels.length) : 0}%
                  </h2>
                </div>
                <TrendingUp className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-purple-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Satisfaction
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {channels.length > 0 ? (channels.reduce((sum, c) => sum + c.averageRating, 0) / channels.length).toFixed(1) : 'N/A'}
                  </h2>
                </div>
                <Star className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-yellow-600'}`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
