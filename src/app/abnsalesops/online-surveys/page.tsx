'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  Monitor,
  Plus,
  QrCode,
  Mail,
  Globe,
  BarChart3,
  Settings,
  Star,
  Users,
  Eye,
  Edit,
  Share,
  Download,
  Link,
  Calendar,
  Target,
  TrendingUp,
  Activity,
  CheckCircle,
  Clock
} from 'lucide-react';

interface OnlineSurvey {
  id: string;
  title: string;
  description: string;
  questions: SurveyQuestion[];
  distributionMethods: ('qr' | 'email' | 'website' | 'social')[];
  status: 'draft' | 'active' | 'completed' | 'paused';
  createdAt: string;
  startDate?: string;
  endDate?: string;
  responses: number;
  views: number;
  completionRate: number;
  averageRating: number;
  targetResponses: number;
  qrCodeUrl?: string;
  surveyUrl: string;
  embedCode?: string;
}

interface SurveyQuestion {
  id: string;
  type: 'rating' | 'multiple-choice' | 'text' | 'yes-no' | 'scale' | 'matrix';
  question: string;
  options?: string[];
  required: boolean;
  logic?: QuestionLogic;
}

interface QuestionLogic {
  condition: string;
  action: 'skip' | 'show' | 'end';
  target: string;
}

interface SurveyResponse {
  id: string;
  surveyId: string;
  respondentId: string;
  answers: Record<string, any>;
  completedAt: string;
  source: 'qr' | 'email' | 'website' | 'social';
  timeSpent: number;
  ipAddress: string;
  userAgent: string;
}

export default function OnlineSurveysPage() {
  const [surveys, setSurveys] = useState<OnlineSurvey[]>([]);
  const [responses, setResponses] = useState<SurveyResponse[]>([]);
  const [activeTab, setActiveTab] = useState<'surveys' | 'responses' | 'analytics' | 'distribution'>('surveys');
  const [selectedSurvey, setSelectedSurvey] = useState<OnlineSurvey | null>(null);
  const [showCreateSurvey, setShowCreateSurvey] = useState(false);
  const [loading, setLoading] = useState(true);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      setSurveys([
        {
          id: 'survey_001',
          title: 'Customer Experience Survey 2024',
          description: 'Annual customer experience and satisfaction survey',
          questions: [
            {
              id: 'q1',
              type: 'rating',
              question: 'How would you rate your overall experience with our service?',
              required: true
            },
            {
              id: 'q2',
              type: 'multiple-choice',
              question: 'Which aspect of our service impressed you most?',
              options: ['Customer Support', 'Product Quality', 'Delivery Speed', 'Pricing', 'User Interface'],
              required: true
            },
            {
              id: 'q3',
              type: 'text',
              question: 'What suggestions do you have for improvement?',
              required: false
            }
          ],
          distributionMethods: ['qr', 'email', 'website'],
          status: 'active',
          createdAt: '2024-01-15T10:00:00Z',
          startDate: '2024-01-20T00:00:00Z',
          endDate: '2024-02-20T23:59:59Z',
          responses: 234,
          views: 456,
          completionRate: 78,
          averageRating: 4.3,
          targetResponses: 500,
          surveyUrl: 'https://surveys.abnsalesops.com/survey/001',
          qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://surveys.abnsalesops.com/survey/001'
        },
        {
          id: 'survey_002',
          title: 'Product Feedback Survey',
          description: 'Feedback on our latest product features',
          questions: [
            {
              id: 'q1',
              type: 'scale',
              question: 'How easy was it to use the new features?',
              required: true
            },
            {
              id: 'q2',
              type: 'yes-no',
              question: 'Would you recommend this product to others?',
              required: true
            }
          ],
          distributionMethods: ['email', 'social'],
          status: 'completed',
          createdAt: '2024-01-01T10:00:00Z',
          startDate: '2024-01-05T00:00:00Z',
          endDate: '2024-01-15T23:59:59Z',
          responses: 89,
          views: 156,
          completionRate: 85,
          averageRating: 4.1,
          targetResponses: 100,
          surveyUrl: 'https://surveys.abnsalesops.com/survey/002'
        }
      ]);

      setResponses([
        {
          id: 'response_001',
          surveyId: 'survey_001',
          respondentId: 'resp_001',
          answers: {
            'q1': 5,
            'q2': 'Customer Support',
            'q3': 'Keep up the great work!'
          },
          completedAt: '2024-01-25T14:30:00Z',
          source: 'qr',
          timeSpent: 180,
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0...'
        }
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'paused': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'draft': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'paused': return <Clock className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDistributionIcon = (method: string) => {
    switch (method) {
      case 'qr': return <QrCode className="h-4 w-4" />;
      case 'email': return <Mail className="h-4 w-4" />;
      case 'website': return <Globe className="h-4 w-4" />;
      case 'social': return <Share className="h-4 w-4" />;
      default: return <Link className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              Online Surveys
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Send surveys via QR code, email, website with comprehensive distribution and analytics
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button
              onClick={() => setShowCreateSurvey(true)}
              className={getButtonClass()}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Survey
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'surveys', label: 'Surveys', icon: Monitor },
          { id: 'responses', label: 'Responses', icon: Users },
          { id: 'analytics', label: 'Analytics', icon: BarChart3 },
          { id: 'distribution', label: 'Distribution', icon: Share }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? isPaperTheme
                  ? 'bg-black text-white border-2 border-black'
                  : 'bg-indigo-600 text-white'
                : isPaperTheme
                  ? 'bg-white text-black border-2 border-black hover:bg-[#f0f0f0]'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Surveys Tab */}
      {activeTab === 'surveys' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {surveys.map((survey) => (
            <Card key={survey.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{survey.title}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {survey.description}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(survey.status)}`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(survey.status)}
                      {survey.status}
                    </div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Questions:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.questions.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Responses:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.responses}/{survey.targetResponses}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Completion Rate:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.completionRate}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.averageRating}/5</span>
                    </div>
                  </div>
                </div>

                {/* Distribution Methods */}
                <div className="flex gap-1 mb-4">
                  {survey.distributionMethods.map((method, index) => (
                    <div
                      key={index}
                      className={`p-2 rounded ${isPaperTheme ? 'bg-[#e0e0e0] border border-black' : 'bg-gray-100'}`}
                      title={method}
                    >
                      {getDistributionIcon(method)}
                    </div>
                  ))}
                </div>

                {/* Progress bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                  <div 
                    className="h-2 rounded-full bg-indigo-600"
                    style={{ 
                      width: `${Math.min(100, (survey.responses / survey.targetResponses) * 100)}%` 
                    }}
                  ></div>
                </div>

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedSurvey(survey)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Share className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Responses Tab */}
      {activeTab === 'responses' && (
        <div className="space-y-6">
          {responses.map((response) => (
            <Card key={response.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>Response #{response.id.slice(-6)}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      Survey: {surveys.find(s => s.id === response.surveyId)?.title}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`px-2 py-1 rounded text-xs ${isPaperTheme ? 'bg-[#e0e0e0] text-black border border-black' : 'bg-blue-100 text-blue-800'}`}>
                      {response.source}
                    </div>
                    <span className={`text-xs ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-500'}`}>
                      {new Date(response.completedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  {Object.entries(response.answers).map(([questionId, answer]) => (
                    <div key={questionId} className="border-l-4 border-indigo-200 pl-4">
                      <p className={`text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'}`}>
                        Question {questionId}
                      </p>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                        {typeof answer === 'number' ? `${answer}/5` : answer}
                      </p>
                    </div>
                  ))}
                </div>

                <div className="flex justify-between items-center mt-4 pt-4 border-t">
                  <span className={`text-xs ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-500'}`}>
                    Time spent: {Math.round(response.timeSpent / 60)} minutes
                  </span>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Surveys
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{surveys.length}</h2>
                </div>
                <Monitor className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Responses
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {surveys.reduce((sum, s) => sum + s.responses, 0)}
                  </h2>
                </div>
                <Users className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Completion Rate
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {surveys.length > 0 ? Math.round(surveys.reduce((sum, s) => sum + s.completionRate, 0) / surveys.length) : 0}%
                  </h2>
                </div>
                <Target className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-purple-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Rating
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {surveys.length > 0 ? (surveys.reduce((sum, s) => sum + s.averageRating, 0) / surveys.length).toFixed(1) : 'N/A'}
                  </h2>
                </div>
                <Star className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-yellow-600'}`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Distribution Tab */}
      {activeTab === 'distribution' && selectedSurvey && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle>QR Code Distribution</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              {selectedSurvey.qrCodeUrl && (
                <img 
                  src={selectedSurvey.qrCodeUrl} 
                  alt="Survey QR Code" 
                  className="mx-auto mb-4"
                />
              )}
              <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'} mb-4`}>
                Scan this QR code to access the survey
              </p>
              <Button className={getButtonClass()}>
                <Download className="h-4 w-4 mr-2" />
                Download QR Code
              </Button>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle>Survey Links</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  Direct Link
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    className={getInputClass()}
                    value={selectedSurvey.surveyUrl}
                    readOnly
                  />
                  <Button variant="outline">
                    <Link className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  Embed Code
                </label>
                <textarea
                  className={getInputClass()}
                  rows={4}
                  value={`<iframe src="${selectedSurvey.surveyUrl}" width="100%" height="600" frameborder="0"></iframe>`}
                  readOnly
                />
              </div>

              <div className="flex gap-2">
                <Button className={getButtonClass()}>
                  <Mail className="h-4 w-4 mr-2" />
                  Email Campaign
                </Button>
                <Button variant="outline">
                  <Share className="h-4 w-4 mr-2" />
                  Social Share
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
