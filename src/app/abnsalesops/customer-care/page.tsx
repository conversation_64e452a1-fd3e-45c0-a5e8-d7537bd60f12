'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  Headphones,
  Plus,
  Search,
  Filter,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Mail,
  Phone,
  MessageSquare,
  Calendar,
  Star,
  TrendingUp,
  Activity,
  Users,
  BarChart3,
  Settings,
  Tag,
  Archive,
  RefreshCw,
  Send,
  Paperclip,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';

interface CustomerCareTicket {
  id: string;
  ticketNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  subject: string;
  description: string;
  category: 'technical' | 'billing' | 'general' | 'complaint' | 'feature-request';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in-progress' | 'waiting-customer' | 'resolved' | 'closed';
  assignedAgent?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  firstResponseTime?: number;
  resolutionTime?: number;
  satisfactionRating?: number;
  tags: string[];
  responses: TicketResponse[];
  attachments: string[];
  escalated: boolean;
  repeatCustomer: boolean;
  relatedTickets: string[];
}

interface TicketResponse {
  id: string;
  ticketId: string;
  authorType: 'customer' | 'agent' | 'system';
  authorName: string;
  content: string;
  timestamp: string;
  isInternal: boolean;
  attachments?: string[];
}

interface CareAgent {
  id: string;
  name: string;
  email: string;
  status: 'online' | 'busy' | 'away' | 'offline';
  specialties: string[];
  activeTickets: number;
  totalTickets: number;
  averageRating: number;
  responseTime: number;
}

export default function CustomerCarePage() {
  const [tickets, setTickets] = useState<CustomerCareTicket[]>([]);
  const [agents, setAgents] = useState<CareAgent[]>([]);
  const [activeTab, setActiveTab] = useState<'tickets' | 'agents' | 'analytics' | 'settings'>('tickets');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [selectedTicket, setSelectedTicket] = useState<CustomerCareTicket | null>(null);
  const [showCreateTicket, setShowCreateTicket] = useState(false);
  const [loading, setLoading] = useState(true);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [ticketsRes, agentsRes] = await Promise.all([
        fetch('/api/abnsalesops/customer-care/tickets'),
        fetch('/api/abnsalesops/customer-care/agents')
      ]);

      if (ticketsRes.ok) {
        const ticketsData = await ticketsRes.json();
        setTickets(ticketsData);
      }

      if (agentsRes.ok) {
        const agentsData = await agentsRes.json();
        setAgents(agentsData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTicket = async (ticketData: Partial<CustomerCareTicket>) => {
    try {
      const response = await fetch('/api/abnsalesops/customer-care/tickets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(ticketData)
      });

      if (response.ok) {
        const newTicket = await response.json();
        setTickets(prev => [newTicket, ...prev]);
        setShowCreateTicket(false);
      }
    } catch (error) {
      console.error('Error creating ticket:', error);
    }
  };

  const handleUpdateTicket = async (ticketId: string, updates: Partial<CustomerCareTicket>) => {
    try {
      const response = await fetch(`/api/abnsalesops/customer-care/tickets/${ticketId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        const updatedTicket = await response.json();
        setTickets(prev => prev.map(t => t.id === ticketId ? updatedTicket : t));
        if (selectedTicket?.id === ticketId) {
          setSelectedTicket(updatedTicket);
        }
      }
    } catch (error) {
      console.error('Error updating ticket:', error);
    }
  };

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || ticket.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || ticket.priority === filterPriority;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800';
      case 'waiting-customer': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'in-progress': return <RefreshCw className="h-4 w-4 text-yellow-500" />;
      case 'waiting-customer': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'resolved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'closed': return <Archive className="h-4 w-4 text-gray-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              Comprehensive Customer Care
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Handling repeated requests to satisfy customers
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button
              onClick={() => setShowCreateTicket(true)}
              className={getButtonClass()}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Ticket
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'tickets', label: 'Tickets', icon: MessageSquare },
          { id: 'agents', label: 'Agents', icon: Users },
          { id: 'analytics', label: 'Analytics', icon: BarChart3 },
          { id: 'settings', label: 'Settings', icon: Settings }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? isPaperTheme
                  ? 'bg-black text-white border-2 border-black'
                  : 'bg-indigo-600 text-white'
                : isPaperTheme
                  ? 'bg-white text-black border-2 border-black hover:bg-[#f0f0f0]'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tickets Tab */}
      {activeTab === 'tickets' && (
        <div>
          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 ${isPaperTheme ? 'text-black' : 'text-gray-400'}`} />
              <input
                type="text"
                placeholder="Search tickets..."
                className={`${getInputClass()} pl-10`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className={getInputClass()}
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="open">Open</option>
              <option value="in-progress">In Progress</option>
              <option value="waiting-customer">Waiting Customer</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
            <select
              className={getInputClass()}
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
            >
              <option value="all">All Priority</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>

          {/* Tickets Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTickets.map((ticket) => (
              <Card key={ticket.id} className={getCardClass()}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className={getHeadingClass(4)}>#{ticket.ticketNumber}</h3>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                        {ticket.subject}
                      </p>
                    </div>
                    <div className="flex flex-col gap-1">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(ticket.status)}
                          {ticket.status}
                        </div>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-600'}>{ticket.customerName}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-600'}>{ticket.customerEmail}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-600'}>
                        {new Date(ticket.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setSelectedTicket(ticket)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      {ticket.repeatCustomer && (
                        <div className="w-2 h-2 bg-orange-500 rounded-full" title="Repeat Customer" />
                      )}
                      {ticket.escalated && (
                        <div className="w-2 h-2 bg-red-500 rounded-full" title="Escalated" />
                      )}
                      <span className={`text-xs ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-500'}`}>
                        {ticket.responses.length} responses
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Agents Tab */}
      {activeTab === 'agents' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {agents.map((agent) => (
            <Card key={agent.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{agent.name}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {agent.email}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    agent.status === 'online' ? 'bg-green-100 text-green-800' :
                    agent.status === 'busy' ? 'bg-red-100 text-red-800' :
                    agent.status === 'away' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {agent.status}
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Active Tickets:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{agent.activeTickets}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Total Handled:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{agent.totalTickets}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{agent.averageRating}/5</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Response Time:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{agent.responseTime}min</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {agent.specialties.map((specialty, index) => (
                    <span
                      key={index}
                      className={`px-2 py-1 rounded text-xs ${isPaperTheme ? 'bg-[#e0e0e0] text-black border border-black' : 'bg-blue-100 text-blue-800'}`}
                    >
                      {specialty}
                    </span>
                  ))}
                </div>

                <Button size="sm" variant="outline" className="w-full">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Assign Ticket
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Tickets
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{tickets.length}</h2>
                </div>
                <MessageSquare className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Open Tickets
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {tickets.filter(t => t.status === 'open').length}
                  </h2>
                </div>
                <AlertCircle className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-red-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Resolution Rate
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {tickets.length > 0 ? Math.round((tickets.filter(t => t.status === 'resolved').length / tickets.length) * 100) : 0}%
                  </h2>
                </div>
                <CheckCircle className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Satisfaction
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {tickets.filter(t => t.satisfactionRating).length > 0 
                      ? (tickets.reduce((sum, t) => sum + (t.satisfactionRating || 0), 0) / tickets.filter(t => t.satisfactionRating).length).toFixed(1)
                      : 'N/A'
                    }
                  </h2>
                </div>
                <Star className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-yellow-600'}`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle>Ticket Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  Auto-Assignment
                </label>
                <select className={getInputClass()}>
                  <option>Round Robin</option>
                  <option>Least Loaded</option>
                  <option>Skill Based</option>
                  <option>Manual</option>
                </select>
              </div>
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  Escalation Threshold (hours)
                </label>
                <input
                  type="number"
                  className={getInputClass()}
                  defaultValue="24"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium ${isPaperTheme ? 'text-black' : 'text-gray-700'} mb-2`}>
                  SLA Response Time (hours)
                </label>
                <input
                  type="number"
                  className={getInputClass()}
                  defaultValue="4"
                />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <input type="checkbox" defaultChecked />
                <label className={`text-sm ${isPaperTheme ? 'text-black' : 'text-gray-700'}`}>
                  Email notifications for new tickets
                </label>
              </div>
              <div className="flex items-center gap-2">
                <input type="checkbox" defaultChecked />
                <label className={`text-sm ${isPaperTheme ? 'text-black' : 'text-gray-700'}`}>
                  SMS alerts for urgent tickets
                </label>
              </div>
              <div className="flex items-center gap-2">
                <input type="checkbox" />
                <label className={`text-sm ${isPaperTheme ? 'text-black' : 'text-gray-700'}`}>
                  Slack integration
                </label>
              </div>
              <div className="flex items-center gap-2">
                <input type="checkbox" defaultChecked />
                <label className={`text-sm ${isPaperTheme ? 'text-black' : 'text-gray-700'}`}>
                  Customer satisfaction surveys
                </label>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
