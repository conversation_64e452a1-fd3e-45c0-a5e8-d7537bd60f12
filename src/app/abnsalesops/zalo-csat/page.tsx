'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  MessageCircle,
  Plus,
  Send,
  Users,
  BarChart3,
  Settings,
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Calendar,
  Target,
  TrendingUp,
  Activity,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';

interface ZaloSurvey {
  id: string;
  name: string;
  description: string;
  questions: SurveyQuestion[];
  targetAudience: string[];
  status: 'draft' | 'active' | 'completed' | 'paused';
  createdAt: string;
  scheduledAt?: string;
  responses: number;
  completionRate: number;
  averageRating: number;
}

interface SurveyQuestion {
  id: string;
  type: 'rating' | 'multiple-choice' | 'text' | 'yes-no';
  question: string;
  options?: string[];
  required: boolean;
}

interface ZaloCampaign {
  id: string;
  name: string;
  type: 'survey' | 'feedback' | 'promotion' | 'announcement';
  message: string;
  targetSegment: string;
  scheduledAt: string;
  status: 'scheduled' | 'sent' | 'failed';
  recipients: number;
  delivered: number;
  opened: number;
  responded: number;
  createdAt: string;
}

interface ZaloContact {
  id: string;
  name: string;
  zaloId: string;
  phone: string;
  email?: string;
  segment: string;
  lastInteraction: string;
  responseRate: number;
  averageRating: number;
  tags: string[];
}

export default function ZaloCSATPage() {
  const [surveys, setSurveys] = useState<ZaloSurvey[]>([]);
  const [campaigns, setCampaigns] = useState<ZaloCampaign[]>([]);
  const [contacts, setContacts] = useState<ZaloContact[]>([]);
  const [activeTab, setActiveTab] = useState<'surveys' | 'campaigns' | 'contacts' | 'analytics'>('surveys');
  const [showCreateSurvey, setShowCreateSurvey] = useState(false);
  const [showCreateCampaign, setShowCreateCampaign] = useState(false);
  const [loading, setLoading] = useState(true);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      setSurveys([
        {
          id: 'survey_001',
          name: 'Customer Satisfaction Survey Q1 2024',
          description: 'Quarterly customer satisfaction assessment',
          questions: [
            {
              id: 'q1',
              type: 'rating',
              question: 'How satisfied are you with our service?',
              required: true
            },
            {
              id: 'q2',
              type: 'multiple-choice',
              question: 'What can we improve?',
              options: ['Response time', 'Product quality', 'Customer support', 'Pricing'],
              required: false
            }
          ],
          targetAudience: ['premium-customers', 'recent-buyers'],
          status: 'active',
          createdAt: '2024-01-15T10:00:00Z',
          responses: 156,
          completionRate: 78,
          averageRating: 4.2
        }
      ]);

      setCampaigns([
        {
          id: 'campaign_001',
          name: 'Monthly Feedback Request',
          type: 'survey',
          message: 'Xin chào! Chúng tôi muốn nghe ý kiến của bạn về dịch vụ. Vui lòng dành 2 phút để trả lời khảo sát.',
          targetSegment: 'active-customers',
          scheduledAt: '2024-01-25T09:00:00Z',
          status: 'sent',
          recipients: 500,
          delivered: 485,
          opened: 320,
          responded: 156,
          createdAt: '2024-01-20T14:30:00Z'
        }
      ]);

      setContacts([
        {
          id: 'contact_001',
          name: 'Nguyen Van An',
          zaloId: 'zalo_123456',
          phone: '+84901234567',
          email: '<EMAIL>',
          segment: 'premium-customers',
          lastInteraction: '2024-01-25T15:30:00Z',
          responseRate: 85,
          averageRating: 4.5,
          tags: ['loyal', 'high-value']
        }
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'sent': return 'bg-green-100 text-green-800';
      case 'draft': case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'paused': case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': case 'sent': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'draft': case 'scheduled': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'paused': case 'failed': return <Clock className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              Zalo CSAT for Businesses
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Send surveys and personalized campaigns via Zalo messaging platform
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button
              onClick={() => setShowCreateSurvey(true)}
              className={getButtonClass()}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Survey
            </Button>
            <Button
              onClick={() => setShowCreateCampaign(true)}
              className={getButtonClass()}
            >
              <Send className="h-4 w-4 mr-2" />
              New Campaign
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'surveys', label: 'Surveys', icon: MessageSquare },
          { id: 'campaigns', label: 'Campaigns', icon: Send },
          { id: 'contacts', label: 'Contacts', icon: Users },
          { id: 'analytics', label: 'Analytics', icon: BarChart3 }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? isPaperTheme
                  ? 'bg-black text-white border-2 border-black'
                  : 'bg-indigo-600 text-white'
                : isPaperTheme
                  ? 'bg-white text-black border-2 border-black hover:bg-[#f0f0f0]'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Surveys Tab */}
      {activeTab === 'surveys' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {surveys.map((survey) => (
            <Card key={survey.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{survey.name}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {survey.description}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(survey.status)}`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(survey.status)}
                      {survey.status}
                    </div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Questions:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.questions.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Responses:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.responses}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Completion Rate:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.completionRate}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.averageRating}/5</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Campaigns Tab */}
      {activeTab === 'campaigns' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {campaigns.map((campaign) => (
            <Card key={campaign.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{campaign.name}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {campaign.type}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(campaign.status)}
                      {campaign.status}
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <p className={`text-sm ${isPaperTheme ? 'text-black' : 'text-gray-700'} line-clamp-3`}>
                    {campaign.message}
                  </p>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Recipients:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{campaign.recipients}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Delivered:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{campaign.delivered}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Opened:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{campaign.opened}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Responded:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{campaign.responded}</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analytics
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Contacts Tab */}
      {activeTab === 'contacts' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {contacts.map((contact) => (
            <Card key={contact.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{contact.name}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {contact.phone}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    contact.responseRate > 70 ? 'bg-green-100 text-green-800' : 
                    contact.responseRate > 40 ? 'bg-yellow-100 text-yellow-800' : 
                    'bg-red-100 text-red-800'
                  }`}>
                    {contact.responseRate}% response
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Segment:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{contact.segment}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{contact.averageRating}/5</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Last Interaction:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>
                      {new Date(contact.lastInteraction).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {contact.tags.map((tag, index) => (
                    <span
                      key={index}
                      className={`px-2 py-1 rounded text-xs ${isPaperTheme ? 'bg-[#e0e0e0] text-black border border-black' : 'bg-blue-100 text-blue-800'}`}
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <Button size="sm" variant="outline" className="w-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Surveys
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{surveys.length}</h2>
                </div>
                <MessageSquare className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Responses
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {surveys.reduce((sum, s) => sum + s.responses, 0)}
                  </h2>
                </div>
                <Users className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Completion Rate
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {surveys.length > 0 ? Math.round(surveys.reduce((sum, s) => sum + s.completionRate, 0) / surveys.length) : 0}%
                  </h2>
                </div>
                <Target className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-purple-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. CSAT Score
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {surveys.length > 0 ? (surveys.reduce((sum, s) => sum + s.averageRating, 0) / surveys.length).toFixed(1) : 'N/A'}
                  </h2>
                </div>
                <Star className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-yellow-600'}`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
