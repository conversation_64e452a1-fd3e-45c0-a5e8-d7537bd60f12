'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  Store,
  Plus,
  Monitor,
  BarChart3,
  Settings,
  Star,
  Users,
  MapPin,
  Calendar,
  TrendingUp,
  Activity,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  Wifi,
  Tablet,
  CreditCard
} from 'lucide-react';

interface POSLocation {
  id: string;
  name: string;
  address: string;
  city: string;
  manager: string;
  posDevices: POSDevice[];
  status: 'active' | 'inactive' | 'maintenance';
  totalSurveys: number;
  todayResponses: number;
  averageRating: number;
  lastSync: string;
}

interface POSDevice {
  id: string;
  locationId: string;
  deviceName: string;
  deviceType: 'tablet' | 'kiosk' | 'terminal';
  status: 'online' | 'offline' | 'error';
  currentSurvey?: string;
  responsesToday: number;
  lastResponse: string;
  batteryLevel?: number;
}

interface InstoreSurvey {
  id: string;
  title: string;
  description: string;
  questions: SurveyQuestion[];
  targetLocations: string[];
  status: 'draft' | 'active' | 'completed' | 'paused';
  createdAt: string;
  startDate?: string;
  endDate?: string;
  totalResponses: number;
  averageRating: number;
  completionRate: number;
  averageTime: number;
}

interface SurveyQuestion {
  id: string;
  type: 'rating' | 'multiple-choice' | 'text' | 'yes-no' | 'emoji';
  question: string;
  options?: string[];
  required: boolean;
}

export default function InstoreSurveysPage() {
  const [locations, setLocations] = useState<POSLocation[]>([]);
  const [devices, setDevices] = useState<POSDevice[]>([]);
  const [surveys, setSurveys] = useState<InstoreSurvey[]>([]);
  const [activeTab, setActiveTab] = useState<'locations' | 'devices' | 'surveys' | 'analytics'>('locations');
  const [loading, setLoading] = useState(true);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      setLocations([
        {
          id: 'loc_001',
          name: 'ABN Store District 1',
          address: '123 Nguyen Hue Street',
          city: 'Ho Chi Minh City',
          manager: 'Nguyen Van An',
          posDevices: [],
          status: 'active',
          totalSurveys: 1250,
          todayResponses: 45,
          averageRating: 4.3,
          lastSync: '2024-01-25T16:30:00Z'
        },
        {
          id: 'loc_002',
          name: 'ABN Store District 3',
          address: '456 Le Van Sy Street',
          city: 'Ho Chi Minh City',
          manager: 'Tran Thi Mai',
          posDevices: [],
          status: 'active',
          totalSurveys: 890,
          todayResponses: 32,
          averageRating: 4.1,
          lastSync: '2024-01-25T16:25:00Z'
        }
      ]);

      setDevices([
        {
          id: 'device_001',
          locationId: 'loc_001',
          deviceName: 'Checkout Counter 1',
          deviceType: 'tablet',
          status: 'online',
          currentSurvey: 'survey_001',
          responsesToday: 23,
          lastResponse: '2024-01-25T16:15:00Z',
          batteryLevel: 85
        },
        {
          id: 'device_002',
          locationId: 'loc_001',
          deviceName: 'Customer Service Kiosk',
          deviceType: 'kiosk',
          status: 'online',
          currentSurvey: 'survey_001',
          responsesToday: 22,
          lastResponse: '2024-01-25T16:20:00Z'
        },
        {
          id: 'device_003',
          locationId: 'loc_002',
          deviceName: 'Exit Survey Terminal',
          deviceType: 'terminal',
          status: 'offline',
          responsesToday: 0,
          lastResponse: '2024-01-24T18:30:00Z'
        }
      ]);

      setSurveys([
        {
          id: 'survey_001',
          title: 'Store Experience Survey',
          description: 'Quick feedback on in-store shopping experience',
          questions: [
            {
              id: 'q1',
              type: 'emoji',
              question: 'How was your shopping experience today?',
              required: true
            },
            {
              id: 'q2',
              type: 'rating',
              question: 'Rate our customer service',
              required: true
            },
            {
              id: 'q3',
              type: 'multiple-choice',
              question: 'What brought you to our store today?',
              options: ['Browsing', 'Specific Purchase', 'Sale/Promotion', 'Recommendation'],
              required: false
            }
          ],
          targetLocations: ['loc_001', 'loc_002'],
          status: 'active',
          createdAt: '2024-01-20T10:00:00Z',
          startDate: '2024-01-22T00:00:00Z',
          totalResponses: 567,
          averageRating: 4.2,
          completionRate: 89,
          averageTime: 45
        }
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'online': return 'bg-green-100 text-green-800';
      case 'inactive': case 'offline': return 'bg-red-100 text-red-800';
      case 'maintenance': case 'error': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': case 'online': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'inactive': case 'offline': return <Clock className="h-4 w-4 text-red-500" />;
      case 'maintenance': case 'error': return <Clock className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'tablet': return <Tablet className="h-5 w-5" />;
      case 'kiosk': return <Monitor className="h-5 w-5" />;
      case 'terminal': return <CreditCard className="h-5 w-5" />;
      default: return <Monitor className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              In-store Surveys
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Survey at points of sale via POS machines with real-time data collection
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button className={getButtonClass()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Location
            </Button>
            <Button className={getButtonClass()}>
              <Plus className="h-4 w-4 mr-2" />
              Create Survey
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'locations', label: 'Locations', icon: Store },
          { id: 'devices', label: 'POS Devices', icon: Monitor },
          { id: 'surveys', label: 'Surveys', icon: BarChart3 },
          { id: 'analytics', label: 'Analytics', icon: TrendingUp }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? isPaperTheme
                  ? 'bg-black text-white border-2 border-black'
                  : 'bg-indigo-600 text-white'
                : isPaperTheme
                  ? 'bg-white text-black border-2 border-black hover:bg-[#f0f0f0]'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Locations Tab */}
      {activeTab === 'locations' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {locations.map((location) => (
            <Card key={location.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{location.name}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {location.address}
                    </p>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {location.city}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(location.status)}`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(location.status)}
                      {location.status}
                    </div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Manager:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{location.manager}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Total Surveys:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{location.totalSurveys}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Today:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{location.todayResponses}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{location.averageRating}/5</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Last Sync:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>
                      {new Date(location.lastSync).toLocaleTimeString()}
                    </span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Devices Tab */}
      {activeTab === 'devices' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {devices.map((device) => (
            <Card key={device.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded ${isPaperTheme ? 'bg-[#e0e0e0] border border-black' : 'bg-gray-100'}`}>
                      {getDeviceIcon(device.deviceType)}
                    </div>
                    <div>
                      <h3 className={getHeadingClass(4)}>{device.deviceName}</h3>
                      <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                        {device.deviceType}
                      </p>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(device.status)}`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(device.status)}
                      {device.status}
                    </div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Location:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>
                      {locations.find(l => l.id === device.locationId)?.name}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Responses Today:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{device.responsesToday}</span>
                  </div>
                  {device.batteryLevel && (
                    <div className="flex justify-between text-sm">
                      <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Battery:</span>
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{device.batteryLevel}%</span>
                    </div>
                  )}
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Last Response:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>
                      {device.lastResponse ? new Date(device.lastResponse).toLocaleTimeString() : 'Never'}
                    </span>
                  </div>
                </div>

                {device.batteryLevel && (
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div 
                      className={`h-2 rounded-full ${
                        device.batteryLevel > 50 ? 'bg-green-500' : 
                        device.batteryLevel > 20 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${device.batteryLevel}%` }}
                    ></div>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Wifi className="h-4 w-4 mr-2" />
                    Sync
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Surveys Tab */}
      {activeTab === 'surveys' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {surveys.map((survey) => (
            <Card key={survey.id} className={getCardClass()}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={getHeadingClass(4)}>{survey.title}</h3>
                    <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                      {survey.description}
                    </p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(survey.status)}`}>
                    {survey.status}
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Questions:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.questions.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Responses:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.totalResponses}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Completion Rate:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.completionRate}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Time:</span>
                    <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.averageTime}s</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className={isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}>Avg. Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className={isPaperTheme ? 'text-black' : 'text-gray-900'}>{survey.averageRating}/5</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Total Locations
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>{locations.length}</h2>
                </div>
                <Store className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Active Devices
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {devices.filter(d => d.status === 'online').length}
                  </h2>
                </div>
                <Monitor className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Today's Responses
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {locations.reduce((sum, l) => sum + l.todayResponses, 0)}
                  </h2>
                </div>
                <Users className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-purple-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                    Avg. Rating
                  </p>
                  <h2 className={`${getHeadingClass(2)} mt-1`}>
                    {locations.length > 0 ? (locations.reduce((sum, l) => sum + l.averageRating, 0) / locations.length).toFixed(1) : 'N/A'}
                  </h2>
                </div>
                <Star className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-yellow-600'}`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
