'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useThemeStyles } from '@/themes/ThemeProvider';
import {
  Target,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
  Activity,
  Clock,
  Users,
  Star,
  Heart,
  Zap,
  DollarSign,
  Phone,
  CheckCircle,
  Timer,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';
import {
  ResponsiveContainer,
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  AreaChart,
  Area,
  BarChart as Re<PERSON>rtsBar<PERSON>hart,
  <PERSON>,
  <PERSON><PERSON> as <PERSON>charts<PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';

interface MetricData {
  id: string;
  name: string;
  value: number;
  target: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  change: number;
  description: string;
  category: 'satisfaction' | 'efficiency' | 'financial' | 'operational';
  lastUpdated: string;
  historicalData: Array<{
    date: string;
    value: number;
  }>;
}

interface DashboardMetrics {
  csat: MetricData;
  nps: MetricData;
  ces: MetricData;
  clv: MetricData;
  frt: MetricData;
  fcr: MetricData;
  aht: MetricData;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export default function MetricsDashboardPage() {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  const { getCardClass, getHeadingClass, getInputClass, getButtonClass, isPaperTheme } = useThemeStyles();

  useEffect(() => {
    setMounted(true);
    fetchMetrics();
  }, [selectedPeriod]);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/abnsalesops/metrics-dashboard?period=${selectedPeriod}`);
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
      }
    } catch (error) {
      console.error('Error fetching metrics:', error);
      // Fallback data
      setMetrics({
        csat: {
          id: 'csat',
          name: 'CSAT',
          value: 4.2,
          target: 4.5,
          unit: '/5',
          trend: 'up',
          change: 0.3,
          description: 'Customer Satisfaction Rating',
          category: 'satisfaction',
          lastUpdated: new Date().toISOString(),
          historicalData: [
            { date: '2024-01-01', value: 3.9 },
            { date: '2024-01-08', value: 4.0 },
            { date: '2024-01-15', value: 4.1 },
            { date: '2024-01-22', value: 4.2 }
          ]
        },
        nps: {
          id: 'nps',
          name: 'NPS',
          value: 65,
          target: 70,
          unit: '',
          trend: 'up',
          change: 8,
          description: 'Net Promoter Score',
          category: 'satisfaction',
          lastUpdated: new Date().toISOString(),
          historicalData: [
            { date: '2024-01-01', value: 57 },
            { date: '2024-01-08', value: 60 },
            { date: '2024-01-15', value: 63 },
            { date: '2024-01-22', value: 65 }
          ]
        },
        ces: {
          id: 'ces',
          name: 'CES',
          value: 2.1,
          target: 2.0,
          unit: '/7',
          trend: 'down',
          change: -0.2,
          description: 'Customer Effort Score',
          category: 'satisfaction',
          lastUpdated: new Date().toISOString(),
          historicalData: [
            { date: '2024-01-01', value: 2.3 },
            { date: '2024-01-08', value: 2.2 },
            { date: '2024-01-15', value: 2.1 },
            { date: '2024-01-22', value: 2.1 }
          ]
        },
        clv: {
          id: 'clv',
          name: 'CLV',
          value: 15420,
          target: 18000,
          unit: '$',
          trend: 'up',
          change: 1250,
          description: 'Customer Lifetime Value',
          category: 'financial',
          lastUpdated: new Date().toISOString(),
          historicalData: [
            { date: '2024-01-01', value: 14170 },
            { date: '2024-01-08', value: 14580 },
            { date: '2024-01-15', value: 15000 },
            { date: '2024-01-22', value: 15420 }
          ]
        },
        frt: {
          id: 'frt',
          name: 'FRT',
          value: 12,
          target: 10,
          unit: 'min',
          trend: 'down',
          change: -3,
          description: 'First Response Time',
          category: 'efficiency',
          lastUpdated: new Date().toISOString(),
          historicalData: [
            { date: '2024-01-01', value: 15 },
            { date: '2024-01-08', value: 14 },
            { date: '2024-01-15', value: 13 },
            { date: '2024-01-22', value: 12 }
          ]
        },
        fcr: {
          id: 'fcr',
          name: 'FCR',
          value: 78,
          target: 85,
          unit: '%',
          trend: 'up',
          change: 5,
          description: 'First Contact Resolution',
          category: 'efficiency',
          lastUpdated: new Date().toISOString(),
          historicalData: [
            { date: '2024-01-01', value: 73 },
            { date: '2024-01-08', value: 75 },
            { date: '2024-01-15', value: 76 },
            { date: '2024-01-22', value: 78 }
          ]
        },
        aht: {
          id: 'aht',
          name: 'AHT',
          value: 8.5,
          target: 7.0,
          unit: 'min',
          trend: 'down',
          change: -1.2,
          description: 'Average Handling Time',
          category: 'efficiency',
          lastUpdated: new Date().toISOString(),
          historicalData: [
            { date: '2024-01-01', value: 9.7 },
            { date: '2024-01-08', value: 9.2 },
            { date: '2024-01-15', value: 8.8 },
            { date: '2024-01-22', value: 8.5 }
          ]
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const getMetricIcon = (metricId: string) => {
    switch (metricId) {
      case 'csat': return <Star className="h-6 w-6" />;
      case 'nps': return <Heart className="h-6 w-6" />;
      case 'ces': return <Zap className="h-6 w-6" />;
      case 'clv': return <DollarSign className="h-6 w-6" />;
      case 'frt': return <Clock className="h-6 w-6" />;
      case 'fcr': return <CheckCircle className="h-6 w-6" />;
      case 'aht': return <Timer className="h-6 w-6" />;
      default: return <Activity className="h-6 w-6" />;
    }
  };

  const getMetricColor = (metricId: string) => {
    switch (metricId) {
      case 'csat': return isPaperTheme ? 'text-black' : 'text-yellow-600';
      case 'nps': return isPaperTheme ? 'text-black' : 'text-pink-600';
      case 'ces': return isPaperTheme ? 'text-black' : 'text-purple-600';
      case 'clv': return isPaperTheme ? 'text-black' : 'text-green-600';
      case 'frt': return isPaperTheme ? 'text-black' : 'text-blue-600';
      case 'fcr': return isPaperTheme ? 'text-black' : 'text-emerald-600';
      case 'aht': return isPaperTheme ? 'text-black' : 'text-orange-600';
      default: return isPaperTheme ? 'text-black' : 'text-gray-600';
    }
  };

  const getTrendIcon = (trend: string, change: number) => {
    if (trend === 'up') {
      return <TrendingUp className={`h-4 w-4 ${change > 0 ? 'text-green-500' : 'text-red-500'}`} />;
    } else if (trend === 'down') {
      return <TrendingDown className={`h-4 w-4 ${change < 0 ? 'text-green-500' : 'text-red-500'}`} />;
    }
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  const isMetricGood = (metric: MetricData) => {
    // For CES and response times, lower is better
    if (['ces', 'frt', 'aht'].includes(metric.id)) {
      return metric.value <= metric.target;
    }
    // For others, higher is better
    return metric.value >= metric.target;
  };

  const filteredMetrics = metrics ? Object.values(metrics).filter(metric => 
    selectedCategory === 'all' || metric.category === selectedCategory
  ) : [];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className={`px-4 sm:px-6 lg:px-8 ${isPaperTheme ? 'py-4' : 'py-8'}`}>
      {/* Header */}
      <div className={isPaperTheme ? 'mb-6' : 'mb-8'}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className={isPaperTheme ? getHeadingClass(3) : getHeadingClass(1)}>
              Sales Metrics Dashboard
            </h1>
            <p className={`${isPaperTheme ? 'mt-1' : 'mt-2'} ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
              Track CSAT, NPS, CES, CLV, FRT, FCR, and AHT with real-time analytics
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <select
              className={getInputClass()}
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <select
              className={getInputClass()}
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option value="all">All Categories</option>
              <option value="satisfaction">Satisfaction</option>
              <option value="efficiency">Efficiency</option>
              <option value="financial">Financial</option>
              <option value="operational">Operational</option>
            </select>
            <Button onClick={fetchMetrics} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button className={getButtonClass()}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {filteredMetrics.map((metric) => (
          <Card key={metric.id} className={getCardClass()}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-full ${isPaperTheme ? 'bg-[#e0e0e0] border-2 border-black' : 'bg-gray-50'}`}>
                  <div className={getMetricColor(metric.id)}>
                    {getMetricIcon(metric.id)}
                  </div>
                </div>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                  isMetricGood(metric) 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {isMetricGood(metric) ? 'On Target' : 'Below Target'}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className={`${getHeadingClass(4)} text-sm font-medium`}>
                    {metric.name}
                  </h3>
                  <div className="flex items-center gap-1">
                    {getTrendIcon(metric.trend, metric.change)}
                    <span className={`text-xs ${
                      (metric.trend === 'up' && metric.change > 0) || (metric.trend === 'down' && metric.change < 0)
                        ? 'text-green-600' 
                        : 'text-red-600'
                    }`}>
                      {Math.abs(metric.change)}{metric.unit}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-baseline gap-2">
                  <span className={`${getHeadingClass(2)} text-2xl font-bold`}>
                    {metric.unit === '$' ? '$' : ''}{metric.value.toLocaleString()}{metric.unit !== '$' ? metric.unit : ''}
                  </span>
                  <span className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-500'}`}>
                    / {metric.unit === '$' ? '$' : ''}{metric.target.toLocaleString()}{metric.unit !== '$' ? metric.unit : ''}
                  </span>
                </div>
                
                <p className={`text-xs ${isPaperTheme ? 'text-[#666666] font-bold' : 'text-gray-600'}`}>
                  {metric.description}
                </p>
                
                {/* Progress bar */}
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      isMetricGood(metric) ? 'bg-green-500' : 'bg-red-500'
                    }`}
                    style={{ 
                      width: `${Math.min(100, (metric.value / metric.target) * 100)}%` 
                    }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      {mounted && metrics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Satisfaction Metrics Trend */}
          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className={`h-5 w-5 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
                Satisfaction Metrics Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsLineChart>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    data={metrics.csat.historicalData}
                    stroke="#8884d8" 
                    name="CSAT"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    data={metrics.nps.historicalData}
                    stroke="#82ca9d" 
                    name="NPS"
                  />
                </RechartsLineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Efficiency Metrics */}
          <Card className={getCardClass()}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className={`h-5 w-5 ${isPaperTheme ? 'text-black' : 'text-indigo-600'}`} />
                Efficiency Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsBarChart data={[
                  { name: 'FRT', value: metrics.frt.value, target: metrics.frt.target },
                  { name: 'FCR', value: metrics.fcr.value, target: metrics.fcr.target },
                  { name: 'AHT', value: metrics.aht.value, target: metrics.aht.target }
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#8884d8" name="Current" />
                  <Bar dataKey="target" fill="#82ca9d" name="Target" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Metric Categories Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className={getCardClass()}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                  Satisfaction Score
                </p>
                <h2 className={`${getHeadingClass(2)} mt-1`}>
                  {metrics ? ((metrics.csat.value + metrics.nps.value/20 + (7-metrics.ces.value)/7*5) / 3).toFixed(1) : 'N/A'}
                </h2>
              </div>
              <Star className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-yellow-600'}`} />
            </div>
          </CardContent>
        </Card>

        <Card className={getCardClass()}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                  Efficiency Score
                </p>
                <h2 className={`${getHeadingClass(2)} mt-1`}>
                  {metrics ? Math.round((metrics.fcr.value + (100 - metrics.frt.value*2) + (100 - metrics.aht.value*5)) / 3) : 'N/A'}
                </h2>
              </div>
              <Zap className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-blue-600'}`} />
            </div>
          </CardContent>
        </Card>

        <Card className={getCardClass()}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                  Financial Impact
                </p>
                <h2 className={`${getHeadingClass(2)} mt-1`}>
                  ${metrics ? (metrics.clv.value / 1000).toFixed(0) : 0}K
                </h2>
              </div>
              <DollarSign className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-green-600'}`} />
            </div>
          </CardContent>
        </Card>

        <Card className={getCardClass()}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isPaperTheme ? 'text-[#666666] font-bold uppercase tracking-wide' : 'text-gray-500'}`}>
                  Overall Health
                </p>
                <h2 className={`${getHeadingClass(2)} mt-1`}>
                  {metrics ? Math.round(
                    (Object.values(metrics).reduce((sum, metric) => 
                      sum + (isMetricGood(metric) ? 100 : 50), 0) / Object.values(metrics).length)
                  ) : 0}%
                </h2>
              </div>
              <Activity className={`h-8 w-8 ${isPaperTheme ? 'text-black' : 'text-purple-600'}`} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
