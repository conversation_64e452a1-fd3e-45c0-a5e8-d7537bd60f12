import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const DATA_FILE = path.join(process.cwd(), 'data/apps/abnsalesops/metrics-dashboard.json');

function readData() {
  try {
    if (!fs.existsSync(DATA_FILE)) {
      const initialData = {
        metrics: {},
        aggregatedMetrics: {},
        trends: {},
        benchmarks: {},
        alerts: [],
        goals: {},
        lastUpdated: new Date().toISOString()
      };
      fs.writeFileSync(DATA_FILE, JSON.stringify(initialData, null, 2));
      return initialData;
    }
    const data = fs.readFileSync(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading metrics data:', error);
    return { metrics: {}, aggregatedMetrics: {}, trends: {}, benchmarks: {}, alerts: [], goals: {} };
  }
}

function writeData(data: any) {
  try {
    const dir = path.dirname(DATA_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing metrics data:', error);
    throw error;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d';
    
    const data = readData();
    
    // Filter data based on period if needed
    let filteredData = data;
    
    switch (period) {
      case '7d':
        // Return last 7 days of data
        break;
      case '30d':
        // Return last 30 days of data (default)
        break;
      case '90d':
        // Return last 90 days of data
        break;
      case '1y':
        // Return last year of data
        break;
    }
    
    return NextResponse.json(filteredData.metrics);
  } catch (error) {
    console.error('Error fetching metrics:', error);
    return NextResponse.json({ error: 'Failed to fetch metrics' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const metricsUpdate = await request.json();
    const data = readData();
    
    // Update metrics with new values
    Object.keys(metricsUpdate).forEach(metricKey => {
      if (data.metrics[metricKey]) {
        data.metrics[metricKey] = {
          ...data.metrics[metricKey],
          ...metricsUpdate[metricKey],
          lastUpdated: new Date().toISOString()
        };
      }
    });
    
    // Recalculate aggregated metrics
    const metrics = data.metrics;
    data.aggregatedMetrics = {
      satisfactionScore: metrics.csat && metrics.nps && metrics.ces ? 
        ((metrics.csat.value + metrics.nps.value/20 + (7-metrics.ces.value)/7*5) / 3) : 0,
      efficiencyScore: metrics.fcr && metrics.frt && metrics.aht ?
        Math.round((metrics.fcr.value + (100 - metrics.frt.value*2) + (100 - metrics.aht.value*5)) / 3) : 0,
      financialImpact: metrics.clv ? metrics.clv.value : 0,
      overallHealth: Object.values(metrics).length > 0 ?
        Math.round(Object.values(metrics).reduce((sum: number, metric: any) => {
          const isGood = ['ces', 'frt', 'aht'].includes(metric.id) ? 
            metric.value <= metric.target : metric.value >= metric.target;
          return sum + (isGood ? 100 : 50);
        }, 0) / Object.values(metrics).length) : 0
    };
    
    data.lastUpdated = new Date().toISOString();
    
    writeData(data);
    
    return NextResponse.json(data.metrics);
  } catch (error) {
    console.error('Error updating metrics:', error);
    return NextResponse.json({ error: 'Failed to update metrics' }, { status: 500 });
  }
}
