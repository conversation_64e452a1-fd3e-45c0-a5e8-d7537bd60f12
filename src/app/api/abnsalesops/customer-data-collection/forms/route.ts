import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const DATA_FILE = path.join(process.cwd(), 'data/apps/abnsalesops/customer-data-collection.json');

interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'textarea' | 'checkbox' | 'date';
  required: boolean;
  options?: string[];
  placeholder?: string;
}

interface CollectionForm {
  id: string;
  name: string;
  description: string;
  fields: FormField[];
  isActive: boolean;
  submissions: number;
  createdAt: string;
}

function readData() {
  try {
    if (!fs.existsSync(DATA_FILE)) {
      const initialData = {
        customers: [],
        forms: [],
        analytics: {
          totalCustomers: 0,
          activeForms: 0,
          conversionRate: 0,
          averageScore: 0,
          statusDistribution: {},
          sourceDistribution: {},
          industryDistribution: {}
        },
        settings: {
          autoAssignLeads: true,
          emailNotifications: true,
          scoreThreshold: 70,
          autoFollowUpDays: 3,
          duplicateDetection: true,
          dataRetentionDays: 365
        }
      };
      fs.writeFileSync(DATA_FILE, JSON.stringify(initialData, null, 2));
      return initialData;
    }
    const data = fs.readFileSync(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading data:', error);
    return { customers: [], forms: [], analytics: {}, settings: {} };
  }
}

function writeData(data: any) {
  try {
    // Ensure directory exists
    const dir = path.dirname(DATA_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing data:', error);
    throw error;
  }
}

function generateId(): string {
  return 'form_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateFieldId(): string {
  return 'field_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

export async function GET() {
  try {
    const data = readData();
    return NextResponse.json(data.forms);
  } catch (error) {
    console.error('Error fetching forms:', error);
    return NextResponse.json({ error: 'Failed to fetch forms' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.json();
    const data = readData();

    // Generate IDs for fields if not provided
    const fieldsWithIds = formData.fields.map((field: any) => ({
      ...field,
      id: field.id || generateFieldId()
    }));

    const newForm: CollectionForm = {
      id: generateId(),
      name: formData.name || '',
      description: formData.description || '',
      fields: fieldsWithIds,
      isActive: formData.isActive !== undefined ? formData.isActive : true,
      submissions: 0,
      createdAt: new Date().toISOString()
    };

    data.forms.unshift(newForm);

    // Update analytics
    data.analytics.activeForms = data.forms.filter((f: CollectionForm) => f.isActive).length;

    writeData(data);

    return NextResponse.json(newForm, { status: 201 });
  } catch (error) {
    console.error('Error creating form:', error);
    return NextResponse.json({ error: 'Failed to create form' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const formData = await request.json();
    const data = readData();

    const formIndex = data.forms.findIndex((f: CollectionForm) => f.id === formData.id);
    if (formIndex === -1) {
      return NextResponse.json({ error: 'Form not found' }, { status: 404 });
    }

    // Generate IDs for new fields if not provided
    const fieldsWithIds = formData.fields.map((field: any) => ({
      ...field,
      id: field.id || generateFieldId()
    }));

    const updatedForm = {
      ...data.forms[formIndex],
      ...formData,
      fields: fieldsWithIds
    };

    data.forms[formIndex] = updatedForm;

    // Update analytics
    data.analytics.activeForms = data.forms.filter((f: CollectionForm) => f.isActive).length;

    writeData(data);

    return NextResponse.json(updatedForm);
  } catch (error) {
    console.error('Error updating form:', error);
    return NextResponse.json({ error: 'Failed to update form' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const formId = searchParams.get('id');

    if (!formId) {
      return NextResponse.json({ error: 'Form ID is required' }, { status: 400 });
    }

    const data = readData();
    const formIndex = data.forms.findIndex((f: CollectionForm) => f.id === formId);

    if (formIndex === -1) {
      return NextResponse.json({ error: 'Form not found' }, { status: 404 });
    }

    data.forms.splice(formIndex, 1);

    // Update analytics
    data.analytics.activeForms = data.forms.filter((f: CollectionForm) => f.isActive).length;

    writeData(data);

    return NextResponse.json({ message: 'Form deleted successfully' });
  } catch (error) {
    console.error('Error deleting form:', error);
    return NextResponse.json({ error: 'Failed to delete form' }, { status: 500 });
  }
}
