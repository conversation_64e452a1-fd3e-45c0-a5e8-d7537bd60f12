import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const DATA_FILE = path.join(process.cwd(), 'data/apps/abnsalesops/customer-data-collection.json');

interface CustomerData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  position: string;
  industry: string;
  location: string;
  website: string;
  source: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  notes: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  lastContact: string;
  score: number;
}

function readData() {
  try {
    if (!fs.existsSync(DATA_FILE)) {
      const initialData = {
        customers: [],
        forms: [],
        analytics: {
          totalCustomers: 0,
          activeForms: 0,
          conversionRate: 0,
          averageScore: 0,
          statusDistribution: {},
          sourceDistribution: {},
          industryDistribution: {}
        },
        settings: {
          autoAssignLeads: true,
          emailNotifications: true,
          scoreThreshold: 70,
          autoFollowUpDays: 3,
          duplicateDetection: true,
          dataRetentionDays: 365
        }
      };
      fs.writeFileSync(DATA_FILE, JSON.stringify(initialData, null, 2));
      return initialData;
    }
    const data = fs.readFileSync(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading data:', error);
    return { customers: [], forms: [], analytics: {}, settings: {} };
  }
}

function writeData(data: any) {
  try {
    // Ensure directory exists
    const dir = path.dirname(DATA_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing data:', error);
    throw error;
  }
}

function generateId(): string {
  return 'cust_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function calculateScore(customer: Partial<CustomerData>): number {
  let score = 50; // Base score

  // Industry scoring
  const highValueIndustries = ['Technology', 'Healthcare', 'Finance'];
  if (customer.industry && highValueIndustries.includes(customer.industry)) {
    score += 20;
  }

  // Position scoring
  const seniorPositions = ['CEO', 'CTO', 'Director', 'Manager', 'VP'];
  if (customer.position && seniorPositions.some(pos => customer.position!.includes(pos))) {
    score += 15;
  }

  // Contact information completeness
  if (customer.email) score += 5;
  if (customer.phone) score += 5;
  if (customer.website) score += 5;

  // Company information
  if (customer.company) score += 10;

  return Math.min(score, 100);
}

export async function GET() {
  try {
    const data = readData();
    return NextResponse.json(data.customers);
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json({ error: 'Failed to fetch customers' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const customerData = await request.json();
    const data = readData();

    // Check for duplicate email if duplicate detection is enabled
    if (data.settings.duplicateDetection && customerData.email) {
      const existingCustomer = data.customers.find((c: CustomerData) => c.email === customerData.email);
      if (existingCustomer) {
        return NextResponse.json({ error: 'Customer with this email already exists' }, { status: 400 });
      }
    }

    const newCustomer: CustomerData = {
      id: generateId(),
      firstName: customerData.firstName || '',
      lastName: customerData.lastName || '',
      email: customerData.email || '',
      phone: customerData.phone || '',
      company: customerData.company || '',
      position: customerData.position || '',
      industry: customerData.industry || '',
      location: customerData.location || '',
      website: customerData.website || '',
      source: customerData.source || 'Manual Entry',
      status: customerData.status || 'new',
      notes: customerData.notes || '',
      tags: customerData.tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastContact: customerData.lastContact || '',
      score: calculateScore(customerData)
    };

    data.customers.unshift(newCustomer);

    // Update analytics
    data.analytics.totalCustomers = data.customers.length;
    data.analytics.averageScore = Math.round(
      data.customers.reduce((sum: number, c: CustomerData) => sum + c.score, 0) / data.customers.length
    );
    data.analytics.conversionRate = Math.round(
      (data.customers.filter((c: CustomerData) => c.status === 'converted').length / data.customers.length) * 100
    );

    // Update status distribution
    data.analytics.statusDistribution = data.customers.reduce((acc: any, customer: CustomerData) => {
      acc[customer.status] = (acc[customer.status] || 0) + 1;
      return acc;
    }, {});

    // Update source distribution
    data.analytics.sourceDistribution = data.customers.reduce((acc: any, customer: CustomerData) => {
      acc[customer.source] = (acc[customer.source] || 0) + 1;
      return acc;
    }, {});

    // Update industry distribution
    data.analytics.industryDistribution = data.customers.reduce((acc: any, customer: CustomerData) => {
      if (customer.industry) {
        acc[customer.industry] = (acc[customer.industry] || 0) + 1;
      }
      return acc;
    }, {});

    writeData(data);

    return NextResponse.json(newCustomer, { status: 201 });
  } catch (error) {
    console.error('Error creating customer:', error);
    return NextResponse.json({ error: 'Failed to create customer' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const customerData = await request.json();
    const data = readData();

    const customerIndex = data.customers.findIndex((c: CustomerData) => c.id === customerData.id);
    if (customerIndex === -1) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 });
    }

    const updatedCustomer = {
      ...data.customers[customerIndex],
      ...customerData,
      updatedAt: new Date().toISOString(),
      score: calculateScore(customerData)
    };

    data.customers[customerIndex] = updatedCustomer;

    // Update analytics
    data.analytics.averageScore = Math.round(
      data.customers.reduce((sum: number, c: CustomerData) => sum + c.score, 0) / data.customers.length
    );
    data.analytics.conversionRate = Math.round(
      (data.customers.filter((c: CustomerData) => c.status === 'converted').length / data.customers.length) * 100
    );

    writeData(data);

    return NextResponse.json(updatedCustomer);
  } catch (error) {
    console.error('Error updating customer:', error);
    return NextResponse.json({ error: 'Failed to update customer' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('id');

    if (!customerId) {
      return NextResponse.json({ error: 'Customer ID is required' }, { status: 400 });
    }

    const data = readData();
    const customerIndex = data.customers.findIndex((c: CustomerData) => c.id === customerId);

    if (customerIndex === -1) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 });
    }

    data.customers.splice(customerIndex, 1);

    // Update analytics
    data.analytics.totalCustomers = data.customers.length;
    if (data.customers.length > 0) {
      data.analytics.averageScore = Math.round(
        data.customers.reduce((sum: number, c: CustomerData) => sum + c.score, 0) / data.customers.length
      );
      data.analytics.conversionRate = Math.round(
        (data.customers.filter((c: CustomerData) => c.status === 'converted').length / data.customers.length) * 100
      );
    } else {
      data.analytics.averageScore = 0;
      data.analytics.conversionRate = 0;
    }

    writeData(data);

    return NextResponse.json({ message: 'Customer deleted successfully' });
  } catch (error) {
    console.error('Error deleting customer:', error);
    return NextResponse.json({ error: 'Failed to delete customer' }, { status: 500 });
  }
}
