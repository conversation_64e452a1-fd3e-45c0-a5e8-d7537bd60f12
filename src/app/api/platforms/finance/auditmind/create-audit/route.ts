import { NextRequest, NextResponse } from 'next/server';
import { AuditCreationService } from '@/app/platforms/finance/auditmind/services/auditCreationService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { clientInfo, selectedCategories, scheduleInfo } = body;
    
    if (!clientInfo || !selectedCategories || !scheduleInfo) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate client info
    if (!clientInfo.name || !clientInfo.industry || !clientInfo.size ||
        !clientInfo.contactPerson || !clientInfo.email || !clientInfo.auditTypes ||
        !Array.isArray(clientInfo.auditTypes) || clientInfo.auditTypes.length === 0) {
      return NextResponse.json(
        { error: 'Missing required client information or audit types' },
        { status: 400 }
      );
    }

    // Validate schedule info
    if (!scheduleInfo.startDate || !scheduleInfo.expectedCompletion || !scheduleInfo.leadAuditor) {
      return NextResponse.json(
        { error: 'Missing required schedule information' },
        { status: 400 }
      );
    }

    // Validate categories
    if (!Array.isArray(selectedCategories) || selectedCategories.length === 0) {
      return NextResponse.json(
        { error: 'At least one audit category must be selected' },
        { status: 400 }
      );
    }

    // Create the audit
    const result = await AuditCreationService.createAudit({
      clientInfo,
      selectedCategories,
      scheduleInfo
    });

    return NextResponse.json({
      success: true,
      message: 'Audit created successfully',
      data: result
    });

  } catch (error) {
    console.error('Error creating audit:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
