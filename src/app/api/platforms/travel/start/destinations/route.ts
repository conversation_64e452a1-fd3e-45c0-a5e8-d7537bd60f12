import { NextRequest, NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    const dataPath = join(process.cwd(), 'data/apps/platforms/travel/start/destinations.json');
    const data = readFileSync(dataPath, 'utf8');
    const destinations = JSON.parse(data);

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url);
    const country = searchParams.get('country');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const limit = searchParams.get('limit');

    let filteredDestinations = destinations;

    // Apply filters
    if (country) {
      filteredDestinations = filteredDestinations.filter((dest: any) => 
        dest.country.toLowerCase().includes(country.toLowerCase())
      );
    }

    if (minPrice) {
      filteredDestinations = filteredDestinations.filter((dest: any) => 
        dest.averagePrice >= parseInt(minPrice)
      );
    }

    if (maxPrice) {
      filteredDestinations = filteredDestinations.filter((dest: any) => 
        dest.averagePrice <= parseInt(maxPrice)
      );
    }

    // Apply limit
    if (limit) {
      filteredDestinations = filteredDestinations.slice(0, parseInt(limit));
    }

    return NextResponse.json(filteredDestinations);
  } catch (error) {
    console.error('Error loading destinations:', error);
    return NextResponse.json({ error: 'Failed to load destinations' }, { status: 500 });
  }
}