import { NextRequest, NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const type = searchParams.get('type');
    const location = searchParams.get('location');

    if (!query) {
      return NextResponse.json({ error: 'Search query is required' }, { status: 400 });
    }

    // Load services and destinations
    const servicesPath = join(process.cwd(), 'data/apps/platforms/travel/start/services.json');
    const destinationsPath = join(process.cwd(), 'data/apps/platforms/travel/start/destinations.json');
    
    const servicesData = readFileSync(servicesPath, 'utf8');
    const destinationsData = readFileSync(destinationsPath, 'utf8');
    
    const services = JSON.parse(servicesData);
    const destinations = JSON.parse(destinationsData);

    // Search in services
    let filteredServices = services.filter((service: any) => {
      const matchesQuery = 
        service.name.toLowerCase().includes(query.toLowerCase()) ||
        service.location.toLowerCase().includes(query.toLowerCase()) ||
        service.description.toLowerCase().includes(query.toLowerCase());
      
      const matchesType = !type || service.type === type;
      const matchesLocation = !location || service.location.toLowerCase().includes(location.toLowerCase());
      
      return matchesQuery && matchesType && matchesLocation;
    });

    // Search in destinations
    let filteredDestinations = destinations.filter((destination: any) => {
      return destination.name.toLowerCase().includes(query.toLowerCase()) ||
             destination.country.toLowerCase().includes(query.toLowerCase()) ||
             destination.description.toLowerCase().includes(query.toLowerCase());
    });

    // Combine results
    const results = {
      services: filteredServices,
      destinations: filteredDestinations,
      totalResults: filteredServices.length + filteredDestinations.length
    };

    return NextResponse.json(results);
  } catch (error) {
    console.error('Error performing search:', error);
    return NextResponse.json({ error: 'Search failed' }, { status: 500 });
  }
}