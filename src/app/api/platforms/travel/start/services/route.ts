import { NextRequest, NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    const dataPath = join(process.cwd(), 'data/apps/platforms/travel/start/services.json');
    const data = readFileSync(dataPath, 'utf8');
    const services = JSON.parse(data);

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const location = searchParams.get('location');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');

    let filteredServices = services;

    // Apply filters
    if (type) {
      filteredServices = filteredServices.filter((service: any) => service.type === type);
    }

    if (location) {
      filteredServices = filteredServices.filter((service: any) => 
        service.location.toLowerCase().includes(location.toLowerCase())
      );
    }

    if (minPrice) {
      filteredServices = filteredServices.filter((service: any) => service.price >= parseInt(minPrice));
    }

    if (maxPrice) {
      filteredServices = filteredServices.filter((service: any) => service.price <= parseInt(maxPrice));
    }

    return NextResponse.json(filteredServices);
  } catch (error) {
    console.error('Error loading services:', error);
    return NextResponse.json({ error: 'Failed to load services' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Here you would typically save the new service to the database
    // For now, we'll just return a success response
    
    return NextResponse.json({ 
      success: true, 
      message: 'Service created successfully',
      serviceId: `service_${Date.now()}`
    });
  } catch (error) {
    console.error('Error creating service:', error);
    return NextResponse.json({ error: 'Failed to create service' }, { status: 500 });
  }
}