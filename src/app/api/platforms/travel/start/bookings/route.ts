import { NextRequest, NextResponse } from 'next/server';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { OneIDService } from '@/app/backbone/oneid/services/OneIDService';

const BOOKINGS_FILE = join(process.cwd(), 'data/apps/platforms/travel/start/bookings.json');

interface Booking {
  id: string;
  userId: string;
  serviceId: string;
  serviceName: string;
  serviceType: 'hotel' | 'flight' | 'tour' | 'car_rental';
  checkIn?: string;
  checkOut?: string;
  departureDate?: string;
  returnDate?: string;
  guests?: number;
  passengers?: number;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  paymentStatus: 'pending' | 'paid' | 'refunded';
  createdAt: string;
  specialRequests?: string;
}

// GET - Retrieve bookings
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    const bookingsData = readFileSync(BOOKINGS_FILE, 'utf-8');
    let bookings: Booking[] = JSON.parse(bookingsData);

    // Filter by user ID if provided
    if (userId) {
      bookings = bookings.filter(booking => booking.userId === userId);
    }

    return NextResponse.json(bookings);
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return NextResponse.json({ error: 'Failed to fetch bookings' }, { status: 500 });
  }
}

// POST - Create new booking
export async function POST(request: NextRequest) {
  try {
    // Verify OneID authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const oneIDService = OneIDService.getInstance();
    await oneIDService.initialize();
    const sessionResult = await oneIDService.validateSession(token);

    if (!sessionResult.valid || !sessionResult.user) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }

    const bookingData = await request.json();
    
    // Generate booking ID
    const bookingId = `booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newBooking: Booking = {
      id: bookingId,
      userId: sessionResult.user.id,
      serviceId: bookingData.serviceId,
      serviceName: bookingData.serviceName,
      serviceType: bookingData.serviceType,
      checkIn: bookingData.checkIn,
      checkOut: bookingData.checkOut,
      departureDate: bookingData.departureDate,
      returnDate: bookingData.returnDate,
      guests: bookingData.guests,
      passengers: bookingData.passengers,
      totalPrice: bookingData.totalPrice,
      status: 'pending',
      paymentStatus: 'pending',
      createdAt: new Date().toISOString(),
      specialRequests: bookingData.specialRequests
    };

    // Read existing bookings
    const bookingsData = readFileSync(BOOKINGS_FILE, 'utf-8');
    const bookings: Booking[] = JSON.parse(bookingsData);
    
    // Add new booking
    bookings.push(newBooking);
    
    // Write back to file
    writeFileSync(BOOKINGS_FILE, JSON.stringify(bookings, null, 2));

    return NextResponse.json(newBooking, { status: 201 });
  } catch (error) {
    console.error('Error creating booking:', error);
    return NextResponse.json({ error: 'Failed to create booking' }, { status: 500 });
  }
}

// PUT - Update booking
export async function PUT(request: NextRequest) {
  try {
    // Verify OneID authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const oneIDService = OneIDService.getInstance();
    await oneIDService.initialize();
    const sessionResult = await oneIDService.validateSession(token);

    if (!sessionResult.valid || !sessionResult.user) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const bookingId = searchParams.get('id');
    
    if (!bookingId) {
      return NextResponse.json({ error: 'Booking ID required' }, { status: 400 });
    }

    const updateData = await request.json();

    // Read existing bookings
    const bookingsData = readFileSync(BOOKINGS_FILE, 'utf-8');
    const bookings: Booking[] = JSON.parse(bookingsData);
    
    // Find booking
    const bookingIndex = bookings.findIndex(booking => booking.id === bookingId);
    if (bookingIndex === -1) {
      return NextResponse.json({ error: 'Booking not found' }, { status: 404 });
    }

    // Check if user owns the booking
    if (bookings[bookingIndex].userId !== sessionResult.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Update booking
    bookings[bookingIndex] = { ...bookings[bookingIndex], ...updateData };
    
    // Write back to file
    writeFileSync(BOOKINGS_FILE, JSON.stringify(bookings, null, 2));

    return NextResponse.json(bookings[bookingIndex]);
  } catch (error) {
    console.error('Error updating booking:', error);
    return NextResponse.json({ error: 'Failed to update booking' }, { status: 500 });
  }
}