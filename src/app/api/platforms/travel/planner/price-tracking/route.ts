import { NextRequest, NextResponse } from 'next/server';
import { priceTrackingService } from '@/lib/platforms/travel/planner/services/price-tracking';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const serviceId = searchParams.get('serviceId');
    const type = searchParams.get('type'); // 'alerts', 'history', 'insights'
    const days = searchParams.get('days');
    
    if (type === 'alerts' && userId) {
      // Get user's price alerts
      const alerts = await priceTrackingService.getUserPriceAlerts(userId);
      return NextResponse.json(alerts);
    }
    
    if (type === 'history' && serviceId) {
      // Get price history for service
      const history = await priceTrackingService.getPriceHistory(
        serviceId,
        days ? parseInt(days) : 30
      );
      return NextResponse.json(history);
    }
    
    if (type === 'insights' && serviceId) {
      // Get price insights for service
      const insights = await priceTrackingService.getPriceInsights(serviceId);
      
      if (!insights) {
        return NextResponse.json(
          { error: 'No price data available for this service' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(insights);
    }
    
    if (type === 'all-alerts') {
      // Get all active alerts (admin function)
      const alerts = await priceTrackingService.getAllActiveAlerts();
      return NextResponse.json(alerts);
    }
    
    return NextResponse.json(
      { error: 'Invalid request. Specify type (alerts, history, insights) and required parameters' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error fetching price tracking data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch price tracking data' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, ...data } = body;
    
    switch (action) {
      case 'create_alert':
        const { 
          userId, 
          serviceType, 
          serviceId, 
          serviceName, 
          destination, 
          targetPrice, 
          currentPrice, 
          priceDropThreshold, 
          maxNotifications 
        } = data;
        
        if (!userId || !serviceType || !serviceId || !serviceName || !destination || !targetPrice) {
          return NextResponse.json(
            { error: 'Missing required fields for price alert' },
            { status: 400 }
          );
        }
        
        const alert = await priceTrackingService.createPriceAlert({
          userId,
          serviceType,
          serviceId,
          serviceName,
          destination,
          targetPrice,
          currentPrice: currentPrice || 0,
          priceDropThreshold: priceDropThreshold || 10,
          isActive: true,
          maxNotifications: maxNotifications || 5
        });
        
        return NextResponse.json(alert, { status: 201 });
        
      case 'record_price':
        const { recordServiceType, recordServiceId, price, currency, source, metadata } = data;
        
        if (!recordServiceType || !recordServiceId || !price) {
          return NextResponse.json(
            { error: 'Missing required fields: recordServiceType, recordServiceId, price' },
            { status: 400 }
          );
        }
        
        const priceRecord = await priceTrackingService.recordPrice(
          recordServiceType,
          recordServiceId,
          price,
          currency,
          source,
          metadata
        );
        
        return NextResponse.json(priceRecord, { status: 201 });
        
      case 'bulk_update_prices':
        const { updates } = data;
        
        if (!updates || !Array.isArray(updates)) {
          return NextResponse.json(
            { error: 'Missing or invalid updates array' },
            { status: 400 }
          );
        }
        
        await priceTrackingService.bulkUpdatePrices(updates);
        return NextResponse.json({ success: true, updatedCount: updates.length });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error processing price tracking request:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to process price tracking request' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { alertId, updates } = body;
    
    if (!alertId || !updates) {
      return NextResponse.json(
        { error: 'Missing required fields: alertId, updates' },
        { status: 400 }
      );
    }
    
    const updatedAlert = await priceTrackingService.updatePriceAlert(alertId, updates);
    return NextResponse.json(updatedAlert);
  } catch (error) {
    console.error('Error updating price alert:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update price alert' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const alertId = searchParams.get('alertId');
    const userId = searchParams.get('userId');
    
    if (!alertId || !userId) {
      return NextResponse.json(
        { error: 'Missing required parameters: alertId, userId' },
        { status: 400 }
      );
    }
    
    const success = await priceTrackingService.deletePriceAlert(alertId, userId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Price alert not found or access denied' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting price alert:', error);
    return NextResponse.json(
      { error: 'Failed to delete price alert' },
      { status: 500 }
    );
  }
}
