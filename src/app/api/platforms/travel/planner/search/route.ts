import { NextRequest, NextResponse } from 'next/server';
import { travelServiceRepository } from '@/lib/platforms/travel/planner/services/repository';
import { ServiceSearchParams } from '@/lib/platforms/travel/planner/services/types';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    
    // Build search parameters from query string
    const searchParamsObj: ServiceSearchParams = {
      type: searchParams.get('type') as 'flight' | 'accommodation' | 'activity' | null,
      destination: searchParams.get('destination') || undefined,
      origin: searchParams.get('origin') || undefined,
      budget: searchParams.get('budget') 
        ? parseInt(searchParams.get('budget') as string, 10) 
        : undefined,
      passengers: searchParams.get('passengers') 
        ? parseInt(searchParams.get('passengers') as string, 10) 
        : undefined,
    };

    // Handle date parameters
    if (searchParams.get('checkIn')) {
      searchParamsObj.checkIn = new Date(searchParams.get('checkIn') as string);
    }
    
    if (searchParams.get('checkOut')) {
      searchParamsObj.checkOut = new Date(searchParams.get('checkOut') as string);
    }
    
    if (searchParams.get('duration')) {
      searchParamsObj.duration = parseInt(searchParams.get('duration') as string, 10);
    }
    
    // Perform the search
    const searchResults = await travelServiceRepository.searchServices(searchParamsObj);
    
    return NextResponse.json(searchResults);
  } catch (error) {
    console.error('Error searching travel services:', error);
    return NextResponse.json(
      { error: 'Failed to search travel services' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Allow more complex search params in the request body
    const searchParams: ServiceSearchParams = {
      type: body.type,
      destination: body.destination,
      origin: body.origin,
      checkIn: body.checkIn ? new Date(body.checkIn) : undefined,
      checkOut: body.checkOut ? new Date(body.checkOut) : undefined,
      duration: body.duration,
      passengers: body.passengers,
      budget: body.budget,
      // Include any additional parameters
      ...(body.additionalParams || {})
    };
    
    // Perform the search
    const searchResults = await travelServiceRepository.searchServices(searchParams);
    
    return NextResponse.json(searchResults);
  } catch (error) {
    console.error('Error searching travel services:', error);
    return NextResponse.json(
      { error: 'Failed to search travel services' },
      { status: 500 }
    );
  }
}