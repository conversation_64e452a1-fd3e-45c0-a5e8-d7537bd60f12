import { NextRequest, NextResponse } from 'next/server';
import { userPreferencesService } from '@/lib/platforms/travel/planner/services/user-preferences';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }
    
    const preferences = await userPreferencesService.getUserPreferences(userId);
    
    if (!preferences) {
      return NextResponse.json(
        { error: 'User preferences not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(preferences);
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user preferences' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, preferences } = body;
    
    if (!userId || !preferences) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and preferences' },
        { status: 400 }
      );
    }
    
    // Ensure userId matches
    preferences.userId = userId;
    
    const savedPreferences = await userPreferencesService.saveUserPreferences(preferences);
    return NextResponse.json(savedPreferences, { status: 201 });
  } catch (error) {
    console.error('Error saving user preferences:', error);
    return NextResponse.json(
      { error: 'Failed to save user preferences' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, updates } = body;
    
    if (!userId || !updates) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and updates' },
        { status: 400 }
      );
    }
    
    const updatedPreferences = await userPreferencesService.updateUserPreferences(userId, updates);
    return NextResponse.json(updatedPreferences);
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return NextResponse.json(
      { error: 'Failed to update user preferences' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }
    
    const success = await userPreferencesService.deleteUserPreferences(userId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'User preferences not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user preferences:', error);
    return NextResponse.json(
      { error: 'Failed to delete user preferences' },
      { status: 500 }
    );
  }
}
