import { NextRequest, NextResponse } from 'next/server';
import { userPreferencesService } from '@/lib/platforms/travel/planner/services/user-preferences';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, destination } = body;
    
    if (!userId || !destination) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and destination' },
        { status: 400 }
      );
    }
    
    const updatedPreferences = await userPreferencesService.addSavedDestination(userId, destination);
    return NextResponse.json(updatedPreferences);
  } catch (error) {
    console.error('Error adding saved destination:', error);
    return NextResponse.json(
      { error: 'Failed to add saved destination' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, destination } = body;
    
    if (!userId || !destination) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and destination' },
        { status: 400 }
      );
    }
    
    const updatedPreferences = await userPreferencesService.removeSavedDestination(userId, destination);
    return NextResponse.json(updatedPreferences);
  } catch (error) {
    console.error('Error removing saved destination:', error);
    return NextResponse.json(
      { error: 'Failed to remove saved destination' },
      { status: 500 }
    );
  }
}
