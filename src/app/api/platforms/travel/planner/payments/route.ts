import { NextRequest, NextResponse } from 'next/server';
import { paymentService } from '@/lib/platforms/travel/planner/services/payment-service';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const transactionId = searchParams.get('transactionId');
    const type = searchParams.get('type'); // 'methods' or 'transactions'
    
    if (transactionId) {
      // Get specific transaction
      const transaction = await paymentService.getTransaction(transactionId);
      
      if (!transaction) {
        return NextResponse.json(
          { error: 'Transaction not found' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(transaction);
    }
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }
    
    if (type === 'methods') {
      // Get user's payment methods
      const paymentMethods = await paymentService.getUserPaymentMethods(userId);
      return NextResponse.json(paymentMethods);
    }
    
    if (type === 'transactions') {
      // Get user's payment transactions
      const transactions = await paymentService.getUserTransactions(userId);
      return NextResponse.json(transactions);
    }
    
    if (type === 'stats') {
      // Get user's payment statistics
      const stats = await paymentService.getUserPaymentStats(userId);
      return NextResponse.json(stats);
    }
    
    // Default: return both methods and recent transactions
    const [paymentMethods, transactions] = await Promise.all([
      paymentService.getUserPaymentMethods(userId),
      paymentService.getUserTransactions(userId)
    ]);
    
    return NextResponse.json({
      paymentMethods,
      transactions: transactions.slice(0, 10) // Last 10 transactions
    });
  } catch (error) {
    console.error('Error fetching payment data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment data' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, userId, ...data } = body;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required field: userId' },
        { status: 400 }
      );
    }
    
    switch (action) {
      case 'add_payment_method':
        const { type, details, billingAddress, isDefault } = data;
        
        if (!type || !details) {
          return NextResponse.json(
            { error: 'Missing required fields: type and details' },
            { status: 400 }
          );
        }
        
        const paymentMethod = await paymentService.addPaymentMethod({
          userId,
          type,
          details,
          billingAddress,
          isDefault: isDefault || false
        });
        
        return NextResponse.json(paymentMethod, { status: 201 });
        
      case 'create_payment_intent':
        const { amount, currency, description, metadata } = data;
        
        if (!amount || !description) {
          return NextResponse.json(
            { error: 'Missing required fields: amount and description' },
            { status: 400 }
          );
        }
        
        const paymentIntent = await paymentService.createPaymentIntent(
          userId,
          amount,
          currency || 'USD',
          description,
          metadata || {}
        );
        
        return NextResponse.json(paymentIntent, { status: 201 });
        
      case 'process_payment':
        const { bookingId, paymentMethodId, paymentAmount, paymentCurrency } = data;
        
        if (!bookingId || !paymentMethodId || !paymentAmount) {
          return NextResponse.json(
            { error: 'Missing required fields: bookingId, paymentMethodId, and paymentAmount' },
            { status: 400 }
          );
        }
        
        const transaction = await paymentService.processPayment(
          userId,
          bookingId,
          paymentMethodId,
          paymentAmount,
          paymentCurrency || 'USD'
        );
        
        return NextResponse.json(transaction, { status: 201 });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: add_payment_method, create_payment_intent, process_payment' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error processing payment request:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to process payment request' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, paymentMethodId, transactionId, ...data } = body;
    
    switch (action) {
      case 'update_payment_method':
        if (!paymentMethodId) {
          return NextResponse.json(
            { error: 'Missing required field: paymentMethodId' },
            { status: 400 }
          );
        }
        
        const updatedMethod = await paymentService.updatePaymentMethod(paymentMethodId, data);
        return NextResponse.json(updatedMethod);
        
      case 'refund_payment':
        if (!transactionId) {
          return NextResponse.json(
            { error: 'Missing required field: transactionId' },
            { status: 400 }
          );
        }
        
        const { refundAmount, reason } = data;
        const refundedTransaction = await paymentService.refundPayment(
          transactionId,
          refundAmount,
          reason
        );
        
        return NextResponse.json(refundedTransaction);
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: update_payment_method, refund_payment' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error updating payment data:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update payment data' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const paymentMethodId = searchParams.get('paymentMethodId');
    
    if (!paymentMethodId) {
      return NextResponse.json(
        { error: 'Missing required parameter: paymentMethodId' },
        { status: 400 }
      );
    }
    
    const success = await paymentService.deletePaymentMethod(paymentMethodId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Payment method not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting payment method:', error);
    return NextResponse.json(
      { error: 'Failed to delete payment method' },
      { status: 500 }
    );
  }
}
