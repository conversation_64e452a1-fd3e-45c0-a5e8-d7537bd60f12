import { NextRequest, NextResponse } from 'next/server';
import { tripSharingService } from '@/lib/platforms/travel/planner/services/trip-sharing';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const shareToken = searchParams.get('shareToken');
    const userId = searchParams.get('userId');
    const viewerId = searchParams.get('viewerId');
    
    if (shareToken) {
      // Get shared trip by token
      const sharedTrip = await tripSharingService.getSharedTrip(shareToken, viewerId || undefined);
      
      if (!sharedTrip) {
        return NextResponse.json(
          { error: 'Shared trip not found or expired' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(sharedTrip);
    }
    
    if (userId) {
      // Get user's shared trips
      const sharedTrips = await tripSharingService.getUserSharedTrips(userId);
      return NextResponse.json(sharedTrips);
    }
    
    return NextResponse.json(
      { error: 'Either shareToken or userId parameter is required' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error fetching shared trips:', error);
    return NextResponse.json(
      { error: 'Failed to fetch shared trips' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, ...data } = body;
    
    switch (action) {
      case 'share_trip':
        const { tripPlanId, ownerId, title, description, isPublic, allowComments, allowCollaboration, expiresAt, sharedWith } = data;
        
        if (!tripPlanId || !ownerId || !title) {
          return NextResponse.json(
            { error: 'Missing required fields: tripPlanId, ownerId, title' },
            { status: 400 }
          );
        }
        
        const sharedTrip = await tripSharingService.shareTrip(tripPlanId, ownerId, {
          title,
          description,
          isPublic,
          allowComments,
          allowCollaboration,
          expiresAt: expiresAt ? new Date(expiresAt) : undefined,
          sharedWith
        });
        
        return NextResponse.json(sharedTrip, { status: 201 });
        
      case 'add_collaborator':
        const { shareId, userId, role, invitedBy } = data;
        
        if (!shareId || !userId || !role || !invitedBy) {
          return NextResponse.json(
            { error: 'Missing required fields: shareId, userId, role, invitedBy' },
            { status: 400 }
          );
        }
        
        const updatedTrip = await tripSharingService.addCollaborator(shareId, userId, role, invitedBy);
        return NextResponse.json(updatedTrip);
        
      case 'add_comment':
        const { tripShareId, commentUserId, userName, content, parentCommentId } = data;
        
        if (!tripShareId || !commentUserId || !userName || !content) {
          return NextResponse.json(
            { error: 'Missing required fields: tripShareId, commentUserId, userName, content' },
            { status: 400 }
          );
        }
        
        const comment = await tripSharingService.addComment(
          tripShareId,
          commentUserId,
          userName,
          content,
          parentCommentId
        );
        
        return NextResponse.json(comment, { status: 201 });
        
      case 'submit_edit':
        const { editTripShareId, editUserId, editUserName, editType, editDescription, changes } = data;
        
        if (!editTripShareId || !editUserId || !editUserName || !editType || !editDescription || !changes) {
          return NextResponse.json(
            { error: 'Missing required fields for collaborative edit' },
            { status: 400 }
          );
        }
        
        const edit = await tripSharingService.submitCollaborativeEdit(
          editTripShareId,
          editUserId,
          editUserName,
          editType,
          editDescription,
          changes
        );
        
        return NextResponse.json(edit, { status: 201 });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error processing sharing request:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to process sharing request' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, ...data } = body;
    
    switch (action) {
      case 'update_shared_trip':
        const { shareId, updates } = data;
        
        if (!shareId || !updates) {
          return NextResponse.json(
            { error: 'Missing required fields: shareId, updates' },
            { status: 400 }
          );
        }
        
        const updatedTrip = await tripSharingService.updateSharedTrip(shareId, updates);
        return NextResponse.json(updatedTrip);
        
      case 'review_edit':
        const { editId, reviewerId, status } = data;
        
        if (!editId || !reviewerId || !status) {
          return NextResponse.json(
            { error: 'Missing required fields: editId, reviewerId, status' },
            { status: 400 }
          );
        }
        
        const reviewedEdit = await tripSharingService.reviewEdit(editId, reviewerId, status);
        return NextResponse.json(reviewedEdit);
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error updating sharing data:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update sharing data' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const shareId = searchParams.get('shareId');
    const ownerId = searchParams.get('ownerId');
    
    if (!shareId || !ownerId) {
      return NextResponse.json(
        { error: 'Missing required parameters: shareId, ownerId' },
        { status: 400 }
      );
    }
    
    const success = await tripSharingService.deleteSharedTrip(shareId, ownerId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Shared trip not found or access denied' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting shared trip:', error);
    return NextResponse.json(
      { error: 'Failed to delete shared trip' },
      { status: 500 }
    );
  }
}
