import { NextRequest, NextResponse } from 'next/server';
import { aiService } from '@/lib/platforms/travel/planner/services/ai-service';
import { userPreferencesService } from '@/lib/platforms/travel/planner/services/user-preferences';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { text, userId } = body;
    
    if (!text) {
      return NextResponse.json(
        { error: 'Missing required field: text' },
        { status: 400 }
      );
    }
    
    // Parse the natural language input
    const criteria = await aiService.parseNaturalLanguageInput({ text, userId });
    
    // Get user preferences if userId is provided
    let userPreferences = null;
    if (userId) {
      userPreferences = await userPreferencesService.getUserPreferences(userId);
    }
    
    // Get AI recommendations
    const recommendations = await aiService.getDestinationRecommendations(criteria, userPreferences || undefined);
    
    // Get AI insights if destination is specified
    let insights = null;
    if (criteria.destination) {
      insights = await aiService.enhanceTripOptions(criteria.destination, criteria, userPreferences || undefined);
    }
    
    return NextResponse.json({
      criteria,
      recommendations,
      insights
    });
  } catch (error) {
    console.error('Error parsing natural language input:', error);
    return NextResponse.json(
      { error: 'Failed to parse natural language input' },
      { status: 500 }
    );
  }
}
