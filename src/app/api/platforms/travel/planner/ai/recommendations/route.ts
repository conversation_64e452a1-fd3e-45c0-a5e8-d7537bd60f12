import { NextRequest, NextResponse } from 'next/server';
import { aiService } from '@/lib/platforms/travel/planner/services/ai-service';
import { userPreferencesService } from '@/lib/platforms/travel/planner/services/user-preferences';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }
    
    // Get user preferences
    const userPreferences = await userPreferencesService.getUserPreferences(userId);
    
    // Get personalized suggestions
    const suggestions = await aiService.getPersonalizedSuggestions(userId, userPreferences || undefined);
    
    return NextResponse.json({
      suggestions,
      userPreferences
    });
  } catch (error) {
    console.error('Error getting personalized recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to get personalized recommendations' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { criteria, userId } = body;
    
    if (!criteria) {
      return NextResponse.json(
        { error: 'Missing required field: criteria' },
        { status: 400 }
      );
    }
    
    // Get user preferences if userId is provided
    let userPreferences = null;
    if (userId) {
      userPreferences = await userPreferencesService.getUserPreferences(userId);
    }
    
    // Get destination recommendations based on criteria
    const recommendations = await aiService.getDestinationRecommendations(criteria, userPreferences || undefined);
    
    return NextResponse.json({
      recommendations,
      criteria
    });
  } catch (error) {
    console.error('Error getting destination recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to get destination recommendations' },
      { status: 500 }
    );
  }
}
