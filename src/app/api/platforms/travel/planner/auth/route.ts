import { NextRequest, NextResponse } from 'next/server';
import { userService } from '@/lib/platforms/travel/planner/services/user-service';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, email, userId, sessionId } = body;
    
    switch (action) {
      case 'login':
        if (!email) {
          return NextResponse.json(
            { error: 'Email is required for login' },
            { status: 400 }
          );
        }
        
        // Get user by email
        const user = await userService.getUserByEmail(email);
        if (!user || !user.isActive) {
          return NextResponse.json(
            { error: 'User not found or inactive' },
            { status: 404 }
          );
        }
        
        // Create session
        const deviceInfo = req.headers.get('user-agent') || undefined;
        const ipAddress = req.headers.get('x-forwarded-for') || 
                         req.headers.get('x-real-ip') || 
                         undefined;
        
        const session = await userService.createSession(user.id, deviceInfo, ipAddress);
        
        return NextResponse.json({
          user,
          session,
          message: 'Login successful'
        });
        
      case 'logout':
        if (!sessionId) {
          return NextResponse.json(
            { error: 'Session ID is required for logout' },
            { status: 400 }
          );
        }
        
        await userService.invalidateSession(sessionId);
        
        return NextResponse.json({
          message: 'Logout successful'
        });
        
      case 'validate':
        if (!sessionId) {
          return NextResponse.json(
            { error: 'Session ID is required for validation' },
            { status: 400 }
          );
        }
        
        const validSession = await userService.getSession(sessionId);
        if (!validSession) {
          return NextResponse.json(
            { error: 'Invalid or expired session' },
            { status: 401 }
          );
        }
        
        const sessionUser = await userService.getUserById(validSession.userId);
        if (!sessionUser || !sessionUser.isActive) {
          return NextResponse.json(
            { error: 'User not found or inactive' },
            { status: 404 }
          );
        }
        
        return NextResponse.json({
          user: sessionUser,
          session: validSession,
          valid: true
        });
        
      case 'logout-all':
        if (!userId) {
          return NextResponse.json(
            { error: 'User ID is required for logout-all' },
            { status: 400 }
          );
        }
        
        await userService.invalidateAllUserSessions(userId);
        
        return NextResponse.json({
          message: 'All sessions invalidated'
        });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: login, logout, validate, logout-all' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in auth endpoint:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const sessionId = searchParams.get('sessionId');
    
    if (sessionId) {
      // Get specific session
      const session = await userService.getSession(sessionId);
      
      if (!session) {
        return NextResponse.json(
          { error: 'Session not found or expired' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(session);
    }
    
    if (userId) {
      // Get user's active sessions
      const sessions = await userService.getUserSessions(userId);
      return NextResponse.json(sessions);
    }
    
    return NextResponse.json(
      { error: 'Either userId or sessionId parameter is required' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error fetching sessions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sessions' },
      { status: 500 }
    );
  }
}
