import { NextRequest, NextResponse } from 'next/server';
import { travelServiceRepository } from '@/lib/platforms/travel/planner/services/repository';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type') as 'flight' | 'accommodation' | 'activity' | null;
    const id = searchParams.get('id');
    const destination = searchParams.get('destination');

    // If ID is provided, return a specific service
    if (id) {
      const service = await travelServiceRepository.getServiceById(id);
      
      if (!service) {
        return NextResponse.json(
          { error: 'Service not found' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(service);
    }
    
    // If destination is provided, return services for that destination
    if (destination) {
      const services = await travelServiceRepository.getServicesForDestination(destination);
      return NextResponse.json(services);
    }
    
    // If type is provided, return services of that type
    if (type) {
      switch (type) {
        case 'flight':
          return NextResponse.json(await travelServiceRepository.getFlightServices());
        case 'accommodation':
          return NextResponse.json(await travelServiceRepository.getAccommodationServices());
        case 'activity':
          return NextResponse.json(await travelServiceRepository.getActivityServices());
        default:
          return NextResponse.json(
            { error: 'Invalid service type' },
            { status: 400 }
          );
      }
    }
    
    // If no parameters, return all services
    const allServices = await travelServiceRepository.getAllServices();
    return NextResponse.json(allServices);
  } catch (error) {
    console.error('Error fetching travel services:', error);
    return NextResponse.json(
      { error: 'Failed to fetch travel services' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { type, service } = body;
    
    if (!type || !service) {
      return NextResponse.json(
        { error: 'Missing required fields: type and service' },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (type) {
      case 'flight':
        result = await travelServiceRepository.addFlightService(service);
        break;
      case 'accommodation':
        result = await travelServiceRepository.addAccommodationService(service);
        break;
      case 'activity':
        result = await travelServiceRepository.addActivityService(service);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid service type' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error adding travel service:', error);
    return NextResponse.json(
      { error: 'Failed to add travel service' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const { type, id, serviceUpdate } = body;
    
    if (!type || !id || !serviceUpdate) {
      return NextResponse.json(
        { error: 'Missing required fields: type, id, and serviceUpdate' },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (type) {
      case 'flight':
        result = await travelServiceRepository.updateFlightService(id, serviceUpdate);
        break;
      case 'accommodation':
        result = await travelServiceRepository.updateAccommodationService(id, serviceUpdate);
        break;
      case 'activity':
        result = await travelServiceRepository.updateActivityService(id, serviceUpdate);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid service type' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating travel service:', error);
    return NextResponse.json(
      { error: 'Failed to update travel service' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Missing required parameter: id' },
        { status: 400 }
      );
    }
    
    const success = await travelServiceRepository.deleteService(id);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Service not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting travel service:', error);
    return NextResponse.json(
      { error: 'Failed to delete travel service' },
      { status: 500 }
    );
  }
}