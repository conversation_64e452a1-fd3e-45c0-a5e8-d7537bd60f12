import { NextRequest, NextResponse } from 'next/server';
import { tripPlannerService } from '@/lib/platforms/travel/planner/services/trip-planner';
import { travelServiceRepository } from '@/lib/platforms/travel/planner/services/repository';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const customerId = searchParams.get('customerId');
    
    if (!customerId) {
      return NextResponse.json(
        { error: 'Missing required parameter: customerId' },
        { status: 400 }
      );
    }
    
    const tripPlans = await tripPlannerService.getCustomerTripPlans(customerId);
    return NextResponse.json(tripPlans);
  } catch (error) {
    console.error('Error fetching trip plans:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trip plans' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { customerId, options } = body;
    
    if (!customerId || !options) {
      return NextResponse.json(
        { error: 'Missing required fields: customerId and options' },
        { status: 400 }
      );
    }
    
    // Validate required options
    const { destination, duration, budget, groupSize } = options;
    if (!destination || !duration || !budget || !groupSize) {
      return NextResponse.json(
        { error: 'Missing required trip options: destination, duration, budget, groupSize' },
        { status: 400 }
      );
    }
    
    // Check if services exist for the destination
    const servicesForDestination = await travelServiceRepository.getServicesForDestination(destination);
    if (
      servicesForDestination.flights.length === 0 &&
      servicesForDestination.accommodations.length === 0 &&
      servicesForDestination.activities.length === 0
    ) {
      return NextResponse.json(
        { error: `No travel services available for destination: ${destination}` },
        { status: 404 }
      );
    }
    
    // Build the trip plan
    const tripPlan = await tripPlannerService.buildTripPlan(options);
    
    // Save the trip plan for the customer
    const customerPlan = await tripPlannerService.saveCustomerTripPlan(customerId, tripPlan);
    
    return NextResponse.json(customerPlan, { status: 201 });
  } catch (error) {
    console.error('Error creating trip plan:', error);
    return NextResponse.json(
      { error: 'Failed to create trip plan' },
      { status: 500 }
    );
  }
}

// Endpoint for booking services
export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const { customerId, tripPlanId, action, servicesToBook } = body;
    
    if (!customerId || !tripPlanId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: customerId, tripPlanId, action' },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (action) {
      case 'book':
        if (!servicesToBook || !Array.isArray(servicesToBook) || servicesToBook.length === 0) {
          return NextResponse.json(
            { error: 'Missing or invalid servicesToBook' },
            { status: 400 }
          );
        }
        
        result = await tripPlannerService.bookTripServices(customerId, tripPlanId, servicesToBook);
        break;
        
      case 'cancel':
        result = await tripPlannerService.cancelTripPlan(customerId, tripPlanId);
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: book, cancel' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating trip plan:', error);
    return NextResponse.json(
      { error: 'Failed to update trip plan' },
      { status: 500 }
    );
  }
}