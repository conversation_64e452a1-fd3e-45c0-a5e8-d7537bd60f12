import { NextRequest, NextResponse } from 'next/server';
import { bookingHistoryService } from '@/lib/platforms/travel/planner/services/booking-history';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const customerId = searchParams.get('customerId');
    
    if (!customerId) {
      return NextResponse.json(
        { error: 'Missing required parameter: customerId' },
        { status: 400 }
      );
    }
    
    const stats = await bookingHistoryService.getCustomerBookingStats(customerId);
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching booking stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch booking stats' },
      { status: 500 }
    );
  }
}
