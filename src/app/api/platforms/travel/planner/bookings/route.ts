import { NextRequest, NextResponse } from 'next/server';
import { bookingHistoryService } from '@/lib/platforms/travel/planner/services/booking-history';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const customerId = searchParams.get('customerId');
    const bookingId = searchParams.get('bookingId');
    const status = searchParams.get('status');
    
    if (bookingId) {
      // Get specific booking
      const booking = await bookingHistoryService.getBookingById(bookingId);
      
      if (!booking) {
        return NextResponse.json(
          { error: 'Booking not found' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(booking);
    }
    
    if (customerId) {
      // Get customer booking history
      const bookings = await bookingHistoryService.getCustomerBookingHistory(customerId);
      return NextResponse.json(bookings);
    }
    
    if (status) {
      // Get bookings by status
      const bookings = await bookingHistoryService.getBookingsByStatus(status as any);
      return NextResponse.json(bookings);
    }
    
    // Get all bookings (admin only)
    const allBookings = await bookingHistoryService.getAllBookings();
    return NextResponse.json(allBookings);
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bookings' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const booking = body;
    
    if (!booking.id || !booking.customerId || !booking.tripPlanId) {
      return NextResponse.json(
        { error: 'Missing required fields: id, customerId, tripPlanId' },
        { status: 400 }
      );
    }
    
    const savedBooking = await bookingHistoryService.addBooking(booking);
    return NextResponse.json(savedBooking, { status: 201 });
  } catch (error) {
    console.error('Error creating booking:', error);
    return NextResponse.json(
      { error: 'Failed to create booking' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { bookingId, status, confirmationNumber, notes } = body;
    
    if (!bookingId) {
      return NextResponse.json(
        { error: 'Missing required field: bookingId' },
        { status: 400 }
      );
    }
    
    let updatedBooking;
    
    if (confirmationNumber) {
      updatedBooking = await bookingHistoryService.updateBookingConfirmation(bookingId, confirmationNumber);
    } else if (status) {
      updatedBooking = await bookingHistoryService.updateBookingStatus(bookingId, status, notes);
    } else {
      return NextResponse.json(
        { error: 'Either status or confirmationNumber must be provided' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(updatedBooking);
  } catch (error) {
    console.error('Error updating booking:', error);
    return NextResponse.json(
      { error: 'Failed to update booking' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const bookingId = searchParams.get('bookingId');
    
    if (!bookingId) {
      return NextResponse.json(
        { error: 'Missing required parameter: bookingId' },
        { status: 400 }
      );
    }
    
    const success = await bookingHistoryService.deleteBooking(bookingId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting booking:', error);
    return NextResponse.json(
      { error: 'Failed to delete booking' },
      { status: 500 }
    );
  }
}
