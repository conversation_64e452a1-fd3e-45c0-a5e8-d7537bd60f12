import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'WorldStats - Real-time Global Business Intelligence',
  description: 'Real-time world statistics focused on business opportunities, investment insights, and market intelligence with emphasis on Asia-Pacific region.',
  keywords: 'world statistics, business intelligence, investment opportunities, Asia Pacific, market data, economic indicators',
}

export default function StatsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">WS</span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900">WorldStats</h1>
              </div>
              <span className="text-sm text-gray-500 hidden sm:block">Real-time Global Business Intelligence</span>
            </div>
            <nav className="flex space-x-6">
              <a href="/web/stats" className="text-gray-700 hover:text-blue-600 font-medium">Dashboard</a>
              <a href="/web/stats/population" className="text-gray-700 hover:text-blue-600 font-medium">Population</a>
              <a href="/web/stats/economy" className="text-gray-700 hover:text-blue-600 font-medium">Economy</a>
              <a href="/web/stats/trade" className="text-gray-700 hover:text-blue-600 font-medium">Trade</a>
              <a href="/web/stats/environment" className="text-gray-700 hover:text-blue-600 font-medium">Environment</a>
              <a href="/web/stats/technology" className="text-gray-700 hover:text-blue-600 font-medium">Technology</a>
              <a href="/web/stats/opportunities" className="text-gray-700 hover:text-blue-600 font-medium">Opportunities</a>
            </nav>
          </div>
        </div>
      </header>
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
      <footer className="bg-gray-800 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">WorldStats</h3>
              <p className="text-gray-300 text-sm">Real-time global statistics for smart business decisions and investment opportunities.</p>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Categories</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li><a href="/web/stats/population" className="hover:text-white">Population & Demographics</a></li>
                <li><a href="/web/stats/economy" className="hover:text-white">Economic Indicators</a></li>
                <li><a href="/web/stats/trade" className="hover:text-white">Trade & Commerce</a></li>
                <li><a href="/web/stats/environment" className="hover:text-white">Environment & ESG</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Regions</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li><a href="/web/stats/regions/asia" className="hover:text-white">Asia-Pacific</a></li>
                <li><a href="/web/stats/regions/asean" className="hover:text-white">ASEAN</a></li>
                <li><a href="/web/stats/regions/china" className="hover:text-white">China</a></li>
                <li><a href="/web/stats/regions/india" className="hover:text-white">India</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Business Intelligence</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li><a href="/web/stats/opportunities" className="hover:text-white">Investment Opportunities</a></li>
                <li><a href="/web/stats/insights" className="hover:text-white">Market Insights</a></li>
                <li><a href="/web/stats/trends" className="hover:text-white">Emerging Trends</a></li>
                <li><a href="/web/stats/reports" className="hover:text-white">Business Reports</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2025 WorldStats. All rights reserved. Data sources: UN, World Bank, IMF, WHO, and other international organizations.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
