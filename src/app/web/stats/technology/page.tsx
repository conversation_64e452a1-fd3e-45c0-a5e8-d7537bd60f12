'use client'

import { useState, useEffect } from 'react'
import { StatCounter } from '../components/StatCounter'

export default function TechnologyPage() {
  const [techData, setTechData] = useState(null)

  useEffect(() => {
    // Load technology data
    fetch('/api/web/stats/technology')
      .then(res => res.json())
      .then(data => setTechData(data))
      .catch(err => console.error('Failed to load technology data:', err))
  }, [])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white p-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">💻 Technology & Innovation</h1>
        <p className="text-xl opacity-90">Digital transformation, AI adoption, and tech investment opportunities</p>
      </div>

      {/* Global Tech Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCounter
          title="Internet Users"
          value="5,200,000,000"
          increment={15.8}
          unit=""
          color="blue"
          description="Global internet penetration"
        />
        <StatCounter
          title="Mobile Connections"
          value="8,800,000,000"
          increment={28.5}
          unit=""
          color="green"
          description="Active mobile subscriptions"
        />
        <StatCounter
          title="AI Investment"
          value="180,000,000,000"
          increment={5708}
          unit="USD"
          color="purple"
          description="Global AI market value"
        />
        <StatCounter
          title="Cloud Spending"
          value="490,000,000,000"
          increment={15548}
          unit="USD"
          color="indigo"
          description="Annual cloud services"
        />
      </div>

      {/* Asia-Pacific Tech Landscape */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Asia-Pacific Technology Leaders</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              country: "China",
              flag: "🇨🇳",
              techSpending: "$445B",
              digitalEconomy: "$7.1T",
              internetUsers: "1.05B",
              mobileUsers: "1.67B",
              aiCompanies: "1,340",
              unicorns: 173,
              strengths: ["AI/ML", "E-commerce", "Fintech", "5G"],
              techGiants: ["Alibaba", "Tencent", "ByteDance", "Baidu"]
            },
            {
              country: "Japan",
              flag: "🇯🇵",
              techSpending: "$156B",
              digitalEconomy: "$1.2T",
              internetUsers: "118M",
              mobileUsers: "198M",
              aiCompanies: "285",
              unicorns: 7,
              strengths: ["Robotics", "Gaming", "Hardware", "IoT"],
              techGiants: ["SoftBank", "Sony", "Nintendo", "Rakuten"]
            },
            {
              country: "South Korea",
              flag: "🇰🇷",
              techSpending: "$89B",
              digitalEconomy: "$580B",
              internetUsers: "50M",
              mobileUsers: "71M",
              aiCompanies: "195",
              unicorns: 12,
              strengths: ["5G", "Gaming", "Semiconductors", "Display"],
              techGiants: ["Samsung", "LG", "Naver", "Kakao"]
            },
            {
              country: "India",
              flag: "🇮🇳",
              techSpending: "$101B",
              digitalEconomy: "$800B",
              internetUsers: "692M",
              mobileUsers: "1.17B",
              aiCompanies: "650",
              unicorns: 108,
              strengths: ["Software", "IT Services", "Fintech", "EdTech"],
              techGiants: ["TCS", "Infosys", "Flipkart", "Paytm"]
            },
            {
              country: "Singapore",
              flag: "🇸🇬",
              techSpending: "$18B",
              digitalEconomy: "$106B",
              internetUsers: "5.1M",
              mobileUsers: "8.9M",
              aiCompanies: "125",
              unicorns: 5,
              strengths: ["Fintech", "Smart City", "Cybersecurity", "Blockchain"],
              techGiants: ["Grab", "Sea Limited", "Shopee", "Razer"]
            },
            {
              country: "Australia",
              flag: "🇦🇺",
              techSpending: "$67B",
              digitalEconomy: "$315B",
              internetUsers: "22M",
              mobileUsers: "32M",
              aiCompanies: "180",
              unicorns: 8,
              strengths: ["Mining Tech", "AgTech", "Fintech", "HealthTech"],
              techGiants: ["Atlassian", "Canva", "Afterpay", "Xero"]
            }
          ].map((country, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-5">
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">{country.flag}</span>
                <h3 className="font-semibold text-gray-900">{country.country}</h3>
              </div>
              
              <div className="space-y-3 text-sm">
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tech Spending:</span>
                    <span className="font-semibold text-blue-600">{country.techSpending}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Digital Economy:</span>
                    <span className="font-semibold text-green-600">{country.digitalEconomy}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Internet Users:</span>
                    <span className="font-medium">{country.internetUsers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Mobile Users:</span>
                    <span className="font-medium">{country.mobileUsers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">AI Companies:</span>
                    <span className="font-medium text-purple-600">{country.aiCompanies}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Unicorns:</span>
                    <span className="font-medium text-yellow-600">{country.unicorns}</span>
                  </div>
                </div>
                
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Tech Strengths:</h4>
                  <div className="flex flex-wrap gap-1">
                    {country.strengths.map((strength, strengthIndex) => (
                      <span key={strengthIndex} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {strength}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Tech Giants:</h4>
                  <div className="flex flex-wrap gap-1">
                    {country.techGiants.map((giant, giantIndex) => (
                      <span key={giantIndex} className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                        {giant}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Technology Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Emerging Technologies</h3>
          <div className="space-y-4">
            {[
              { tech: "Artificial Intelligence", market: "$190B", growth: "+37%", adoption: "68%" },
              { tech: "5G Networks", market: "$85B", growth: "+45%", adoption: "23%" },
              { tech: "Internet of Things", market: "$650B", growth: "+25%", adoption: "41%" },
              { tech: "Blockchain", market: "$32B", growth: "+68%", adoption: "15%" },
              { tech: "Quantum Computing", market: "$1.3B", growth: "+32%", adoption: "3%" },
              { tech: "Extended Reality (AR/VR)", market: "$28B", growth: "+55%", adoption: "12%" }
            ].map((tech, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{tech.tech}</span>
                  <div className="text-sm text-gray-600">
                    Adoption: {tech.adoption}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">{tech.market}</div>
                  <div className="text-sm text-blue-600">{tech.growth}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Digital Transformation</h3>
          <div className="space-y-4">
            {[
              { sector: "Financial Services", digitization: "78%", investment: "$125B", priority: "High" },
              { sector: "Retail & E-commerce", digitization: "65%", investment: "$89B", priority: "High" },
              { sector: "Healthcare", digitization: "45%", investment: "$67B", priority: "Medium" },
              { sector: "Manufacturing", digitization: "52%", investment: "$156B", priority: "High" },
              { sector: "Education", digitization: "38%", investment: "$43B", priority: "Medium" },
              { sector: "Government", digitization: "35%", investment: "$78B", priority: "Medium" }
            ].map((sector, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{sector.sector}</span>
                  <div className="text-sm text-gray-600">
                    Investment: {sector.investment}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-blue-600">{sector.digitization}</div>
                  <div className={`text-xs px-2 py-1 rounded-full ${
                    sector.priority === 'High' ? 'bg-red-100 text-red-800' :
                    sector.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {sector.priority}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Startup Ecosystem */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Tech Startup Ecosystem</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { city: "Beijing", unicorns: 45, funding: "$28B", startups: "12,500", focus: "AI, Fintech" },
            { city: "Shanghai", unicorns: 28, funding: "$18B", startups: "8,900", focus: "E-commerce, AI" },
            { city: "Shenzhen", unicorns: 22, funding: "$15B", startups: "7,200", focus: "Hardware, IoT" },
            { city: "Bangalore", unicorns: 38, funding: "$12B", startups: "9,800", focus: "SaaS, Fintech" },
            { city: "Singapore", unicorns: 5, funding: "$8B", startups: "4,200", focus: "Fintech, Logistics" },
            { city: "Tokyo", unicorns: 7, funding: "$6B", startups: "3,800", focus: "Gaming, Robotics" },
            { city: "Seoul", unicorns: 12, funding: "$5B", startups: "3,200", focus: "Gaming, E-commerce" },
            { city: "Sydney", unicorns: 8, funding: "$4B", startups: "2,900", focus: "Fintech, SaaS" }
          ].map((city, index) => (
            <div key={index} className="border border-gray-200 rounded p-4">
              <h4 className="font-semibold text-gray-900 mb-3">{city.city}</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Unicorns:</span>
                  <span className="font-semibold text-yellow-600">{city.unicorns}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Funding:</span>
                  <span className="font-semibold text-green-600">{city.funding}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Startups:</span>
                  <span className="font-medium">{city.startups}</span>
                </div>
                <div className="mt-2">
                  <span className="text-xs text-gray-600">Focus: </span>
                  <span className="text-xs font-medium">{city.focus}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Technology Investment Opportunities */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">🎯 Technology Investment Opportunities</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            {
              title: "Artificial Intelligence & Machine Learning",
              description: "AI-powered solutions across industries driving automation and insights",
              market: "$190B",
              growth: "+37%",
              regions: ["China", "India", "Singapore", "Japan"],
              icon: "🤖"
            },
            {
              title: "Fintech & Digital Payments",
              description: "Digital financial services and payment solutions for underbanked populations",
              market: "$180B",
              growth: "+25%",
              regions: ["India", "Indonesia", "Vietnam", "Philippines"],
              icon: "💳"
            },
            {
              title: "E-commerce & Digital Marketplaces",
              description: "Online retail platforms and B2B marketplaces connecting buyers and sellers",
              market: "$2.8T",
              growth: "+15%",
              regions: ["China", "India", "Indonesia", "Thailand"],
              icon: "🛒"
            },
            {
              title: "Cybersecurity Solutions",
              description: "Security technologies protecting digital infrastructure and data",
              market: "$85B",
              growth: "+28%",
              regions: ["Singapore", "Japan", "Australia", "South Korea"],
              icon: "🔒"
            },
            {
              title: "Cloud Computing & SaaS",
              description: "Cloud infrastructure and software-as-a-service solutions",
              market: "$490B",
              growth: "+18%",
              regions: ["China", "India", "Japan", "Australia"],
              icon: "☁️"
            },
            {
              title: "IoT & Smart Cities",
              description: "Connected devices and smart infrastructure for urban development",
              market: "$650B",
              growth: "+25%",
              regions: ["Singapore", "South Korea", "Japan", "China"],
              icon: "🏙️"
            }
          ].map((opp, index) => (
            <div key={index} className="bg-white rounded-lg p-5 shadow-sm border border-gray-200">
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{opp.icon}</span>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">{opp.title}</h4>
                  <p className="text-sm text-gray-600 mb-3">{opp.description}</p>
                  
                  <div className="flex items-center space-x-4 mb-3">
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-gray-500">Market:</span>
                      <span className="font-semibold text-green-600">{opp.market}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-gray-500">Growth:</span>
                      <span className="font-semibold text-blue-600">{opp.growth}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {opp.regions.map((region, regionIndex) => (
                      <span key={regionIndex} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        {region}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Data Sources</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Technology Data</h4>
            <ul className="space-y-1">
              <li>• International Telecommunication Union</li>
              <li>• Statista Digital Market Outlook</li>
              <li>• IDC Technology Research</li>
              <li>• Gartner Technology Trends</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Startup Ecosystem</h4>
            <ul className="space-y-1">
              <li>• CB Insights</li>
              <li>• PitchBook</li>
              <li>• Crunchbase</li>
              <li>• Startup Genome</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Investment Data</h4>
            <ul className="space-y-1">
              <li>• KPMG Venture Pulse</li>
              <li>• EY Global VC Trends</li>
              <li>• Preqin Alternative Assets</li>
              <li>• McKinsey Technology Trends</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Market Research</h4>
            <ul className="space-y-1">
              <li>• Forrester Research</li>
              <li>• Boston Consulting Group</li>
              <li>• Deloitte Tech Trends</li>
              <li>• Accenture Technology Vision</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
