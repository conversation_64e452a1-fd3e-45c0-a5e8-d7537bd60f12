'use client'

import { useState, useEffect } from 'react'
import { StatCounter } from '../components/StatCounter'

export default function PopulationPage() {
  const [populationData, setPopulationData] = useState(null)

  useEffect(() => {
    // Load population data
    fetch('/api/web/stats/population')
      .then(res => res.json())
      .then(data => setPopulationData(data))
      .catch(err => console.error('Failed to load population data:', err))
  }, [])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl text-white p-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">👥 Population & Demographics</h1>
        <p className="text-xl opacity-90">Workforce trends, consumer markets, and demographic shifts across Asia-Pacific</p>
      </div>

      {/* Global Population Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCounter
          title="World Population"
          value="8,100,000,000"
          increment={2.4}
          unit="people"
          color="blue"
          description="Growing at 0.85% annually"
        />
        <StatCounter
          title="Asia Population"
          value="4,700,000,000"
          increment={1.1}
          unit="people"
          color="green"
          description="58% of world population"
        />
        <StatCounter
          title="Working Age (15-64)"
          value="2,900,000,000"
          increment={0.7}
          unit="people"
          color="purple"
          description="Asia-Pacific workforce"
        />
        <StatCounter
          title="Urban Population"
          value="2,300,000,000"
          increment={1.8}
          unit="people"
          color="indigo"
          description="49% urbanization rate"
        />
      </div>

      {/* Regional Breakdown */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Regional Population Distribution</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { country: "China", population: "1.41B", growth: "+0.2%", workforce: "990M", urbanization: "64%", flag: "🇨🇳" },
            { country: "India", population: "1.38B", growth: "+0.8%", workforce: "900M", urbanization: "35%", flag: "🇮🇳" },
            { country: "Indonesia", population: "274M", growth: "+0.9%", workforce: "185M", urbanization: "57%", flag: "🇮🇩" },
            { country: "Pakistan", population: "231M", growth: "+1.9%", workforce: "140M", urbanization: "37%", flag: "🇵🇰" },
            { country: "Bangladesh", population: "167M", growth: "+1.0%", workforce: "110M", urbanization: "39%", flag: "🇧🇩" },
            { country: "Japan", population: "125M", growth: "-0.5%", workforce: "75M", urbanization: "92%", flag: "🇯🇵" }
          ].map((country, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-2xl">{country.flag}</span>
                <h3 className="font-semibold text-gray-900">{country.country}</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Population:</span>
                  <span className="font-medium">{country.population}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Growth Rate:</span>
                  <span className={`font-medium ${country.growth.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {country.growth}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Workforce:</span>
                  <span className="font-medium">{country.workforce}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Urbanization:</span>
                  <span className="font-medium">{country.urbanization}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Demographic Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Age Demographics</h3>
          <div className="space-y-4">
            {[
              { ageGroup: "0-14 years", percentage: "24.8%", population: "1.17B", trend: "declining" },
              { ageGroup: "15-64 years", percentage: "67.2%", population: "3.16B", trend: "stable" },
              { ageGroup: "65+ years", percentage: "8.0%", population: "376M", trend: "growing" }
            ].map((group, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{group.ageGroup}</span>
                  <span className="text-sm text-gray-600 ml-2">({group.population})</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold text-blue-600">{group.percentage}</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    group.trend === 'growing' ? 'bg-green-100 text-green-800' :
                    group.trend === 'declining' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {group.trend}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Business Implications</h3>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">🎯 Consumer Market Opportunities</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 3.16B working-age consumers in Asia-Pacific</li>
                <li>• Rising middle class in India, Indonesia, Vietnam</li>
                <li>• Urban consumer spending power increasing</li>
              </ul>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">👷 Workforce Trends</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Large skilled workforce in tech hubs</li>
                <li>• Aging population in Japan, South Korea</li>
                <li>• Youth demographic dividend in India, Indonesia</li>
              </ul>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">🏙️ Urbanization Impact</h4>
              <ul className="text-sm text-purple-800 space-y-1">
                <li>• Smart city infrastructure demand</li>
                <li>• Urban services and logistics growth</li>
                <li>• Real estate and construction opportunities</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Data Sources</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Population Data</h4>
            <ul className="space-y-1">
              <li>• UN World Population Prospects 2024</li>
              <li>• National Statistical Offices</li>
              <li>• World Bank Population Data</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Demographic Analysis</h4>
            <ul className="space-y-1">
              <li>• OECD Demographics</li>
              <li>• Asian Development Bank</li>
              <li>• McKinsey Global Institute</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Business Intelligence</h4>
            <ul className="space-y-1">
              <li>• Euromonitor International</li>
              <li>• Nielsen Consumer Insights</li>
              <li>• Deloitte Market Research</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
