'use client'

import { useState, useEffect } from 'react'

export default function AdminPage() {
  const [metrics, setMetrics] = useState<any>(null)
  const [schedule, setSchedule] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('metrics')

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      const [metricsRes, scheduleRes] = await Promise.all([
        fetch('/api/web/stats/metrics'),
        fetch('/api/web/stats/scheduler')
      ])
      
      const metricsData = await metricsRes.json()
      const scheduleData = await scheduleRes.json()
      
      setMetrics(metricsData.data)
      setSchedule(scheduleData.data || [])
    } catch (error) {
      console.error('Failed to load data:', error)
    }
  }

  const updateMetric = async (category: string, metric?: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/web/stats/groq-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ category, metric })
      })
      
      const result = await response.json()
      
      if (result.success) {
        await loadData()
        alert(`Updated ${result.updatedCount} metrics`)
      } else {
        alert(`Error: ${result.error}`)
      }
    } catch (error) {
      alert(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const toggleMetric = async (category: string, metric: string, enabled: boolean) => {
    try {
      const response = await fetch('/api/web/stats/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          category, 
          metric, 
          data: { enabled } 
        })
      })
      
      if (response.ok) {
        await loadData()
      }
    } catch (error) {
      console.error('Failed to toggle metric:', error)
    }
  }

  const runScheduledUpdate = async (category: string, metric?: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/web/stats/scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run', category, metric })
      })
      
      const result = await response.json()
      
      if (result.success) {
        await loadData()
        alert('Update completed successfully')
      } else {
        alert(`Error: ${result.error}`)
      }
    } catch (error) {
      alert(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  if (!metrics) {
    return (
      <div className="p-8">
        <div className="text-center">Loading admin panel...</div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">WorldStats Admin</h1>
        <p className="text-gray-600">Manage metrics, data sources, and update schedules</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {['metrics', 'schedule', 'sources'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </nav>
      </div>

      {/* Metrics Tab */}
      {activeTab === 'metrics' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">Metrics Management</h2>
            <button
              onClick={() => updateMetric('all')}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Updating...' : 'Update All Metrics'}
            </button>
          </div>

          {Object.entries(metrics.categories).map(([category, categoryMetrics]) => (
            <div key={category} className="bg-white border border-gray-200 rounded-lg">
              <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                <h3 className="font-medium text-gray-900 uppercase">{category}</h3>
                <button
                  onClick={() => updateMetric(category)}
                  disabled={loading}
                  className="text-sm bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 disabled:opacity-50"
                >
                  Update Category
                </button>
              </div>
              
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(categoryMetrics as any).map(([metric, config]) => (
                    <div key={metric} className="border border-gray-200 rounded p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium text-sm">{(config as any).title || metric}</h4>
                          <p className="text-xs text-gray-500">{(config as any).source}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={(config as any).enabled}
                              onChange={(e) => toggleMetric(category, metric, e.target.checked)}
                              className="rounded"
                            />
                            <span className="ml-1 text-xs">Enabled</span>
                          </label>
                        </div>
                      </div>
                      
                      <div className="text-xs space-y-1">
                        {(config as any).value && (
                          <div>Value: <span className="font-mono">{(config as any).value}</span></div>
                        )}
                        {(config as any).baseValue && (
                          <div>Base: <span className="font-mono">{(config as any).baseValue}</span></div>
                        )}
                        {(config as any).incrementPerSecond && (
                          <div>Inc/sec: <span className="font-mono">{(config as any).incrementPerSecond}</span></div>
                        )}
                        {(config as any).lastUpdated && (
                          <div>Updated: <span className="font-mono">{new Date((config as any).lastUpdated).toLocaleString()}</span></div>
                        )}
                      </div>
                      
                      <button
                        onClick={() => updateMetric(category, metric)}
                        disabled={loading}
                        className="mt-2 w-full text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 disabled:opacity-50"
                      >
                        Update Now
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Schedule Tab */}
      {activeTab === 'schedule' && (
        <div className="space-y-6">
          <h2 className="text-lg font-semibold">Update Schedule</h2>
          
          <div className="bg-white border border-gray-200 rounded-lg">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left">Category</th>
                    <th className="px-4 py-3 text-left">Metric</th>
                    <th className="px-4 py-3 text-left">Schedule</th>
                    <th className="px-4 py-3 text-left">Last Run</th>
                    <th className="px-4 py-3 text-left">Next Run</th>
                    <th className="px-4 py-3 text-left">Status</th>
                    <th className="px-4 py-3 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {schedule.map((entry, index) => (
                    <tr key={index} className="border-t border-gray-200">
                      <td className="px-4 py-3 font-medium">{entry.category}</td>
                      <td className="px-4 py-3">{entry.metric || 'All'}</td>
                      <td className="px-4 py-3 font-mono text-xs">{entry.schedule}</td>
                      <td className="px-4 py-3 text-xs">
                        {entry.lastRun ? new Date(entry.lastRun).toLocaleString() : 'Never'}
                      </td>
                      <td className="px-4 py-3 text-xs">
                        {entry.nextRun ? new Date(entry.nextRun).toLocaleString() : 'N/A'}
                      </td>
                      <td className="px-4 py-3">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          entry.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {entry.enabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <button
                          onClick={() => runScheduledUpdate(entry.category, entry.metric)}
                          disabled={loading}
                          className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 disabled:opacity-50"
                        >
                          Run Now
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Sources Tab */}
      {activeTab === 'sources' && (
        <div className="space-y-6">
          <h2 className="text-lg font-semibold">Data Sources</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(metrics.dataSources?.reliability || {}).map(([source, reliability]) => (
              <div key={source} className="bg-white border border-gray-200 rounded p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium">{source}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    (reliability as number) >= 95 ? 'bg-green-100 text-green-800' :
                    (reliability as number) >= 90 ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {reliability}%
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  <div>Frequency: {metrics.dataSources?.updateFrequency?.[source] || 'Unknown'}</div>
                  <div>Reliability: {reliability}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
