'use client'

import { useState, useEffect } from 'react'
import { StatCounter } from '../components/StatCounter'

export default function TradePage() {
  const [tradeData, setTradeData] = useState(null)

  useEffect(() => {
    // Load trade data
    fetch('/api/web/stats/trade')
      .then(res => res.json())
      .then(data => setTradeData(data))
      .catch(err => console.error('Failed to load trade data:', err))
  }, [])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-2xl text-white p-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">🚢 Trade & Commerce</h1>
        <p className="text-xl opacity-90">Global trade flows, supply chain metrics, and commercial opportunities</p>
      </div>

      {/* Global Trade Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCounter
          title="Daily Trade Volume"
          value="65,000,000,000"
          increment={750000}
          unit="USD"
          color="purple"
          description="Global merchandise trade"
        />
        <StatCounter
          title="Container Throughput"
          value="450,000,000"
          increment={14.3}
          unit="TEU"
          color="blue"
          description="Annual container volume"
        />
        <StatCounter
          title="E-commerce Sales"
          value="5,800,000,000,000"
          increment={183823}
          unit="USD"
          color="green"
          description="Global online retail"
        />
        <StatCounter
          title="Supply Chain Value"
          value="15,000,000,000,000"
          increment={475463}
          unit="USD"
          color="indigo"
          description="Global logistics market"
        />
      </div>

      {/* Asia-Pacific Trade Hub */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Asia-Pacific Trade Powerhouses</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              country: "China",
              flag: "🇨🇳",
              exports: "$3.59T",
              imports: "$2.71T",
              tradeBalance: "+$878B",
              topExports: ["Electronics", "Machinery", "Textiles"],
              topPartners: ["USA", "EU", "ASEAN"],
              portThroughput: "280M TEU"
            },
            {
              country: "Japan", 
              flag: "🇯🇵",
              exports: "$756B",
              imports: "$721B",
              tradeBalance: "+$35B",
              topExports: ["Vehicles", "Machinery", "Electronics"],
              topPartners: ["China", "USA", "South Korea"],
              portThroughput: "22M TEU"
            },
            {
              country: "South Korea",
              flag: "🇰🇷",
              exports: "$683B",
              imports: "$615B", 
              tradeBalance: "+$68B",
              topExports: ["Semiconductors", "Vehicles", "Petrochemicals"],
              topPartners: ["China", "USA", "Vietnam"],
              portThroughput: "28M TEU"
            },
            {
              country: "Singapore",
              flag: "🇸🇬",
              exports: "$626B",
              imports: "$571B",
              tradeBalance: "+$55B",
              topExports: ["Electronics", "Chemicals", "Refined Petroleum"],
              topPartners: ["China", "Malaysia", "USA"],
              portThroughput: "37M TEU"
            },
            {
              country: "Taiwan",
              flag: "🇹🇼",
              exports: "$479B",
              imports: "$382B",
              tradeBalance: "+$97B",
              topExports: ["Semiconductors", "Electronics", "Machinery"],
              topPartners: ["China", "USA", "Hong Kong"],
              portThroughput: "17M TEU"
            },
            {
              country: "India",
              flag: "🇮🇳",
              exports: "$447B",
              imports: "$507B",
              tradeBalance: "-$60B",
              topExports: ["Pharmaceuticals", "IT Services", "Textiles"],
              topPartners: ["USA", "UAE", "China"],
              portThroughput: "16M TEU"
            }
          ].map((country, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-5">
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">{country.flag}</span>
                <h3 className="font-semibold text-gray-900">{country.country}</h3>
              </div>
              
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Exports:</span>
                  <span className="font-semibold text-green-600">{country.exports}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Imports:</span>
                  <span className="font-semibold text-blue-600">{country.imports}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Trade Balance:</span>
                  <span className={`font-semibold ${country.tradeBalance.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {country.tradeBalance}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Port Volume:</span>
                  <span className="font-medium">{country.portThroughput}</span>
                </div>
                
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Top Exports:</h4>
                  <div className="flex flex-wrap gap-1">
                    {country.topExports.map((export_, exportIndex) => (
                      <span key={exportIndex} className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                        {export_}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Key Partners:</h4>
                  <div className="flex flex-wrap gap-1">
                    {country.topPartners.map((partner, partnerIndex) => (
                      <span key={partnerIndex} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {partner}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Supply Chain & Logistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Major Shipping Routes</h3>
          <div className="space-y-4">
            {[
              { route: "Asia-Europe", volume: "25M TEU", value: "$1.2T", growth: "+5.2%" },
              { route: "Trans-Pacific", volume: "28M TEU", value: "$1.8T", growth: "+3.8%" },
              { route: "Intra-Asia", volume: "45M TEU", value: "$2.1T", growth: "+6.1%" },
              { route: "Asia-Middle East", volume: "12M TEU", value: "$450B", growth: "+7.3%" },
              { route: "Asia-Africa", volume: "8M TEU", value: "$280B", growth: "+9.2%" }
            ].map((route, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{route.route}</span>
                  <div className="text-sm text-gray-600">
                    {route.volume} • {route.value}
                  </div>
                </div>
                <span className="text-sm px-2 py-1 rounded-full bg-green-100 text-green-800">
                  {route.growth}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">E-commerce Growth</h3>
          <div className="space-y-4">
            {[
              { country: "China", market: "$2.8T", growth: "+8.2%", penetration: "52%" },
              { country: "India", market: "$120B", growth: "+25.8%", penetration: "8%" },
              { country: "Indonesia", market: "$55B", growth: "+22.1%", penetration: "12%" },
              { country: "Japan", market: "$200B", growth: "+9.1%", penetration: "15%" },
              { country: "South Korea", market: "$85B", growth: "+12.3%", penetration: "28%" },
              { country: "Thailand", market: "$18B", growth: "+18.5%", penetration: "11%" }
            ].map((market, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{market.country}</span>
                  <div className="text-sm text-gray-600">
                    Market: {market.market} • Penetration: {market.penetration}
                  </div>
                </div>
                <span className="text-sm px-2 py-1 rounded-full bg-green-100 text-green-800">
                  {market.growth}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Commodity Prices */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Key Commodity Prices</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { commodity: "Crude Oil", price: "$78.50", unit: "/barrel", change: "+2.1%", trend: "rising" },
            { commodity: "Natural Gas", price: "$3.85", unit: "/MMBtu", change: "+5.8%", trend: "rising" },
            { commodity: "Gold", price: "$2,045", unit: "/oz", change: "+0.8%", trend: "stable" },
            { commodity: "Copper", price: "$8,420", unit: "/ton", change: "+3.2%", trend: "rising" },
            { commodity: "Iron Ore", price: "$115", unit: "/ton", change: "-1.5%", trend: "declining" },
            { commodity: "Coal", price: "$95", unit: "/ton", change: "+4.1%", trend: "rising" },
            { commodity: "Soybeans", price: "$1,485", unit: "/bushel", change: "+2.8%", trend: "rising" },
            { commodity: "Rice", price: "$16.20", unit: "/cwt", change: "+1.2%", trend: "stable" }
          ].map((commodity, index) => (
            <div key={index} className="p-3 border border-gray-200 rounded">
              <div className="text-sm text-gray-600 mb-1">{commodity.commodity}</div>
              <div className="font-semibold text-gray-900 mb-1">
                {commodity.price}<span className="text-xs text-gray-500">{commodity.unit}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className={`px-2 py-1 rounded-full ${
                  commodity.change.startsWith('+') 
                    ? 'bg-green-100 text-green-800'
                    : commodity.change.startsWith('-')
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {commodity.change}
                </span>
                <span className="text-gray-500">{commodity.trend}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Business Opportunities */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">🎯 Trade & Commerce Opportunities</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            {
              title: "Supply Chain Technology",
              description: "AI-powered logistics and automation driving efficiency gains",
              market: "$450B",
              growth: "+15.3%",
              regions: ["China", "Vietnam", "Thailand"],
              icon: "🚛"
            },
            {
              title: "Cross-border E-commerce",
              description: "Digital platforms connecting Asian suppliers with global buyers",
              market: "$180B", 
              growth: "+22.8%",
              regions: ["Singapore", "Malaysia", "Indonesia"],
              icon: "🌐"
            },
            {
              title: "Green Shipping Solutions",
              description: "Sustainable maritime transport and port automation",
              market: "$85B",
              growth: "+18.5%",
              regions: ["Japan", "South Korea", "Singapore"],
              icon: "🚢"
            },
            {
              title: "Trade Finance Technology",
              description: "Blockchain and digital solutions for trade financing",
              market: "$65B",
              growth: "+25.1%",
              regions: ["Hong Kong", "Singapore", "India"],
              icon: "💳"
            }
          ].map((opp, index) => (
            <div key={index} className="bg-white rounded-lg p-5 shadow-sm border border-gray-200">
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{opp.icon}</span>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">{opp.title}</h4>
                  <p className="text-sm text-gray-600 mb-3">{opp.description}</p>
                  
                  <div className="flex items-center space-x-4 mb-3">
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-gray-500">Market:</span>
                      <span className="font-semibold text-green-600">{opp.market}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-gray-500">Growth:</span>
                      <span className="font-semibold text-blue-600">{opp.growth}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {opp.regions.map((region, regionIndex) => (
                      <span key={regionIndex} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        {region}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Data Sources</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Trade Statistics</h4>
            <ul className="space-y-1">
              <li>• WTO Trade Statistics</li>
              <li>• UN Comtrade Database</li>
              <li>• National Customs Agencies</li>
              <li>• Port Authorities</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Shipping & Logistics</h4>
            <ul className="space-y-1">
              <li>• Container Trade Statistics</li>
              <li>• Lloyd's List Intelligence</li>
              <li>• Drewry Maritime Research</li>
              <li>• Alphaliner</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">E-commerce Data</h4>
            <ul className="space-y-1">
              <li>• eMarketer</li>
              <li>• Statista Digital Market</li>
              <li>• Euromonitor International</li>
              <li>• McKinsey Global Institute</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Commodity Prices</h4>
            <ul className="space-y-1">
              <li>• Bloomberg Commodities</li>
              <li>• Reuters Commodity Data</li>
              <li>• London Metal Exchange</li>
              <li>• Chicago Board of Trade</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
