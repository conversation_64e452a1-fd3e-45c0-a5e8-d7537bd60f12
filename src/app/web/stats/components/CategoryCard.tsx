interface CategoryCardProps {
  title: string
  description: string
  icon: string
  href: string
  stats: Array<{
    label: string
    value: string
    trend: string
  }>
}

export function CategoryCard({ title, description, icon, href, stats }: CategoryCardProps) {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{icon}</span>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
        <a 
          href={href}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          View Details →
        </a>
      </div>
      
      <p className="text-gray-600 text-sm mb-4">{description}</p>
      
      <div className="space-y-3">
        {stats.map((stat, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-sm text-gray-700">{stat.label}</span>
            <div className="flex items-center space-x-2">
              <span className="font-semibold text-gray-900">{stat.value}</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                stat.trend.startsWith('+') || stat.trend === 'improving'
                  ? 'bg-green-100 text-green-800'
                  : stat.trend.startsWith('-')
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {stat.trend}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
