'use client'

import { useState, useEffect } from 'react'

interface MetricsGridProps {
  metrics: any
}

export function MetricsGrid({ metrics }: MetricsGridProps) {
  const [liveData, setLiveData] = useState<any>({})

  useEffect(() => {
    if (!metrics) return

    const timer = setInterval(() => {
      const now = Date.now()
      const updated = { ...metrics }
      
      // Update real-time counters
      Object.keys(updated.realTime || {}).forEach(key => {
        const metric = updated.realTime[key]
        if (metric.incrementPerSecond) {
          const elapsed = (now - new Date(metric.lastUpdate).getTime()) / 1000
          metric.currentValue = metric.baseValue + (metric.incrementPerSecond * elapsed)
        }
      })
      
      setLiveData(updated)
    }, 1000)

    return () => clearInterval(timer)
  }, [metrics])

  const formatNumber = (num: number, decimals = 0) => {
    if (num >= 1e12) return (num / 1e12).toFixed(decimals) + 'T'
    if (num >= 1e9) return (num / 1e9).toFixed(decimals) + 'B'
    if (num >= 1e6) return (num / 1e6).toFixed(decimals) + 'M'
    if (num >= 1e3) return (num / 1e3).toFixed(decimals) + 'K'
    return num.toLocaleString()
  }

  const MetricCard = ({ title, value, change, unit, source, updated }: any) => (
    <div className="bg-white border border-gray-200 p-3">
      <div className="flex justify-between items-start mb-1">
        <span className="text-xs text-gray-600 uppercase tracking-wide">{title}</span>
        <span className={`text-xs px-1 rounded ${
          change > 0 ? 'bg-green-100 text-green-800' : 
          change < 0 ? 'bg-red-100 text-red-800' : 
          'bg-gray-100 text-gray-800'
        }`}>
          {change > 0 ? '+' : ''}{change}%
        </span>
      </div>
      <div className="text-lg font-bold text-gray-900 mb-1">
        {typeof value === 'number' ? formatNumber(value) : value}
        {unit && <span className="text-sm text-gray-500 ml-1">{unit}</span>}
      </div>
      <div className="text-xs text-gray-500">
        <div>{source}</div>
        <div>{updated}</div>
      </div>
    </div>
  )

  if (!metrics) {
    return (
      <div className="bg-gray-100 rounded p-8 text-center">
        <div className="text-gray-500">Loading metrics...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Real-time Counters */}
      <div className="bg-gray-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">LIVE COUNTERS</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          <MetricCard
            title="World Population"
            value={liveData.realTime?.worldPopulation?.currentValue || 8100000000}
            change={0.85}
            unit=""
            source="UN"
            updated="Live"
          />
          <MetricCard
            title="Global GDP"
            value={liveData.realTime?.globalGDP?.currentValue || 105000000000000}
            change={3.1}
            unit="USD"
            source="WB/IMF"
            updated="Live"
          />
          <MetricCard
            title="Trade Volume"
            value={liveData.realTime?.tradeVolume?.currentValue || 65000000000}
            change={6.8}
            unit="USD"
            source="WTO"
            updated="Live"
          />
          <MetricCard
            title="CO2 Emissions"
            value={liveData.realTime?.co2Emissions?.currentValue || 100000000}
            change={-2.1}
            unit="tons"
            source="IEA"
            updated="Live"
          />
          <MetricCard
            title="Internet Users"
            value={liveData.realTime?.internetUsers?.currentValue || 5200000000}
            change={4.1}
            unit=""
            source="ITU"
            updated="Live"
          />
          <MetricCard
            title="Renewable Energy"
            value={liveData.realTime?.renewableEnergy?.currentValue || 850000}
            change={18.5}
            unit="MWh"
            source="IRENA"
            updated="Live"
          />
        </div>
      </div>

      {/* Demographics */}
      <div className="bg-indigo-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">DEMOGRAPHICS</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
          <MetricCard title="Working Age Global" value={5100000000} change={0.8} unit="" source="UN/ILO" updated="2024" />
          <MetricCard title="Working Age Asia" value={2900000000} change={0.4} unit="" source="UN/ADB" updated="2024" />
          <MetricCard title="Urban Global" value={4400000000} change={1.8} unit="" source="UN Habitat" updated="2024" />
          <MetricCard title="Urban Asia" value={2300000000} change={2.1} unit="" source="UN Habitat" updated="2024" />
          <MetricCard title="Middle Class Global" value={3200000000} change={4.2} unit="" source="Brookings" updated="2024" />
          <MetricCard title="Middle Class Asia" value={2100000000} change={6.8} unit="" source="ADB" updated="2024" />
        </div>
      </div>

      {/* Country Profiles */}
      <div className="bg-blue-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">COUNTRY PROFILES</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          {[
            {
              country: "China", code: "CN", pop: 1412, gdp: 17700, growth: 5.2,
              unemployment: 5.2, ease: 46, competitiveness: 28, innovation: 12,
              strengths: ["Manufacturing", "Infrastructure"],
              opportunities: ["Green Tech", "AI/Robotics"]
            },
            {
              country: "India", code: "IN", pop: 1380, gdp: 3700, growth: 6.8,
              unemployment: 7.8, ease: 63, competitiveness: 68, innovation: 40,
              strengths: ["Demographics", "IT Services"],
              opportunities: ["Digital Economy", "Manufacturing"]
            },
            {
              country: "Japan", code: "JP", pop: 125, gdp: 4200, growth: 1.8,
              unemployment: 2.6, ease: 29, competitiveness: 6, innovation: 13,
              strengths: ["Technology", "Quality"],
              opportunities: ["Automation", "Healthcare Tech"]
            },
            {
              country: "South Korea", code: "KR", pop: 52, gdp: 1800, growth: 3.1,
              unemployment: 2.9, ease: 5, competitiveness: 13, innovation: 10,
              strengths: ["Technology", "Education"],
              opportunities: ["K-Culture", "Semiconductors"]
            }
          ].map((country, index) => (
            <div key={index} className="bg-white border border-gray-200 p-3">
              <div className="text-xs font-bold text-gray-900 mb-2">{country.country} ({country.code})</div>
              <div className="space-y-1 text-xs">
                <div className="grid grid-cols-2 gap-1">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Pop:</span>
                    <span className="font-medium">{country.pop}M</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">GDP:</span>
                    <span className="font-medium">${country.gdp}B</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Growth:</span>
                    <span className="text-green-600">{country.growth}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Unemploy:</span>
                    <span className="font-medium">{country.unemployment}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Ease:</span>
                    <span className="font-medium">#{country.ease}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Compete:</span>
                    <span className="font-medium">#{country.competitiveness}</span>
                  </div>
                </div>
                <div className="mt-2">
                  <div className="text-xs text-gray-700 mb-1">Strengths:</div>
                  <div className="flex flex-wrap gap-1">
                    {country.strengths.map((strength, i) => (
                      <span key={i} className="text-xs bg-green-100 text-green-800 px-1 py-0.5 rounded">
                        {strength}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="mt-1">
                  <div className="text-xs text-gray-700 mb-1">Opportunities:</div>
                  <div className="flex flex-wrap gap-1">
                    {country.opportunities.map((opp, i) => (
                      <span key={i} className="text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded">
                        {opp}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Market Indicators */}
      <div className="bg-green-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">MARKET INDICATORS</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          <MetricCard title="Nikkei 225" value={33853} change={1.2} unit="" source="TSE" updated="15:00 JST" />
          <MetricCard title="Shanghai" value={3089} change={0.8} unit="" source="SSE" updated="15:00 CST" />
          <MetricCard title="Sensex" value={72240} change={1.5} unit="" source="BSE" updated="15:30 IST" />
          <MetricCard title="KOSPI" value={2467} change={0.9} unit="" source="KRX" updated="15:30 KST" />
          <MetricCard title="SET" value={1439} change={0.6} unit="" source="SET" updated="16:30 ICT" />
          <MetricCard title="KLCI" value={1542} change={0.4} unit="" source="BM" updated="17:00 MYT" />
        </div>
      </div>

      {/* Commodities */}
      <div className="bg-yellow-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">COMMODITIES</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
          <MetricCard title="Crude Oil" value={78.50} change={2.1} unit="/bbl" source="NYMEX" updated="Real-time" />
          <MetricCard title="Gold" value={2045} change={0.8} unit="/oz" source="COMEX" updated="Real-time" />
          <MetricCard title="Copper" value={8420} change={3.2} unit="/ton" source="LME" updated="Real-time" />
          <MetricCard title="Iron Ore" value={115} change={-1.5} unit="/ton" source="DCE" updated="Real-time" />
          <MetricCard title="Natural Gas" value={3.85} change={5.8} unit="/MMBtu" source="NYMEX" updated="Real-time" />
          <MetricCard title="Coal" value={95} change={4.1} unit="/ton" source="ICE" updated="Real-time" />
          <MetricCard title="Soybeans" value={1485} change={2.8} unit="/bu" source="CBOT" updated="Real-time" />
          <MetricCard title="Rice" value={16.20} change={1.2} unit="/cwt" source="CBOT" updated="Real-time" />
        </div>
      </div>

      {/* Business Opportunities */}
      <div className="bg-green-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">BUSINESS OPPORTUNITIES</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3">
          {[
            { title: "Green Tech", market: 850, growth: 18.5, potential: "Very High", timeframe: "2025-2030" },
            { title: "Fintech", market: 320, growth: 22.8, potential: "Very High", timeframe: "2024-2027" },
            { title: "HealthTech", market: 180, growth: 25.2, potential: "High", timeframe: "2024-2028" },
            { title: "E-commerce", market: 2800, growth: 15.3, potential: "High", timeframe: "2024-2026" },
            { title: "AI Market", market: 450, growth: 35.2, potential: "Very High", timeframe: "2024-2030" }
          ].map((opp, index) => (
            <div key={index} className="bg-white border border-gray-200 p-3">
              <div className="text-xs font-bold text-gray-900 mb-2">{opp.title}</div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600">Market:</span>
                  <span className="font-medium">${opp.market}B</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Growth:</span>
                  <span className="text-green-600">{opp.growth}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Potential:</span>
                  <span className={`font-medium ${opp.potential === 'Very High' ? 'text-green-600' : 'text-blue-600'}`}>
                    {opp.potential}
                  </span>
                </div>
                <div className="text-xs text-gray-500 mt-1">{opp.timeframe}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Competitive Intelligence */}
      <div className="bg-orange-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">COMPETITIVE INTELLIGENCE</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white border border-gray-200 p-3">
            <h4 className="text-xs font-bold text-gray-900 mb-2">GLOBAL COMPETITIVENESS</h4>
            <div className="space-y-1 text-xs">
              {[
                { country: "Singapore", rank: 1 },
                { country: "Denmark", rank: 2 },
                { country: "Ireland", rank: 3 },
                { country: "Switzerland", rank: 4 },
                { country: "Taiwan", rank: 6 },
                { country: "Hong Kong", rank: 10 }
              ].map((item, i) => (
                <div key={i} className="flex justify-between">
                  <span className="text-gray-700">{item.country}</span>
                  <span className="font-medium">#{item.rank}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white border border-gray-200 p-3">
            <h4 className="text-xs font-bold text-gray-900 mb-2">INNOVATION INDEX</h4>
            <div className="space-y-1 text-xs">
              {[
                { country: "Singapore", rank: 3 },
                { country: "South Korea", rank: 10 },
                { country: "China", rank: 12 },
                { country: "Japan", rank: 13 },
                { country: "Hong Kong", rank: 14 },
                { country: "India", rank: 40 }
              ].map((item, i) => (
                <div key={i} className="flex justify-between">
                  <span className="text-gray-700">{item.country}</span>
                  <span className="font-medium">#{item.rank}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white border border-gray-200 p-3">
            <h4 className="text-xs font-bold text-gray-900 mb-2">EASE OF BUSINESS</h4>
            <div className="space-y-1 text-xs">
              {[
                { country: "Singapore", rank: 2 },
                { country: "Hong Kong", rank: 3 },
                { country: "South Korea", rank: 5 },
                { country: "Taiwan", rank: 15 },
                { country: "Japan", rank: 29 },
                { country: "China", rank: 46 }
              ].map((item, i) => (
                <div key={i} className="flex justify-between">
                  <span className="text-gray-700">{item.country}</span>
                  <span className="font-medium">#{item.rank}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Investment Flows */}
      <div className="bg-purple-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">INVESTMENT FLOWS</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          <MetricCard title="VC Asia" value={85000000000} change={12.0} unit="USD" source="PitchBook" updated="Q4 2024" />
          <MetricCard title="FDI Asia" value={450000000000} change={8.0} unit="USD" source="UNCTAD" updated="2024" />
          <MetricCard title="IPO Asia" value={45000000000} change={-15.2} unit="USD" source="EY" updated="2024" />
          <MetricCard title="PE Asia" value={180000000000} change={8.5} unit="USD" source="Bain" updated="2024" />
          <MetricCard title="ESG Invest" value={2300000000000} change={18.0} unit="USD" source="GSIA" updated="2024" />
          <MetricCard title="Unicorns Asia" value={285} change={15.2} unit="" source="CB Insights" updated="2024" />
        </div>
      </div>

      {/* Vietnam Spotlight */}
      <div className="bg-red-50 p-4 rounded">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">🇻🇳 VIETNAM SPOTLIGHT</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          <MetricCard title="Vietnam GDP" value={408000000000} change={6.5} unit="USD" source="GSO" updated="2024" />
          <MetricCard title="Vietnam FDI" value={15800000000} change={8.2} unit="USD" source="MPI" updated="2024" />
          <MetricCard title="Vietnam Exports" value={371000000000} change={10.5} unit="USD" source="Customs" updated="2024" />
          <MetricCard title="Manufacturing PMI" value={50.8} change={2.1} unit="" source="S&P" updated="Dec 2024" />
          <MetricCard title="Tourism Recovery" value={12600000} change={65.2} unit="visitors" source="VNAT" updated="2024" />
          <MetricCard title="Startup Funding" value={800000000} change={45.0} unit="USD" source="TFI" updated="2024" />
        </div>
        <div className="mt-4 flex space-x-4">
          <a
            href="/web/stats/vietnam"
            className="text-sm bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Vietnam Overview →
          </a>
          <a
            href="/web/stats/vietnam/investment"
            className="text-sm bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700"
          >
            Investment Opportunities →
          </a>
        </div>
      </div>
    </div>
  )
}
