export function RegionalFocus() {
  const regions = [
    {
      name: "China",
      flag: "🇨🇳",
      population: "1.41B",
      gdp: "$17.7T",
      growth: "+5.2%",
      opportunities: ["Manufacturing Hub", "Tech Innovation", "Green Energy"],
      href: "/web/stats/regions/china"
    },
    {
      name: "India",
      flag: "🇮🇳", 
      population: "1.38B",
      gdp: "$3.7T",
      growth: "+6.8%",
      opportunities: ["Digital Services", "Pharmaceuticals", "Renewable Energy"],
      href: "/web/stats/regions/india"
    },
    {
      name: "ASEAN",
      flag: "🌏",
      population: "668M",
      gdp: "$3.6T", 
      growth: "+4.5%",
      opportunities: ["Supply Chain Hub", "Tourism", "Fintech"],
      href: "/web/stats/regions/asean"
    },
    {
      name: "Japan",
      flag: "🇯🇵",
      population: "125M",
      gdp: "$4.2T",
      growth: "+1.8%",
      opportunities: ["Robotics", "Healthcare Tech", "Sustainability"],
      href: "/web/stats/regions/japan"
    }
  ]

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Asia-Pacific Focus</h2>
        <a href="/web/stats/regions" className="text-blue-600 hover:text-blue-800 font-medium">
          View All Regions →
        </a>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {regions.map((region, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-2xl">{region.flag}</span>
              <h3 className="font-semibold text-gray-900">{region.name}</h3>
            </div>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Population:</span>
                <span className="font-medium">{region.population}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">GDP:</span>
                <span className="font-medium">{region.gdp}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Growth:</span>
                <span className="font-medium text-green-600">{region.growth}</span>
              </div>
            </div>
            
            <div className="mb-4">
              <h4 className="text-xs font-medium text-gray-700 mb-2">Key Opportunities:</h4>
              <div className="flex flex-wrap gap-1">
                {region.opportunities.map((opp, oppIndex) => (
                  <span key={oppIndex} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {opp}
                  </span>
                ))}
              </div>
            </div>
            
            <a 
              href={region.href}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Explore {region.name} →
            </a>
          </div>
        ))}
      </div>
    </div>
  )
}
