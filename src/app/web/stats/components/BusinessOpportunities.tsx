export function BusinessOpportunities() {
  const opportunities = [
    {
      title: "Green Energy Transition",
      description: "Asia-Pacific renewable energy market expected to reach $1.5T by 2030",
      market: "$850B",
      growth: "+12.5%",
      regions: ["China", "India", "Australia"],
      icon: "🌱"
    },
    {
      title: "Digital Healthcare",
      description: "Telemedicine and health tech adoption accelerating post-pandemic",
      market: "$180B", 
      growth: "+18.2%",
      regions: ["Singapore", "South Korea", "Japan"],
      icon: "🏥"
    },
    {
      title: "Fintech Innovation",
      description: "Digital payments and blockchain solutions expanding rapidly",
      market: "$320B",
      growth: "+22.8%",
      regions: ["India", "Indonesia", "Philippines"],
      icon: "💳"
    },
    {
      title: "Supply Chain Tech",
      description: "AI-powered logistics and automation driving efficiency gains",
      market: "$450B",
      growth: "+15.3%",
      regions: ["China", "Vietnam", "Thailand"],
      icon: "🚛"
    }
  ]

  return (
    <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">🎯 Top Business Opportunities</h2>
          <p className="text-gray-600 mt-1">Data-driven insights for smart investments</p>
        </div>
        <a href="/web/stats/opportunities" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 font-medium">
          Explore All Opportunities
        </a>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {opportunities.map((opp, index) => (
          <div key={index} className="bg-white rounded-lg p-5 shadow-sm border border-gray-200">
            <div className="flex items-start space-x-3">
              <span className="text-2xl">{opp.icon}</span>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-2">{opp.title}</h3>
                <p className="text-sm text-gray-600 mb-3">{opp.description}</p>
                
                <div className="flex items-center space-x-4 mb-3">
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-500">Market Size:</span>
                    <span className="font-semibold text-green-600">{opp.market}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-500">Growth:</span>
                    <span className="font-semibold text-blue-600">{opp.growth}</span>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {opp.regions.map((region, regionIndex) => (
                    <span key={regionIndex} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                      {region}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
