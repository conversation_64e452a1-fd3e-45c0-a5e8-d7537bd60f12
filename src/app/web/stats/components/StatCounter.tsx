'use client'

import { useState, useEffect } from 'react'

interface StatCounterProps {
  title: string
  value: string
  increment: number
  unit: string
  color: 'blue' | 'green' | 'purple' | 'red' | 'yellow' | 'indigo'
  description?: string
}

export function StatCounter({ title, value, increment, unit, color, description }: StatCounterProps) {
  const [currentValue, setCurrentValue] = useState(parseFloat(value.replace(/,/g, '')))
  
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentValue(prev => prev + increment)
    }, 1000)

    return () => clearInterval(timer)
  }, [increment])

  const formatNumber = (num: number) => {
    if (num >= 1e12) {
      return (num / 1e12).toFixed(1) + 'T'
    } else if (num >= 1e9) {
      return (num / 1e9).toFixed(1) + 'B'
    } else if (num >= 1e6) {
      return (num / 1e6).toFixed(1) + 'M'
    } else if (num >= 1e3) {
      return (num / 1e3).toFixed(1) + 'K'
    }
    return Math.floor(num).toLocaleString()
  }

  const colorClasses = {
    blue: 'from-blue-500 to-blue-600 text-blue-600',
    green: 'from-green-500 to-green-600 text-green-600',
    purple: 'from-purple-500 to-purple-600 text-purple-600',
    red: 'from-red-500 to-red-600 text-red-600',
    yellow: 'from-yellow-500 to-yellow-600 text-yellow-600',
    indigo: 'from-indigo-500 to-indigo-600 text-indigo-600'
  }

  const bgColorClasses = {
    blue: 'bg-blue-50',
    green: 'bg-green-50',
    purple: 'bg-purple-50',
    red: 'bg-red-50',
    yellow: 'bg-yellow-50',
    indigo: 'bg-indigo-50'
  }

  return (
    <div className={`${bgColorClasses[color]} rounded-lg p-6 shadow-sm border border-gray-200`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${colorClasses[color].split(' ').slice(0, 2).join(' ')} animate-pulse`}></div>
      </div>
      
      <div className="space-y-2">
        <div className={`text-2xl font-bold ${colorClasses[color].split(' ')[2]}`}>
          {formatNumber(currentValue)}
        </div>
        
        <div className="text-xs text-gray-500">
          {unit && <span className="mr-2">({unit})</span>}
          <span className="text-green-600">+{increment.toLocaleString()}/sec</span>
        </div>
        
        {description && (
          <p className="text-xs text-gray-600 mt-2">{description}</p>
        )}
      </div>
    </div>
  )
}
