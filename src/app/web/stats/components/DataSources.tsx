export function DataSources() {
  const sources = [
    {
      category: "Economic Data",
      sources: [
        { name: "World Bank", code: "WB", api: "data.worldbank.org", frequency: "Monthly", reliability: 95 },
        { name: "IMF", code: "IMF", api: "imf.org/external/datamapper", frequency: "Quarterly", reliability: 98 },
        { name: "OECD", code: "OECD", api: "stats.oecd.org", frequency: "Monthly", reliability: 96 },
        { name: "Asian Development Bank", code: "ADB", api: "adb.org/data", frequency: "Quarterly", reliability: 94 }
      ]
    },
    {
      category: "Market Data",
      sources: [
        { name: "Bloomberg", code: "BBG", api: "bloomberg.com/professional", frequency: "Real-time", reliability: 99 },
        { name: "Reuters", code: "RTR", api: "refinitiv.com", frequency: "Real-time", reliability: 99 },
        { name: "Yahoo Finance", code: "YF", api: "finance.yahoo.com", frequency: "Real-time", reliability: 92 },
        { name: "Alpha Vantage", code: "AV", api: "alphavantage.co", frequency: "Real-time", reliability: 90 }
      ]
    },
    {
      category: "Trade Data",
      sources: [
        { name: "WTO", code: "WTO", api: "wto.org/statistics", frequency: "Monthly", reliability: 97 },
        { name: "UN Comtrade", code: "COMTRADE", api: "comtrade.un.org", frequency: "Monthly", reliability: 95 },
        { name: "Container Trade Statistics", code: "CTS", api: "containertradestatistics.com", frequency: "Monthly", reliability: 93 },
        { name: "Port Authorities", code: "PORTS", api: "various", frequency: "Monthly", reliability: 88 }
      ]
    },
    {
      category: "Population Data",
      sources: [
        { name: "UN Population Division", code: "UNPD", api: "population.un.org", frequency: "Annual", reliability: 98 },
        { name: "World Bank Population", code: "WBP", api: "data.worldbank.org", frequency: "Annual", reliability: 96 },
        { name: "National Statistical Offices", code: "NSO", api: "various", frequency: "Annual", reliability: 94 },
        { name: "Euromonitor", code: "EMI", api: "euromonitor.com", frequency: "Quarterly", reliability: 91 }
      ]
    },
    {
      category: "Environmental Data",
      sources: [
        { name: "IEA", code: "IEA", api: "iea.org/data", frequency: "Monthly", reliability: 97 },
        { name: "IRENA", code: "IRENA", api: "irena.org/statistics", frequency: "Annual", reliability: 95 },
        { name: "Global Carbon Atlas", code: "GCA", api: "globalcarbonatlas.org", frequency: "Annual", reliability: 93 },
        { name: "Climate Action Tracker", code: "CAT", api: "climateactiontracker.org", frequency: "Quarterly", reliability: 89 }
      ]
    },
    {
      category: "Technology Data",
      sources: [
        { name: "ITU", code: "ITU", api: "itu.int/statistics", frequency: "Annual", reliability: 96 },
        { name: "Statista", code: "STAT", api: "statista.com", frequency: "Monthly", reliability: 88 },
        { name: "CB Insights", code: "CBI", api: "cbinsights.com", frequency: "Real-time", reliability: 92 },
        { name: "PitchBook", code: "PB", api: "pitchbook.com", frequency: "Real-time", reliability: 94 }
      ]
    }
  ]

  const getReliabilityColor = (score: number) => {
    if (score >= 95) return "text-green-600 bg-green-100"
    if (score >= 90) return "text-blue-600 bg-blue-100"
    if (score >= 85) return "text-yellow-600 bg-yellow-100"
    return "text-red-600 bg-red-100"
  }

  const getFrequencyColor = (freq: string) => {
    if (freq === "Real-time") return "text-green-600 bg-green-100"
    if (freq === "Daily") return "text-blue-600 bg-blue-100"
    if (freq === "Monthly") return "text-yellow-600 bg-yellow-100"
    return "text-gray-600 bg-gray-100"
  }

  return (
    <div className="bg-white border border-gray-200 rounded">
      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
        <h3 className="text-sm font-semibold text-gray-900">DATA SOURCES & RELIABILITY</h3>
      </div>
      
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sources.map((category, categoryIndex) => (
            <div key={categoryIndex}>
              <h4 className="text-xs font-semibold text-gray-700 uppercase tracking-wide mb-3">
                {category.category}
              </h4>
              <div className="space-y-2">
                {category.sources.map((source, sourceIndex) => (
                  <div key={sourceIndex} className="border border-gray-200 rounded p-2">
                    <div className="flex justify-between items-start mb-1">
                      <div>
                        <span className="text-sm font-medium text-gray-900">{source.name}</span>
                        <span className="text-xs text-gray-500 ml-1">({source.code})</span>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${getReliabilityColor(source.reliability)}`}>
                        {source.reliability}%
                      </span>
                    </div>
                    
                    <div className="text-xs text-gray-600 mb-1">{source.api}</div>
                    
                    <div className="flex justify-between items-center">
                      <span className={`text-xs px-2 py-1 rounded-full ${getFrequencyColor(source.frequency)}`}>
                        {source.frequency}
                      </span>
                      <div className="flex space-x-1">
                        <div className={`w-2 h-2 rounded-full ${source.reliability >= 95 ? 'bg-green-500' : source.reliability >= 90 ? 'bg-blue-500' : 'bg-yellow-500'}`}></div>
                        <div className={`w-2 h-2 rounded-full ${source.frequency === 'Real-time' ? 'bg-green-500' : source.frequency === 'Daily' ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center text-xs text-gray-600">
            <div className="flex space-x-4">
              <span>🟢 Real-time</span>
              <span>🔵 Daily/Monthly</span>
              <span>🟡 Quarterly/Annual</span>
              <span>🔴 Irregular</span>
            </div>
            <div className="flex space-x-4">
              <span>Reliability: 🟢 95%+ 🔵 90%+ 🟡 85%+ 🔴 &lt;85%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
