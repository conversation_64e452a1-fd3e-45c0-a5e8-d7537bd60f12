'use client'

import { useState, useEffect } from 'react'
import { StatCounter } from './components/StatCounter'
import { MetricsGrid } from './components/MetricsGrid'
import { DataSources } from './components/DataSources'

export default function WorldStatsPage() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [metrics, setMetrics] = useState(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    // Load metrics data
    fetch('/api/web/stats/metrics')
      .then(res => res.json())
      .then(data => setMetrics(data))
      .catch(err => console.error('Failed to load metrics:', err))

    return () => clearInterval(timer)
  }, [])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gray-900 text-white p-4 rounded">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">WorldStats</h1>
            <span className="text-sm opacity-75">Real-time Business Intelligence</span>
          </div>
          <div className="text-right">
            <div className="text-lg font-mono">{currentTime.toISOString().slice(0, 19)}Z</div>
            <div className="text-xs opacity-75">Last Update: {metrics?.lastUpdated || 'Loading...'}</div>
          </div>
        </div>
      </div>

      {/* Live Metrics Grid */}
      <MetricsGrid metrics={metrics} />

      {/* Data Sources */}
      <DataSources />
    </div>
  )
}
