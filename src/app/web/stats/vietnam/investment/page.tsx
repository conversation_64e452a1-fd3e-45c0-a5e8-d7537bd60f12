'use client'

import { useState, useEffect } from 'react'

export default function VietnamInvestmentPage() {
  const [investmentData, setInvestmentData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load Vietnam investment data
    fetch('/api/web/stats/data-fetcher', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        metric: 'vietnamInvestment',
        category: 'investment',
        groqPrompt: 'Get comprehensive Vietnam investment opportunities, market analysis, FDI data, startup ecosystem, real estate, manufacturing, and business entry strategies in JSON format'
      })
    })
    .then(res => res.json())
    .then(data => {
      setInvestmentData(data.data)
      setLoading(false)
    })
    .catch(err => {
      console.error('Failed to load Vietnam investment data:', err)
      setLoading(false)
    })
  }, [])

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="text-gray-500">Loading Vietnam investment data...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 to-yellow-500 text-white p-6 rounded-lg">
        <div className="flex items-center space-x-4">
          <span className="text-4xl">🇻🇳💰</span>
          <div>
            <h1 className="text-3xl font-bold">Vietnam Investment Opportunities</h1>
            <p className="text-red-100">Southeast Asia's Fastest Growing Investment Destination</p>
          </div>
        </div>
      </div>

      {/* Investment Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {[
          { label: "FDI Inflows", value: "$15.8B", change: "+8.2%", color: "green" },
          { label: "New Projects", value: "2,847", change: "+12.5%", color: "blue" },
          { label: "Registered Capital", value: "$20.3B", change: "+15.8%", color: "purple" },
          { label: "Startups Funded", value: "285", change: "+35%", color: "indigo" },
          { label: "VC Investment", value: "$800M", change: "+45%", color: "green" },
          { label: "IPO Pipeline", value: "12", change: "+200%", color: "yellow" }
        ].map((metric, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded p-4">
            <div className="text-xs text-gray-600 uppercase tracking-wide mb-1">{metric.label}</div>
            <div className="text-xl font-bold text-gray-900 mb-1">{metric.value}</div>
            <div className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
              {metric.change}
            </div>
          </div>
        ))}
      </div>

      {/* Top Investment Opportunities */}
      <div className="bg-white border border-gray-200 rounded p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6">🚀 Top Investment Opportunities</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[
            {
              title: "Manufacturing & Supply Chain",
              market: "$180B",
              growth: "+12%",
              timeframe: "2024-2027",
              risk: "Medium",
              roi: "15-25%",
              description: "China+1 strategy driving manufacturing relocation",
              opportunities: [
                "Electronics assembly and components",
                "Automotive parts manufacturing", 
                "Textile and garment production",
                "Food processing and packaging"
              ],
              keyFactors: ["Low labor costs", "Strategic location", "Trade agreements", "Infrastructure development"]
            },
            {
              title: "Digital Economy & Fintech",
              market: "$25B",
              growth: "+35%",
              timeframe: "2024-2026",
              risk: "High",
              roi: "25-50%",
              description: "Rapid digitalization and financial inclusion",
              opportunities: [
                "Digital payment solutions",
                "E-commerce platforms",
                "Digital banking services",
                "Blockchain and crypto"
              ],
              keyFactors: ["Young population", "Mobile penetration", "Government support", "Regulatory clarity"]
            },
            {
              title: "Real Estate & Infrastructure",
              market: "$45B",
              growth: "+8%",
              timeframe: "2024-2030",
              risk: "Medium",
              roi: "12-20%",
              description: "Urbanization and industrial development",
              opportunities: [
                "Industrial parks and logistics",
                "Residential developments",
                "Commercial real estate",
                "Smart city projects"
              ],
              keyFactors: ["Urbanization", "FDI growth", "Infrastructure needs", "Government investment"]
            },
            {
              title: "Tourism & Hospitality",
              market: "$35B",
              growth: "+65%",
              timeframe: "2024-2025",
              risk: "Medium-High",
              roi: "18-30%",
              description: "Post-COVID recovery and tourism boom",
              opportunities: [
                "Hotel and resort development",
                "Travel technology platforms",
                "Experience and adventure tourism",
                "Sustainable tourism projects"
              ],
              keyFactors: ["Tourism recovery", "Infrastructure", "Natural beauty", "Cultural heritage"]
            }
          ].map((opportunity, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-5">
              <div className="flex justify-between items-start mb-3">
                <h4 className="text-lg font-semibold text-gray-900">{opportunity.title}</h4>
                <div className="text-right">
                  <div className="text-sm font-bold text-green-600">{opportunity.market}</div>
                  <div className="text-xs text-gray-600">{opportunity.growth}</div>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mb-4">{opportunity.description}</p>
              
              <div className="grid grid-cols-2 gap-4 mb-4 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600">Timeframe:</span>
                  <span className="font-medium">{opportunity.timeframe}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Risk Level:</span>
                  <span className={`font-medium ${
                    opportunity.risk === 'Low' ? 'text-green-600' :
                    opportunity.risk === 'Medium' ? 'text-yellow-600' :
                    opportunity.risk === 'Medium-High' ? 'text-orange-600' :
                    'text-red-600'
                  }`}>{opportunity.risk}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Expected ROI:</span>
                  <span className="font-medium text-green-600">{opportunity.roi}</span>
                </div>
              </div>
              
              <div className="mb-4">
                <h5 className="text-xs font-semibold text-gray-700 mb-2">Key Opportunities:</h5>
                <div className="space-y-1">
                  {opportunity.opportunities.map((opp, i) => (
                    <div key={i} className="text-xs text-gray-600">• {opp}</div>
                  ))}
                </div>
              </div>
              
              <div>
                <h5 className="text-xs font-semibold text-gray-700 mb-2">Success Factors:</h5>
                <div className="flex flex-wrap gap-1">
                  {opportunity.keyFactors.map((factor, i) => (
                    <span key={i} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {factor}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Investment Climate */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white border border-gray-200 rounded p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Investment Incentives</h3>
          <div className="space-y-4">
            {[
              {
                category: "Tax Incentives",
                benefits: [
                  "Corporate tax: 10-15% (vs standard 20%)",
                  "Import duty exemption on machinery",
                  "Land rental incentives",
                  "R&D tax deductions"
                ]
              },
              {
                category: "Special Economic Zones",
                benefits: [
                  "Streamlined licensing procedures",
                  "Enhanced infrastructure",
                  "Preferential policies",
                  "One-stop service centers"
                ]
              },
              {
                category: "Sector-Specific Incentives",
                benefits: [
                  "High-tech: 10% tax for 15 years",
                  "Manufacturing: Land lease incentives",
                  "Green energy: Feed-in tariffs",
                  "Tourism: Infrastructure support"
                ]
              }
            ].map((incentive, index) => (
              <div key={index} className="p-4 bg-green-50 rounded">
                <h4 className="font-semibold text-green-900 mb-2">{incentive.category}</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  {incentive.benefits.map((benefit, i) => (
                    <li key={i}>• {benefit}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Market Entry Strategies</h3>
          <div className="space-y-4">
            {[
              {
                strategy: "Joint Venture",
                pros: ["Local market knowledge", "Regulatory compliance", "Established networks"],
                cons: ["Shared control", "Profit sharing", "Cultural differences"],
                suitability: "Manufacturing, Real Estate"
              },
              {
                strategy: "Wholly Foreign-Owned Enterprise",
                pros: ["Full control", "100% profits", "Strategic flexibility"],
                cons: ["Higher risk", "Regulatory complexity", "Local knowledge gap"],
                suitability: "Technology, Services"
              },
              {
                strategy: "Representative Office",
                pros: ["Low cost", "Market research", "Relationship building"],
                cons: ["No revenue generation", "Limited activities", "Temporary solution"],
                suitability: "Market Entry, Research"
              }
            ].map((strategy, index) => (
              <div key={index} className="border border-gray-200 rounded p-4">
                <h4 className="font-semibold text-gray-900 mb-2">{strategy.strategy}</h4>
                <div className="grid grid-cols-2 gap-2 mb-2">
                  <div>
                    <h5 className="text-xs font-medium text-green-700 mb-1">Pros:</h5>
                    <ul className="text-xs text-green-600 space-y-1">
                      {strategy.pros.map((pro, i) => (
                        <li key={i}>• {pro}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-red-700 mb-1">Cons:</h5>
                    <ul className="text-xs text-red-600 space-y-1">
                      {strategy.cons.map((con, i) => (
                        <li key={i}>• {con}</li>
                      ))}
                    </ul>
                  </div>
                </div>
                <div className="text-xs">
                  <span className="text-gray-600">Best for: </span>
                  <span className="font-medium">{strategy.suitability}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Regional Analysis */}
      <div className="bg-white border border-gray-200 rounded p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Regional Investment Hotspots</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            {
              region: "Ho Chi Minh City",
              population: "9.0M",
              gdp: "$58B",
              strengths: ["Financial center", "Tech hub", "Logistics"],
              industries: ["Fintech", "E-commerce", "Manufacturing"],
              fdi: "$3.2B"
            },
            {
              region: "Hanoi",
              population: "8.1M", 
              gdp: "$45B",
              strengths: ["Government", "Education", "Culture"],
              industries: ["Technology", "Automotive", "Textiles"],
              fdi: "$2.8B"
            },
            {
              region: "Da Nang",
              population: "1.2M",
              gdp: "$8B",
              strengths: ["Tourism", "Port", "IT services"],
              industries: ["Tourism", "Software", "Logistics"],
              fdi: "$1.1B"
            },
            {
              region: "Hai Phong",
              population: "2.0M",
              gdp: "$12B",
              strengths: ["Port", "Manufacturing", "Logistics"],
              industries: ["Electronics", "Automotive", "Chemicals"],
              fdi: "$1.8B"
            },
            {
              region: "Can Tho",
              population: "1.3M",
              gdp: "$6B",
              strengths: ["Agriculture", "Food processing", "River transport"],
              industries: ["Agribusiness", "Food", "Aquaculture"],
              fdi: "$0.5B"
            },
            {
              region: "Binh Duong",
              population: "2.5M",
              gdp: "$18B",
              strengths: ["Manufacturing", "Industrial parks", "Proximity to HCMC"],
              industries: ["Electronics", "Textiles", "Furniture"],
              fdi: "$2.1B"
            }
          ].map((region, index) => (
            <div key={index} className="border border-gray-200 rounded p-4">
              <h4 className="font-semibold text-gray-900 mb-2">{region.region}</h4>
              <div className="space-y-2 text-xs">
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Population:</span>
                    <span className="font-medium">{region.population}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">GDP:</span>
                    <span className="font-medium">{region.gdp}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">FDI 2024:</span>
                    <span className="font-medium text-green-600">{region.fdi}</span>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium text-gray-700 mb-1">Key Strengths:</h5>
                  <div className="flex flex-wrap gap-1">
                    {region.strengths.map((strength, i) => (
                      <span key={i} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                        {strength}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium text-gray-700 mb-1">Key Industries:</h5>
                  <div className="flex flex-wrap gap-1">
                    {region.industries.map((industry, i) => (
                      <span key={i} className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                        {industry}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Assessment */}
      <div className="bg-white border border-gray-200 rounded p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Investment Risk Assessment</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Risk Factors</h4>
            <div className="space-y-3">
              {[
                { risk: "Political Risk", level: "Low", description: "Stable government, consistent policies" },
                { risk: "Currency Risk", level: "Medium", description: "VND volatility vs USD" },
                { risk: "Regulatory Risk", level: "Medium", description: "Evolving legal framework" },
                { risk: "Market Risk", level: "Medium-High", description: "Competition, market saturation" },
                { risk: "Operational Risk", level: "Medium", description: "Infrastructure, skills gap" }
              ].map((item, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <span className="font-medium text-gray-900">{item.risk}</span>
                    <div className="text-xs text-gray-600">{item.description}</div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    item.level === 'Low' ? 'bg-green-100 text-green-800' :
                    item.level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {item.level}
                  </span>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Mitigation Strategies</h4>
            <div className="space-y-3">
              {[
                { strategy: "Local Partnerships", benefit: "Navigate regulations, cultural understanding" },
                { strategy: "Diversified Portfolio", benefit: "Spread risk across sectors/regions" },
                { strategy: "Currency Hedging", benefit: "Protect against VND fluctuations" },
                { strategy: "Phased Investment", benefit: "Test market before full commitment" },
                { strategy: "Professional Services", benefit: "Legal, tax, and compliance support" }
              ].map((item, index) => (
                <div key={index} className="p-3 bg-blue-50 rounded">
                  <h5 className="font-medium text-blue-900">{item.strategy}</h5>
                  <p className="text-xs text-blue-800">{item.benefit}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded p-4">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">DATA SOURCES</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Investment Data</h4>
            <ul className="space-y-1">
              <li>• Foreign Investment Agency (FIA)</li>
              <li>• Ministry of Planning & Investment</li>
              <li>• Vietnam Investment Review</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Market Research</h4>
            <ul className="space-y-1">
              <li>• Vietnam Chamber of Commerce</li>
              <li>• McKinsey Vietnam</li>
              <li>• Deloitte Vietnam</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Startup Ecosystem</h4>
            <ul className="space-y-1">
              <li>• Topica Founder Institute</li>
              <li>• Vietnam Startup Report</li>
              <li>• 500 Startups Vietnam</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Risk Assessment</h4>
            <ul className="space-y-1">
              <li>• Moody's Analytics</li>
              <li>• S&P Global Ratings</li>
              <li>• Political Risk Services</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
