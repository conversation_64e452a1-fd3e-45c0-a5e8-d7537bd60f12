'use client'

import { useState, useEffect } from 'react'

export default function VietnamPage() {
  const [vietnamData, setVietnamData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load Vietnam data
    Promise.all([
      fetch('/api/web/stats/metrics').then(res => res.json()),
      fetch('/api/web/stats/data-fetcher', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric: 'vietnam',
          category: 'countryProfiles',
          groqPrompt: 'Get comprehensive Vietnam economic data, demographics, business climate, and latest statistics in JSON format'
        })
      }).then(res => res.json())
    ])
    .then(([metricsData, vietnamSpecific]) => {
      setVietnamData({
        metrics: metricsData.data,
        specific: vietnamSpecific.data
      })
      setLoading(false)
    })
    .catch(err => {
      console.error('Failed to load Vietnam data:', err)
      setLoading(false)
    })
  }, [])

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="text-gray-500">Loading Vietnam data...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-red-600 text-white p-6 rounded-lg">
        <div className="flex items-center space-x-4">
          <span className="text-4xl">🇻🇳</span>
          <div>
            <h1 className="text-3xl font-bold">Vietnam Business Intelligence</h1>
            <p className="text-red-100">Southeast Asia's Manufacturing Powerhouse</p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {[
          { label: "Population", value: "98M", change: "+0.9%", color: "blue" },
          { label: "GDP", value: "$408B", change: "****%", color: "green" },
          { label: "GDP per Capita", value: "$4,164", change: "****%", color: "green" },
          { label: "FDI", value: "$15.8B", change: "****%", color: "purple" },
          { label: "Exports", value: "$371B", change: "+10.5%", color: "indigo" },
          { label: "Unemployment", value: "2.3%", change: "-0.2%", color: "green" }
        ].map((metric, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded p-4">
            <div className="text-xs text-gray-600 uppercase tracking-wide mb-1">{metric.label}</div>
            <div className="text-xl font-bold text-gray-900 mb-1">{metric.value}</div>
            <div className={`text-xs px-2 py-1 rounded-full ${
              metric.change.startsWith('+') ? 'bg-green-100 text-green-800' : 
              metric.change.startsWith('-') && metric.label === 'Unemployment' ? 'bg-green-100 text-green-800' :
              'bg-red-100 text-red-800'
            }`}>
              {metric.change}
            </div>
          </div>
        ))}
      </div>

      {/* Economic Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white border border-gray-200 rounded p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Economic Indicators</h3>
          <div className="space-y-3">
            {[
              { indicator: "GDP Growth", value: "6.5%", benchmark: "Asia: 4.2%", status: "outperforming" },
              { indicator: "Inflation Rate", value: "3.2%", benchmark: "Target: 4%", status: "controlled" },
              { indicator: "Manufacturing PMI", value: "50.8", benchmark: "Expansion: >50", status: "expanding" },
              { indicator: "Services PMI", value: "52.1", benchmark: "Expansion: >50", status: "strong" },
              { indicator: "Trade Balance", value: "+$10B", benchmark: "Surplus", status: "positive" },
              { indicator: "FX Reserves", value: "$86B", benchmark: "6.2 months import", status: "adequate" }
            ].map((item, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{item.indicator}</span>
                  <div className="text-xs text-gray-600">{item.benchmark}</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-gray-900">{item.value}</div>
                  <div className={`text-xs px-2 py-1 rounded-full ${
                    item.status === 'outperforming' || item.status === 'strong' || item.status === 'positive' 
                      ? 'bg-green-100 text-green-800'
                      : item.status === 'expanding' || item.status === 'controlled' || item.status === 'adequate'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {item.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Trade & Investment</h3>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Top Export Products</h4>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { product: "Electronics", value: "$142B", share: "38%" },
                  { product: "Textiles", value: "$39B", share: "11%" },
                  { product: "Footwear", value: "$22B", share: "6%" },
                  { product: "Machinery", value: "$18B", share: "5%" }
                ].map((item, i) => (
                  <div key={i} className="text-xs">
                    <div className="font-medium">{item.product}</div>
                    <div className="text-gray-600">{item.value} ({item.share})</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Major Trading Partners</h4>
              <div className="space-y-1">
                {[
                  { country: "China", trade: "$175B", flag: "🇨🇳" },
                  { country: "USA", trade: "$123B", flag: "🇺🇸" },
                  { country: "South Korea", trade: "$68B", flag: "🇰🇷" },
                  { country: "Japan", trade: "$46B", flag: "🇯🇵" }
                ].map((partner, i) => (
                  <div key={i} className="flex justify-between items-center text-xs">
                    <div className="flex items-center space-x-1">
                      <span>{partner.flag}</span>
                      <span>{partner.country}</span>
                    </div>
                    <span className="font-medium">{partner.trade}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-gray-700 mb-2">FDI by Sector (2024)</h4>
              <div className="space-y-1">
                {[
                  { sector: "Manufacturing", amount: "$8.2B", share: "52%" },
                  { sector: "Real Estate", amount: "$2.1B", share: "13%" },
                  { sector: "Technology", amount: "$1.8B", share: "11%" },
                  { sector: "Energy", amount: "$1.2B", share: "8%" }
                ].map((sector, i) => (
                  <div key={i} className="flex justify-between items-center text-xs">
                    <span>{sector.sector}</span>
                    <div>
                      <span className="font-medium">{sector.amount}</span>
                      <span className="text-gray-600 ml-1">({sector.share})</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* SWOT Analysis */}
      <div className="bg-white border border-gray-200 rounded p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">SWOT Analysis</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-green-50 rounded">
            <h4 className="font-semibold text-green-900 mb-3">💪 Strengths</h4>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• Young demographics (median age 32)</li>
              <li>• Strategic location in ASEAN</li>
              <li>• Political stability</li>
              <li>• Competitive labor costs</li>
              <li>• Growing middle class</li>
              <li>• Strong manufacturing base</li>
            </ul>
          </div>
          
          <div className="p-4 bg-red-50 rounded">
            <h4 className="font-semibold text-red-900 mb-3">⚠️ Weaknesses</h4>
            <ul className="text-sm text-red-800 space-y-1">
              <li>• Infrastructure gaps</li>
              <li>• Skills shortage</li>
              <li>• Bureaucratic processes</li>
              <li>• Environmental challenges</li>
              <li>• Limited capital markets</li>
              <li>• Currency volatility</li>
            </ul>
          </div>
          
          <div className="p-4 bg-blue-50 rounded">
            <h4 className="font-semibold text-blue-900 mb-3">🚀 Opportunities</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• China+1 manufacturing shift</li>
              <li>• Digital transformation</li>
              <li>• Tourism recovery</li>
              <li>• Green energy transition</li>
              <li>• EVFTA trade benefits</li>
              <li>• Startup ecosystem growth</li>
            </ul>
          </div>
          
          <div className="p-4 bg-yellow-50 rounded">
            <h4 className="font-semibold text-yellow-900 mb-3">⚡ Threats</h4>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• China economic slowdown</li>
              <li>• Climate change impacts</li>
              <li>• US-China trade tensions</li>
              <li>• Regional competition</li>
              <li>• Rising labor costs</li>
              <li>• Debt sustainability</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Business Climate */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white border border-gray-200 rounded p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Business Rankings</h3>
          <div className="space-y-3">
            {[
              { ranking: "Ease of Doing Business", position: 70, total: 190, trend: "↗️ +1" },
              { ranking: "Global Competitiveness", position: 67, total: 141, trend: "↗️ +10" },
              { ranking: "Innovation Index", position: 44, total: 132, trend: "↗️ +2" },
              { ranking: "Corruption Perception", position: 77, total: 180, trend: "↘️ -8" },
              { ranking: "Economic Freedom", position: 61, total: 178, trend: "→ 0" }
            ].map((item, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span className="font-medium text-gray-900">{item.ranking}</span>
                <div className="text-right">
                  <div className="font-bold">#{item.position}/{item.total}</div>
                  <div className="text-xs text-gray-600">{item.trend}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Key Industries</h3>
          <div className="grid grid-cols-2 gap-4">
            {[
              { industry: "Electronics", contribution: "38%", growth: "+12%", icon: "📱" },
              { industry: "Textiles", contribution: "15%", growth: "+8%", icon: "👕" },
              { industry: "Tourism", contribution: "8%", growth: "+65%", icon: "✈️" },
              { industry: "Agriculture", contribution: "12%", growth: "+3%", icon: "🌾" },
              { industry: "Automotive", contribution: "6%", growth: "+25%", icon: "🚗" },
              { industry: "Technology", contribution: "4%", growth: "+35%", icon: "💻" }
            ].map((industry, index) => (
              <div key={index} className="p-3 border border-gray-200 rounded">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-lg">{industry.icon}</span>
                  <span className="font-medium text-sm">{industry.industry}</span>
                </div>
                <div className="text-xs space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-600">GDP Share:</span>
                    <span className="font-medium">{industry.contribution}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Growth:</span>
                    <span className="text-green-600">{industry.growth}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded p-4">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">DATA SOURCES</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Economic Data</h4>
            <ul className="space-y-1">
              <li>• General Statistics Office (GSO)</li>
              <li>• State Bank of Vietnam (SBV)</li>
              <li>• Ministry of Planning & Investment</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Trade Data</h4>
            <ul className="space-y-1">
              <li>• Vietnam Customs</li>
              <li>• Ministry of Industry & Trade</li>
              <li>• Vietnam Chamber of Commerce</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Investment Data</h4>
            <ul className="space-y-1">
              <li>• Foreign Investment Agency</li>
              <li>• Vietnam Investment Review</li>
              <li>• UNCTAD FDI Statistics</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">Rankings</h4>
            <ul className="space-y-1">
              <li>• World Bank Doing Business</li>
              <li>• World Economic Forum</li>
              <li>• Heritage Foundation</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
