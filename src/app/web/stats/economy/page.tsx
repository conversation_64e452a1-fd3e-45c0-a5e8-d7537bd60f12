'use client'

import { useState, useEffect } from 'react'
import { StatCounter } from '../components/StatCounter'

export default function EconomyPage() {
  const [economyData, setEconomyData] = useState(null)

  useEffect(() => {
    // Load economy data
    fetch('/api/web/stats/economy')
      .then(res => res.json())
      .then(data => setEconomyData(data))
      .catch(err => console.error('Failed to load economy data:', err))
  }, [])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl text-white p-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">📈 Economic Indicators</h1>
        <p className="text-xl opacity-90">GDP growth, market valuations, and investment opportunities across global markets</p>
      </div>

      {/* Global Economic Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCounter
          title="Global GDP"
          value="105,000,000,000,000"
          increment={3300000}
          unit="USD"
          color="green"
          description="Nominal GDP worldwide"
        />
        <StatCounter
          title="Stock Market Cap"
          value="45,000,000,000,000"
          increment={1500000}
          unit="USD"
          color="blue"
          description="Global equity markets"
        />
        <StatCounter
          title="FDI Flows Today"
          value="2,400,000,000"
          increment={27777}
          unit="USD"
          color="purple"
          description="Foreign direct investment"
        />
        <StatCounter
          title="Currency Traded"
          value="7,500,000,000,000"
          increment={86805555}
          unit="USD"
          color="indigo"
          description="Daily forex volume"
        />
      </div>

      {/* Asia-Pacific Economic Overview */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Asia-Pacific Economic Powerhouses</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { 
              country: "China", 
              flag: "🇨🇳",
              gdp: "$17.7T", 
              growth: "+5.2%", 
              gdpPerCapita: "$12,556",
              marketCap: "$15.2T",
              opportunities: ["Green tech", "AI/Robotics", "Healthcare"]
            },
            { 
              country: "Japan", 
              flag: "🇯🇵",
              gdp: "$4.2T", 
              growth: "+1.8%", 
              gdpPerCapita: "$33,815",
              marketCap: "$6.8T",
              opportunities: ["Automation", "Sustainability", "Senior care"]
            },
            { 
              country: "India", 
              flag: "🇮🇳",
              gdp: "$3.7T", 
              growth: "+6.8%", 
              gdpPerCapita: "$2,277",
              marketCap: "$4.3T",
              opportunities: ["Digital services", "Fintech", "Pharma"]
            },
            { 
              country: "South Korea", 
              flag: "🇰🇷",
              gdp: "$1.8T", 
              growth: "+3.1%", 
              gdpPerCapita: "$34,758",
              marketCap: "$2.1T",
              opportunities: ["Semiconductors", "K-culture", "Gaming"]
            },
            { 
              country: "Indonesia", 
              flag: "🇮🇩",
              gdp: "$1.3T", 
              growth: "+5.0%", 
              gdpPerCapita: "$4,256",
              marketCap: "$650B",
              opportunities: ["E-commerce", "Commodities", "Tourism"]
            },
            { 
              country: "Taiwan", 
              flag: "🇹🇼",
              gdp: "$790B", 
              growth: "+2.9%", 
              gdpPerCapita: "$33,143",
              marketCap: "$1.8T",
              opportunities: ["Semiconductors", "Tech manufacturing", "Biotech"]
            }
          ].map((country, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-5">
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">{country.flag}</span>
                <h3 className="font-semibold text-gray-900">{country.country}</h3>
              </div>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">GDP:</span>
                  <span className="font-semibold text-green-600">{country.gdp}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Growth:</span>
                  <span className="font-semibold text-blue-600">{country.growth}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">GDP per capita:</span>
                  <span className="font-medium">{country.gdpPerCapita}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Market Cap:</span>
                  <span className="font-medium">{country.marketCap}</span>
                </div>
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Key Opportunities:</h4>
                  <div className="flex flex-wrap gap-1">
                    {country.opportunities.map((opp, oppIndex) => (
                      <span key={oppIndex} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        {opp}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Market Indicators */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Stock Market Performance</h3>
          <div className="space-y-4">
            {[
              { index: "Nikkei 225 (Japan)", value: "33,853", change: "+1.2%", marketCap: "$6.8T" },
              { index: "Shanghai Composite", value: "3,089", change: "+0.8%", marketCap: "$8.1T" },
              { index: "Sensex (India)", value: "72,240", change: "+1.5%", marketCap: "$4.3T" },
              { index: "KOSPI (South Korea)", value: "2,467", change: "+0.9%", marketCap: "$2.1T" },
              { index: "SET (Thailand)", value: "1,439", change: "+0.6%", marketCap: "$520B" },
              { index: "KLCI (Malaysia)", value: "1,542", change: "+0.4%", marketCap: "$450B" }
            ].map((market, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{market.index}</span>
                  <span className="text-sm text-gray-600 ml-2">({market.marketCap})</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="font-semibold text-gray-900">{market.value}</span>
                  <span className={`text-sm px-2 py-1 rounded-full ${
                    market.change.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {market.change}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Investment Flows & Opportunities</h3>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">💰 Venture Capital</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <div className="flex justify-between">
                  <span>Asia VC Investment 2024:</span>
                  <span className="font-semibold">$85B</span>
                </div>
                <div className="flex justify-between">
                  <span>Growth vs 2023:</span>
                  <span className="font-semibold text-green-600">+12%</span>
                </div>
                <div className="text-xs mt-2">Top sectors: Fintech, AI, Healthcare</div>
              </div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">🏭 Foreign Direct Investment</h4>
              <div className="text-sm text-green-800 space-y-1">
                <div className="flex justify-between">
                  <span>Asia FDI Inflows 2024:</span>
                  <span className="font-semibold">$890B</span>
                </div>
                <div className="flex justify-between">
                  <span>Growth vs 2023:</span>
                  <span className="font-semibold text-green-600">+8%</span>
                </div>
                <div className="text-xs mt-2">Top destinations: China, India, Singapore</div>
              </div>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">📊 IPO Activity</h4>
              <div className="text-sm text-purple-800 space-y-1">
                <div className="flex justify-between">
                  <span>Asia IPO Proceeds 2024:</span>
                  <span className="font-semibold">$45B</span>
                </div>
                <div className="flex justify-between">
                  <span>Number of IPOs:</span>
                  <span className="font-semibold">285</span>
                </div>
                <div className="text-xs mt-2">Top exchanges: Hong Kong, Shanghai, Mumbai</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Economic Indicators Dashboard */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Key Economic Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { indicator: "Inflation Rate", value: "3.2%", trend: "stable", region: "Asia Average" },
            { indicator: "Interest Rates", value: "2.8%", trend: "rising", region: "Central Banks" },
            { indicator: "Unemployment", value: "4.1%", trend: "declining", region: "Asia Pacific" },
            { indicator: "Trade Balance", value: "+$450B", trend: "positive", region: "Asia Surplus" },
            { indicator: "Currency Strength", value: "Mixed", trend: "volatile", region: "vs USD" },
            { indicator: "Commodity Prices", value: "+5.2%", trend: "rising", region: "YoY Change" },
            { indicator: "Manufacturing PMI", value: "52.1", trend: "expanding", region: "Asia Average" },
            { indicator: "Services PMI", value: "54.3", trend: "expanding", region: "Asia Average" }
          ].map((indicator, index) => (
            <div key={index} className="p-3 border border-gray-200 rounded">
              <div className="text-sm text-gray-600 mb-1">{indicator.indicator}</div>
              <div className="font-semibold text-gray-900 mb-1">{indicator.value}</div>
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">{indicator.region}</span>
                <span className={`px-2 py-1 rounded-full ${
                  indicator.trend === 'rising' || indicator.trend === 'expanding' || indicator.trend === 'positive' 
                    ? 'bg-green-100 text-green-800'
                    : indicator.trend === 'declining' 
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {indicator.trend}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Data Sources</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Economic Data</h4>
            <ul className="space-y-1">
              <li>• World Bank</li>
              <li>• International Monetary Fund</li>
              <li>• OECD Statistics</li>
              <li>• Asian Development Bank</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Market Data</h4>
            <ul className="space-y-1">
              <li>• Bloomberg Terminal</li>
              <li>• Reuters Eikon</li>
              <li>• Stock Exchanges</li>
              <li>• Central Banks</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Investment Data</h4>
            <ul className="space-y-1">
              <li>• PitchBook</li>
              <li>• CB Insights</li>
              <li>• Preqin</li>
              <li>• EY Global IPO Trends</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Business Intelligence</h4>
            <ul className="space-y-1">
              <li>• McKinsey Global Institute</li>
              <li>• BCG Research</li>
              <li>• Deloitte Insights</li>
              <li>• PwC Analysis</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
