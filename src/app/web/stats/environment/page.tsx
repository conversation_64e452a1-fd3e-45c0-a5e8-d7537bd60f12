'use client'

import { useState, useEffect } from 'react'
import { StatCounter } from '../components/StatCounter'

export default function EnvironmentPage() {
  const [environmentData, setEnvironmentData] = useState(null)

  useEffect(() => {
    // Load environment data
    fetch('/api/web/stats/environment')
      .then(res => res.json())
      .then(data => setEnvironmentData(data))
      .catch(err => console.error('Failed to load environment data:', err))
  }, [])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-teal-600 rounded-2xl text-white p-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">🌱 Environment & ESG</h1>
        <p className="text-xl opacity-90">Carbon emissions, renewable energy adoption, and sustainability investment opportunities</p>
      </div>

      {/* Global Environmental Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCounter
          title="CO2 Emissions Today"
          value="100,000,000"
          increment={1157}
          unit="tons"
          color="red"
          description="From fossil fuel combustion"
        />
        <StatCounter
          title="Renewable Energy"
          value="850,000"
          increment={9.8}
          unit="MWh"
          color="green"
          description="Clean energy generated today"
        />
        <StatCounter
          title="ESG Investments"
          value="2,300,000,000,000"
          increment={7305936}
          unit="USD"
          color="blue"
          description="Global sustainable investing"
        />
        <StatCounter
          title="Forest Loss Today"
          value="27,000"
          increment={0.31}
          unit="hectares"
          color="yellow"
          description="Net deforestation"
        />
      </div>

      {/* Asia-Pacific Environmental Overview */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Asia-Pacific Environmental Performance</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              country: "China",
              flag: "🇨🇳",
              co2Emissions: "11.5 Gt",
              renewableShare: "31.8%",
              esgInvestment: "$180B",
              carbonIntensity: "-3.2%",
              greenOpportunities: ["Solar manufacturing", "EV batteries", "Wind power"]
            },
            {
              country: "Japan",
              flag: "🇯🇵",
              co2Emissions: "1.1 Gt",
              renewableShare: "22.4%",
              esgInvestment: "$85B",
              carbonIntensity: "-2.8%",
              greenOpportunities: ["Hydrogen technology", "Energy storage", "Green steel"]
            },
            {
              country: "India",
              flag: "🇮🇳",
              co2Emissions: "2.9 Gt",
              renewableShare: "42.5%",
              esgInvestment: "$65B",
              carbonIntensity: "-4.1%",
              greenOpportunities: ["Solar power", "Green hydrogen", "Sustainable agriculture"]
            },
            {
              country: "South Korea",
              flag: "🇰🇷",
              co2Emissions: "0.65 Gt",
              renewableShare: "9.8%",
              esgInvestment: "$45B",
              carbonIntensity: "-1.9%",
              greenOpportunities: ["Green hydrogen", "Battery technology", "Smart grids"]
            },
            {
              country: "Indonesia",
              flag: "🇮🇩",
              co2Emissions: "0.68 Gt",
              renewableShare: "15.7%",
              esgInvestment: "$25B",
              carbonIntensity: "-2.1%",
              greenOpportunities: ["Geothermal energy", "Sustainable palm oil", "Forest conservation"]
            },
            {
              country: "Australia",
              flag: "🇦🇺",
              co2Emissions: "0.42 Gt",
              renewableShare: "35.9%",
              esgInvestment: "$35B",
              carbonIntensity: "-5.2%",
              greenOpportunities: ["Solar exports", "Green mining", "Carbon capture"]
            }
          ].map((country, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-5">
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">{country.flag}</span>
                <h3 className="font-semibold text-gray-900">{country.country}</h3>
              </div>
              
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">CO2 Emissions:</span>
                  <span className="font-semibold text-red-600">{country.co2Emissions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Renewable Share:</span>
                  <span className="font-semibold text-green-600">{country.renewableShare}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">ESG Investment:</span>
                  <span className="font-semibold text-blue-600">{country.esgInvestment}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Carbon Intensity:</span>
                  <span className="font-semibold text-green-600">{country.carbonIntensity}</span>
                </div>
                
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Green Opportunities:</h4>
                  <div className="flex flex-wrap gap-1">
                    {country.greenOpportunities.map((opp, oppIndex) => (
                      <span key={oppIndex} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        {opp}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Renewable Energy & Climate Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Renewable Energy Capacity</h3>
          <div className="space-y-4">
            {[
              { country: "China", capacity: "1,355 GW", share: "31.8%", growth: "+15.2%", type: "Solar, Wind, Hydro" },
              { country: "India", capacity: "175 GW", share: "42.5%", growth: "+18.9%", type: "Solar, Wind" },
              { country: "Japan", capacity: "125 GW", share: "22.4%", growth: "+8.1%", type: "Solar, Hydro" },
              { country: "Australia", capacity: "35 GW", share: "35.9%", growth: "+12.3%", type: "Solar, Wind" },
              { country: "South Korea", capacity: "28 GW", share: "9.8%", growth: "+22.1%", type: "Solar, Wind" },
              { country: "Indonesia", capacity: "12 GW", share: "15.7%", growth: "+25.8%", type: "Geothermal, Hydro" }
            ].map((country, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{country.country}</span>
                  <div className="text-sm text-gray-600">
                    {country.capacity} • {country.type}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">{country.share}</div>
                  <div className="text-sm text-green-800">{country.growth}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold text-gray-900 mb-4">ESG Investment Flows</h3>
          <div className="space-y-4">
            {[
              { category: "Clean Energy", investment: "$890B", growth: "+18.5%", share: "38%" },
              { category: "Sustainable Transport", investment: "$320B", growth: "+25.2%", share: "14%" },
              { category: "Green Buildings", investment: "$280B", growth: "+12.8%", share: "12%" },
              { category: "Water Management", investment: "$180B", growth: "+15.3%", share: "8%" },
              { category: "Waste Management", investment: "$150B", growth: "+22.1%", share: "6%" },
              { category: "Carbon Capture", investment: "$85B", growth: "+45.8%", share: "4%" }
            ].map((category, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{category.category}</span>
                  <div className="text-sm text-gray-600">
                    {category.share} of total ESG investment
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-blue-600">{category.investment}</div>
                  <div className="text-sm text-green-800">{category.growth}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Climate Targets & Progress */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Climate Commitments & Progress</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            {
              country: "China",
              netZeroTarget: "2060",
              carbonPeak: "2030",
              progress: "65%",
              keyPolicies: ["ETS expansion", "Coal phase-down", "Green finance"]
            },
            {
              country: "Japan",
              netZeroTarget: "2050",
              carbonPeak: "Achieved",
              progress: "72%",
              keyPolicies: ["Green transformation", "Hydrogen strategy", "Carbon tax"]
            },
            {
              country: "South Korea",
              netZeroTarget: "2050",
              carbonPeak: "2030",
              progress: "58%",
              keyPolicies: ["Green New Deal", "K-taxonomy", "Carbon neutrality"]
            },
            {
              country: "India",
              netZeroTarget: "2070",
              carbonPeak: "2030",
              progress: "45%",
              keyPolicies: ["Solar mission", "Green hydrogen", "EV policy"]
            },
            {
              country: "Indonesia",
              netZeroTarget: "2060",
              carbonPeak: "2030",
              progress: "38%",
              keyPolicies: ["Moratorium extension", "B30 biodiesel", "Carbon tax"]
            },
            {
              country: "Australia",
              netZeroTarget: "2050",
              carbonPeak: "Achieved",
              progress: "68%",
              keyPolicies: ["Safeguard mechanism", "Renewable zones", "Green bonds"]
            }
          ].map((country, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded">
              <h4 className="font-semibold text-gray-900 mb-3">{country.country}</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Net Zero Target:</span>
                  <span className="font-medium">{country.netZeroTarget}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Carbon Peak:</span>
                  <span className="font-medium">{country.carbonPeak}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Progress:</span>
                  <span className="font-semibold text-green-600">{country.progress}</span>
                </div>
                <div className="mt-3">
                  <h5 className="text-xs font-medium text-gray-700 mb-1">Key Policies:</h5>
                  <div className="flex flex-wrap gap-1">
                    {country.keyPolicies.map((policy, policyIndex) => (
                      <span key={policyIndex} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {policy}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Green Business Opportunities */}
      <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">🎯 Green Business Opportunities</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            {
              title: "Renewable Energy Infrastructure",
              description: "Solar, wind, and energy storage systems across Asia-Pacific",
              market: "$850B",
              growth: "+18.5%",
              regions: ["China", "India", "Australia"],
              icon: "⚡"
            },
            {
              title: "Green Hydrogen Economy",
              description: "Production, storage, and distribution of clean hydrogen",
              market: "$180B",
              growth: "+45.2%",
              regions: ["Japan", "South Korea", "Australia"],
              icon: "💧"
            },
            {
              title: "Sustainable Agriculture",
              description: "Precision farming, vertical agriculture, and food security",
              market: "$320B",
              growth: "+12.8%",
              regions: ["India", "Indonesia", "Vietnam"],
              icon: "🌾"
            },
            {
              title: "Carbon Management",
              description: "Carbon capture, utilization, and storage technologies",
              market: "$85B",
              growth: "+35.1%",
              regions: ["China", "Japan", "Singapore"],
              icon: "🏭"
            },
            {
              title: "Circular Economy Solutions",
              description: "Waste-to-energy, recycling, and resource recovery",
              market: "$150B",
              growth: "+22.3%",
              regions: ["Japan", "South Korea", "Taiwan"],
              icon: "♻️"
            },
            {
              title: "Green Finance & Insurance",
              description: "ESG investing, green bonds, and climate risk management",
              market: "$450B",
              growth: "+28.7%",
              regions: ["Singapore", "Hong Kong", "Tokyo"],
              icon: "💚"
            }
          ].map((opp, index) => (
            <div key={index} className="bg-white rounded-lg p-5 shadow-sm border border-gray-200">
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{opp.icon}</span>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">{opp.title}</h4>
                  <p className="text-sm text-gray-600 mb-3">{opp.description}</p>
                  
                  <div className="flex items-center space-x-4 mb-3">
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-gray-500">Market:</span>
                      <span className="font-semibold text-green-600">{opp.market}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-gray-500">Growth:</span>
                      <span className="font-semibold text-blue-600">{opp.growth}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {opp.regions.map((region, regionIndex) => (
                      <span key={regionIndex} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        {region}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Data Sources</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Climate Data</h4>
            <ul className="space-y-1">
              <li>• IPCC Reports</li>
              <li>• IEA Energy Statistics</li>
              <li>• Global Carbon Atlas</li>
              <li>• Climate Action Tracker</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Renewable Energy</h4>
            <ul className="space-y-1">
              <li>• IRENA Global Energy</li>
              <li>• BloombergNEF</li>
              <li>• Wood Mackenzie</li>
              <li>• Rystad Energy</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">ESG Investment</h4>
            <ul className="space-y-1">
              <li>• Global Sustainable Investment</li>
              <li>• Climate Bonds Initiative</li>
              <li>• Morningstar Sustainalytics</li>
              <li>• MSCI ESG Research</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Environmental Policy</h4>
            <ul className="space-y-1">
              <li>• UNEP Emissions Gap</li>
              <li>• NDC Partnership</li>
              <li>• Carbon Tracker Initiative</li>
              <li>• World Resources Institute</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
