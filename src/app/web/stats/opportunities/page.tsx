'use client'

import { useState, useEffect } from 'react'

export default function OpportunitiesPage() {
  const [opportunitiesData, setOpportunitiesData] = useState(null)

  useEffect(() => {
    // Load opportunities data
    fetch('/api/web/stats/opportunities')
      .then(res => res.json())
      .then(data => setOpportunitiesData(data))
      .catch(err => console.error('Failed to load opportunities data:', err))
  }, [])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl text-white p-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">💰 Investment Opportunities</h1>
        <p className="text-xl opacity-90">Data-driven insights for smart business decisions and market opportunities</p>
      </div>

      {/* Investment Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          { title: "Total Market Size", value: "$2.8T", description: "Identified opportunities", color: "bg-blue-500" },
          { title: "Average Growth", value: "18.5%", description: "Annual growth rate", color: "bg-green-500" },
          { title: "Active Sectors", value: "12", description: "High-growth industries", color: "bg-purple-500" },
          { title: "Key Markets", value: "15", description: "Asia-Pacific countries", color: "bg-orange-500" }
        ].map((metric, index) => (
          <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className={`w-12 h-12 ${metric.color} rounded-lg flex items-center justify-center mb-4`}>
              <span className="text-white text-xl font-bold">{index + 1}</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
            <div className="text-sm text-gray-600">{metric.description}</div>
          </div>
        ))}
      </div>

      {/* Top Investment Opportunities */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">🚀 Top Investment Opportunities</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[
            {
              title: "Green Energy Transition",
              description: "Renewable energy infrastructure, storage systems, and grid modernization across Asia-Pacific",
              marketSize: "$850B",
              growth: "+18.5%",
              timeframe: "2025-2030",
              riskLevel: "Medium",
              keyMarkets: ["China", "India", "Australia", "Japan"],
              subSectors: [
                { name: "Solar Power", size: "$320B", growth: "+22%" },
                { name: "Wind Energy", size: "$280B", growth: "+18%" },
                { name: "Energy Storage", size: "$150B", growth: "+35%" },
                { name: "Smart Grids", size: "$100B", growth: "+15%" }
              ],
              icon: "🌱"
            },
            {
              title: "Digital Healthcare Revolution",
              description: "Telemedicine, AI diagnostics, and health tech platforms transforming healthcare delivery",
              marketSize: "$320B",
              growth: "+25.2%",
              timeframe: "2024-2028",
              riskLevel: "Medium-High",
              keyMarkets: ["Singapore", "South Korea", "Japan", "India"],
              subSectors: [
                { name: "Telemedicine", size: "$85B", growth: "+28%" },
                { name: "AI Diagnostics", size: "$65B", growth: "+45%" },
                { name: "Digital Therapeutics", size: "$45B", growth: "+38%" },
                { name: "Health Data Analytics", size: "$125B", growth: "+18%" }
              ],
              icon: "🏥"
            },
            {
              title: "Fintech Innovation",
              description: "Digital payments, blockchain solutions, and financial inclusion technologies",
              marketSize: "$280B",
              growth: "+22.8%",
              timeframe: "2024-2027",
              riskLevel: "High",
              keyMarkets: ["India", "Indonesia", "Philippines", "Vietnam"],
              subSectors: [
                { name: "Digital Payments", size: "$120B", growth: "+25%" },
                { name: "Blockchain/DeFi", size: "$65B", growth: "+55%" },
                { name: "Neobanking", size: "$45B", growth: "+32%" },
                { name: "InsurTech", size: "$50B", growth: "+28%" }
              ],
              icon: "💳"
            },
            {
              title: "Supply Chain Technology",
              description: "AI-powered logistics, automation, and supply chain optimization solutions",
              marketSize: "$450B",
              growth: "+15.3%",
              timeframe: "2025-2029",
              riskLevel: "Low-Medium",
              keyMarkets: ["China", "Vietnam", "Thailand", "Malaysia"],
              subSectors: [
                { name: "Warehouse Automation", size: "$180B", growth: "+18%" },
                { name: "Last-Mile Delivery", size: "$120B", growth: "+22%" },
                { name: "Supply Chain Analytics", size: "$85B", growth: "+25%" },
                { name: "IoT Tracking", size: "$65B", growth: "+28%" }
              ],
              icon: "🚛"
            }
          ].map((opportunity, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              <div className="flex items-start space-x-4 mb-4">
                <span className="text-3xl">{opportunity.icon}</span>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{opportunity.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{opportunity.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Market Size:</span>
                      <span className="font-semibold text-green-600">{opportunity.marketSize}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Growth Rate:</span>
                      <span className="font-semibold text-blue-600">{opportunity.growth}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Timeframe:</span>
                      <span className="font-medium">{opportunity.timeframe}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Risk Level:</span>
                      <span className={`font-medium ${
                        opportunity.riskLevel === 'Low-Medium' ? 'text-green-600' :
                        opportunity.riskLevel === 'Medium' ? 'text-yellow-600' :
                        opportunity.riskLevel === 'Medium-High' ? 'text-orange-600' :
                        'text-red-600'
                      }`}>{opportunity.riskLevel}</span>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Key Markets:</h4>
                    <div className="flex flex-wrap gap-1">
                      {opportunity.keyMarkets.map((market, marketIndex) => (
                        <span key={marketIndex} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {market}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Sub-sectors:</h4>
                    <div className="space-y-2">
                      {opportunity.subSectors.map((sector, sectorIndex) => (
                        <div key={sectorIndex} className="flex justify-between items-center text-xs">
                          <span className="text-gray-600">{sector.name}</span>
                          <div className="flex space-x-2">
                            <span className="font-medium">{sector.size}</span>
                            <span className="text-green-600">{sector.growth}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Emerging Markets Spotlight */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">🌟 Emerging Markets Spotlight</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              country: "Vietnam",
              flag: "🇻🇳",
              gdpGrowth: "6.5%",
              population: "98M",
              opportunities: [
                { sector: "Manufacturing Hub", potential: "High", investment: "$45B" },
                { sector: "E-commerce Growth", potential: "Very High", investment: "$12B" },
                { sector: "Renewable Energy", potential: "High", investment: "$18B" }
              ]
            },
            {
              country: "Philippines",
              flag: "🇵🇭",
              gdpGrowth: "5.8%",
              population: "112M",
              opportunities: [
                { sector: "BPO Services", potential: "High", investment: "$25B" },
                { sector: "Fintech Solutions", potential: "Very High", investment: "$8B" },
                { sector: "Tourism Tech", potential: "Medium", investment: "$15B" }
              ]
            },
            {
              country: "Bangladesh",
              flag: "🇧🇩",
              gdpGrowth: "6.2%",
              population: "167M",
              opportunities: [
                { sector: "Textile Innovation", potential: "High", investment: "$20B" },
                { sector: "Digital Banking", potential: "Very High", investment: "$6B" },
                { sector: "AgriTech", potential: "High", investment: "$10B" }
              ]
            }
          ].map((market, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-5">
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">{market.flag}</span>
                <h3 className="font-semibold text-gray-900">{market.country}</h3>
              </div>
              
              <div className="space-y-2 mb-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">GDP Growth:</span>
                  <span className="font-semibold text-green-600">{market.gdpGrowth}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Population:</span>
                  <span className="font-medium">{market.population}</span>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Investment Opportunities:</h4>
                <div className="space-y-3">
                  {market.opportunities.map((opp, oppIndex) => (
                    <div key={oppIndex} className="p-3 bg-gray-50 rounded">
                      <div className="flex justify-between items-start mb-1">
                        <span className="text-sm font-medium text-gray-900">{opp.sector}</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          opp.potential === 'Very High' ? 'bg-green-100 text-green-800' :
                          opp.potential === 'High' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {opp.potential}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600">Investment: {opp.investment}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Investment Risk Assessment */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">⚖️ Risk Assessment Matrix</h2>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4">Opportunity</th>
                <th className="text-left py-3 px-4">Market Risk</th>
                <th className="text-left py-3 px-4">Regulatory Risk</th>
                <th className="text-left py-3 px-4">Technology Risk</th>
                <th className="text-left py-3 px-4">Competition Risk</th>
                <th className="text-left py-3 px-4">Overall Rating</th>
              </tr>
            </thead>
            <tbody>
              {[
                { opportunity: "Green Energy", market: "Low", regulatory: "Medium", technology: "Low", competition: "High", overall: "Medium" },
                { opportunity: "Digital Healthcare", market: "Medium", regulatory: "High", technology: "Medium", competition: "Medium", overall: "Medium-High" },
                { opportunity: "Fintech", market: "High", regulatory: "High", technology: "Medium", competition: "High", overall: "High" },
                { opportunity: "Supply Chain Tech", market: "Low", regulatory: "Low", technology: "Low", competition: "Medium", overall: "Low-Medium" },
                { opportunity: "E-commerce", market: "Medium", regulatory: "Medium", technology: "Low", competition: "High", overall: "Medium" },
                { opportunity: "AgriTech", market: "Medium", regulatory: "Medium", technology: "Medium", competition: "Low", overall: "Medium" }
              ].map((risk, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-3 px-4 font-medium">{risk.opportunity}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      risk.market === 'Low' ? 'bg-green-100 text-green-800' :
                      risk.market === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {risk.market}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      risk.regulatory === 'Low' ? 'bg-green-100 text-green-800' :
                      risk.regulatory === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {risk.regulatory}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      risk.technology === 'Low' ? 'bg-green-100 text-green-800' :
                      risk.technology === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {risk.technology}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      risk.competition === 'Low' ? 'bg-green-100 text-green-800' :
                      risk.competition === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {risk.competition}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      risk.overall.includes('Low') ? 'bg-green-100 text-green-800' :
                      risk.overall.includes('Medium') && !risk.overall.includes('High') ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {risk.overall}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Data Sources */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Data Sources & Methodology</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Market Research</h4>
            <ul className="space-y-1">
              <li>• McKinsey Global Institute</li>
              <li>• BCG Research Reports</li>
              <li>• Deloitte Market Analysis</li>
              <li>• PwC Industry Insights</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Investment Data</h4>
            <ul className="space-y-1">
              <li>• PitchBook Database</li>
              <li>• CB Insights</li>
              <li>• Preqin Alternative Assets</li>
              <li>• EY Global IPO Trends</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Risk Assessment</h4>
            <ul className="space-y-1">
              <li>• World Economic Forum</li>
              <li>• Political Risk Services</li>
              <li>• Moody's Analytics</li>
              <li>• S&P Global Ratings</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
