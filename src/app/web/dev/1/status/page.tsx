'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

export default function StatusPage() {
  const [status, setStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    setLoading(true);
    const token = localStorage.getItem('oneid-token');
    
    const statusData = {
      hasToken: !!token,
      currentUrl: window.location.href,
      pathname: window.location.pathname,
      timestamp: new Date().toISOString()
    };

    if (token) {
      try {
        const response = await fetch('/api/backbone/oneid/access/check/permissions', {
          headers: {
            'Authorization': `Bear<PERSON> ${token}`
          }
        });
        
        if (response.ok) {
          const result = await response.json();
          statusData.tokenValid = true;
          statusData.user = result.data;
        } else {
          statusData.tokenValid = false;
          statusData.error = 'Token validation failed';
        }
      } catch (error) {
        statusData.tokenValid = false;
        statusData.error = 'Network error';
      }
    }

    setStatus(statusData);
    setLoading(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">OneID Protection Status</h1>
          <p className="text-gray-600">Debug information for the protected test page</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Authentication Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {status.hasToken ? (
                  status.tokenValid ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )
                ) : (
                  <AlertCircle className="h-5 w-5 text-yellow-600" />
                )}
                Authentication Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Has Token</label>
                <div>
                  <Badge variant={status.hasToken ? "default" : "destructive"}>
                    {status.hasToken ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
              
              {status.hasToken && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Token Valid</label>
                  <div>
                    <Badge variant={status.tokenValid ? "default" : "destructive"}>
                      {status.tokenValid ? "Valid" : "Invalid"}
                    </Badge>
                  </div>
                </div>
              )}

              {status.error && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Error</label>
                  <p className="text-red-600 text-sm">{status.error}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Page Information */}
          <Card>
            <CardHeader>
              <CardTitle>Page Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Current URL</label>
                <p className="text-sm font-mono bg-gray-100 p-2 rounded">{status.currentUrl}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Pathname</label>
                <p className="text-sm font-mono bg-gray-100 p-2 rounded">{status.pathname}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Timestamp</label>
                <p className="text-sm">{new Date(status.timestamp).toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          {/* User Information */}
          {status.user && (
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>User Information</CardTitle>
                <CardDescription>Current authenticated user details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">User ID</label>
                    <p className="text-sm font-mono">{status.user.userId}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">Company ID</label>
                    <p className="text-sm font-mono">{status.user.companyId || 'None'}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">Roles</label>
                    <p className="text-sm">{status.user.roleCount || 0}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">Permissions</label>
                    <p className="text-sm">{status.user.permissionCount || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Actions */}
        <div className="mt-8 flex gap-4">
          <Button onClick={checkStatus} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
          
          <Button onClick={() => window.location.href = '/web/dev/1'}>
            Go to Protected Page
          </Button>
          
          <Button onClick={() => window.location.href = '/web/dev/1/login'} variant="outline">
            Go to Login
          </Button>
          
          {status.hasToken && (
            <Button 
              onClick={() => {
                localStorage.removeItem('oneid-token');
                window.location.reload();
              }}
              variant="destructive"
            >
              Clear Token
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
