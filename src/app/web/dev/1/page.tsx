'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, Users, Lock, CheckCircle, AlertCircle, LogOut } from 'lucide-react';

interface UserInfo {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email?: string;
  userType: string;
  companyId?: string;
  roles: Array<{
    id: string;
    name: string;
    description: string;
  }>;
  permissions: Array<{
    id: string;
    name: string;
    resource: string;
    action: string;
    accessLevel: string;
  }>;
}

export default function TestLandingPage() {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('oneid-token');
      if (!token) {
        setError('No authentication token found. Please login first.');
        setLoading(false);
        return;
      }

      // For test tokens, decode directly instead of API call
      console.log('[AUTH] Checking test token...');
      try {
        const tokenData = JSON.parse(Buffer.from(token, 'base64').toString());
        console.log('[AUTH] Token data:', tokenData);

        // Check if token is expired
        if (tokenData.exp && tokenData.exp < Math.floor(Date.now() / 1000)) {
          console.log('[AUTH] Token expired');
          setError('Session expired. Please login again.');
          setLoading(false);
          return;
        }

        // Create user object from token data
        const userData = {
          userId: tokenData.userId,
          username: tokenData.username,
          firstName: 'Test',
          lastName: 'User',
          email: `${tokenData.username}@testdev.example.com`,
          userType: tokenData.userType,
          companyId: tokenData.companyId,
          roles: [],
          permissions: []
        };

        setUser(userData);
        console.log('[AUTH] Authentication successful for:', tokenData.username);

      } catch (tokenError) {
        console.error('[AUTH] Token verification failed:', tokenError);
        setError('Invalid authentication token. Please login again.');
      }
    } catch (err) {
      setError('Failed to verify authentication.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('oneid-token');
    // Clear the cookie as well
    document.cookie = 'oneid-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    window.location.href = '/web/dev/1/login';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying access...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-red-700">Access Denied</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button
              onClick={() => window.location.href = '/web/dev/1/login'}
              className="w-full"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Protected Test Page
              </h1>
            </div>
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                Welcome, {user?.firstName} {user?.lastName}
              </span>
              <Button variant="outline" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Success Message */}
        <Card className="mb-8 border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600 mr-4" />
              <div>
                <h2 className="text-lg font-semibold text-green-800">
                  🎉 Access Granted!
                </h2>
                <p className="text-green-700">
                  You have successfully accessed this protected resource. This page is secured by ABN OneID.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Information
              </CardTitle>
              <CardDescription>
                Your authenticated user details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Username</label>
                <p className="text-lg">{user?.username}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Full Name</label>
                <p className="text-lg">{user?.firstName} {user?.lastName}</p>
              </div>
              {user?.email && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-lg">{user.email}</p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-gray-500">User Type</label>
                <Badge variant="outline" className="ml-2">
                  {user?.userType}
                </Badge>
              </div>
              {user?.companyId && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Company</label>
                  <Badge className="ml-2 bg-blue-100 text-blue-800">
                    Company User
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Access Control
              </CardTitle>
              <CardDescription>
                Your roles and permissions for this resource
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Roles ({user?.roles.length || 0})</label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {user?.roles.map(role => (
                    <Badge key={role.id} variant="secondary">
                      {role.name}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Permissions ({user?.permissions.length || 0})</label>
                <div className="max-h-32 overflow-y-auto mt-2">
                  <div className="space-y-1">
                    {user?.permissions.slice(0, 5).map(permission => (
                      <div key={permission.id} className="text-sm">
                        <Badge variant="outline" className="mr-2">
                          {permission.action}
                        </Badge>
                        {permission.name}
                      </div>
                    ))}
                    {(user?.permissions.length || 0) > 5 && (
                      <p className="text-sm text-gray-500">
                        ... and {(user?.permissions.length || 0) - 5} more permissions
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Test Content */}
        <Card>
          <CardHeader>
            <CardTitle>Protected Content</CardTitle>
            <CardDescription>
              This content is only visible to authorized users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <h3>🔒 Secure Development Environment</h3>
              <p>
                This is a test landing page protected by the ABN OneID authentication and authorization system. 
                Only users with proper permissions can access this content.
              </p>
              
              <h4>Features Demonstrated:</h4>
              <ul>
                <li>✅ JWT Token Authentication</li>
                <li>✅ Role-Based Access Control</li>
                <li>✅ Permission Verification</li>
                <li>✅ User Context Display</li>
                <li>✅ Secure Session Management</li>
              </ul>

              <h4>Access Requirements:</h4>
              <ul>
                <li>Valid OneID authentication token</li>
                <li>Active user account</li>
                <li>Appropriate permissions for this resource</li>
              </ul>

              <div className="bg-blue-50 p-4 rounded-lg mt-6">
                <h5 className="text-blue-800 font-semibold">Development Note:</h5>
                <p className="text-blue-700 text-sm">
                  This page demonstrates the OneID protection system. In a real application, 
                  you would define specific permissions and roles required to access different 
                  parts of your application.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
