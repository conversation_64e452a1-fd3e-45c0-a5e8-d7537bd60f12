'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, LogIn, Users, Eye, EyeOff } from 'lucide-react';

const TEST_USERS = [
  {
    username: 'dev.admin',
    email: '<EMAIL>',
    role: 'Development Administrator',
    access: 'Full Access',
    color: 'bg-green-100 text-green-800'
  },
  {
    username: 'dev.user',
    email: '<EMAIL>',
    role: 'Development User',
    access: 'Standard Access',
    color: 'bg-blue-100 text-blue-800'
  },
  {
    username: 'dev.viewer',
    email: '<EMAIL>',
    role: 'Development Viewer',
    access: 'Read-Only Access',
    color: 'bg-yellow-100 text-yellow-800'
  },
  {
    username: 'no.access',
    email: '<EMAIL>',
    role: 'No Role',
    access: 'No Access',
    color: 'bg-red-100 text-red-800'
  }
];

export default function TestLoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('password123'); // Default password for testing
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Use test login endpoint for development
      const response = await fetch('/api/backbone/oneid/auth/test-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username
        })
      });

      const result = await response.json();
      console.log('[LOGIN] Login response:', result);

      if (response.ok && result.success) {
        // Store token in both localStorage and cookie for middleware access
        localStorage.setItem('oneid-token', result.token);

        // Set cookie for middleware to access
        document.cookie = `oneid-token=${result.token}; path=/; max-age=${24 * 60 * 60}; SameSite=Lax`;

        console.log('[LOGIN] Token stored in localStorage and cookie');

        // Get redirect URL from query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const redirectUrl = urlParams.get('redirect');

        // Redirect to the original page or default to protected page
        if (redirectUrl) {
          console.log('[LOGIN] Redirecting to:', redirectUrl);
          window.location.href = decodeURIComponent(redirectUrl);
        } else {
          console.log('[LOGIN] No redirect URL, going to default page');
          window.location.href = '/web/dev/1';
        }
      } else {
        setError(result.error || 'Login failed');
      }
    } catch {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickLogin = (testUsername: string) => {
    setUsername(testUsername);
    setPassword('password123');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Login Form */}
        <Card>
          <CardHeader className="text-center">
            <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <CardTitle>OneID Test Login</CardTitle>
            <CardDescription>
              Login to access the protected test page
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter username"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter password"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Logging in...
                  </>
                ) : (
                  <>
                    <LogIn className="h-4 w-4 mr-2" />
                    Login
                  </>
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Test password for all users: <code className="bg-gray-100 px-2 py-1 rounded">password123</code>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Test Users */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Test Users
            </CardTitle>
            <CardDescription>
              Click on a user to quick-login and test access levels
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {TEST_USERS.map((user) => (
              <div
                key={user.username}
                className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleQuickLogin(user.username)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{user.username}</h3>
                  <Badge className={user.color}>
                    {user.access}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-1">{user.email}</p>
                <p className="text-sm text-gray-500">{user.role}</p>
              </div>
            ))}

            <Alert>
              <AlertDescription>
                <strong>Access Test:</strong> Try logging in with different users to see how access control works.
                Only users with assigned roles can access the protected page.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
