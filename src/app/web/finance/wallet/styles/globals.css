/* Wero Wallet CSS Variables */
:root {
  --wero-black: #1d1c1c;
  --wero-yellow: #fff48d;
  --wero-white: #ffffff;
  --wero-light-gray: #f5f5f5;
  --wero-gray: #666666;
  --wero-dark-gray: #333333;
  --wero-border: #e0e0e0;
  --wero-hover: #f0f0f0;
  
  /* Typography */
  --wero-font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --wero-font-size-xs: 0.75rem;
  --wero-font-size-sm: 0.875rem;
  --wero-font-size-base: 1rem;
  --wero-font-size-lg: 1.125rem;
  --wero-font-size-xl: 1.25rem;
  --wero-font-size-2xl: 1.5rem;
  --wero-font-size-3xl: 1.875rem;
  --wero-font-size-4xl: 2.25rem;
  --wero-font-size-5xl: 3rem;
  
  /* Spacing */
  --wero-spacing-xs: 0.25rem;
  --wero-spacing-sm: 0.5rem;
  --wero-spacing-md: 1rem;
  --wero-spacing-lg: 1.5rem;
  --wero-spacing-xl: 2rem;
  --wero-spacing-2xl: 3rem;
  --wero-spacing-3xl: 4rem;
  --wero-spacing-4xl: 6rem;
  
  /* Border radius */
  --wero-radius-sm: 0.25rem;
  --wero-radius-md: 0.5rem;
  --wero-radius-lg: 0.75rem;
  --wero-radius-xl: 1rem;
  --wero-radius-full: 9999px;
  
  /* Shadows */
  --wero-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --wero-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --wero-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Transitions */
  --wero-transition: all 0.2s ease-in-out;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--wero-font-primary);
  font-size: var(--wero-font-size-base);
  line-height: 1.6;
  color: var(--wero-black);
  background-color: var(--wero-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wero-wallet {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Utility Classes */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--wero-spacing-lg);
}

.container-wide {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--wero-spacing-lg);
}

.section {
  padding: var(--wero-spacing-4xl) 0;
}

.section-sm {
  padding: var(--wero-spacing-2xl) 0;
}

.section-lg {
  padding: var(--wero-spacing-4xl) 0;
}

/* Typography */
.heading-1 {
  font-size: var(--wero-font-size-5xl);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--wero-spacing-lg);
}

.heading-2 {
  font-size: var(--wero-font-size-4xl);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--wero-spacing-md);
}

.heading-3 {
  font-size: var(--wero-font-size-3xl);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--wero-spacing-md);
}

.heading-4 {
  font-size: var(--wero-font-size-2xl);
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: var(--wero-spacing-sm);
}

.body-large {
  font-size: var(--wero-font-size-lg);
  line-height: 1.6;
  margin-bottom: var(--wero-spacing-md);
}

.body-text {
  font-size: var(--wero-font-size-base);
  line-height: 1.6;
  margin-bottom: var(--wero-spacing-md);
}

.body-small {
  font-size: var(--wero-font-size-sm);
  line-height: 1.5;
  color: var(--wero-gray);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--wero-spacing-md) var(--wero-spacing-xl);
  font-size: var(--wero-font-size-base);
  font-weight: 600;
  text-decoration: none;
  border: none;
  border-radius: var(--wero-radius-lg);
  cursor: pointer;
  transition: var(--wero-transition);
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--wero-shadow-md);
}

.btn-primary {
  background-color: var(--wero-black);
  color: var(--wero-white);
}

.btn-primary:hover {
  background-color: var(--wero-dark-gray);
}

.btn-secondary {
  background-color: var(--wero-yellow);
  color: var(--wero-black);
}

.btn-secondary:hover {
  background-color: #ffe066;
}

.btn-outline {
  background-color: transparent;
  color: var(--wero-black);
  border: 2px solid var(--wero-black);
}

.btn-outline:hover {
  background-color: var(--wero-black);
  color: var(--wero-white);
}

.btn-white {
  background-color: var(--wero-white);
  color: var(--wero-black);
  border: 2px solid var(--wero-white);
}

.btn-white:hover {
  background-color: transparent;
  color: var(--wero-white);
}

.btn-lg {
  padding: var(--wero-spacing-lg) var(--wero-spacing-2xl);
  font-size: var(--wero-font-size-lg);
}

.btn-sm {
  padding: var(--wero-spacing-sm) var(--wero-spacing-lg);
  font-size: var(--wero-font-size-sm);
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--wero-spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Cards */
.card {
  background-color: var(--wero-white);
  border-radius: var(--wero-radius-xl);
  padding: var(--wero-spacing-xl);
  box-shadow: var(--wero-shadow-sm);
  transition: var(--wero-transition);
}

.card:hover {
  box-shadow: var(--wero-shadow-lg);
  transform: translateY(-2px);
}

.card-highlight {
  background-color: var(--wero-yellow);
  color: var(--wero-black);
}

/* Forms */
.form-group {
  margin-bottom: var(--wero-spacing-lg);
}

.form-label {
  display: block;
  font-size: var(--wero-font-size-sm);
  font-weight: 600;
  margin-bottom: var(--wero-spacing-sm);
  color: var(--wero-black);
}

.form-input {
  width: 100%;
  padding: var(--wero-spacing-md);
  font-size: var(--wero-font-size-base);
  border: 2px solid var(--wero-border);
  border-radius: var(--wero-radius-md);
  background-color: var(--wero-white);
  transition: var(--wero-transition);
}

.form-input:focus {
  outline: none;
  border-color: var(--wero-black);
  box-shadow: 0 0 0 3px rgba(29, 28, 28, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--wero-spacing-md);
  }
  
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .heading-1 {
    font-size: var(--wero-font-size-4xl);
  }
  
  .heading-2 {
    font-size: var(--wero-font-size-3xl);
  }
}

@media (max-width: 768px) {
  .section {
    padding: var(--wero-spacing-2xl) 0;
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
    gap: var(--wero-spacing-md);
  }
  
  .heading-1 {
    font-size: var(--wero-font-size-3xl);
  }
  
  .heading-2 {
    font-size: var(--wero-font-size-2xl);
  }
  
  .btn {
    width: 100%;
    padding: var(--wero-spacing-lg) var(--wero-spacing-xl);
  }
  
  .btn-lg {
    padding: var(--wero-spacing-lg) var(--wero-spacing-xl);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--wero-spacing-sm);
  }
  
  .heading-1 {
    font-size: var(--wero-font-size-2xl);
  }
  
  .heading-2 {
    font-size: var(--wero-font-size-xl);
  }
  
  .card {
    padding: var(--wero-spacing-lg);
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Special Wero styles */
.wero-highlight {
  background: linear-gradient(120deg, var(--wero-yellow) 0%, var(--wero-yellow) 100%);
  background-repeat: no-repeat;
  background-size: 100% 0.2em;
  background-position: 0 88%;
  transition: background-size 0.25s ease-in;
}

.wero-highlight:hover {
  background-size: 100% 88%;
}

.ticker {
  white-space: nowrap;
  overflow: hidden;
  animation: ticker 20s linear infinite;
}

@keyframes ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.gradient-text {
  background: linear-gradient(135deg, var(--wero-black), var(--wero-gray));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}