'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ChevronDownIcon, Bars3Icon, XMarkIcon, GlobeAltIcon } from '@heroicons/react/24/outline';

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isBusinessDropdownOpen, setIsBusinessDropdownOpen] = useState(false);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="wero-header">
      <div className="container">
        <nav className="nav-main">
          {/* Logo */}
          <div className="nav-logo">
            <Link href="/web/finance/wallet" className="logo-link">
              <div className="logo">
                <span className="logo-text">wero</span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="nav-menu">
            <ul className="nav-list">
              <li className="nav-item">
                <Link href="/web/finance/wallet/individuals" className="nav-link">
                  Individuals
                </Link>
              </li>
              <li className="nav-item nav-item-dropdown">
                <button 
                  className="nav-link nav-dropdown-trigger"
                  onClick={() => setIsBusinessDropdownOpen(!isBusinessDropdownOpen)}
                >
                  Business
                  <ChevronDownIcon className="dropdown-icon" />
                </button>
                {isBusinessDropdownOpen && (
                  <div className="nav-dropdown">
                    <Link href="/web/finance/wallet/merchants" className="dropdown-link">
                      Merchants
                    </Link>
                    <Link href="/web/finance/wallet/professionals" className="dropdown-link">
                      Professionals
                    </Link>
                  </div>
                )}
              </li>
              <li className="nav-item">
                <Link href="/web/finance/wallet/support" className="nav-link">
                  Support
                </Link>
              </li>
              <li className="nav-item">
                <Link href="/web/finance/wallet/about" className="nav-link">
                  About
                </Link>
              </li>
              <li className="nav-item">
                <Link href="/web/finance/wallet/news" className="nav-link">
                  News
                </Link>
              </li>
            </ul>
          </div>

          {/* Language Selector & Social */}
          <div className="nav-actions">
            <div className="language-selector">
              <button 
                className="language-trigger"
                onClick={() => setIsLanguageDropdownOpen(!isLanguageDropdownOpen)}
              >
                <GlobeAltIcon className="language-icon" />
                EN
                <ChevronDownIcon className="dropdown-icon" />
              </button>
              {isLanguageDropdownOpen && (
                <div className="language-dropdown">
                  <button className="language-option">EN</button>
                  <button className="language-option">FR</button>
                  <button className="language-option">DE</button>
                  <button className="language-option">NL</button>
                </div>
              )}
            </div>

            <div className="social-links">
              <a href="#" className="social-link" aria-label="LinkedIn">
                <svg className="social-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
              <a href="#" className="social-link" aria-label="Twitter">
                <svg className="social-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
            </div>

            {/* Mobile Menu Button */}
            <button 
              className="mobile-menu-button"
              onClick={toggleMobileMenu}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <XMarkIcon className="menu-icon" />
              ) : (
                <Bars3Icon className="menu-icon" />
              )}
            </button>
          </div>
        </nav>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="mobile-menu">
            <div className="mobile-menu-content">
              <ul className="mobile-nav-list">
                <li className="mobile-nav-item">
                  <Link href="/web/finance/wallet/individuals" className="mobile-nav-link">
                    Individuals
                  </Link>
                </li>
                <li className="mobile-nav-item">
                  <div className="mobile-nav-group">
                    <span className="mobile-nav-label">Business</span>
                    <Link href="/web/finance/wallet/merchants" className="mobile-nav-sublink">
                      Merchants
                    </Link>
                    <Link href="/web/finance/wallet/professionals" className="mobile-nav-sublink">
                      Professionals
                    </Link>
                  </div>
                </li>
                <li className="mobile-nav-item">
                  <Link href="/web/finance/wallet/support" className="mobile-nav-link">
                    Support
                  </Link>
                </li>
                <li className="mobile-nav-item">
                  <Link href="/web/finance/wallet/about" className="mobile-nav-link">
                    About
                  </Link>
                </li>
                <li className="mobile-nav-item">
                  <Link href="/web/finance/wallet/news" className="mobile-nav-link">
                    News
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .wero-header {
          background-color: var(--wero-white);
          border-bottom: 1px solid var(--wero-border);
          position: sticky;
          top: 0;
          z-index: 100;
        }

        .nav-main {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: var(--wero-spacing-lg) 0;
        }

        .nav-logo .logo-link {
          text-decoration: none;
        }

        .logo {
          display: flex;
          align-items: center;
        }

        .logo-text {
          font-size: var(--wero-font-size-2xl);
          font-weight: 700;
          color: var(--wero-black);
        }

        .nav-menu {
          display: none;
        }

        .nav-list {
          display: flex;
          list-style: none;
          gap: var(--wero-spacing-xl);
          margin: 0;
          padding: 0;
        }

        .nav-item {
          position: relative;
        }

        .nav-link {
          color: var(--wero-black);
          text-decoration: none;
          font-weight: 500;
          font-size: var(--wero-font-size-base);
          transition: var(--wero-transition);
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-xs);
          padding: var(--wero-spacing-sm);
          border: none;
          background: none;
          cursor: pointer;
        }

        .nav-link:hover {
          color: var(--wero-gray);
        }

        .dropdown-icon {
          width: 1rem;
          height: 1rem;
          transition: var(--wero-transition);
        }

        .nav-item-dropdown:hover .dropdown-icon {
          transform: rotate(180deg);
        }

        .nav-dropdown {
          position: absolute;
          top: 100%;
          left: 0;
          background-color: var(--wero-white);
          border: 1px solid var(--wero-border);
          border-radius: var(--wero-radius-md);
          box-shadow: var(--wero-shadow-lg);
          padding: var(--wero-spacing-sm);
          min-width: 150px;
          z-index: 50;
        }

        .dropdown-link {
          display: block;
          color: var(--wero-black);
          text-decoration: none;
          padding: var(--wero-spacing-sm) var(--wero-spacing-md);
          border-radius: var(--wero-radius-sm);
          transition: var(--wero-transition);
        }

        .dropdown-link:hover {
          background-color: var(--wero-hover);
        }

        .nav-actions {
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-lg);
        }

        .language-selector {
          position: relative;
          display: none;
        }

        .language-trigger {
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-xs);
          background: none;
          border: none;
          color: var(--wero-black);
          font-size: var(--wero-font-size-sm);
          cursor: pointer;
          padding: var(--wero-spacing-sm);
          border-radius: var(--wero-radius-sm);
          transition: var(--wero-transition);
        }

        .language-trigger:hover {
          background-color: var(--wero-hover);
        }

        .language-icon {
          width: 1rem;
          height: 1rem;
        }

        .language-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          background-color: var(--wero-white);
          border: 1px solid var(--wero-border);
          border-radius: var(--wero-radius-md);
          box-shadow: var(--wero-shadow-lg);
          padding: var(--wero-spacing-sm);
          min-width: 80px;
          z-index: 50;
        }

        .language-option {
          display: block;
          width: 100%;
          background: none;
          border: none;
          color: var(--wero-black);
          text-align: left;
          padding: var(--wero-spacing-xs) var(--wero-spacing-sm);
          border-radius: var(--wero-radius-sm);
          cursor: pointer;
          transition: var(--wero-transition);
        }

        .language-option:hover {
          background-color: var(--wero-hover);
        }

        .social-links {
          display: none;
          gap: var(--wero-spacing-md);
        }

        .social-link {
          color: var(--wero-gray);
          transition: var(--wero-transition);
        }

        .social-link:hover {
          color: var(--wero-black);
        }

        .social-icon {
          width: 1.25rem;
          height: 1.25rem;
        }

        .mobile-menu-button {
          display: flex;
          background: none;
          border: none;
          cursor: pointer;
          color: var(--wero-black);
          padding: var(--wero-spacing-sm);
        }

        .menu-icon {
          width: 1.5rem;
          height: 1.5rem;
        }

        .mobile-menu {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background-color: var(--wero-white);
          border-bottom: 1px solid var(--wero-border);
          padding: var(--wero-spacing-lg) 0;
        }

        .mobile-menu-content {
          padding: 0 var(--wero-spacing-lg);
        }

        .mobile-nav-list {
          list-style: none;
          margin: 0;
          padding: 0;
        }

        .mobile-nav-item {
          border-bottom: 1px solid var(--wero-border);
        }

        .mobile-nav-item:last-child {
          border-bottom: none;
        }

        .mobile-nav-link {
          display: block;
          color: var(--wero-black);
          text-decoration: none;
          padding: var(--wero-spacing-md) 0;
          font-weight: 500;
        }

        .mobile-nav-group {
          padding: var(--wero-spacing-md) 0;
        }

        .mobile-nav-label {
          display: block;
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .mobile-nav-sublink {
          display: block;
          color: var(--wero-gray);
          text-decoration: none;
          padding: var(--wero-spacing-sm) var(--wero-spacing-md);
          margin-left: var(--wero-spacing-md);
        }

        @media (min-width: 768px) {
          .nav-menu {
            display: block;
          }

          .language-selector {
            display: block;
          }

          .social-links {
            display: flex;
          }

          .mobile-menu-button {
            display: none;
          }
        }
      `}</style>
    </header>
  );
}