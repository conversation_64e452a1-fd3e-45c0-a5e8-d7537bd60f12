'use client';

import React, { useState } from 'react';
import { EnvelopeIcon, CheckIcon } from '@heroicons/react/24/outline';

export default function NewsletterSection() {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true);
      setIsLoading(false);
      setEmail('');
    }, 1500);
  };

  if (isSubscribed) {
    return (
      <section className="newsletter-section">
        <div className="container">
          <div className="newsletter-success">
            <div className="success-icon">
              <CheckIcon className="icon" />
            </div>
            <h2 className="success-title">You're all set!</h2>
            <p className="success-message">
              Thank you for subscribing. You'll receive the latest Wero updates and insights directly in your inbox.
            </p>
            <button 
              className="btn btn-outline"
              onClick={() => setIsSubscribed(false)}
            >
              Subscribe Another Email
            </button>
          </div>
        </div>

        <style jsx>{`
          .newsletter-section {
            padding: var(--wero-spacing-4xl) 0;
            background: linear-gradient(135deg, var(--wero-yellow) 0%, #fff48d 100%);
          }

          .newsletter-success {
            text-align: center;
            max-width: 500px;
            margin: 0 auto;
          }

          .success-icon {
            width: 80px;
            height: 80px;
            background-color: var(--wero-white);
            border-radius: var(--wero-radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--wero-spacing-lg);
            box-shadow: var(--wero-shadow-lg);
          }

          .success-icon .icon {
            width: 40px;
            height: 40px;
            color: #22c55e;
          }

          .success-title {
            font-size: var(--wero-font-size-2xl);
            font-weight: 600;
            color: var(--wero-black);
            margin-bottom: var(--wero-spacing-md);
          }

          .success-message {
            font-size: var(--wero-font-size-base);
            color: var(--wero-black);
            line-height: 1.6;
            margin-bottom: var(--wero-spacing-xl);
            opacity: 0.8;
          }
        `}</style>
      </section>
    );
  }

  return (
    <section className="newsletter-section">
      <div className="container">
        <div className="newsletter-content">
          <div className="newsletter-text">
            <h2 className="newsletter-title">
              Stay in the{' '}
              <span className="title-highlight">loop</span>
            </h2>
            <p className="newsletter-description">
              Get the latest updates on Wero features, new partnerships, and insights 
              about the future of European payments delivered straight to your inbox.
            </p>
            
            <div className="newsletter-benefits">
              <div className="benefit-item">
                <div className="benefit-icon">🚀</div>
                <span>Product updates & new features</span>
              </div>
              <div className="benefit-item">
                <div className="benefit-icon">🏦</div>
                <span>New bank partnerships</span>
              </div>
              <div className="benefit-item">
                <div className="benefit-icon">💡</div>
                <span>Payment industry insights</span>
              </div>
            </div>
          </div>

          <div className="newsletter-form-container">
            <div className="form-card">
              <div className="form-header">
                <div className="form-icon">
                  <EnvelopeIcon className="icon" />
                </div>
                <h3 className="form-title">Join 50,000+ subscribers</h3>
                <p className="form-subtitle">
                  No spam, unsubscribe at any time
                </p>
              </div>

              <form onSubmit={handleSubmit} className="newsletter-form">
                <div className="input-group">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="email-input"
                    required
                  />
                  <button
                    type="submit"
                    disabled={isLoading || !email}
                    className={`submit-btn ${isLoading ? 'loading' : ''}`}
                  >
                    {isLoading ? (
                      <div className="loading-spinner"></div>
                    ) : (
                      'Subscribe'
                    )}
                  </button>
                </div>
                
                <div className="form-privacy">
                  <p className="privacy-text">
                    By subscribing, you agree to our{' '}
                    <a href="/privacy" className="privacy-link">Privacy Policy</a>{' '}
                    and consent to receive updates from Wero.
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div className="social-proof">
          <div className="proof-stats">
            <div className="stat">
              <div className="stat-number">50K+</div>
              <div className="stat-label">Newsletter subscribers</div>
            </div>
            <div className="stat-divider"></div>
            <div className="stat">
              <div className="stat-number">8</div>
              <div className="stat-label">Partner banks</div>
            </div>
            <div className="stat-divider"></div>
            <div className="stat">
              <div className="stat-number">3</div>
              <div className="stat-label">Countries connected</div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .newsletter-section {
          padding: var(--wero-spacing-4xl) 0;
          background: linear-gradient(135deg, var(--wero-yellow) 0%, #fff48d 100%);
        }

        .newsletter-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--wero-spacing-4xl);
          align-items: center;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .newsletter-text {
          max-width: 500px;
        }

        .newsletter-title {
          font-size: var(--wero-font-size-4xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-lg);
          line-height: 1.2;
        }

        .title-highlight {
          background: linear-gradient(120deg, var(--wero-white) 0%, var(--wero-white) 100%);
          background-repeat: no-repeat;
          background-size: 100% 0.3em;
          background-position: 0 85%;
        }

        .newsletter-description {
          font-size: var(--wero-font-size-lg);
          color: var(--wero-black);
          line-height: 1.6;
          margin-bottom: var(--wero-spacing-xl);
          opacity: 0.8;
        }

        .newsletter-benefits {
          display: flex;
          flex-direction: column;
          gap: var(--wero-spacing-md);
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-sm);
          font-size: var(--wero-font-size-base);
          color: var(--wero-black);
          font-weight: 500;
        }

        .benefit-icon {
          font-size: var(--wero-font-size-lg);
          width: 24px;
          text-align: center;
        }

        .newsletter-form-container {
          display: flex;
          justify-content: center;
        }

        .form-card {
          background-color: var(--wero-white);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-2xl);
          box-shadow: var(--wero-shadow-lg);
          max-width: 400px;
          width: 100%;
        }

        .form-header {
          text-align: center;
          margin-bottom: var(--wero-spacing-xl);
        }

        .form-icon {
          width: 60px;
          height: 60px;
          background-color: var(--wero-yellow);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto var(--wero-spacing-md);
        }

        .form-icon .icon {
          width: 30px;
          height: 30px;
          color: var(--wero-black);
        }

        .form-title {
          font-size: var(--wero-font-size-xl);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .form-subtitle {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
        }

        .newsletter-form {
          width: 100%;
        }

        .input-group {
          display: flex;
          flex-direction: column;
          gap: var(--wero-spacing-sm);
          margin-bottom: var(--wero-spacing-md);
        }

        .email-input {
          width: 100%;
          padding: var(--wero-spacing-md);
          border: 2px solid var(--wero-border);
          border-radius: var(--wero-radius-lg);
          font-size: var(--wero-font-size-base);
          transition: var(--wero-transition);
          background-color: var(--wero-white);
        }

        .email-input:focus {
          outline: none;
          border-color: var(--wero-black);
          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        .submit-btn {
          width: 100%;
          background-color: var(--wero-black);
          color: var(--wero-white);
          border: none;
          border-radius: var(--wero-radius-lg);
          padding: var(--wero-spacing-md);
          font-size: var(--wero-font-size-base);
          font-weight: 600;
          cursor: pointer;
          transition: var(--wero-transition);
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 48px;
        }

        .submit-btn:hover:not(:disabled) {
          background-color: var(--wero-dark-gray);
          transform: translateY(-1px);
        }

        .submit-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .submit-btn.loading {
          pointer-events: none;
        }

        .loading-spinner {
          width: 20px;
          height: 20px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top-color: var(--wero-white);
          animation: spin 1s ease-in-out infinite;
        }

        .form-privacy {
          text-align: center;
        }

        .privacy-text {
          font-size: var(--wero-font-size-xs);
          color: var(--wero-gray);
          line-height: 1.4;
        }

        .privacy-link {
          color: var(--wero-black);
          text-decoration: underline;
          font-weight: 500;
        }

        .privacy-link:hover {
          text-decoration: none;
        }

        .social-proof {
          text-align: center;
        }

        .proof-stats {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: var(--wero-spacing-xl);
          max-width: 600px;
          margin: 0 auto;
        }

        .stat {
          text-align: center;
        }

        .stat-number {
          font-size: var(--wero-font-size-2xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .stat-label {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-black);
          opacity: 0.7;
        }

        .stat-divider {
          width: 1px;
          height: 40px;
          background-color: rgba(0, 0, 0, 0.2);
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }

        @media (max-width: 1024px) {
          .newsletter-content {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-2xl);
            text-align: center;
          }

          .newsletter-benefits {
            align-items: center;
          }
        }

        @media (max-width: 768px) {
          .newsletter-section {
            padding: var(--wero-spacing-2xl) 0;
          }

          .newsletter-title {
            font-size: var(--wero-font-size-3xl);
          }

          .newsletter-description {
            font-size: var(--wero-font-size-base);
          }

          .form-card {
            padding: var(--wero-spacing-lg);
          }

          .proof-stats {
            flex-direction: column;
            gap: var(--wero-spacing-md);
          }

          .stat-divider {
            width: 40px;
            height: 1px;
          }
        }

        @media (max-width: 480px) {
          .input-group {
            gap: var(--wero-spacing-xs);
          }

          .newsletter-benefits {
            gap: var(--wero-spacing-sm);
          }
        }
      `}</style>
    </section>
  );
}