'use client';

import React, { useState } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

const faqs = [
  {
    id: 1,
    question: 'What is Wero?',
    answer: 'Wero is a new instant payment solution that allows you to send money using just a phone number. Built by European banks, it enables real-time transfers between Belgium, France, and Germany in 10 seconds or less.'
  },
  {
    id: 2,
    question: 'How fast are Wero transfers?',
    answer: 'Wero transfers are processed in real-time, typically completing within 10 seconds. Unlike traditional bank transfers that can take hours or days, Wero works 24/7, including weekends and holidays.'
  },
  {
    id: 3,
    question: 'Which banks support Wero?',
    answer: 'Wero is supported by major European banks including BNP Paribas, Société Générale, Deutsche Bank, Commerzbank, KBC, ING, Belfius, and Crédit Agricole. The network continues to expand across Europe.'
  },
  {
    id: 4,
    question: 'Is Wero secure?',
    answer: 'Yes, <PERSON><PERSON> uses bank-level security with advanced encryption and fraud protection. All transactions are protected by the same security standards as your bank, and the service is fully regulated and compliant with European financial regulations.'
  },
  {
    id: 5,
    question: 'Do I need a special app to use Wero?',
    answer: 'Wero is integrated directly into your existing banking app or available as a standalone app. You can access Wero through your bank\'s mobile app or download the dedicated Wero app from your app store.'
  },
  {
    id: 6,
    question: 'What information do I need to send money?',
    answer: 'All you need is the recipient\'s phone number. No more complex IBAN numbers or account details - Wero makes sending money as simple as sending a text message.'
  },
  {
    id: 7,
    question: 'Are there any fees for using Wero?',
    answer: 'Fees may vary depending on your bank. Many transactions within the same country are free, while cross-border transfers may have minimal fees. Check with your bank for specific pricing information.'
  },
  {
    id: 8,
    question: 'Can I send money to other countries?',
    answer: 'Yes, Wero enables seamless cross-border transfers between Belgium, France, and Germany. We are expanding to more European countries to create a truly unified payment network.'
  }
];

export default function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (id: number) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <section className="faq-section">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">
            Frequently Asked{' '}
            <span className="title-highlight">Questions</span>
          </h2>
          <p className="section-subtitle">
            Everything you need to know about Wero and instant payments
          </p>
        </div>

        <div className="faq-content">
          <div className="faq-list">
            {faqs.map((faq) => (
              <div key={faq.id} className="faq-item">
                <button
                  className={`faq-question ${openItems.includes(faq.id) ? 'active' : ''}`}
                  onClick={() => toggleItem(faq.id)}
                >
                  <span className="question-text">{faq.question}</span>
                  <ChevronDownIcon className={`question-icon ${openItems.includes(faq.id) ? 'rotated' : ''}`} />
                </button>
                <div className={`faq-answer ${openItems.includes(faq.id) ? 'open' : ''}`}>
                  <div className="answer-content">
                    <p>{faq.answer}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="faq-support">
            <div className="support-card">
              <div className="support-icon">
                <svg viewBox="0 0 24 24" fill="none" className="icon">
                  <path 
                    d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <div className="support-content">
                <h3 className="support-title">Still have questions?</h3>
                <p className="support-description">
                  Our support team is here to help you get started with Wero
                </p>
                <div className="support-actions">
                  <button className="btn btn-primary">
                    Contact Support
                  </button>
                  <button className="btn btn-outline">
                    Visit Help Center
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="getting-started">
          <div className="getting-started-content">
            <h3 className="getting-started-title">Ready to get started?</h3>
            <p className="getting-started-description">
              Join millions of Europeans already using Wero for instant payments
            </p>
            <div className="getting-started-actions">
              <button className="btn btn-primary btn-lg">
                Activate Wero Now
              </button>
              <button className="btn btn-outline btn-lg">
                Download App
              </button>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .faq-section {
          padding: var(--wero-spacing-4xl) 0;
          background-color: var(--wero-white);
        }

        .section-header {
          text-align: center;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .section-title {
          font-size: var(--wero-font-size-4xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-md);
        }

        .title-highlight {
          background: linear-gradient(120deg, var(--wero-yellow) 0%, var(--wero-yellow) 100%);
          background-repeat: no-repeat;
          background-size: 100% 0.3em;
          background-position: 0 85%;
        }

        .section-subtitle {
          font-size: var(--wero-font-size-lg);
          color: var(--wero-gray);
          max-width: 600px;
          margin: 0 auto;
        }

        .faq-content {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: var(--wero-spacing-4xl);
          align-items: flex-start;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .faq-list {
          space-y: var(--wero-spacing-lg);
        }

        .faq-item {
          background-color: var(--wero-white);
          border: 2px solid var(--wero-border);
          border-radius: var(--wero-radius-xl);
          overflow: hidden;
          transition: var(--wero-transition);
        }

        .faq-item:hover {
          border-color: var(--wero-yellow);
        }

        .faq-question {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--wero-spacing-lg);
          background: none;
          border: none;
          cursor: pointer;
          text-align: left;
          transition: var(--wero-transition);
        }

        .faq-question:hover {
          background-color: var(--wero-hover);
        }

        .faq-question.active {
          background-color: var(--wero-yellow);
        }

        .question-text {
          font-size: var(--wero-font-size-lg);
          font-weight: 600;
          color: var(--wero-black);
        }

        .question-icon {
          width: 20px;
          height: 20px;
          color: var(--wero-black);
          transition: transform 0.3s ease;
          flex-shrink: 0;
        }

        .question-icon.rotated {
          transform: rotate(180deg);
        }

        .faq-answer {
          max-height: 0;
          overflow: hidden;
          transition: max-height 0.3s ease;
        }

        .faq-answer.open {
          max-height: 500px;
        }

        .answer-content {
          padding: 0 var(--wero-spacing-lg) var(--wero-spacing-lg);
          border-top: 1px solid var(--wero-border);
        }

        .answer-content p {
          font-size: var(--wero-font-size-base);
          color: var(--wero-gray);
          line-height: 1.6;
          margin-top: var(--wero-spacing-md);
        }

        .faq-support {
          position: sticky;
          top: var(--wero-spacing-2xl);
        }

        .support-card {
          background-color: var(--wero-light-gray);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-2xl);
          text-align: center;
        }

        .support-icon {
          width: 60px;
          height: 60px;
          background-color: var(--wero-yellow);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto var(--wero-spacing-lg);
        }

        .support-icon .icon {
          width: 30px;
          height: 30px;
          color: var(--wero-black);
        }

        .support-title {
          font-size: var(--wero-font-size-xl);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .support-description {
          font-size: var(--wero-font-size-base);
          color: var(--wero-gray);
          margin-bottom: var(--wero-spacing-lg);
          line-height: 1.5;
        }

        .support-actions {
          display: flex;
          flex-direction: column;
          gap: var(--wero-spacing-sm);
        }

        .getting-started {
          background-color: var(--wero-black);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-3xl);
          text-align: center;
          color: var(--wero-white);
        }

        .getting-started-title {
          font-size: var(--wero-font-size-2xl);
          font-weight: 600;
          margin-bottom: var(--wero-spacing-md);
        }

        .getting-started-description {
          font-size: var(--wero-font-size-lg);
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: var(--wero-spacing-xl);
          max-width: 500px;
          margin-left: auto;
          margin-right: auto;
        }

        .getting-started-actions {
          display: flex;
          gap: var(--wero-spacing-lg);
          justify-content: center;
          flex-wrap: wrap;
        }

        @media (max-width: 1024px) {
          .faq-content {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-2xl);
          }

          .faq-support {
            position: static;
          }
        }

        @media (max-width: 768px) {
          .faq-section {
            padding: var(--wero-spacing-2xl) 0;
          }

          .section-title {
            font-size: var(--wero-font-size-3xl);
          }

          .faq-question {
            padding: var(--wero-spacing-md);
          }

          .question-text {
            font-size: var(--wero-font-size-base);
          }

          .support-card {
            padding: var(--wero-spacing-lg);
          }

          .getting-started-actions {
            flex-direction: column;
            align-items: center;
          }
        }

        @media (max-width: 480px) {
          .answer-content {
            padding: 0 var(--wero-spacing-md) var(--wero-spacing-md);
          }

          .support-actions {
            gap: var(--wero-spacing-xs);
          }
        }
      `}</style>
    </section>
  );
}