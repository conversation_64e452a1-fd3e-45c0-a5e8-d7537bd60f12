'use client';

import React, { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

const uspItems = [
  {
    id: 1,
    icon: '⚡',
    title: 'Super Fast Payments',
    description: 'Transfer money in 10 seconds or less, any time of day or night',
    details: 'Built on cutting-edge infrastructure for instant processing'
  },
  {
    id: 2,
    icon: '🌙',
    title: 'Always On, No Delays',
    description: '24/7 availability means your money moves when you need it to',
    details: 'No more waiting for business hours or bank holidays'
  },
  {
    id: 3,
    icon: '📱',
    title: 'Easy to Access',
    description: 'Simple phone number transfers - no complex account numbers',
    details: 'Just like sending a text message, but with money'
  },
  {
    id: 4,
    icon: '🌍',
    title: 'Borderless Banking',
    description: 'Seamless transfers across Belgium, France, and Germany',
    details: 'One platform, multiple countries, zero hassle'
  },
  {
    id: 5,
    icon: '🇪🇺',
    title: 'Made in Europe',
    description: 'Built by European banks for European consumers',
    details: 'Local expertise, regulatory compliance, trusted security'
  },
  {
    id: 6,
    icon: '🔒',
    title: 'Bank-Level Security',
    description: 'Your money is protected by the same security as your bank',
    details: 'Advanced encryption and fraud protection systems'
  }
];

export default function USPCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % uspItems.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const goToPrevious = () => {
    setIsAutoPlaying(false);
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? uspItems.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setIsAutoPlaying(false);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % uspItems.length);
  };

  const goToSlide = (index: number) => {
    setIsAutoPlaying(false);
    setCurrentIndex(index);
  };

  return (
    <section className="usp-carousel-section">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">
            Why choose{' '}
            <span className="title-highlight">Wero</span>?
          </h2>
          <p className="section-subtitle">
            Discover the features that make Wero the future of European payments
          </p>
        </div>

        <div className="carousel-container">
          <div className="carousel-wrapper">
            <div 
              className="carousel-track"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {uspItems.map((item, index) => (
                <div key={item.id} className="carousel-slide">
                  <div className="usp-card">
                    <div className="usp-icon">
                      <span className="icon-emoji">{item.icon}</span>
                    </div>
                    <div className="usp-content">
                      <h3 className="usp-title">{item.title}</h3>
                      <p className="usp-description">{item.description}</p>
                      <p className="usp-details">{item.details}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="carousel-controls">
            <button 
              className="carousel-btn carousel-btn-prev"
              onClick={goToPrevious}
              aria-label="Previous feature"
            >
              <ChevronLeftIcon className="btn-icon" />
            </button>
            <button 
              className="carousel-btn carousel-btn-next"
              onClick={goToNext}
              aria-label="Next feature"
            >
              <ChevronRightIcon className="btn-icon" />
            </button>
          </div>

          <div className="carousel-indicators">
            {uspItems.map((_, index) => (
              <button
                key={index}
                className={`indicator ${index === currentIndex ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Grid view for larger screens */}
        <div className="usp-grid">
          {uspItems.map((item) => (
            <div key={item.id} className="usp-grid-item">
              <div className="grid-icon">
                <span className="icon-emoji">{item.icon}</span>
              </div>
              <h3 className="grid-title">{item.title}</h3>
              <p className="grid-description">{item.description}</p>
            </div>
          ))}
        </div>

        <div className="cta-section">
          <h3 className="cta-title">Ready to experience the future of payments?</h3>
          <div className="cta-buttons">
            <button className="btn btn-primary btn-lg">
              Get Started Now
            </button>
            <button className="btn btn-outline btn-lg">
              Download the App
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .usp-carousel-section {
          padding: var(--wero-spacing-4xl) 0;
          background: linear-gradient(135deg, var(--wero-light-gray) 0%, var(--wero-white) 100%);
        }

        .section-header {
          text-align: center;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .section-title {
          font-size: var(--wero-font-size-4xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-md);
        }

        .title-highlight {
          background: linear-gradient(120deg, var(--wero-yellow) 0%, var(--wero-yellow) 100%);
          background-repeat: no-repeat;
          background-size: 100% 0.3em;
          background-position: 0 85%;
        }

        .section-subtitle {
          font-size: var(--wero-font-size-lg);
          color: var(--wero-gray);
          max-width: 600px;
          margin: 0 auto;
        }

        .carousel-container {
          position: relative;
          margin-bottom: var(--wero-spacing-4xl);
          display: block;
        }

        .carousel-wrapper {
          overflow: hidden;
          border-radius: var(--wero-radius-xl);
        }

        .carousel-track {
          display: flex;
          transition: transform 0.5s ease-in-out;
        }

        .carousel-slide {
          min-width: 100%;
          padding: 0 var(--wero-spacing-md);
        }

        .usp-card {
          background-color: var(--wero-white);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-2xl);
          text-align: center;
          box-shadow: var(--wero-shadow-lg);
          height: 400px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }

        .usp-icon {
          width: 80px;
          height: 80px;
          background-color: var(--wero-yellow);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: var(--wero-spacing-xl);
        }

        .icon-emoji {
          font-size: var(--wero-font-size-4xl);
        }

        .usp-content {
          max-width: 400px;
        }

        .usp-title {
          font-size: var(--wero-font-size-2xl);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-md);
        }

        .usp-description {
          font-size: var(--wero-font-size-lg);
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-md);
          line-height: 1.6;
        }

        .usp-details {
          font-size: var(--wero-font-size-base);
          color: var(--wero-gray);
          line-height: 1.5;
        }

        .carousel-controls {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 100%;
          display: flex;
          justify-content: space-between;
          padding: 0 var(--wero-spacing-lg);
          pointer-events: none;
        }

        .carousel-btn {
          width: 48px;
          height: 48px;
          background-color: var(--wero-white);
          border: 2px solid var(--wero-border);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: var(--wero-transition);
          pointer-events: auto;
          box-shadow: var(--wero-shadow-md);
        }

        .carousel-btn:hover {
          border-color: var(--wero-black);
          transform: scale(1.05);
        }

        .btn-icon {
          width: 20px;
          height: 20px;
          color: var(--wero-black);
        }

        .carousel-indicators {
          display: flex;
          justify-content: center;
          gap: var(--wero-spacing-sm);
          margin-top: var(--wero-spacing-xl);
        }

        .indicator {
          width: 12px;
          height: 12px;
          border-radius: var(--wero-radius-full);
          border: none;
          background-color: var(--wero-border);
          cursor: pointer;
          transition: var(--wero-transition);
        }

        .indicator.active {
          background-color: var(--wero-black);
        }

        .indicator:hover {
          background-color: var(--wero-gray);
        }

        .usp-grid {
          display: none;
          grid-template-columns: repeat(3, 1fr);
          gap: var(--wero-spacing-xl);
          margin-bottom: var(--wero-spacing-4xl);
        }

        .usp-grid-item {
          background-color: var(--wero-white);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-xl);
          text-align: center;
          box-shadow: var(--wero-shadow-md);
          transition: var(--wero-transition);
        }

        .usp-grid-item:hover {
          transform: translateY(-4px);
          box-shadow: var(--wero-shadow-lg);
        }

        .grid-icon {
          width: 60px;
          height: 60px;
          background-color: var(--wero-yellow);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto var(--wero-spacing-lg);
        }

        .grid-icon .icon-emoji {
          font-size: var(--wero-font-size-2xl);
        }

        .grid-title {
          font-size: var(--wero-font-size-xl);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .grid-description {
          font-size: var(--wero-font-size-base);
          color: var(--wero-gray);
          line-height: 1.6;
        }

        .cta-section {
          text-align: center;
          background-color: var(--wero-black);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-3xl);
          color: var(--wero-white);
        }

        .cta-title {
          font-size: var(--wero-font-size-2xl);
          font-weight: 600;
          margin-bottom: var(--wero-spacing-xl);
        }

        .cta-buttons {
          display: flex;
          gap: var(--wero-spacing-lg);
          justify-content: center;
          flex-wrap: wrap;
        }

        @media (min-width: 1024px) {
          .carousel-container {
            display: none;
          }

          .usp-grid {
            display: grid;
          }
        }

        @media (max-width: 768px) {
          .usp-carousel-section {
            padding: var(--wero-spacing-2xl) 0;
          }

          .section-title {
            font-size: var(--wero-font-size-3xl);
          }

          .section-subtitle {
            font-size: var(--wero-font-size-base);
          }

          .usp-card {
            height: auto;
            padding: var(--wero-spacing-xl);
          }

          .usp-icon {
            width: 60px;
            height: 60px;
          }

          .icon-emoji {
            font-size: var(--wero-font-size-2xl);
          }

          .usp-title {
            font-size: var(--wero-font-size-xl);
          }

          .usp-description {
            font-size: var(--wero-font-size-base);
          }

          .cta-buttons {
            flex-direction: column;
            align-items: center;
          }

          .cta-title {
            font-size: var(--wero-font-size-xl);
          }
        }

        @media (max-width: 480px) {
          .carousel-controls {
            padding: 0;
          }

          .carousel-btn {
            width: 40px;
            height: 40px;
          }

          .btn-icon {
            width: 16px;
            height: 16px;
          }
        }
      `}</style>
    </section>
  );
}