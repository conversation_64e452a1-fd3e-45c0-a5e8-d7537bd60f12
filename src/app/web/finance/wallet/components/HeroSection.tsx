'use client';

import React from 'react';
import Link from 'next/link';
import { PlayIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

export default function HeroSection() {
  return (
    <section className="hero-section">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Move money in real time.{' '}
              <span className="hero-highlight">For real.</span>
            </h1>
            <p className="hero-subtitle">
              Send and receive money between bank accounts in 10 seconds or less, 24/7
            </p>
            
            <div className="hero-actions">
              <Link href="/web/finance/wallet/how-it-works" className="btn btn-primary btn-lg">
                <PlayIcon className="btn-icon" />
                Learn how We<PERSON> works
              </Link>
              <Link href="/web/finance/wallet/activate" className="btn btn-secondary btn-lg">
                Activate Wero now
                <ArrowRightIcon className="btn-icon" />
              </Link>
            </div>
          </div>

          <div className="hero-visual">
            <div className="phone-mockup">
              <div className="phone-frame">
                <div className="phone-screen">
                  <div className="app-interface">
                    <div className="app-header">
                      <div className="app-logo">wero</div>
                      <div className="app-balance">€1,250.00</div>
                    </div>
                    <div className="transfer-card">
                      <div className="transfer-amount">€150.00</div>
                      <div className="transfer-to">To: +33 6 12 34 56 78</div>
                      <div className="transfer-status">
                        <div className="status-indicator active"></div>
                        Sent in 3 seconds
                      </div>
                    </div>
                    <div className="recent-transfers">
                      <div className="transfer-item">
                        <div className="transfer-info">
                          <div className="transfer-name">Marie Dubois</div>
                          <div className="transfer-date">Today, 14:30</div>
                        </div>
                        <div className="transfer-amount-small">-€25.00</div>
                      </div>
                      <div className="transfer-item">
                        <div className="transfer-info">
                          <div className="transfer-name">Coffee Shop</div>
                          <div className="transfer-date">Today, 12:15</div>
                        </div>
                        <div className="transfer-amount-small">-€4.50</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Ticker */}
        <div className="feature-ticker">
          <div className="ticker-track">
            <div className="ticker-item">
              <span className="ticker-icon">⚡</span>
              Fast and hassle-free
            </div>
            <div className="ticker-item">
              <span className="ticker-icon">🌍</span>
              Seamless payments in Belgium, France and Germany
            </div>
            <div className="ticker-item">
              <span className="ticker-icon">📱</span>
              All you need is a phone number
            </div>
            <div className="ticker-item">
              <span className="ticker-icon">🔒</span>
              Bank-level security
            </div>
            <div className="ticker-item">
              <span className="ticker-icon">⚡</span>
              Fast and hassle-free
            </div>
            <div className="ticker-item">
              <span className="ticker-icon">🌍</span>
              Seamless payments in Belgium, France and Germany
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .hero-section {
          background: linear-gradient(135deg, var(--wero-white) 0%, var(--wero-light-gray) 100%);
          padding: var(--wero-spacing-4xl) 0 var(--wero-spacing-2xl);
          min-height: 80vh;
          display: flex;
          align-items: center;
        }

        .hero-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--wero-spacing-4xl);
          align-items: center;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .hero-text {
          max-width: 600px;
        }

        .hero-title {
          font-size: var(--wero-font-size-5xl);
          font-weight: 700;
          line-height: 1.1;
          margin-bottom: var(--wero-spacing-lg);
          color: var(--wero-black);
        }

        .hero-highlight {
          background: linear-gradient(120deg, var(--wero-yellow) 0%, var(--wero-yellow) 100%);
          background-repeat: no-repeat;
          background-size: 100% 0.3em;
          background-position: 0 85%;
        }

        .hero-subtitle {
          font-size: var(--wero-font-size-xl);
          line-height: 1.6;
          color: var(--wero-gray);
          margin-bottom: var(--wero-spacing-2xl);
        }

        .hero-actions {
          display: flex;
          gap: var(--wero-spacing-lg);
          flex-wrap: wrap;
        }

        .btn-icon {
          width: 1.25rem;
          height: 1.25rem;
        }

        .hero-visual {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .phone-mockup {
          position: relative;
          transform: rotate(-5deg);
          animation: float 6s ease-in-out infinite;
        }

        .phone-frame {
          width: 280px;
          height: 560px;
          background: linear-gradient(145deg, #e6e6e6, #ffffff);
          border-radius: 3rem;
          padding: 1.5rem;
          box-shadow: 
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 10px 20px rgba(0, 0, 0, 0.05);
        }

        .phone-screen {
          width: 100%;
          height: 100%;
          background: var(--wero-black);
          border-radius: 2.5rem;
          overflow: hidden;
          position: relative;
        }

        .app-interface {
          background: var(--wero-white);
          height: 100%;
          padding: var(--wero-spacing-lg);
          border-radius: 2.5rem;
        }

        .app-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--wero-spacing-xl);
          padding-bottom: var(--wero-spacing-md);
          border-bottom: 1px solid var(--wero-border);
        }

        .app-logo {
          font-size: var(--wero-font-size-xl);
          font-weight: 700;
          color: var(--wero-black);
        }

        .app-balance {
          font-size: var(--wero-font-size-lg);
          font-weight: 600;
          color: var(--wero-black);
        }

        .transfer-card {
          background: var(--wero-yellow);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-lg);
          margin-bottom: var(--wero-spacing-xl);
          text-align: center;
        }

        .transfer-amount {
          font-size: var(--wero-font-size-3xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .transfer-to {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
          margin-bottom: var(--wero-spacing-md);
        }

        .transfer-status {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: var(--wero-spacing-sm);
          font-size: var(--wero-font-size-sm);
          font-weight: 600;
          color: var(--wero-black);
        }

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #22c55e;
          animation: pulse 2s infinite;
        }

        .recent-transfers {
          space-y: var(--wero-spacing-md);
        }

        .transfer-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--wero-spacing-md);
          background: var(--wero-light-gray);
          border-radius: var(--wero-radius-lg);
          margin-bottom: var(--wero-spacing-sm);
        }

        .transfer-info {
          flex: 1;
        }

        .transfer-name {
          font-size: var(--wero-font-size-sm);
          font-weight: 600;
          color: var(--wero-black);
        }

        .transfer-date {
          font-size: var(--wero-font-size-xs);
          color: var(--wero-gray);
        }

        .transfer-amount-small {
          font-size: var(--wero-font-size-sm);
          font-weight: 600;
          color: var(--wero-black);
        }

        .feature-ticker {
          background-color: var(--wero-black);
          padding: var(--wero-spacing-lg) 0;
          margin: 0 calc(-50vw + 50%);
          overflow: hidden;
          white-space: nowrap;
        }

        .ticker-track {
          display: inline-flex;
          animation: ticker 30s linear infinite;
          gap: var(--wero-spacing-4xl);
        }

        .ticker-item {
          display: inline-flex;
          align-items: center;
          gap: var(--wero-spacing-sm);
          color: var(--wero-white);
          font-size: var(--wero-font-size-base);
          font-weight: 500;
          white-space: nowrap;
        }

        .ticker-icon {
          font-size: var(--wero-font-size-lg);
        }

        @keyframes float {
          0%, 100% {
            transform: rotate(-5deg) translateY(0px);
          }
          50% {
            transform: rotate(-5deg) translateY(-10px);
          }
        }

        @keyframes ticker {
          0% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }

        @media (max-width: 1024px) {
          .hero-content {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-2xl);
            text-align: center;
          }

          .hero-text {
            order: 2;
          }

          .hero-visual {
            order: 1;
          }

          .phone-frame {
            width: 240px;
            height: 480px;
            padding: 1rem;
          }
        }

        @media (max-width: 768px) {
          .hero-section {
            padding: var(--wero-spacing-2xl) 0;
            min-height: auto;
          }

          .hero-title {
            font-size: var(--wero-font-size-3xl);
          }

          .hero-subtitle {
            font-size: var(--wero-font-size-lg);
          }

          .hero-actions {
            flex-direction: column;
            align-items: center;
          }

          .phone-frame {
            width: 200px;
            height: 400px;
          }

          .ticker-item {
            font-size: var(--wero-font-size-sm);
          }
        }
      `}</style>
    </section>
  );
}