'use client';

import React from 'react';
import Link from 'next/link';

const footerSections = [
  {
    title: 'Product',
    links: [
      { name: 'How it works', href: '/web/finance/wallet/how-it-works' },
      { name: 'Security', href: '/web/finance/wallet/security' },
      { name: 'Supported banks', href: '/web/finance/wallet/banks' },
      { name: 'Countries', href: '/web/finance/wallet/countries' },
      { name: 'Pricing', href: '/web/finance/wallet/pricing' }
    ]
  },
  {
    title: 'For Individuals',
    links: [
      { name: 'Send money', href: '/web/finance/wallet/individuals/send' },
      { name: 'Receive money', href: '/web/finance/wallet/individuals/receive' },
      { name: 'Split bills', href: '/web/finance/wallet/individuals/split' },
      { name: 'Family transfers', href: '/web/finance/wallet/individuals/family' },
      { name: 'Download app', href: '/web/finance/wallet/download' }
    ]
  },
  {
    title: 'For Business',
    links: [
      { name: 'Merchants', href: '/web/finance/wallet/merchants' },
      { name: 'Professionals', href: '/web/finance/wallet/professionals' },
      { name: 'API documentation', href: '/web/finance/wallet/api' },
      { name: 'Integration guide', href: '/web/finance/wallet/integration' },
      { name: 'Business account', href: '/web/finance/wallet/business' }
    ]
  },
  {
    title: 'Support',
    links: [
      { name: 'Help center', href: '/web/finance/wallet/help' },
      { name: 'Contact us', href: '/web/finance/wallet/contact' },
      { name: 'FAQ', href: '/web/finance/wallet/faq' },
      { name: 'Status page', href: '/web/finance/wallet/status' },
      { name: 'Report issue', href: '/web/finance/wallet/report' }
    ]
  },
  {
    title: 'Company',
    links: [
      { name: 'About us', href: '/web/finance/wallet/about' },
      { name: 'News', href: '/web/finance/wallet/news' },
      { name: 'Careers', href: '/web/finance/wallet/careers' },
      { name: 'Partners', href: '/web/finance/wallet/partners' },
      { name: 'Press kit', href: '/web/finance/wallet/press' }
    ]
  }
];

const socialLinks = [
  {
    name: 'Twitter',
    href: 'https://twitter.com/wero',
    icon: (
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
      </svg>
    )
  },
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/company/wero',
    icon: (
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
      </svg>
    )
  },
  {
    name: 'Facebook',
    href: 'https://facebook.com/wero',
    icon: (
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
      </svg>
    )
  },
  {
    name: 'Instagram',
    href: 'https://instagram.com/wero',
    icon: (
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.019.43c-.537.233-.994.542-1.447.994-.452.453-.761.91-.994 1.447-.226.501-.348 1.075-.382 2.022C2.013 7.989 2 8.396 2 12.017s.013 4.028.048 4.976c.034.947.156 1.521.382 2.022.233.537.542.994.994 1.447.453.452.91.761 1.447.994.501.226 1.075.348 2.022.382.948.035 1.355.048 4.976.048s4.028-.013 4.976-.048c.947-.034 1.521-.156 2.022-.382.537-.233.994-.542 1.447-.994.452-.453.761-.91.994-1.447.226-.501.348-1.075.382-2.022.035-.948.048-1.355.048-4.976s-.013-4.028-.048-4.976c-.034-.947-.156-1.521-.382-2.022-.233-.537-.542-.994-.994-1.447-.453-.452-.91-.761-1.447-.994-.501-.226-1.075-.348-2.022-.382C16.045.013 15.638 0 12.017 0zm0 2.162c3.204 0 3.584.012 4.849.07.3.01.602.05.898.118.254.06.498.152.718.284.22.132.417.302.585.47.168.168.338.365.47.585.132.22.224.464.284.718.068.296.108.598.118.898.058 1.265.07 1.645.07 4.849s-.012 3.584-.07 4.849c-.01.3-.05.602-.118.898-.06.254-.152.498-.284.718-.132.22-.302.417-.47.585-.168.168-.365.338-.585.47-.22.132-.464.224-.718.284-.296.068-.598.108-.898.118-1.265.058-1.645.07-4.849.07s-3.584-.012-4.849-.07c-.3-.01-.602-.05-.898-.118-.254-.06-.498-.152-.718-.284-.22-.132-.417-.302-.585-.47-.168-.168-.338-.365-.47-.585-.132-.22-.224-.464-.284-.718-.068-.296-.108-.598-.118-.898-.058-1.265-.07-1.645-.07-4.849s.012-3.584.07-4.849c.01-.3.05-.602.118-.898.06-.254.152-.498.284-.718.132-.22.302-.417.47-.585.168-.168.365-.338.585-.47.22-.132.464-.224.718-.284.296-.068.598-.108.898-.118 1.265-.058 1.645-.07 4.849-.07z"/>
        <path d="M12.017 7.757c-2.34 0-4.26 1.92-4.26 4.26s1.92 4.26 4.26 4.26 4.26-1.92 4.26-4.26-1.92-4.26-4.26-4.26zm0 7.044c-1.533 0-2.784-1.251-2.784-2.784s1.251-2.784 2.784-2.784 2.784 1.251 2.784 2.784-1.251 2.784-2.784 2.784z"/>
        <circle cx="16.406" cy="7.594" r="1.44"/>
      </svg>
    )
  }
];

const legalLinks = [
  { name: 'Privacy Policy', href: '/web/finance/wallet/privacy' },
  { name: 'Terms of Service', href: '/web/finance/wallet/terms' },
  { name: 'Cookie Policy', href: '/web/finance/wallet/cookies' },
  { name: 'Legal Notice', href: '/web/finance/wallet/legal' }
];

export default function Footer() {
  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-main">
          <div className="footer-brand">
            <Link href="/web/finance/wallet" className="brand-link">
              <div className="brand-logo">wero</div>
            </Link>
            <p className="brand-description">
              The future of European payments. Send money instantly across Belgium, 
              France, and Germany with just a phone number.
            </p>
            
            <div className="brand-stats">
              <div className="stat">
                <span className="stat-number">8+</span>
                <span className="stat-label">Partner Banks</span>
              </div>
              <div className="stat">
                <span className="stat-number">3</span>
                <span className="stat-label">Countries</span>
              </div>
              <div className="stat">
                <span className="stat-number">10s</span>
                <span className="stat-label">Transfer Time</span>
              </div>
            </div>

            <div className="social-links">
              <span className="social-title">Follow us</span>
              <div className="social-icons">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.href}
                    className="social-link"
                    aria-label={social.name}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>
          </div>

          <div className="footer-links">
            {footerSections.map((section) => (
              <div key={section.title} className="footer-section">
                <h3 className="section-title">{section.title}</h3>
                <ul className="section-links">
                  {section.links.map((link) => (
                    <li key={link.name}>
                      <Link href={link.href} className="footer-link">
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-legal">
            <div className="legal-links">
              {legalLinks.map((link, index) => (
                <span key={link.name}>
                  <Link href={link.href} className="legal-link">
                    {link.name}
                  </Link>
                  {index < legalLinks.length - 1 && <span className="separator">•</span>}
                </span>
              ))}
            </div>
            
            <div className="regulatory-info">
              <p className="regulatory-text">
                Wero is operated by European banks and is regulated by national financial authorities. 
                All transactions are protected by bank-level security and European financial regulations.
              </p>
            </div>
          </div>

          <div className="footer-copyright">
            <p className="copyright-text">
              © {new Date().getFullYear()} Wero. All rights reserved.
            </p>
            <p className="version-info">
              Version 2.1.4 • Built with ❤️ in Europe
            </p>
          </div>
        </div>

        <div className="footer-badges">
          <div className="security-badges">
            <div className="badge">
              <div className="badge-icon">🔒</div>
              <div className="badge-text">
                <div className="badge-title">Bank-Level Security</div>
                <div className="badge-subtitle">256-bit encryption</div>
              </div>
            </div>
            
            <div className="badge">
              <div className="badge-icon">🇪🇺</div>
              <div className="badge-text">
                <div className="badge-title">EU Regulated</div>
                <div className="badge-subtitle">Fully compliant</div>
              </div>
            </div>
            
            <div className="badge">
              <div className="badge-icon">⚡</div>
              <div className="badge-text">
                <div className="badge-title">Instant Transfers</div>
                <div className="badge-subtitle">10 seconds or less</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .footer {
          background-color: var(--wero-black);
          color: var(--wero-white);
          padding: var(--wero-spacing-4xl) 0 var(--wero-spacing-xl);
        }

        .footer-main {
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: var(--wero-spacing-4xl);
          margin-bottom: var(--wero-spacing-4xl);
        }

        .footer-brand {
          max-width: 400px;
        }

        .brand-link {
          text-decoration: none;
          display: inline-block;
          margin-bottom: var(--wero-spacing-lg);
        }

        .brand-logo {
          font-size: var(--wero-font-size-3xl);
          font-weight: 700;
          color: var(--wero-white);
        }

        .brand-description {
          font-size: var(--wero-font-size-base);
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.6;
          margin-bottom: var(--wero-spacing-xl);
        }

        .brand-stats {
          display: flex;
          gap: var(--wero-spacing-lg);
          margin-bottom: var(--wero-spacing-xl);
        }

        .stat {
          text-align: center;
        }

        .stat-number {
          display: block;
          font-size: var(--wero-font-size-xl);
          font-weight: 700;
          color: var(--wero-yellow);
          margin-bottom: var(--wero-spacing-xs);
        }

        .stat-label {
          font-size: var(--wero-font-size-xs);
          color: rgba(255, 255, 255, 0.6);
        }

        .social-links {
          margin-top: var(--wero-spacing-xl);
        }

        .social-title {
          font-size: var(--wero-font-size-sm);
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: var(--wero-spacing-sm);
          display: block;
        }

        .social-icons {
          display: flex;
          gap: var(--wero-spacing-md);
        }

        .social-link {
          width: 40px;
          height: 40px;
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: var(--wero-radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(255, 255, 255, 0.8);
          transition: var(--wero-transition);
          text-decoration: none;
        }

        .social-link:hover {
          background-color: var(--wero-yellow);
          color: var(--wero-black);
          transform: translateY(-2px);
        }

        .social-link svg {
          width: 20px;
          height: 20px;
        }

        .footer-links {
          display: grid;
          grid-template-columns: repeat(5, 1fr);
          gap: var(--wero-spacing-xl);
        }

        .footer-section {
          margin-bottom: var(--wero-spacing-lg);
        }

        .section-title {
          font-size: var(--wero-font-size-base);
          font-weight: 600;
          color: var(--wero-white);
          margin-bottom: var(--wero-spacing-md);
        }

        .section-links {
          list-style: none;
          margin: 0;
          padding: 0;
        }

        .section-links li {
          margin-bottom: var(--wero-spacing-sm);
        }

        .footer-link {
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          font-size: var(--wero-font-size-sm);
          transition: var(--wero-transition);
          display: block;
          padding: var(--wero-spacing-xs) 0;
        }

        .footer-link:hover {
          color: var(--wero-yellow);
          padding-left: var(--wero-spacing-sm);
        }

        .footer-bottom {
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          padding-top: var(--wero-spacing-xl);
          margin-bottom: var(--wero-spacing-xl);
        }

        .footer-legal {
          margin-bottom: var(--wero-spacing-lg);
        }

        .legal-links {
          display: flex;
          flex-wrap: wrap;
          gap: var(--wero-spacing-md);
          align-items: center;
          margin-bottom: var(--wero-spacing-md);
        }

        .legal-link {
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          font-size: var(--wero-font-size-sm);
          transition: var(--wero-transition);
        }

        .legal-link:hover {
          color: var(--wero-yellow);
        }

        .separator {
          color: rgba(255, 255, 255, 0.3);
          margin: 0 var(--wero-spacing-sm);
        }

        .regulatory-info {
          max-width: 600px;
        }

        .regulatory-text {
          font-size: var(--wero-font-size-xs);
          color: rgba(255, 255, 255, 0.6);
          line-height: 1.5;
        }

        .footer-copyright {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: var(--wero-spacing-md);
        }

        .copyright-text {
          font-size: var(--wero-font-size-sm);
          color: rgba(255, 255, 255, 0.7);
        }

        .version-info {
          font-size: var(--wero-font-size-xs);
          color: rgba(255, 255, 255, 0.5);
        }

        .footer-badges {
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          padding-top: var(--wero-spacing-xl);
        }

        .security-badges {
          display: flex;
          justify-content: center;
          gap: var(--wero-spacing-xl);
          flex-wrap: wrap;
        }

        .badge {
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-sm);
          background-color: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: var(--wero-radius-lg);
          padding: var(--wero-spacing-md) var(--wero-spacing-lg);
        }

        .badge-icon {
          font-size: var(--wero-font-size-lg);
        }

        .badge-title {
          font-size: var(--wero-font-size-sm);
          font-weight: 600;
          color: var(--wero-white);
          margin-bottom: var(--wero-spacing-xs);
        }

        .badge-subtitle {
          font-size: var(--wero-font-size-xs);
          color: rgba(255, 255, 255, 0.7);
        }

        @media (max-width: 1024px) {
          .footer-main {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-2xl);
          }

          .footer-links {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--wero-spacing-lg);
          }

          .brand-stats {
            justify-content: center;
            margin-bottom: var(--wero-spacing-lg);
          }
        }

        @media (max-width: 768px) {
          .footer {
            padding: var(--wero-spacing-2xl) 0 var(--wero-spacing-lg);
          }

          .footer-links {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--wero-spacing-md);
          }

          .footer-copyright {
            flex-direction: column;
            text-align: center;
            gap: var(--wero-spacing-sm);
          }

          .legal-links {
            justify-content: center;
          }

          .security-badges {
            flex-direction: column;
            align-items: center;
            gap: var(--wero-spacing-md);
          }

          .badge {
            width: 100%;
            max-width: 300px;
            justify-content: center;
          }
        }

        @media (max-width: 480px) {
          .footer-links {
            grid-template-columns: 1fr;
          }

          .legal-links {
            flex-direction: column;
            align-items: center;
            gap: var(--wero-spacing-sm);
          }

          .separator {
            display: none;
          }

          .brand-stats {
            flex-direction: column;
            gap: var(--wero-spacing-md);
            text-align: center;
          }

          .social-icons {
            justify-content: center;
          }
        }
      `}</style>
    </footer>
  );
}