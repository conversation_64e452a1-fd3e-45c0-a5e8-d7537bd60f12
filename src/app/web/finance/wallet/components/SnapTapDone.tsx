'use client';

import React, { useState } from 'react';
import { PlayIcon } from '@heroicons/react/24/outline';

const scenarios = [
  {
    id: 1,
    title: 'Split the Bill',
    description: 'Dinner with friends? Split the bill instantly with just their phone numbers.',
    steps: [
      { action: 'Snap', detail: 'Take a photo of the receipt' },
      { action: 'Tap', detail: 'Select friends to split with' },
      { action: 'Done', detail: 'Money sent in seconds' }
    ],
    visual: 'split-bill',
    amount: '€23.50',
    recipient: '+33 6 12 34 56 78'
  },
  {
    id: 2,
    title: 'Pay a Merchant',
    description: 'Shopping made simple - pay any participating merchant with a tap.',
    steps: [
      { action: 'Snap', detail: 'Scan the merchant QR code' },
      { action: 'Tap', detail: 'Confirm the amount' },
      { action: 'Done', detail: 'Payment completed instantly' }
    ],
    visual: 'pay-merchant',
    amount: '€45.99',
    recipient: 'Coffee Corner'
  },
  {
    id: 3,
    title: 'Send to Family',
    description: 'Supporting family across Europe has never been easier.',
    steps: [
      { action: 'Snap', detail: 'Open Wero app' },
      { action: 'Tap', detail: 'Enter phone number' },
      { action: 'Done', detail: 'Money arrives instantly' }
    ],
    visual: 'send-family',
    amount: '€200.00',
    recipient: 'Mom'
  }
];

export default function SnapTapDone() {
  const [activeScenario, setActiveScenario] = useState(0);

  return (
    <section className="snap-tap-done-section">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">
            <span className="title-highlight">Snap, Tap & Done</span>
          </h2>
          <p className="section-subtitle">
            See how easy it is to move money with Wero in real-life scenarios
          </p>
        </div>

        <div className="scenarios-container">
          <div className="scenario-tabs">
            {scenarios.map((scenario, index) => (
              <button
                key={scenario.id}
                className={`scenario-tab ${index === activeScenario ? 'active' : ''}`}
                onClick={() => setActiveScenario(index)}
              >
                <div className="tab-icon">
                  {scenario.visual === 'split-bill' && '🧾'}
                  {scenario.visual === 'pay-merchant' && '🏪'}
                  {scenario.visual === 'send-family' && '👨‍👩‍👧‍👦'}
                </div>
                <div className="tab-content">
                  <h3 className="tab-title">{scenario.title}</h3>
                  <p className="tab-description">{scenario.description}</p>
                </div>
              </button>
            ))}
          </div>

          <div className="scenario-content">
            <div className="content-visual">
              <div className="phone-demo">
                <div className="phone-frame">
                  <div className="phone-screen">
                    <div className="app-demo">
                      <div className="demo-header">
                        <div className="demo-logo">wero</div>
                        <div className="demo-time">14:23</div>
                      </div>
                      
                      <div className="demo-main">
                        <div className="transfer-preview">
                          <div className="amount-display">
                            {scenarios[activeScenario].amount}
                          </div>
                          <div className="recipient-display">
                            To: {scenarios[activeScenario].recipient}
                          </div>
                          <div className="status-display">
                            <div className="status-dot"></div>
                            Ready to send
                          </div>
                        </div>
                        
                        <div className="action-button">
                          <button className="send-button">
                            <PlayIcon className="send-icon" />
                            Send Money
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="content-steps">
              <div className="steps-header">
                <h3 className="steps-title">{scenarios[activeScenario].title}</h3>
                <p className="steps-description">{scenarios[activeScenario].description}</p>
              </div>

              <div className="steps-list">
                {scenarios[activeScenario].steps.map((step, index) => (
                  <div key={index} className="step-item">
                    <div className="step-number">{index + 1}</div>
                    <div className="step-content">
                      <div className="step-action">{step.action}</div>
                      <div className="step-detail">{step.detail}</div>
                    </div>
                    <div className="step-visual">
                      {step.action === 'Snap' && '📸'}
                      {step.action === 'Tap' && '👆'}
                      {step.action === 'Done' && '✅'}
                    </div>
                  </div>
                ))}
              </div>

              <div className="steps-footer">
                <div className="time-badge">
                  <span className="time-icon">⚡</span>
                  Completed in under 10 seconds
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="features-highlight">
          <div className="features-grid">
            <div className="feature-item">
              <div className="feature-icon">📱</div>
              <h4 className="feature-title">Mobile First</h4>
              <p className="feature-description">
                Designed for your smartphone with intuitive gestures
              </p>
            </div>
            
            <div className="feature-item">
              <div className="feature-icon">🔒</div>
              <h4 className="feature-title">Secure by Design</h4>
              <p className="feature-description">
                Every transaction is protected by bank-level security
              </p>
            </div>
            
            <div className="feature-item">
              <div className="feature-icon">🌐</div>
              <h4 className="feature-title">Cross-Border</h4>
              <p className="feature-description">
                Send money across Belgium, France, and Germany seamlessly
              </p>
            </div>
            
            <div className="feature-item">
              <div className="feature-icon">⚡</div>
              <h4 className="feature-title">Instant Settlement</h4>
              <p className="feature-description">
                Money arrives in the recipient's account immediately
              </p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .snap-tap-done-section {
          padding: var(--wero-spacing-4xl) 0;
          background-color: var(--wero-white);
        }

        .section-header {
          text-align: center;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .section-title {
          font-size: var(--wero-font-size-4xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-md);
        }

        .title-highlight {
          background: linear-gradient(120deg, var(--wero-yellow) 0%, var(--wero-yellow) 100%);
          background-repeat: no-repeat;
          background-size: 100% 0.3em;
          background-position: 0 85%;
        }

        .section-subtitle {
          font-size: var(--wero-font-size-lg);
          color: var(--wero-gray);
          max-width: 600px;
          margin: 0 auto;
        }

        .scenarios-container {
          margin-bottom: var(--wero-spacing-4xl);
        }

        .scenario-tabs {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: var(--wero-spacing-lg);
          margin-bottom: var(--wero-spacing-2xl);
        }

        .scenario-tab {
          background-color: var(--wero-white);
          border: 2px solid var(--wero-border);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-lg);
          cursor: pointer;
          transition: var(--wero-transition);
          text-align: left;
          display: flex;
          gap: var(--wero-spacing-md);
          align-items: flex-start;
        }

        .scenario-tab:hover {
          border-color: var(--wero-yellow);
          transform: translateY(-2px);
        }

        .scenario-tab.active {
          border-color: var(--wero-yellow);
          background-color: var(--wero-yellow);
        }

        .tab-icon {
          font-size: var(--wero-font-size-2xl);
          flex-shrink: 0;
        }

        .tab-content {
          flex: 1;
        }

        .tab-title {
          font-size: var(--wero-font-size-lg);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .tab-description {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
          line-height: 1.5;
        }

        .scenario-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--wero-spacing-4xl);
          align-items: center;
        }

        .content-visual {
          display: flex;
          justify-content: center;
        }

        .phone-demo {
          position: relative;
          animation: float 4s ease-in-out infinite;
        }

        .phone-frame {
          width: 280px;
          height: 560px;
          background: linear-gradient(145deg, #e6e6e6, #ffffff);
          border-radius: 3rem;
          padding: 1.5rem;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .phone-screen {
          width: 100%;
          height: 100%;
          background: var(--wero-black);
          border-radius: 2.5rem;
          overflow: hidden;
        }

        .app-demo {
          background: var(--wero-white);
          height: 100%;
          padding: var(--wero-spacing-lg);
          border-radius: 2.5rem;
        }

        .demo-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--wero-spacing-2xl);
          padding-bottom: var(--wero-spacing-md);
          border-bottom: 1px solid var(--wero-border);
        }

        .demo-logo {
          font-size: var(--wero-font-size-xl);
          font-weight: 700;
          color: var(--wero-black);
        }

        .demo-time {
          font-size: var(--wero-font-size-sm);
          font-weight: 600;
          color: var(--wero-black);
        }

        .demo-main {
          text-align: center;
        }

        .transfer-preview {
          background: var(--wero-light-gray);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-xl);
          margin-bottom: var(--wero-spacing-xl);
        }

        .amount-display {
          font-size: var(--wero-font-size-3xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .recipient-display {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
          margin-bottom: var(--wero-spacing-md);
        }

        .status-display {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: var(--wero-spacing-sm);
          font-size: var(--wero-font-size-sm);
          font-weight: 600;
          color: var(--wero-black);
        }

        .status-dot {
          width: 8px;
          height: 8px;
          background-color: #22c55e;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }

        .action-button {
          margin-top: var(--wero-spacing-xl);
        }

        .send-button {
          background-color: var(--wero-black);
          color: var(--wero-white);
          border: none;
          border-radius: var(--wero-radius-lg);
          padding: var(--wero-spacing-md) var(--wero-spacing-xl);
          font-size: var(--wero-font-size-base);
          font-weight: 600;
          cursor: pointer;
          transition: var(--wero-transition);
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-sm);
          margin: 0 auto;
        }

        .send-button:hover {
          background-color: var(--wero-dark-gray);
          transform: translateY(-1px);
        }

        .send-icon {
          width: 1rem;
          height: 1rem;
        }

        .content-steps {
          max-width: 400px;
        }

        .steps-header {
          margin-bottom: var(--wero-spacing-xl);
        }

        .steps-title {
          font-size: var(--wero-font-size-2xl);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .steps-description {
          font-size: var(--wero-font-size-base);
          color: var(--wero-gray);
          line-height: 1.6;
        }

        .steps-list {
          margin-bottom: var(--wero-spacing-xl);
        }

        .step-item {
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-lg);
          margin-bottom: var(--wero-spacing-lg);
          padding: var(--wero-spacing-lg);
          background-color: var(--wero-light-gray);
          border-radius: var(--wero-radius-lg);
        }

        .step-item:last-child {
          margin-bottom: 0;
        }

        .step-number {
          width: 32px;
          height: 32px;
          background-color: var(--wero-black);
          color: var(--wero-white);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: var(--wero-font-size-sm);
          flex-shrink: 0;
        }

        .step-content {
          flex: 1;
        }

        .step-action {
          font-size: var(--wero-font-size-base);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .step-detail {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
        }

        .step-visual {
          font-size: var(--wero-font-size-xl);
          flex-shrink: 0;
        }

        .steps-footer {
          text-align: center;
        }

        .time-badge {
          display: inline-flex;
          align-items: center;
          gap: var(--wero-spacing-sm);
          background-color: var(--wero-yellow);
          color: var(--wero-black);
          padding: var(--wero-spacing-sm) var(--wero-spacing-lg);
          border-radius: var(--wero-radius-full);
          font-size: var(--wero-font-size-sm);
          font-weight: 600;
        }

        .time-icon {
          font-size: var(--wero-font-size-base);
        }

        .features-highlight {
          background-color: var(--wero-light-gray);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-3xl);
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: var(--wero-spacing-xl);
        }

        .feature-item {
          text-align: center;
        }

        .feature-icon {
          font-size: var(--wero-font-size-3xl);
          margin-bottom: var(--wero-spacing-md);
        }

        .feature-title {
          font-size: var(--wero-font-size-lg);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .feature-description {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
          line-height: 1.5;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @media (max-width: 1024px) {
          .scenario-content {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-2xl);
            text-align: center;
          }

          .features-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--wero-spacing-lg);
          }
        }

        @media (max-width: 768px) {
          .snap-tap-done-section {
            padding: var(--wero-spacing-2xl) 0;
          }

          .section-title {
            font-size: var(--wero-font-size-3xl);
          }

          .scenario-tabs {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-md);
          }

          .phone-frame {
            width: 240px;
            height: 480px;
            padding: 1rem;
          }

          .features-grid {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-md);
          }

          .features-highlight {
            padding: var(--wero-spacing-xl);
          }
        }

        @media (max-width: 480px) {
          .phone-frame {
            width: 200px;
            height: 400px;
          }

          .tab-content {
            text-align: left;
          }

          .tab-description {
            display: none;
          }
        }
      `}</style>
    </section>
  );
}