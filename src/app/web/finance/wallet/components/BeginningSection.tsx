'use client';

import React from 'react';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

export default function BeginningSection() {
  return (
    <section className="beginning-section">
      <div className="container">
        <div className="beginning-content">
          <div className="beginning-text">
            <h2 className="beginning-title">
              This is just the{' '}
              <span className="beginning-highlight">beginning</span>
            </h2>
            <p className="beginning-description">
              Wero is the first step towards a unified European payment ecosystem. 
              Built by European banks for Europeans, we're making instant payments 
              the new standard across borders.
            </p>
            <div className="beginning-stats">
              <div className="stat-item">
                <div className="stat-number">10s</div>
                <div className="stat-label">Average transfer time</div>
              </div>
              <div className="stat-divider"></div>
              <div className="stat-item">
                <div className="stat-number">24/7</div>
                <div className="stat-label">Always available</div>
              </div>
              <div className="stat-divider"></div>
              <div className="stat-item">
                <div className="stat-number">3</div>
                <div className="stat-label">Countries connected</div>
              </div>
            </div>
          </div>

          <div className="beginning-visual">
            <div className="vision-card">
              <div className="vision-header">
                <div className="vision-icon">
                  <svg viewBox="0 0 24 24" fill="none" className="icon">
                    <path 
                      d="M12 2L2 7L12 12L22 7L12 2Z" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                    <path 
                      d="M2 17L12 22L22 17" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                    <path 
                      d="M2 12L12 17L22 12" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <h3 className="vision-title">The Future of European Banking</h3>
              </div>
              
              <div className="vision-content">
                <div className="vision-item">
                  <div className="vision-step">1</div>
                  <div className="vision-text">
                    <h4>Connect Your Bank</h4>
                    <p>Link your existing bank account from participating European banks</p>
                  </div>
                </div>
                
                <div className="vision-item">
                  <div className="vision-step">2</div>
                  <div className="vision-text">
                    <h4>Send with Phone Numbers</h4>
                    <p>No more IBAN numbers - just use a phone number to send money</p>
                  </div>
                </div>
                
                <div className="vision-item">
                  <div className="vision-step">3</div>
                  <div className="vision-text">
                    <h4>Instant Transfers</h4>
                    <p>Money arrives in seconds, not days - across Belgium, France & Germany</p>
                  </div>
                </div>
              </div>

              <div className="vision-footer">
                <button className="vision-cta">
                  Learn more about our vision
                  <ArrowRightIcon className="cta-icon" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="expansion-preview">
          <div className="expansion-header">
            <h3 className="expansion-title">Coming Soon</h3>
            <p className="expansion-subtitle">
              We're expanding across Europe to create a truly unified payment network
            </p>
          </div>
          
          <div className="countries-grid">
            <div className="country-card available">
              <div className="country-flag">🇧🇪</div>
              <div className="country-name">Belgium</div>
              <div className="country-status">Available Now</div>
            </div>
            
            <div className="country-card available">
              <div className="country-flag">🇫🇷</div>
              <div className="country-name">France</div>
              <div className="country-status">Available Now</div>
            </div>
            
            <div className="country-card available">
              <div className="country-flag">🇩🇪</div>
              <div className="country-name">Germany</div>
              <div className="country-status">Available Now</div>
            </div>
            
            <div className="country-card coming-soon">
              <div className="country-flag">🇳🇱</div>
              <div className="country-name">Netherlands</div>
              <div className="country-status">Coming Soon</div>
            </div>
            
            <div className="country-card coming-soon">
              <div className="country-flag">🇪🇸</div>
              <div className="country-name">Spain</div>
              <div className="country-status">Coming Soon</div>
            </div>
            
            <div className="country-card coming-soon">
              <div className="country-flag">🇮🇹</div>
              <div className="country-name">Italy</div>
              <div className="country-status">Coming Soon</div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .beginning-section {
          padding: var(--wero-spacing-4xl) 0;
          background-color: var(--wero-white);
        }

        .beginning-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--wero-spacing-4xl);
          align-items: center;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .beginning-text {
          max-width: 500px;
        }

        .beginning-title {
          font-size: var(--wero-font-size-4xl);
          font-weight: 700;
          line-height: 1.2;
          margin-bottom: var(--wero-spacing-lg);
          color: var(--wero-black);
        }

        .beginning-highlight {
          background: linear-gradient(120deg, var(--wero-yellow) 0%, var(--wero-yellow) 100%);
          background-repeat: no-repeat;
          background-size: 100% 0.3em;
          background-position: 0 85%;
        }

        .beginning-description {
          font-size: var(--wero-font-size-lg);
          line-height: 1.6;
          color: var(--wero-gray);
          margin-bottom: var(--wero-spacing-2xl);
        }

        .beginning-stats {
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-lg);
        }

        .stat-item {
          text-align: center;
        }

        .stat-number {
          font-size: var(--wero-font-size-3xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .stat-label {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
        }

        .stat-divider {
          width: 1px;
          height: 40px;
          background-color: var(--wero-border);
        }

        .beginning-visual {
          display: flex;
          justify-content: center;
        }

        .vision-card {
          background-color: var(--wero-white);
          border: 2px solid var(--wero-border);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-2xl);
          max-width: 400px;
          box-shadow: var(--wero-shadow-lg);
        }

        .vision-header {
          text-align: center;
          margin-bottom: var(--wero-spacing-xl);
        }

        .vision-icon {
          width: 60px;
          height: 60px;
          background-color: var(--wero-yellow);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto var(--wero-spacing-md);
        }

        .vision-icon .icon {
          width: 30px;
          height: 30px;
          color: var(--wero-black);
        }

        .vision-title {
          font-size: var(--wero-font-size-xl);
          font-weight: 600;
          color: var(--wero-black);
        }

        .vision-content {
          margin-bottom: var(--wero-spacing-xl);
        }

        .vision-item {
          display: flex;
          gap: var(--wero-spacing-md);
          margin-bottom: var(--wero-spacing-lg);
        }

        .vision-item:last-child {
          margin-bottom: 0;
        }

        .vision-step {
          width: 32px;
          height: 32px;
          background-color: var(--wero-black);
          color: var(--wero-white);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: var(--wero-font-size-sm);
          flex-shrink: 0;
        }

        .vision-text h4 {
          font-size: var(--wero-font-size-base);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .vision-text p {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
          line-height: 1.5;
        }

        .vision-footer {
          text-align: center;
        }

        .vision-cta {
          display: inline-flex;
          align-items: center;
          gap: var(--wero-spacing-sm);
          background: none;
          border: none;
          color: var(--wero-black);
          font-weight: 600;
          cursor: pointer;
          transition: var(--wero-transition);
          padding: var(--wero-spacing-sm);
          border-radius: var(--wero-radius-md);
        }

        .vision-cta:hover {
          background-color: var(--wero-hover);
        }

        .cta-icon {
          width: 1rem;
          height: 1rem;
        }

        .expansion-preview {
          text-align: center;
        }

        .expansion-header {
          margin-bottom: var(--wero-spacing-2xl);
        }

        .expansion-title {
          font-size: var(--wero-font-size-2xl);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-sm);
        }

        .expansion-subtitle {
          font-size: var(--wero-font-size-lg);
          color: var(--wero-gray);
        }

        .countries-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: var(--wero-spacing-lg);
          max-width: 800px;
          margin: 0 auto;
        }

        .country-card {
          background-color: var(--wero-white);
          border: 2px solid var(--wero-border);
          border-radius: var(--wero-radius-lg);
          padding: var(--wero-spacing-lg);
          text-align: center;
          transition: var(--wero-transition);
        }

        .country-card.available {
          border-color: var(--wero-yellow);
          background-color: var(--wero-yellow);
        }

        .country-card.coming-soon {
          opacity: 0.6;
        }

        .country-card:hover {
          transform: translateY(-2px);
          box-shadow: var(--wero-shadow-md);
        }

        .country-flag {
          font-size: var(--wero-font-size-3xl);
          margin-bottom: var(--wero-spacing-sm);
        }

        .country-name {
          font-size: var(--wero-font-size-base);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .country-status {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
          font-weight: 500;
        }

        .country-card.available .country-status {
          color: var(--wero-black);
          font-weight: 600;
        }

        @media (max-width: 1024px) {
          .beginning-content {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-2xl);
            text-align: center;
          }

          .beginning-stats {
            justify-content: center;
          }
        }

        @media (max-width: 768px) {
          .beginning-section {
            padding: var(--wero-spacing-2xl) 0;
          }

          .beginning-title {
            font-size: var(--wero-font-size-3xl);
          }

          .beginning-description {
            font-size: var(--wero-font-size-base);
          }

          .beginning-stats {
            flex-direction: column;
            gap: var(--wero-spacing-md);
          }

          .stat-divider {
            width: 40px;
            height: 1px;
          }

          .vision-card {
            padding: var(--wero-spacing-lg);
          }

          .countries-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--wero-spacing-md);
          }
        }

        @media (max-width: 480px) {
          .countries-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </section>
  );
}