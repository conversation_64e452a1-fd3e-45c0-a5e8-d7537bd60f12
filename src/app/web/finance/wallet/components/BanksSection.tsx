'use client';

import React from 'react';

const banks = [
  {
    name: 'BNP Paribas',
    country: 'France',
    logo: '🏦',
    color: '#00a94f'
  },
  {
    name: 'Société Générale',
    country: 'France', 
    logo: '🏛️',
    color: '#e31e24'
  },
  {
    name: 'Deutsche Bank',
    country: 'Germany',
    logo: '🏦',
    color: '#0018a8'
  },
  {
    name: 'Commerzbank',
    country: 'Germany',
    logo: '🏛️',
    color: '#ffcc00'
  },
  {
    name: 'KBC',
    country: 'Belgium',
    logo: '🏦',
    color: '#1f5f99'
  },
  {
    name: 'ING',
    country: 'Belgium',
    logo: '🦁',
    color: '#ff6200'
  },
  {
    name: 'Belfius',
    country: 'Belgium',
    logo: '🏛️',
    color: '#7b3192'
  },
  {
    name: 'Crédit Agricole',
    country: 'France',
    logo: '🌱',
    color: '#00a651'
  }
];

export default function BanksSection() {
  return (
    <section className="banks-section">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">
            Backed by your{' '}
            <span className="title-highlight">trusted banks</span>
          </h2>
          <p className="section-subtitle">
            Wero is built and supported by leading European banks, ensuring 
            the highest standards of security and reliability.
          </p>
        </div>

        <div className="banks-showcase">
          <div className="banks-grid">
            {banks.map((bank, index) => (
              <div 
                key={index} 
                className="bank-card"
                style={{ '--bank-color': bank.color } as React.CSSProperties}
              >
                <div className="bank-logo">
                  <span className="logo-icon">{bank.logo}</span>
                </div>
                <div className="bank-info">
                  <h3 className="bank-name">{bank.name}</h3>
                  <p className="bank-country">{bank.country}</p>
                </div>
                <div className="bank-badge">
                  <span className="badge-text">Connected</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="trust-indicators">
          <div className="indicators-grid">
            <div className="indicator-item">
              <div className="indicator-icon">
                <svg viewBox="0 0 24 24" fill="none" className="icon">
                  <path 
                    d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <div className="indicator-content">
                <h4 className="indicator-title">Bank-Level Security</h4>
                <p className="indicator-description">
                  Your money is protected by the same security standards as your bank
                </p>
              </div>
            </div>

            <div className="indicator-item">
              <div className="indicator-icon">
                <svg viewBox="0 0 24 24" fill="none" className="icon">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <path d="9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div className="indicator-content">
                <h4 className="indicator-title">Regulated & Compliant</h4>
                <p className="indicator-description">
                  Fully compliant with European financial regulations and standards
                </p>
              </div>
            </div>

            <div className="indicator-item">
              <div className="indicator-icon">
                <svg viewBox="0 0 24 24" fill="none" className="icon">
                  <path 
                    d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                  <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div className="indicator-content">
                <h4 className="indicator-title">Trusted Partnership</h4>
                <p className="indicator-description">
                  Built in collaboration with Europe's most trusted financial institutions
                </p>
              </div>
            </div>

            <div className="indicator-item">
              <div className="indicator-icon">
                <svg viewBox="0 0 24 24" fill="none" className="icon">
                  <path 
                    d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="12" y1="22.08" x2="12" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div className="indicator-content">
                <h4 className="indicator-title">European Infrastructure</h4>
                <p className="indicator-description">
                  Built on robust European payment infrastructure for maximum reliability
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="partnership-info">
          <div className="partnership-content">
            <div className="partnership-text">
              <h3 className="partnership-title">A True European Partnership</h3>
              <p className="partnership-description">
                Wero represents the future of European banking collaboration. 
                By bringing together the continent's leading financial institutions, 
                we're creating a unified payment ecosystem that puts European 
                consumers first.
              </p>
              <div className="partnership-stats">
                <div className="stat">
                  <div className="stat-number">8+</div>
                  <div className="stat-label">Partner Banks</div>
                </div>
                <div className="stat">
                  <div className="stat-number">3</div>
                  <div className="stat-label">Countries</div>
                </div>
                <div className="stat">
                  <div className="stat-number">50M+</div>
                  <div className="stat-label">Connected Accounts</div>
                </div>
              </div>
            </div>
            <div className="partnership-visual">
              <div className="network-visualization">
                <div className="network-center">
                  <div className="wero-node">wero</div>
                </div>
                <div className="network-connections">
                  {[0, 1, 2, 3, 4, 5].map((index) => (
                    <div 
                      key={index} 
                      className="connection-line"
                      style={{ '--rotation': `${index * 60}deg` } as React.CSSProperties}
                    >
                      <div className="bank-node">{banks[index]?.logo || '🏦'}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="cta-section">
          <div className="cta-content">
            <h3 className="cta-title">Ready to join the Wero network?</h3>
            <p className="cta-description">
              Connect your bank account and start sending money across Europe instantly.
            </p>
            <div className="cta-buttons">
              <button className="btn btn-primary btn-lg">
                Check Bank Compatibility
              </button>
              <button className="btn btn-outline btn-lg">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .banks-section {
          padding: var(--wero-spacing-4xl) 0;
          background: linear-gradient(135deg, var(--wero-white) 0%, var(--wero-light-gray) 100%);
        }

        .section-header {
          text-align: center;
          margin-bottom: var(--wero-spacing-4xl);
        }

        .section-title {
          font-size: var(--wero-font-size-4xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-md);
        }

        .title-highlight {
          background: linear-gradient(120deg, var(--wero-yellow) 0%, var(--wero-yellow) 100%);
          background-repeat: no-repeat;
          background-size: 100% 0.3em;
          background-position: 0 85%;
        }

        .section-subtitle {
          font-size: var(--wero-font-size-lg);
          color: var(--wero-gray);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .banks-showcase {
          margin-bottom: var(--wero-spacing-4xl);
        }

        .banks-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: var(--wero-spacing-lg);
          margin-bottom: var(--wero-spacing-2xl);
        }

        .bank-card {
          background-color: var(--wero-white);
          border: 2px solid var(--wero-border);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-lg);
          display: flex;
          align-items: center;
          gap: var(--wero-spacing-md);
          transition: var(--wero-transition);
          position: relative;
          overflow: hidden;
        }

        .bank-card:hover {
          transform: translateY(-2px);
          box-shadow: var(--wero-shadow-lg);
          border-color: var(--bank-color);
        }

        .bank-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background-color: var(--bank-color);
        }

        .bank-logo {
          width: 60px;
          height: 60px;
          background-color: var(--wero-light-gray);
          border-radius: var(--wero-radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .logo-icon {
          font-size: var(--wero-font-size-2xl);
        }

        .bank-info {
          flex: 1;
        }

        .bank-name {
          font-size: var(--wero-font-size-lg);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .bank-country {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
        }

        .bank-badge {
          background-color: var(--wero-yellow);
          color: var(--wero-black);
          padding: var(--wero-spacing-xs) var(--wero-spacing-sm);
          border-radius: var(--wero-radius-full);
          font-size: var(--wero-font-size-xs);
          font-weight: 600;
        }

        .trust-indicators {
          margin-bottom: var(--wero-spacing-4xl);
        }

        .indicators-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: var(--wero-spacing-xl);
        }

        .indicator-item {
          display: flex;
          gap: var(--wero-spacing-md);
          align-items: flex-start;
        }

        .indicator-icon {
          width: 48px;
          height: 48px;
          background-color: var(--wero-yellow);
          border-radius: var(--wero-radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .indicator-icon .icon {
          width: 24px;
          height: 24px;
          color: var(--wero-black);
        }

        .indicator-content {
          flex: 1;
        }

        .indicator-title {
          font-size: var(--wero-font-size-lg);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .indicator-description {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
          line-height: 1.5;
        }

        .partnership-info {
          margin-bottom: var(--wero-spacing-4xl);
        }

        .partnership-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--wero-spacing-4xl);
          align-items: center;
        }

        .partnership-title {
          font-size: var(--wero-font-size-2xl);
          font-weight: 600;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-md);
        }

        .partnership-description {
          font-size: var(--wero-font-size-base);
          color: var(--wero-gray);
          line-height: 1.6;
          margin-bottom: var(--wero-spacing-xl);
        }

        .partnership-stats {
          display: flex;
          gap: var(--wero-spacing-xl);
        }

        .stat {
          text-align: center;
        }

        .stat-number {
          font-size: var(--wero-font-size-2xl);
          font-weight: 700;
          color: var(--wero-black);
          margin-bottom: var(--wero-spacing-xs);
        }

        .stat-label {
          font-size: var(--wero-font-size-sm);
          color: var(--wero-gray);
        }

        .partnership-visual {
          display: flex;
          justify-content: center;
        }

        .network-visualization {
          position: relative;
          width: 300px;
          height: 300px;
        }

        .network-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .wero-node {
          width: 80px;
          height: 80px;
          background-color: var(--wero-black);
          color: var(--wero-white);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--wero-font-size-lg);
          font-weight: 700;
          box-shadow: var(--wero-shadow-lg);
        }

        .network-connections {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .connection-line {
          position: absolute;
          width: 120px;
          height: 2px;
          background: linear-gradient(90deg, var(--wero-yellow), transparent);
          transform-origin: left center;
          transform: rotate(var(--rotation));
          animation: pulse 3s ease-in-out infinite;
        }

        .bank-node {
          position: absolute;
          right: -20px;
          top: -20px;
          width: 40px;
          height: 40px;
          background-color: var(--wero-white);
          border: 2px solid var(--wero-yellow);
          border-radius: var(--wero-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--wero-font-size-base);
          box-shadow: var(--wero-shadow-md);
        }

        .cta-section {
          background-color: var(--wero-black);
          border-radius: var(--wero-radius-xl);
          padding: var(--wero-spacing-3xl);
          text-align: center;
          color: var(--wero-white);
        }

        .cta-title {
          font-size: var(--wero-font-size-2xl);
          font-weight: 600;
          margin-bottom: var(--wero-spacing-md);
        }

        .cta-description {
          font-size: var(--wero-font-size-lg);
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: var(--wero-spacing-xl);
          max-width: 500px;
          margin-left: auto;
          margin-right: auto;
        }

        .cta-buttons {
          display: flex;
          gap: var(--wero-spacing-lg);
          justify-content: center;
          flex-wrap: wrap;
        }

        @media (max-width: 1024px) {
          .partnership-content {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-2xl);
            text-align: center;
          }

          .partnership-stats {
            justify-content: center;
          }

          .indicators-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--wero-spacing-lg);
          }
        }

        @media (max-width: 768px) {
          .banks-section {
            padding: var(--wero-spacing-2xl) 0;
          }

          .section-title {
            font-size: var(--wero-font-size-3xl);
          }

          .banks-grid {
            grid-template-columns: 1fr;
            gap: var(--wero-spacing-md);
          }

          .indicators-grid {
            grid-template-columns: 1fr;
          }

          .partnership-stats {
            flex-direction: column;
            gap: var(--wero-spacing-md);
          }

          .network-visualization {
            width: 250px;
            height: 250px;
          }

          .cta-buttons {
            flex-direction: column;
            align-items: center;
          }
        }

        @media (max-width: 480px) {
          .bank-card {
            padding: var(--wero-spacing-md);
          }

          .bank-logo {
            width: 48px;
            height: 48px;
          }

          .logo-icon {
            font-size: var(--wero-font-size-lg);
          }

          .cta-section {
            padding: var(--wero-spacing-xl);
          }
        }
      `}</style>
    </section>
  );
}