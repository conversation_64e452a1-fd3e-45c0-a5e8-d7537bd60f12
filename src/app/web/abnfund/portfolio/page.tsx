import React from 'react';
import Link from 'next/link';
import {
  BuildingOffice2Icon,
  ChartBarIcon,
  CpuChipIcon,
  GlobeAltIcon,
  HeartIcon,
  ShieldCheckIcon,
  AcademicCapIcon,
  TruckIcon,
  MicrophoneIcon,
  CameraIcon
} from '@heroicons/react/24/outline';

const companies = [
  { name: '10Web', category: 'Web Development', icon: GlobeAltIcon, color: 'bg-blue-100 text-blue-600' },
  { name: 'Affineon Health', category: 'Healthcare AI', icon: HeartIcon, color: 'bg-red-100 text-red-600' },
  { name: 'Baseten', category: 'ML Infrastructure', icon: CpuChipIcon, color: 'bg-purple-100 text-purple-600' },
  { name: 'Bearing', category: 'Analytics', icon: ChartBarIcon, color: 'bg-green-100 text-green-600' },
  { name: 'Bhuma', category: 'Enterprise AI', icon: BuildingOffice2Icon, color: 'bg-indigo-100 text-indigo-600' },
  { name: '<PERSON>NT<PERSON>', category: 'Food Tech', icon: BuildingOffice2Icon, color: 'bg-orange-100 text-orange-600' },
  { name: 'CallidusAI', category: 'AI Platform', icon: CpuChipIcon, color: 'bg-cyan-100 text-cyan-600' },
  { name: 'Common Sense Privacy', category: 'Privacy Tech', icon: ShieldCheckIcon, color: 'bg-gray-100 text-gray-600' },
  { name: 'Credo AI', category: 'AI Governance', icon: ShieldCheckIcon, color: 'bg-blue-100 text-blue-600' },
  { name: 'DataHeroes', category: 'Data Analytics', icon: ChartBarIcon, color: 'bg-purple-100 text-purple-600' },
  { name: 'Esteam', category: 'Education', icon: AcademicCapIcon, color: 'bg-green-100 text-green-600' },
  { name: 'Factored', category: 'AI Services', icon: CpuChipIcon, color: 'bg-red-100 text-red-600' },
  { name: 'Feenyx', category: 'Enterprise AI', icon: BuildingOffice2Icon, color: 'bg-indigo-100 text-indigo-600' },
  { name: 'Fourth Brain', category: 'AI Education', icon: AcademicCapIcon, color: 'bg-orange-100 text-orange-600' },
  { name: 'Freight Hero', category: 'Logistics', icon: TruckIcon, color: 'bg-cyan-100 text-cyan-600' },
  { name: 'Gaia Dynamics', category: 'Environmental AI', icon: GlobeAltIcon, color: 'bg-green-100 text-green-600' },
  { name: 'Jivi AI', category: 'Healthcare AI', icon: HeartIcon, color: 'bg-red-100 text-red-600' },
  { name: 'Kira', category: 'Legal Tech', icon: BuildingOffice2Icon, color: 'bg-gray-100 text-gray-600' },
  { name: 'LandingAI', category: 'Computer Vision', icon: CameraIcon, color: 'bg-blue-100 text-blue-600' },
  { name: 'Meeno', category: 'Mental Health', icon: HeartIcon, color: 'bg-pink-100 text-pink-600' },
  { name: 'Octagon AI', category: 'AI Platform', icon: CpuChipIcon, color: 'bg-purple-100 text-purple-600' },
  { name: 'Podcastle', category: 'Audio AI', icon: MicrophoneIcon, color: 'bg-indigo-100 text-indigo-600' },
  { name: 'Profitmind', category: 'Business Intelligence', icon: ChartBarIcon, color: 'bg-green-100 text-green-600' },
  { name: 'PuppyDog', category: 'Pet Tech', icon: HeartIcon, color: 'bg-orange-100 text-orange-600' },
  { name: 'RapidFire AI', category: 'AI Platform', icon: CpuChipIcon, color: 'bg-red-100 text-red-600' },
  { name: 'RealAvatar', category: 'Avatar Tech', icon: CameraIcon, color: 'bg-cyan-100 text-cyan-600' },
  { name: 'Rypple', category: 'Fintech', icon: ChartBarIcon, color: 'bg-blue-100 text-blue-600' },
  { name: 'SkyFireAI', category: 'AI Platform', icon: CpuChipIcon, color: 'bg-purple-100 text-purple-600' },
  { name: 'SpeechLab', category: 'Speech AI', icon: MicrophoneIcon, color: 'bg-green-100 text-green-600' },
  { name: 'Sunrise AI', category: 'AI Platform', icon: CpuChipIcon, color: 'bg-orange-100 text-orange-600' },
  { name: 'ValidMind', category: 'AI Validation', icon: ShieldCheckIcon, color: 'bg-indigo-100 text-indigo-600' },
  { name: 'WhyLabs', category: 'ML Monitoring', icon: ChartBarIcon, color: 'bg-cyan-100 text-cyan-600' },
  { name: 'Woebot Health', category: 'Mental Health AI', icon: HeartIcon, color: 'bg-pink-100 text-pink-600' },
  { name: 'WoPa', category: 'Workplace AI', icon: BuildingOffice2Icon, color: 'bg-gray-100 text-gray-600' },
  { name: 'Workera', category: 'Skills Assessment', icon: AcademicCapIcon, color: 'bg-blue-100 text-blue-600' },
  { name: 'Workhelix', category: 'Workforce Analytics', icon: ChartBarIcon, color: 'bg-purple-100 text-purple-600' },
];

export default function PortfolioPage() {
  return (
    <div className="min-h-screen bg-white pb-20">
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>
        <div className="max-w-5xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-2 rounded-full mb-6">
            <BuildingOffice2Icon className="h-4 w-4" />
            Portfolio Companies
          </div>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 text-slate-900">
            Building the Future with <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">AI Innovation</span>
          </h1>
          <p className="text-xl text-slate-600 mb-8 max-w-4xl mx-auto leading-relaxed">
            We empower visionary founders to tackle complex challenges across industries—from healthcare and education
            to logistics and financial services. Our portfolio represents the next generation of AI-powered solutions.
          </p>
          <div className="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
              <div className="text-2xl font-bold text-blue-600 mb-1">36+</div>
              <div className="text-slate-600 font-medium">Portfolio Companies</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
              <div className="text-2xl font-bold text-blue-600 mb-1">$500M+</div>
              <div className="text-slate-600 font-medium">Total Funding Raised</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
              <div className="text-2xl font-bold text-blue-600 mb-1">12</div>
              <div className="text-slate-600 font-medium">Industry Verticals</div>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-slate-900 mb-4">Our Portfolio Companies</h2>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            Innovative startups leveraging AI to solve real-world problems across multiple industries
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {companies.map((company) => {
            const IconComponent = company.icon;
            return (
              <div key={company.name} className="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-xl ${company.color} group-hover:scale-110 transition-transform`}>
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-slate-900 mb-1 group-hover:text-blue-600 transition-colors">
                      {company.name}
                    </h3>
                    <p className="text-sm text-slate-500">{company.category}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Stealth Companies Section */}
      <section className="bg-gradient-to-br from-slate-50 to-blue-50 py-16">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 mb-4">Stealth Mode Companies</h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
              As a venture studio, we partner with exceptional entrepreneurs to build transformative companies.
              We provide expertise in technology, strategy, recruiting, and funding to rapidly launch world-changing businesses.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {[
              { name: 'AR Platform', category: 'Augmented Reality', icon: CameraIcon },
              { name: 'Corporate Travel', category: 'Travel Tech', icon: GlobeAltIcon },
              { name: 'Energy Industry', category: 'Clean Energy', icon: CpuChipIcon },
              { name: 'Government Procurement', category: 'GovTech', icon: BuildingOffice2Icon },
              { name: 'Health Management', category: 'HealthTech', icon: HeartIcon },
              { name: 'Enterprise AI', category: 'AI Platform', icon: CpuChipIcon },
              { name: 'Leadership Coaching', category: 'EdTech', icon: AcademicCapIcon }
            ].map((company) => {
              const IconComponent = company.icon;
              return (
                <div key={company.name} className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-xl">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-slate-900">{company.name}</h3>
                      <p className="text-sm text-slate-500">{company.category}</p>
                    </div>
                  </div>
                  <div className="bg-slate-100 rounded-lg px-3 py-2">
                    <span className="text-xs font-medium text-slate-600">In Development</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Build the Future?</h2>
          <p className="text-xl text-blue-100 mb-10 max-w-2xl mx-auto leading-relaxed">
            Join our ecosystem of innovative founders and strategic partners building the next generation of AI-powered companies.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/web/abnfund/team" className="inline-flex items-center gap-2 bg-white hover:bg-gray-100 text-slate-900 font-semibold px-8 py-4 rounded-xl transition shadow-lg hover:shadow-xl">
              <AcademicCapIcon className="h-5 w-5" />
              For Founders
            </Link>
            <Link href="/web/abnfund/advisory" className="inline-flex items-center gap-2 bg-transparent hover:bg-white hover:text-slate-900 text-white font-semibold px-8 py-4 rounded-xl border-2 border-white transition">
              <BuildingOffice2Icon className="h-5 w-5" />
              For Partners
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
} 