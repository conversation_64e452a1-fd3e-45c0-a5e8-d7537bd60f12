'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeftIcon,
  EyeIcon,
  ChatBubbleLeftIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  UsersIcon,
  BuildingOfficeIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  HeartIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

interface Business {
  id: string;
  title: string;
  description: string;
  category: string;
  industry: string;
  askingPrice: number;
  currency: string;
  revenue: {
    monthly: number;
    annual: number;
    growth: number;
  };
  metrics: any;
  financials: any;
  team: any;
  technology: any;
  assets: any;
  listing: {
    status: string;
    featured: boolean;
    views: number;
    inquiries: number;
    offers: number;
    listedDate: string;
  };
  tags: string[];
  seller: {
    id: string;
    name: string;
    verified: boolean;
  };
  documents: any[];
  images: string[];
}

const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export default function BusinessDetailPage() {
  const params = useParams();
  const businessId = params.id as string;
  const [business, setBusiness] = useState<Business | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [isFavorited, setIsFavorited] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);

  useEffect(() => {
    fetchBusiness();
  }, [businessId]);

  const fetchBusiness = async () => {
    try {
      const response = await fetch(`/api/abnfund/businesses/${businessId}`);
      if (response.ok) {
        const business = await response.json();
        setBusiness(business);
      } else {
        setBusiness(null);
      }
    } catch (error) {
      console.error('Error fetching business:', error);
      setBusiness(null);
    } finally {
      setLoading(false);
    }
  };

  const handleContact = () => {
    setShowContactForm(true);
  };

  const handleMakeOffer = () => {
    // In a real app, this would open an offer modal or redirect to offer page
    alert('Make Offer functionality would be implemented here');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading business details...</p>
        </div>
      </div>
    );
  }

  if (!business) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Business Not Found</h1>
          <p className="text-gray-600 mb-8">The business you're looking for doesn't exist or has been removed.</p>
          <Link href="/web/abnfund/marketplace" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition">
            Back to Marketplace
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-4 mb-6">
            <Link href="/web/abnfund/marketplace" className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition">
              <ArrowLeftIcon className="h-5 w-5" />
              Back to Marketplace
            </Link>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                  {business.category}
                </span>
                {business.listing.featured && (
                  <span className="bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded-full">
                    ⭐ Featured
                  </span>
                )}
                {business.seller.verified && (
                  <span className="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full flex items-center gap-1">
                    <ShieldCheckIcon className="h-4 w-4" />
                    Verified Seller
                  </span>
                )}
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{business.title}</h1>
              <p className="text-lg text-gray-600 mb-6">{business.description}</p>

              <div className="flex items-center gap-6 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <EyeIcon className="h-4 w-4" />
                  {business.listing.views} views
                </div>
                <div className="flex items-center gap-1">
                  <ChatBubbleLeftIcon className="h-4 w-4" />
                  {business.listing.inquiries} inquiries
                </div>
                <div className="flex items-center gap-1">
                  <CurrencyDollarIcon className="h-4 w-4" />
                  {business.listing.offers} offers
                </div>
                <span>Listed {formatDate(business.listing.listedDate)}</span>
              </div>
            </div>

            <div className="lg:w-80">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {formatCurrency(business.askingPrice, business.currency)}
                  </div>
                  <div className="text-sm text-gray-500">Asking Price</div>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Annual Revenue</span>
                    <span className="font-semibold">{formatCurrency(business.revenue.annual)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monthly Revenue</span>
                    <span className="font-semibold">{formatCurrency(business.revenue.monthly)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Growth Rate</span>
                    <span className="font-semibold text-green-600">+{business.revenue.growth}%</span>
                  </div>
                  {business.metrics.mrr && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">MRR</span>
                      <span className="font-semibold">{formatCurrency(business.metrics.mrr)}</span>
                    </div>
                  )}
                </div>

                  <div className="space-y-3">
                  <button
                    onClick={handleMakeOffer}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition"
                  >
                    Make an Offer
                  </button>
                  <button
                    onClick={handleContact}
                    className="w-full bg-white hover:bg-gray-50 text-blue-600 font-semibold py-3 px-4 rounded-lg border-2 border-blue-600 transition"
                  >
                    Contact Seller
                  </button>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setIsFavorited(!isFavorited)}
                      className="flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition"
                    >
                      {isFavorited ? <HeartIconSolid className="h-5 w-5 text-red-500" /> : <HeartIcon className="h-5 w-5" />}
                      Save
                    </button>
                    <button className="flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition">
                      <ShareIcon className="h-5 w-5" />
                      Share
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'financials', label: 'Financials' },
              { id: 'metrics', label: 'Metrics' },
              { id: 'team', label: 'Team' },
              { id: 'technology', label: 'Technology' },
              { id: 'documents', label: 'Documents' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:flex lg:gap-8">
          <div className="lg:flex-1">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Business Overview</h3>
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Industry</h4>
                        <p className="text-gray-600">{business.industry}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Business Model</h4>
                        <p className="text-gray-600">{business.category}</p>
                      </div>
                      {business.assets?.domain && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Domain</h4>
                          <p className="text-gray-600">{business.assets.domain}</p>
                        </div>
                      )}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Team Size</h4>
                        <p className="text-gray-600">{business.team?.size || 'Not specified'} people</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Key Highlights</h3>
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div className="flex flex-wrap gap-2">
                      {business.tags.map((tag) => (
                        <span key={tag} className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'financials' && business.financials && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Financial Information</h3>
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Gross Margin</h4>
                      <p className="text-2xl font-bold text-green-600">{business.financials.grossMargin}%</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Net Margin</h4>
                      <p className="text-2xl font-bold text-green-600">{business.financials.netMargin}%</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">EBITDA</h4>
                      <p className="text-2xl font-bold text-gray-900">{formatCurrency(business.financials.ebitda)}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Monthly Expenses</h4>
                      <p className="text-2xl font-bold text-gray-900">{formatCurrency(business.financials.expenses.monthly)}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'metrics' && business.metrics && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Key Metrics</h3>
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    {Object.entries(business.metrics).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-gray-900 mb-2 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </h4>
                        <p className="text-xl font-bold text-gray-900">
                          {typeof value === 'number' ? 
                            (key.includes('Rate') || key.includes('rate') ? `${value}%` : 
                             key.includes('ltv') || key.includes('cac') ? formatCurrency(value) : 
                             value.toLocaleString()) : 
                            value}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'team' && business.team && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Team Information</h3>
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-2">Team Size</h4>
                    <p className="text-xl font-bold text-gray-900">{business.team.size} people</p>
                  </div>
                  
                  {business.team.keyPersons && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-4">Key Team Members</h4>
                      <div className="space-y-4">
                        {business.team.keyPersons.map((person: any, index: number) => (
                          <div key={index} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                            <div>
                              <h5 className="font-medium text-gray-900">{person.name}</h5>
                              <p className="text-gray-600">{person.role}</p>
                            </div>
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                              person.staying ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {person.staying ? 'Staying' : 'Leaving'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'technology' && business.technology && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Technology Stack</h3>
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Tech Stack</h4>
                      <div className="flex flex-wrap gap-2">
                        {business.technology.stack?.map((tech: string) => (
                          <span key={tech} className="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Infrastructure</h4>
                      <p className="text-gray-600">{business.technology.infrastructure}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Scalability</h4>
                      <p className="text-gray-600">{business.technology.scalability}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'documents' && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Documents</h3>
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  {business.documents && business.documents.length > 0 ? (
                    <div className="space-y-4">
                      {business.documents.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <DocumentTextIcon className="h-8 w-8 text-gray-400" />
                            <div>
                              <h4 className="font-medium text-gray-900">{doc.name}</h4>
                              <p className="text-sm text-gray-500">
                                Uploaded {formatDate(doc.uploaded)}
                                {doc.verified && (
                                  <span className="ml-2 text-green-600">• Verified</span>
                                )}
                              </p>
                            </div>
                          </div>
                          <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded-lg transition">
                            View
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-8">No documents available</p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Contact Form Modal */}
      {showContactForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Contact Seller</h3>
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input type="email" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                <textarea rows={4} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="I'm interested in learning more about your business..."></textarea>
              </div>
              <div className="flex gap-3">
                <button type="submit" className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition">
                  Send Message
                </button>
                <button 
                  type="button" 
                  onClick={() => setShowContactForm(false)}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2 px-4 rounded-lg transition"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
