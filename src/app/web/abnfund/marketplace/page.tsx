'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  StarIcon,
  EyeIcon,
  ChatBubbleLeftIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  BuildingOfficeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface Business {
  id: string;
  title: string;
  description: string;
  category: string;
  industry: string;
  askingPrice: number;
  currency: string;
  revenue: {
    monthly: number;
    annual: number;
    growth: number;
  };
  metrics: any;
  listing: {
    status: string;
    featured: boolean;
    views: number;
    inquiries: number;
    offers: number;
  };
  tags: string[];
  seller: {
    verified: boolean;
  };
}

const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case 'saas':
      return <ComputerDesktopIcon className="h-5 w-5" />;
    case 'mobile app':
      return <DevicePhoneMobileIcon className="h-5 w-5" />;
    case 'e-commerce':
      return <BuildingOfficeIcon className="h-5 w-5" />;
    default:
      return <ChartBarIcon className="h-5 w-5" />;
  }
};

const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export default function MarketplacePage() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState('all');
  const [sortBy, setSortBy] = useState('featured');

  useEffect(() => {
    fetchBusinesses();
  }, []);

  const fetchBusinesses = async () => {
    try {
      const response = await fetch('/api/abnfund/businesses');
      const data = await response.json();
      setBusinesses(data.businesses || []);
    } catch (error) {
      console.error('Error fetching businesses:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredBusinesses = businesses.filter(business => {
    const matchesSearch = business.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         business.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || business.category === selectedCategory;
    const matchesPrice = priceRange === 'all' || 
                        (priceRange === 'under-1m' && business.askingPrice < 1000000) ||
                        (priceRange === '1m-5m' && business.askingPrice >= 1000000 && business.askingPrice < 5000000) ||
                        (priceRange === 'over-5m' && business.askingPrice >= 5000000);
    
    return matchesSearch && matchesCategory && matchesPrice;
  });

  const sortedBusinesses = [...filteredBusinesses].sort((a, b) => {
    switch (sortBy) {
      case 'price-high':
        return b.askingPrice - a.askingPrice;
      case 'price-low':
        return a.askingPrice - b.askingPrice;
      case 'revenue':
        return b.revenue.annual - a.revenue.annual;
      case 'growth':
        return b.revenue.growth - a.revenue.growth;
      default: // featured
        return (b.listing.featured ? 1 : 0) - (a.listing.featured ? 1 : 0);
    }
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading businesses...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              The largest marketplace to buy and sell profitable online businesses
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Join 500k+ entrepreneurs closing life-changing deals. Buy and sell SaaS, ecommerce, agencies, content, newsletters, mobile apps and crypto businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/web/abnfund/sell" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition">
                List Your Business
              </Link>
              <Link href="/web/abnfund/buy" className="bg-white hover:bg-gray-50 text-blue-600 font-semibold px-8 py-3 rounded-lg border-2 border-blue-600 transition">
                Browse Opportunities
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search businesses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filters */}
            <div className="flex gap-4 items-center">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Categories</option>
                <option value="SaaS">SaaS</option>
                <option value="Mobile App">Mobile App</option>
                <option value="E-commerce">E-commerce</option>
              </select>

              <select
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Prices</option>
                <option value="under-1m">Under $1M</option>
                <option value="1m-5m">$1M - $5M</option>
                <option value="over-5m">Over $5M</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="featured">Featured</option>
                <option value="price-high">Price: High to Low</option>
                <option value="price-low">Price: Low to High</option>
                <option value="revenue">Revenue</option>
                <option value="growth">Growth Rate</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Results */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {sortedBusinesses.length} businesses available
          </h2>
        </div>

        {/* Business Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {sortedBusinesses.map((business) => (
            <div key={business.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              {business.listing.featured && (
                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-sm font-semibold px-4 py-2 rounded-t-xl">
                  ⭐ Featured
                </div>
              )}
              
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(business.category)}
                    <span className="text-sm font-medium text-gray-600">{business.category}</span>
                  </div>
                  {business.seller.verified && (
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                      Verified
                    </span>
                  )}
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2">{business.title}</h3>
                <p className="text-gray-600 mb-4 line-clamp-2">{business.description}</p>

                <div className="space-y-3 mb-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Asking Price</span>
                    <span className="font-bold text-lg text-gray-900">
                      {formatCurrency(business.askingPrice, business.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Annual Revenue</span>
                    <span className="font-semibold text-gray-900">
                      {formatCurrency(business.revenue.annual, business.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Growth Rate</span>
                    <span className="font-semibold text-green-600">
                      +{business.revenue.growth}%
                    </span>
                  </div>
                </div>

                <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <EyeIcon className="h-4 w-4" />
                    {business.listing.views}
                  </div>
                  <div className="flex items-center gap-1">
                    <ChatBubbleLeftIcon className="h-4 w-4" />
                    {business.listing.inquiries}
                  </div>
                  <div className="flex items-center gap-1">
                    <CurrencyDollarIcon className="h-4 w-4" />
                    {business.listing.offers}
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {business.tags.slice(0, 3).map((tag) => (
                    <span key={tag} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>

                <Link 
                  href={`/web/abnfund/marketplace/${business.id}`}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition text-center block"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>

        {sortedBusinesses.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No businesses found matching your criteria.</p>
            <p className="text-gray-400 mt-2">Try adjusting your filters or search terms.</p>
          </div>
        )}
      </section>
    </div>
  );
}
