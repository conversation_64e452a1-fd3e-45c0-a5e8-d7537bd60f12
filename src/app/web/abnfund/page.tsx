'use client';

import React from 'react';
import Link from 'next/link';
import {
  ArrowTrendingUpIcon,
  BuildingOffice2Icon,
  CurrencyDollarIcon,
  ChartBarIcon,
  UsersIcon,
  ShieldCheckIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { useThemeStyles } from '@/themes/ThemeProvider';

export default function AIFundPage() {
  const { getButtonClass, getCardClass, getHeadingClass, isPaperTheme } = useThemeStyles();

  return (
    <div className={`min-h-screen transition-all duration-300 ${isPaperTheme ? 'bg-paper-bg font-paper' : 'bg-white font-sans'}`}>
      
      {/* Hero Section */}
      <section className={`relative pt-20 pb-20 px-4 sm:px-6 lg:px-8 transition-all duration-300 ${isPaperTheme ? 'bg-paper-white' : 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50'}`}>
        {!isPaperTheme && <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>}
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div className={`inline-flex items-center gap-2 text-sm font-semibold px-4 py-2 rounded-full mb-6 transition-all duration-300 ${isPaperTheme ? 'bg-paper-black text-paper-white border-2 border-paper-black font-paper uppercase tracking-wide' : 'bg-blue-100 text-blue-800'}`}>
              <ArrowTrendingUpIcon className="h-4 w-4" />
              Leading M&A Platform
            </div>
            <h1 className={`text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight transition-all duration-300 ${isPaperTheme ? 'font-paper' : ''}`}>
              <span className={`block ${isPaperTheme ? 'text-paper-black' : 'text-slate-900'}`}>The Premier Platform for</span>
              <span className={`block ${isPaperTheme ? 'text-paper-black' : 'bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent'}`}>Business Acquisitions</span>
            </h1>
            <p className={`text-xl mb-10 max-w-3xl mx-auto leading-relaxed transition-all duration-300 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-slate-600'}`}>
              Connect with serious buyers and sellers in the world's most trusted marketplace for profitable online businesses.
              From SaaS to e-commerce, find your next strategic acquisition or exit opportunity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Link href="/web/abnfund/marketplace" className={getButtonClass('primary')}>
                <BuildingOffice2Icon className="h-5 w-5" />
                Browse Businesses
                <ArrowRightIcon className="h-4 w-4" />
              </Link>
              <Link href="/web/abnfund/sell" className={getButtonClass('outline')}>
                <CurrencyDollarIcon className="h-5 w-5" />
                List Your Business
              </Link>
            </div>
          </div>

          {/* Service Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <Link href="/web/abnfund/team" className={`group transition-all duration-300 ${getCardClass()} ${isPaperTheme ? 'hover:border-paper-black' : 'hover:border-blue-300'}`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`flex items-center justify-center w-12 h-12 rounded-xl transition-colors ${isPaperTheme ? 'bg-paper-light-gray border-2 border-paper-black group-hover:bg-paper-black' : 'bg-blue-100 group-hover:bg-blue-600'}`}>
                  <UsersIcon className={`h-6 w-6 transition-colors ${isPaperTheme ? 'text-paper-black group-hover:text-paper-white' : 'text-blue-600 group-hover:text-white'}`} />
                </div>
                <ArrowRightIcon className={`h-5 w-5 transition-colors ${isPaperTheme ? 'text-paper-gray group-hover:text-paper-black' : 'text-slate-400 group-hover:text-blue-600'}`} />
              </div>
              <h3 className={getHeadingClass(3)}>Venture Studio</h3>
              <p className={`leading-relaxed ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-slate-600'}`}>From idea to funded startup in 3 months with expert guidance</p>
            </Link>

            <Link href="/web/abnfund/marketplace" className={`group transition-all duration-300 ${getCardClass()} ${isPaperTheme ? 'hover:border-paper-black' : 'hover:border-purple-300'}`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`flex items-center justify-center w-12 h-12 rounded-xl transition-colors ${isPaperTheme ? 'bg-paper-light-gray border-2 border-paper-black group-hover:bg-paper-black' : 'bg-purple-100 group-hover:bg-purple-600'}`}>
                  <BuildingOffice2Icon className={`h-6 w-6 transition-colors ${isPaperTheme ? 'text-paper-black group-hover:text-paper-white' : 'text-purple-600 group-hover:text-white'}`} />
                </div>
                <ArrowRightIcon className={`h-5 w-5 transition-colors ${isPaperTheme ? 'text-paper-gray group-hover:text-paper-black' : 'text-slate-400 group-hover:text-purple-600'}`} />
              </div>
              <h3 className={getHeadingClass(3)}>M&A Marketplace</h3>
              <p className={`leading-relaxed ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-slate-600'}`}>Buy and sell profitable online businesses with confidence</p>
            </Link>

            <Link href="/web/abnfund/sell" className={`group transition-all duration-300 ${getCardClass()} ${isPaperTheme ? 'hover:border-paper-black' : 'hover:border-green-300'}`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`flex items-center justify-center w-12 h-12 rounded-xl transition-colors ${isPaperTheme ? 'bg-paper-light-gray border-2 border-paper-black group-hover:bg-paper-black' : 'bg-green-100 group-hover:bg-green-600'}`}>
                  <CurrencyDollarIcon className={`h-6 w-6 transition-colors ${isPaperTheme ? 'text-paper-black group-hover:text-paper-white' : 'text-green-600 group-hover:text-white'}`} />
                </div>
                <ArrowRightIcon className={`h-5 w-5 transition-colors ${isPaperTheme ? 'text-paper-gray group-hover:text-paper-black' : 'text-slate-400 group-hover:text-green-600'}`} />
              </div>
              <h3 className={getHeadingClass(3)}>Sell Business</h3>
              <p className={`leading-relaxed ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-slate-600'}`}>List your business for maximum value with expert support</p>
            </Link>

            <Link href="/web/abnfund/advisory" className={`group transition-all duration-300 ${getCardClass()} ${isPaperTheme ? 'hover:border-paper-black' : 'hover:border-orange-300'}`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`flex items-center justify-center w-12 h-12 rounded-xl transition-colors ${isPaperTheme ? 'bg-paper-light-gray border-2 border-paper-black group-hover:bg-paper-black' : 'bg-orange-100 group-hover:bg-orange-600'}`}>
                  <ShieldCheckIcon className={`h-6 w-6 transition-colors ${isPaperTheme ? 'text-paper-black group-hover:text-paper-white' : 'text-orange-600 group-hover:text-white'}`} />
                </div>
                <ArrowRightIcon className={`h-5 w-5 transition-colors ${isPaperTheme ? 'text-paper-gray group-hover:text-paper-black' : 'text-slate-400 group-hover:text-orange-600'}`} />
              </div>
              <h3 className={getHeadingClass(3)}>M&A Advisory</h3>
              <p className={`leading-relaxed ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-slate-600'}`}>Expert guidance for complex acquisitions and exits</p>
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className={getCardClass()}>
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <div className={`text-3xl font-bold mb-2 ${isPaperTheme ? 'text-paper-black font-paper' : 'text-blue-600'}`}>$500M+</div>
                <div className={`font-medium ${isPaperTheme ? 'text-paper-gray font-paper uppercase tracking-wide' : 'text-slate-600'}`}>Transaction Volume</div>
              </div>
              <div>
                <div className={`text-3xl font-bold mb-2 ${isPaperTheme ? 'text-paper-black font-paper' : 'text-blue-600'}`}>2,000+</div>
                <div className={`font-medium ${isPaperTheme ? 'text-paper-gray font-paper uppercase tracking-wide' : 'text-slate-600'}`}>Businesses Sold</div>
              </div>
              <div>
                <div className={`text-3xl font-bold mb-2 ${isPaperTheme ? 'text-paper-black font-paper' : 'text-blue-600'}`}>500k+</div>
                <div className={`font-medium ${isPaperTheme ? 'text-paper-gray font-paper uppercase tracking-wide' : 'text-slate-600'}`}>Trusted Users</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-slate-900 mb-4">
              How We <span className="text-blue-600">Accelerate Success</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Our proven methodology helps entrepreneurs and investors achieve their goals faster and more efficiently
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="relative bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl border border-blue-100">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-xl mb-6">
                <CheckCircleIcon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4">Validate</h3>
              <p className="text-slate-600 leading-relaxed">
                We surround you with a tiger team of business leaders and tech experts. Together, we rapidly de-risk your idea with proven methodologies.
              </p>
              <div className="absolute top-4 right-4 text-blue-200 font-bold text-6xl">01</div>
            </div>
            <div className="relative bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-2xl border border-purple-100">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-600 rounded-xl mb-6">
                <ChartBarIcon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4">Build</h3>
              <p className="text-slate-600 leading-relaxed">
                With co-founder matching, pre-seed funding, and a hand-picked core team, we help launch your vision with momentum and strategic direction.
              </p>
              <div className="absolute top-4 right-4 text-purple-200 font-bold text-6xl">02</div>
            </div>
            <div className="relative bg-gradient-to-br from-green-50 to-emerald-50 p-8 rounded-2xl border border-green-100">
              <div className="flex items-center justify-center w-12 h-12 bg-green-600 rounded-xl mb-6">
                <ArrowTrendingUpIcon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4">Scale</h3>
              <p className="text-slate-600 leading-relaxed">
                Go fast. Grow fast. From fundraising to talent acquisition, we provide everything you need to scale your business to new heights.
              </p>
              <div className="absolute top-4 right-4 text-green-200 font-bold text-6xl">03</div>
            </div>
          </div>
        </div>
      </section>

      {/* M&A Marketplace Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-slate-900 mb-4">
              The Premier Marketplace to <span className="text-blue-600">Buy and Sell</span> Profitable Businesses
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              Join 500k+ entrepreneurs closing life-changing deals. Buy and sell SaaS, e-commerce, agencies, content platforms,
              newsletters, mobile apps and digital businesses with confidence and expert support.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-start">
            <div className="bg-white rounded-2xl p-8 shadow-sm border border-slate-200">
              <div className="flex items-center gap-3 mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-xl">
                  <BuildingOffice2Icon className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900">Discover Your Next Acquisition</h3>
              </div>
              <p className="text-slate-600 mb-6 leading-relaxed">
                Browse thousands of vetted online businesses for sale or enter your criteria to find perfect matches.
                Analyze returns with live metrics and make offers with confidence.
              </p>
              <ul className="space-y-4 mb-8">
                <li className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600">Evaluate comprehensive web, customer, and financial metrics</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600">Build and send LOIs and APAs in minutes with templates</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600">Access acquisition financing and expert due diligence support</span>
                </li>
              </ul>
              <Link href="/web/abnfund/buy" className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-xl transition shadow-lg hover:shadow-xl">
                Browse Businesses
                <ArrowRightIcon className="h-4 w-4" />
              </Link>
            </div>
            <div className="bg-white rounded-2xl p-8 shadow-sm border border-slate-200">
              <div className="flex items-center gap-3 mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-xl">
                  <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900">Sell for Maximum Value</h3>
              </div>
              <p className="text-slate-600 mb-6 leading-relaxed">
                Sell your online business by connecting with 500k+ qualified buyers. Get expert help to market and close
                deals that lead to acquisition in as little as 90 days.
              </p>
              <ul className="space-y-4 mb-8">
                <li className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600">Expert assistance to create compelling business listings</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600">Connect with qualified buyers with verified funds and intent</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600">Secure transactions with professional escrow services</span>
                </li>
              </ul>
              <Link href="/web/abnfund/sell" className="inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white font-semibold px-6 py-3 rounded-xl transition shadow-lg hover:shadow-xl">
                List Your Business
                <ArrowRightIcon className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Advisory CTA Section */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-white/[0.05] -z-10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm text-white text-sm font-semibold px-4 py-2 rounded-full mb-6">
            <ShieldCheckIcon className="h-4 w-4" />
            Expert M&A Advisory
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Need Expert M&A Guidance?
          </h2>
          <p className="text-xl text-blue-100 mb-10 max-w-3xl mx-auto leading-relaxed">
            Work with our experienced M&A advisors who have closed over $1B in deals.
            Get personalized guidance throughout your acquisition or exit journey with proven strategies.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/web/abnfund/advisory" className="inline-flex items-center gap-2 bg-white hover:bg-gray-100 text-slate-900 font-semibold px-8 py-4 rounded-xl transition shadow-lg hover:shadow-xl">
              <ShieldCheckIcon className="h-5 w-5" />
              Get Expert Help
            </Link>
            <Link href="/web/abnfund/advisory#services" className="inline-flex items-center gap-2 bg-transparent hover:bg-white hover:text-slate-900 text-white font-semibold px-8 py-4 rounded-xl border-2 border-white transition">
              View Services
              <ArrowRightIcon className="h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}