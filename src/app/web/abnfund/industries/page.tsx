import React from 'react';
import Link from 'next/link';
import { BuildingOffice2Icon, BriefcaseIcon, HeartIcon, AcademicCapIcon, GlobeAltIcon, ShieldCheckIcon, ChartBarIcon, UserGroupIcon, RocketLaunchIcon, BanknotesIcon, TruckIcon, CpuChipIcon, LightBulbIcon, ShoppingBagIcon } from '@heroicons/react/24/outline';

const industries = [
  { name: 'Healthcare', icon: HeartIcon },
  { name: 'Education', icon: AcademicCapIcon },
  { name: 'Finance', icon: BanknotesIcon },
  { name: 'Enterprise Software', icon: CpuChipIcon },
  { name: 'Retail', icon: ShoppingBagIcon },
  { name: 'Logistics & Shipping', icon: TruckIcon },
  { name: 'AI Governance', icon: ShieldCheckIcon },
  { name: 'Energy', icon: LightBulbIcon },
  { name: 'Travel', icon: GlobeAltIcon },
  { name: 'Government', icon: BuildingOffice2Icon },
  { name: 'Productivity', icon: RocketLaunchIcon },
  { name: 'Human Resources', icon: UserGroupIcon },
  { name: 'Risk Management', icon: ChartBarIcon },
];

export default function IndustriesPage() {
  return (
    <div className="min-h-screen bg-white pb-20">
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>
        <div className="max-w-4xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-2 rounded-full mb-6">
            <CpuChipIcon className="h-4 w-4" />
            Industry Focus
          </div>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 text-slate-900">
            <span className="block">Industries Ripe for</span>
            <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">AI Transformation</span>
          </h1>
          <p className="text-xl text-slate-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            At AI Fund, we identify and invest in industries ready for revolutionary AI-driven innovation.
            We partner with visionary founders to build transformative solutions across these key sectors.
          </p>
        </div>
      </section>

      {/* Industries Grid */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-slate-900 mb-4">Target Industries</h2>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            Strategic sectors where AI innovation can create the most significant impact and value
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {industries.map(({ name, icon: Icon }) => (
            <div key={name} className="group bg-white rounded-2xl p-8 shadow-sm border border-slate-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
              <div className="flex flex-col items-center text-center">
                <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl mb-4 group-hover:scale-110 transition-transform">
                  <Icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-slate-900 group-hover:text-blue-600 transition-colors">{name}</h3>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Transform Your Industry?</h2>
          <p className="text-xl text-blue-100 mb-10 max-w-2xl mx-auto leading-relaxed">
            If you're a founder or partner passionate about revolutionizing one of these sectors with AI,
            let's collaborate to build the future together.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/web/abnfund/team" className="inline-flex items-center gap-2 bg-white hover:bg-gray-100 text-slate-900 font-semibold px-8 py-4 rounded-xl transition shadow-lg hover:shadow-xl">
              <AcademicCapIcon className="h-5 w-5" />
              For Founders
            </Link>
            <Link href="/web/abnfund/advisory" className="inline-flex items-center gap-2 bg-transparent hover:bg-white hover:text-slate-900 text-white font-semibold px-8 py-4 rounded-xl border-2 border-white transition">
              <BuildingOffice2Icon className="h-5 w-5" />
              For Partners
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
} 