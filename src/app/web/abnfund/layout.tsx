import type { <PERSON>ada<PERSON> } from 'next';
import React from 'react';
import { ThemeProvider } from '@/themes/ThemeProvider';
import AIFundLayoutClient from './layout-client';

export const metadata: Metadata = {
  title: 'AI Fund - Co-founding companies from idea to Impact',
  description: 'AI Fund is a venture studio. We collaborate with entrepreneurs, venture funds, and corporate partners to create great companies that move humanity forward.',
};

export default function AIFundLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider defaultTheme="current">
      <AIFundLayoutClient>{children}</AIFundLayoutClient>
    </ThemeProvider>
  );
}