'use client';

import React from 'react';
import Link from 'next/link';
import {
  EnvelopeIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  BuildingOffice2Icon
} from '@heroicons/react/24/outline';
import { ThemeToggleButton, useThemeStyles } from '@/themes/ThemeProvider';

export default function AIFundLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isPaperTheme } = useThemeStyles();

  return (
    <div className={`min-h-screen transition-all duration-300 ${isPaperTheme ? 'bg-paper-bg font-paper' : 'bg-white font-sans'}`}>
      {/* Navigation */}
      <nav className={`fixed w-full z-50 shadow-sm transition-all duration-300 ${isPaperTheme ? 'bg-paper-white border-b-2 border-paper-black' : 'bg-white/95 backdrop-blur-sm border-b border-slate-200'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-20 items-center">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center justify-center w-10 h-10 rounded-xl shadow-lg transition-all duration-300 ${isPaperTheme ? 'bg-paper-black border-2 border-paper-black' : 'bg-gradient-to-br from-blue-600 to-purple-600'}`}>
                <ArrowTrendingUpIcon className={`h-6 w-6 ${isPaperTheme ? 'text-paper-white' : 'text-white'}`} />
              </div>
              <span className={`text-2xl font-bold tracking-tight transition-all duration-300 ${isPaperTheme ? 'text-paper-black font-paper uppercase' : 'bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent'}`}>AIFUND</span>
            </div>
            <div className={`hidden md:flex space-x-8 font-medium text-sm transition-all duration-300 ${isPaperTheme ? 'font-paper' : ''}`}>
              <Link href="/web/abnfund/team" className={`transition-colors px-3 py-2 rounded-lg ${isPaperTheme ? 'text-paper-black hover:text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-slate-700 hover:text-blue-600 hover:bg-blue-50'}`}>TEAM</Link>
              <Link href="/web/abnfund/portfolio" className={`transition-colors px-3 py-2 rounded-lg ${isPaperTheme ? 'text-paper-black hover:text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-slate-700 hover:text-blue-600 hover:bg-blue-50'}`}>PORTFOLIO</Link>
              <Link href="/web/abnfund/marketplace" className={`transition-colors px-3 py-2 rounded-lg ${isPaperTheme ? 'text-paper-black hover:text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-slate-700 hover:text-blue-600 hover:bg-blue-50'}`}>MARKETPLACE</Link>
              <Link href="/web/abnfund/sell" className={`transition-colors px-3 py-2 rounded-lg ${isPaperTheme ? 'text-paper-black hover:text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-slate-700 hover:text-blue-600 hover:bg-blue-50'}`}>SELL</Link>
              <Link href="/web/abnfund/buy" className={`transition-colors px-3 py-2 rounded-lg ${isPaperTheme ? 'text-paper-black hover:text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-slate-700 hover:text-blue-600 hover:bg-blue-50'}`}>BUY</Link>
              <Link href="/web/abnfund/advisory" className={`transition-colors px-3 py-2 rounded-lg ${isPaperTheme ? 'text-paper-black hover:text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-slate-700 hover:text-blue-600 hover:bg-blue-50'}`}>ADVISORY</Link>
              <Link href="/web/abnfund/advisory" className={`transition-colors px-3 py-2 rounded-lg ${isPaperTheme ? 'text-paper-black hover:text-paper-black hover:bg-paper-light-gray border-2 border-transparent hover:border-paper-black font-bold uppercase tracking-wide' : 'text-slate-700 hover:text-blue-600 hover:bg-blue-50'}`}>CONTACT</Link>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-xl hover:bg-slate-100 transition-colors">
                <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-slate-600"><circle cx="11" cy="11" r="8" /><path d="M21 21l-2-2" /></svg>
              </button>
              <ThemeToggleButton className="mr-2" />
              <Link href="/web/abnfund/team" className={`font-semibold px-6 py-2.5 rounded-xl shadow-lg hover:shadow-xl transition-all text-sm ${isPaperTheme ? 'bg-paper-black text-paper-white border-2 border-paper-black font-paper uppercase tracking-wide hover:shadow-paper-btn-hover hover:-translate-y-0.5' : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'}`}>BUILD WITH US</Link>
            </div>
          </div>
        </div>
      </nav>

      <main className="pt-36">{children}</main>

      {/* Footer */}
      <footer className={`border-t pt-16 pb-8 mt-20 transition-all duration-300 ${isPaperTheme ? 'bg-paper-white border-paper-black border-t-2' : 'bg-gradient-to-br from-slate-50 to-blue-50 border-slate-200'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-5 gap-8 items-start">
            {/* Logo and description */}
            <div className="col-span-2 flex flex-col items-start">
              <div className="flex items-center mb-4">
                <div className={`flex items-center justify-center w-10 h-10 rounded-xl mr-3 shadow-lg transition-all duration-300 ${isPaperTheme ? 'bg-paper-black border-2 border-paper-black' : 'bg-gradient-to-br from-blue-600 to-purple-600'}`}>
                  <ArrowTrendingUpIcon className={`h-6 w-6 ${isPaperTheme ? 'text-paper-white' : 'text-white'}`} />
                </div>
                <span className={`text-2xl font-bold tracking-tight transition-all duration-300 ${isPaperTheme ? 'text-paper-black font-paper uppercase' : 'bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent'}`}>AIFUND</span>
              </div>
              <p className={`mb-6 leading-relaxed max-w-md transition-all duration-300 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-slate-600'}`}>
                Building great companies that move humanity forward through AI innovation and strategic partnerships.
              </p>
              <div className="flex space-x-3">
                <a href="https://www.linkedin.com/company/aifund/" target="_blank" rel="noopener noreferrer" className={`inline-flex items-center justify-center w-10 h-10 rounded-xl shadow-sm hover:shadow-md transition-all ${isPaperTheme ? 'bg-paper-white hover:bg-paper-light-gray border-2 border-paper-black' : 'bg-white hover:bg-blue-50 border border-slate-200'}`}>
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24" className={isPaperTheme ? 'text-paper-black' : 'text-slate-700'}><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-10h3v10zm-1.5-11.268c-.966 0-1.75-.784-1.75-1.75s.784-1.75 1.75-1.75 1.75.784 1.75 1.75-.784 1.75-1.75 1.75zm15.5 11.268h-3v-5.604c0-1.337-.025-3.063-1.868-3.063-1.868 0-2.154 1.459-2.154 2.967v5.7h-3v-10h2.881v1.367h.041c.401-.761 1.379-1.563 2.838-1.563 3.036 0 3.6 2.001 3.6 4.601v5.595z"/></svg>
                </a>
                <a href="mailto:<EMAIL>" className={`inline-flex items-center justify-center w-10 h-10 rounded-xl shadow-sm hover:shadow-md transition-all ${isPaperTheme ? 'bg-paper-white hover:bg-paper-light-gray border-2 border-paper-black' : 'bg-white hover:bg-blue-50 border border-slate-200'}`}>
                  <EnvelopeIcon className={`h-5 w-5 ${isPaperTheme ? 'text-paper-black' : 'text-slate-700'}`} />
                </a>
              </div>
            </div>
            {/* Company */}
            <div>
              <h4 className={`text-sm font-bold mb-4 uppercase tracking-wider transition-all duration-300 ${isPaperTheme ? 'text-paper-black font-paper' : 'text-slate-900'}`}>Company</h4>
              <ul className="space-y-3">
                <li><Link href="/web/abnfund/team" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Team</Link></li>
                <li><Link href="/web/abnfund/portfolio" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Portfolio</Link></li>
                <li><Link href="/web/abnfund/advisory" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Contact</Link></li>
              </ul>
            </div>
            {/* M&A Portal */}
            <div>
              <h4 className={`text-sm font-bold mb-4 uppercase tracking-wider transition-all duration-300 ${isPaperTheme ? 'text-paper-black font-paper' : 'text-slate-900'}`}>M&A Portal</h4>
              <ul className="space-y-3">
                <li><Link href="/web/abnfund/marketplace" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Browse Businesses</Link></li>
                <li><Link href="/web/abnfund/sell" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Sell Your Business</Link></li>
                <li><Link href="/web/abnfund/buy" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Buy a Business</Link></li>
                <li><Link href="/web/abnfund/advisory" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Advisory Services</Link></li>
              </ul>
            </div>
            {/* Resources */}
            <div>
              <h4 className={`text-sm font-bold mb-4 uppercase tracking-wider transition-all duration-300 ${isPaperTheme ? 'text-paper-black font-paper' : 'text-slate-900'}`}>Resources</h4>
              <ul className="space-y-3">
                <li><Link href="/web/abnfund/marketplace" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Market Insights</Link></li>
                <li><Link href="/web/abnfund/industries" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Industries</Link></li>
                <li><Link href="/web/abnfund/team" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-600 hover:text-blue-600'}`}>Careers</Link></li>
              </ul>
            </div>
          </div>
          <div className={`mt-12 pt-8 transition-all duration-300 ${isPaperTheme ? 'border-t-2 border-paper-black' : 'border-t border-slate-200'}`}>
            <div className="flex flex-col md:flex-row md:justify-between items-center">
              <div className={`text-sm transition-all duration-300 ${isPaperTheme ? 'text-paper-gray font-paper' : 'text-slate-500'}`}>
                © {new Date().getFullYear()} AI Fund. All rights reserved.
              </div>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <Link href="/web/abnfund/advisory" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-500 hover:text-blue-600'}`}>Privacy Policy</Link>
                <Link href="/web/abnfund/advisory" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-500 hover:text-blue-600'}`}>Terms & Conditions</Link>
                <Link href="/web/abnfund/advisory" className={`text-sm transition-colors ${isPaperTheme ? 'text-paper-gray hover:text-paper-black font-paper' : 'text-slate-500 hover:text-blue-600'}`}>Cookie Policy</Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
