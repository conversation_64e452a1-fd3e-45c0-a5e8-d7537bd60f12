'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { 
  CheckCircleIcon,
  StarIcon,
  UserGroupIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  ClockIcon,
  PhoneIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const advisors = [
  {
    id: 'advisor-1',
    name: '<PERSON>',
    title: 'Senior M&A Advisor',
    experience: '20+ years',
    specialties: ['SaaS Valuations', 'Due Diligence', 'Deal Structuring'],
    dealsCompleted: 150,
    totalDealValue: 500000000,
    rating: 4.95,
    reviews: 45,
    hourlyRate: 350,
    image: '/images/advisors/michael.jpg',
    bio: '<PERSON> has 20+ years of experience in M&A advisory, specializing in tech acquisitions and valuations. He has helped close over $500M in deals.',
    certifications: ['CFA', 'CPA', 'M&A Institute']
  },
  {
    id: 'advisor-2',
    name: '<PERSON>',
    title: 'M&A Director',
    experience: '15+ years',
    specialties: ['E-commerce', 'Mobile Apps', 'Strategic Planning'],
    dealsCompleted: 120,
    totalDealValue: 350000000,
    rating: 4.9,
    reviews: 38,
    hourlyRate: 300,
    image: '/images/advisors/sarah.jpg',
    bio: 'Sarah specializes in e-commerce and mobile app acquisitions, with a track record of successful exits and strategic planning.',
    certifications: ['MBA', 'CFA']
  },
  {
    id: 'advisor-3',
    name: 'David Kim',
    title: 'Valuation Expert',
    experience: '12+ years',
    specialties: ['Business Valuation', 'Financial Analysis', 'Market Research'],
    dealsCompleted: 90,
    totalDealValue: 200000000,
    rating: 4.85,
    reviews: 32,
    hourlyRate: 275,
    image: '/images/advisors/david.jpg',
    bio: 'David is a certified business appraiser with expertise in valuing technology companies and conducting comprehensive market analysis.',
    certifications: ['ASA', 'CFA', 'ABV']
  }
];

const services = [
  {
    title: 'Business Valuation',
    description: 'Get an accurate valuation of your business based on market data and financial analysis',
    features: ['Comprehensive financial analysis', 'Market comparables', 'DCF modeling', 'Detailed valuation report'],
    price: 'Starting at $2,500',
    duration: '1-2 weeks',
    icon: <ChartBarIcon className="h-8 w-8" />
  },
  {
    title: 'Sell-Side Advisory',
    description: 'Complete support throughout the entire sale process from preparation to closing',
    features: ['Sale preparation', 'Marketing strategy', 'Buyer qualification', 'Negotiation support', 'Deal management'],
    price: '5% success fee',
    duration: '3-6 months',
    icon: <DocumentTextIcon className="h-8 w-8" />
  },
  {
    title: 'Buy-Side Advisory',
    description: 'Expert guidance for acquiring the right business at the right price',
    features: ['Target identification', 'Due diligence', 'Valuation analysis', 'Negotiation support', 'Integration planning'],
    price: '3% success fee',
    duration: '2-4 months',
    icon: <UserGroupIcon className="h-8 w-8" />
  },
  {
    title: 'Due Diligence',
    description: 'Comprehensive analysis to identify risks and opportunities before acquisition',
    features: ['Financial due diligence', 'Legal review', 'Technical assessment', 'Market analysis', 'Risk assessment'],
    price: 'Starting at $5,000',
    duration: '2-4 weeks',
    icon: <ShieldCheckIcon className="h-8 w-8" />
  }
];

const testimonials = [
  {
    name: 'John Smith',
    company: 'TechCorp Solutions',
    role: 'CEO',
    content: 'Michael helped us sell our SaaS business for 20% above our initial asking price. His expertise in valuations and negotiation was invaluable.',
    rating: 5,
    dealValue: '$2.5M'
  },
  {
    name: 'Maria Garcia',
    company: 'Fashion Marketplace',
    role: 'Founder',
    content: 'Sarah guided us through our first acquisition. Her knowledge of the e-commerce space and attention to detail made the process smooth.',
    rating: 5,
    dealValue: '$750K'
  },
  {
    name: 'Robert Johnson',
    company: 'Growth Capital Partners',
    role: 'Investment Director',
    content: 'David\'s valuation analysis helped us make informed investment decisions. His reports are thorough and well-researched.',
    rating: 5,
    dealValue: '$1.8M'
  }
];

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export default function AdvisoryPage() {
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [showContactForm, setShowContactForm] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-6">
              Expert M&A Advisory Services
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Work with experienced M&A professionals to maximize your exit value or find the perfect acquisition. 
              Our advisors have closed over $1B in deals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => setShowContactForm(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition"
              >
                Get Expert Help
              </button>
              <Link href="#services" className="bg-white hover:bg-gray-50 text-blue-600 font-semibold px-8 py-3 rounded-lg border-2 border-blue-600 transition">
                View Services
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">$1B+</div>
              <div className="text-blue-100">Total Deal Value</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">360+</div>
              <div className="text-blue-100">Deals Completed</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">95%</div>
              <div className="text-blue-100">Success Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">4.9</div>
              <div className="text-blue-100">Average Rating</div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Services</h2>
            <p className="text-lg text-gray-600">Comprehensive M&A advisory services tailored to your needs</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-md transition">
                <div className="flex items-center gap-4 mb-6">
                  <div className="text-blue-600">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900">{service.title}</h3>
                </div>
                
                <p className="text-gray-600 mb-6">{service.description}</p>
                
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
                
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <div className="text-lg font-bold text-gray-900">{service.price}</div>
                    <div className="text-sm text-gray-500">{service.duration}</div>
                  </div>
                </div>
                
                <button 
                  onClick={() => setShowContactForm(true)}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition"
                >
                  Get Started
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Advisors Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Our Advisors</h2>
            <p className="text-lg text-gray-600">Experienced professionals with proven track records</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {advisors.map((advisor) => (
              <div key={advisor.id} className="bg-gray-50 rounded-xl p-8 text-center">
                <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-6"></div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">{advisor.name}</h3>
                <p className="text-blue-600 font-medium mb-4">{advisor.title}</p>
                
                <div className="flex items-center justify-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
                  ))}
                  <span className="text-sm text-gray-600 ml-2">{advisor.rating} ({advisor.reviews} reviews)</span>
                </div>
                
                <div className="space-y-2 mb-6 text-sm text-gray-600">
                  <div>{advisor.experience} experience</div>
                  <div>{advisor.dealsCompleted} deals completed</div>
                  <div>{formatCurrency(advisor.totalDealValue)} total deal value</div>
                  <div>{formatCurrency(advisor.hourlyRate)}/hour</div>
                </div>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {advisor.specialties.map((specialty) => (
                    <span key={specialty} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      {specialty}
                    </span>
                  ))}
                </div>
                
                <button 
                  onClick={() => setShowContactForm(true)}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition"
                >
                  Contact {advisor.name.split(' ')[0]}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Client Success Stories</h2>
            <p className="text-lg text-gray-600">See what our clients say about working with us</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
                  ))}
                </div>
                
                <p className="text-gray-600 mb-6">"{testimonial.content}"</p>
                
                <div className="border-t border-gray-200 pt-4">
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">{testimonial.role}, {testimonial.company}</div>
                  <div className="text-sm text-blue-600 font-medium mt-1">Deal Value: {testimonial.dealValue}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Process</h2>
            <p className="text-lg text-gray-600">How we help you achieve your M&A goals</p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              { step: '1', title: 'Initial Consultation', description: 'Free 30-minute consultation to understand your goals and requirements' },
              { step: '2', title: 'Strategy Development', description: 'Create a customized M&A strategy and timeline for your specific situation' },
              { step: '3', title: 'Execution', description: 'Execute the strategy with ongoing support and expert guidance throughout' },
              { step: '4', title: 'Closing', description: 'Manage the closing process and ensure a smooth transition for all parties' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-xl text-blue-100 mb-8">
            Schedule a free consultation with one of our M&A experts today
          </p>
          <button 
            onClick={() => setShowContactForm(true)}
            className="bg-white hover:bg-gray-100 text-blue-600 font-semibold px-8 py-3 rounded-lg transition"
          >
            Schedule Free Consultation
          </button>
        </div>
      </section>

      {/* Contact Form Modal */}
      {showContactForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Contact Our Advisors</h3>
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input type="email" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <input type="tel" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Service Needed</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option>Business Valuation</option>
                  <option>Sell-Side Advisory</option>
                  <option>Buy-Side Advisory</option>
                  <option>Due Diligence</option>
                  <option>General Consultation</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                <textarea rows={4} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Tell us about your M&A needs..."></textarea>
              </div>
              <div className="flex gap-3">
                <button type="submit" className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition">
                  Send Message
                </button>
                <button 
                  type="button" 
                  onClick={() => setShowContactForm(false)}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-3 px-4 rounded-lg transition"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
