'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  PlusIcon,
  EyeIcon,
  ChatBubbleLeftIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

interface Business {
  id: string;
  title: string;
  description: string;
  category: string;
  askingPrice: number;
  currency: string;
  revenue: {
    annual: number;
    growth: number;
  };
  listing: {
    status: string;
    featured: boolean;
    views: number;
    inquiries: number;
    offers: number;
    listedDate: string;
  };
  tags: string[];
}

const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active':
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    case 'pending':
      return <ClockIcon className="h-5 w-5 text-yellow-500" />;
    case 'sold':
      return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
    case 'inactive':
      return <XCircleIcon className="h-5 w-5 text-gray-500" />;
    default:
      return <ClockIcon className="h-5 w-5 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'sold':
      return 'bg-blue-100 text-blue-800';
    case 'inactive':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function SellPage() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalListings: 0,
    totalViews: 0,
    totalInquiries: 0,
    totalOffers: 0,
    avgPrice: 0
  });

  useEffect(() => {
    fetchSellerData();
  }, []);

  const fetchSellerData = async () => {
    try {
      // In a real app, this would filter by current user's businesses
      const response = await fetch('/api/abnfund/businesses');
      const data = await response.json();
      const userBusinesses = data.businesses || [];
      setBusinesses(userBusinesses);
      
      // Calculate stats
      const totalViews = userBusinesses.reduce((sum: number, b: Business) => sum + b.listing.views, 0);
      const totalInquiries = userBusinesses.reduce((sum: number, b: Business) => sum + b.listing.inquiries, 0);
      const totalOffers = userBusinesses.reduce((sum: number, b: Business) => sum + b.listing.offers, 0);
      const avgPrice = userBusinesses.length > 0 ? 
        userBusinesses.reduce((sum: number, b: Business) => sum + b.askingPrice, 0) / userBusinesses.length : 0;
      
      setStats({
        totalListings: userBusinesses.length,
        totalViews,
        totalInquiries,
        totalOffers,
        avgPrice
      });
    } catch (error) {
      console.error('Error fetching seller data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Seller Dashboard</h1>
              <p className="mt-2 text-gray-600">Manage your business listings and track performance</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Link 
                href="/web/abnfund/sell/new"
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition"
              >
                <PlusIcon className="h-5 w-5" />
                List New Business
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Listings</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalListings}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <EyeIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalViews.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChatBubbleLeftIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Inquiries</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalInquiries}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Offers</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalOffers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg. Price</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.avgPrice)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Business Listings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">Your Business Listings</h2>
          </div>

          {businesses.length === 0 ? (
            <div className="text-center py-12">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No businesses listed yet</h3>
              <p className="text-gray-500 mb-6">Get started by listing your first business for sale</p>
              <Link 
                href="/web/abnfund/sell/new"
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition"
              >
                <PlusIcon className="h-5 w-5" />
                List Your First Business
              </Link>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {businesses.map((business) => (
                <div key={business.id} className="p-6 hover:bg-gray-50 transition">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{business.title}</h3>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(business.listing.status)}
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(business.listing.status)}`}>
                            {business.listing.status.charAt(0).toUpperCase() + business.listing.status.slice(1)}
                          </span>
                        </div>
                        {business.listing.featured && (
                          <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                            ⭐ Featured
                          </span>
                        )}
                      </div>
                      
                      <p className="text-gray-600 mb-3 line-clamp-2">{business.description}</p>
                      
                      <div className="flex items-center gap-6 text-sm text-gray-500">
                        <span className="font-semibold text-gray-900">
                          {formatCurrency(business.askingPrice, business.currency)}
                        </span>
                        <span>Listed {formatDate(business.listing.listedDate)}</span>
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <EyeIcon className="h-4 w-4" />
                            {business.listing.views}
                          </div>
                          <div className="flex items-center gap-1">
                            <ChatBubbleLeftIcon className="h-4 w-4" />
                            {business.listing.inquiries}
                          </div>
                          <div className="flex items-center gap-1">
                            <CurrencyDollarIcon className="h-4 w-4" />
                            {business.listing.offers}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Link
                        href={`/web/abnfund/marketplace/${business.id}`}
                        className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium px-4 py-2 rounded-lg transition"
                      >
                        View
                      </Link>
                      <Link
                        href={`/web/abnfund/sell/${business.id}/edit`}
                        className="bg-blue-100 hover:bg-blue-200 text-blue-700 font-medium px-4 py-2 rounded-lg transition flex items-center gap-1"
                      >
                        <PencilIcon className="h-4 w-4" />
                        Edit
                      </Link>
                      <button className="bg-red-100 hover:bg-red-200 text-red-700 font-medium px-4 py-2 rounded-lg transition flex items-center gap-1">
                        <TrashIcon className="h-4 w-4" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid md:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link 
                href="/web/abnfund/sell/new"
                className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition text-center"
              >
                List New Business
              </Link>
              <Link 
                href="/web/abnfund/advisory"
                className="block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition text-center"
              >
                Get Expert Help
              </Link>
              <Link 
                href="/web/abnfund/sell/analytics"
                className="block w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition text-center"
              >
                View Analytics
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tips for Success</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Add detailed financial information to attract serious buyers</li>
              <li>• Upload supporting documents to build trust</li>
              <li>• Respond quickly to buyer inquiries</li>
              <li>• Consider featuring your listing for more visibility</li>
              <li>• Work with our M&A advisors for expert guidance</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
