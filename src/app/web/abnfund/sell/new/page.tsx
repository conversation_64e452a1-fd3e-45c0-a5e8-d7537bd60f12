'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ArrowLeftIcon,
  InformationCircleIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  UsersIcon,
  CogIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';

interface BusinessFormData {
  title: string;
  description: string;
  category: string;
  industry: string;
  askingPrice: number;
  currency: string;
  revenue: {
    monthly: number;
    annual: number;
    growth: number;
  };
  metrics: {
    customers?: number;
    mrr?: number;
    churnRate?: number;
    ltv?: number;
    cac?: number;
    downloads?: number;
    activeUsers?: number;
    conversionRate?: number;
    avgOrderValue?: number;
  };
  financials: {
    grossMargin: number;
    netMargin: number;
    ebitda: number;
    expenses: {
      monthly: number;
      breakdown: {
        salaries: number;
        marketing: number;
        infrastructure: number;
        other: number;
      };
    };
  };
  team: {
    size: number;
    keyPersons: Array<{
      name: string;
      role: string;
      staying: boolean;
    }>;
  };
  technology: {
    stack: string[];
    infrastructure: string;
    scalability: string;
  };
  assets: {
    domain: string;
    ip: string[];
    contracts: string[];
  };
  tags: string[];
}

const initialFormData: BusinessFormData = {
  title: '',
  description: '',
  category: 'SaaS',
  industry: '',
  askingPrice: 0,
  currency: 'USD',
  revenue: {
    monthly: 0,
    annual: 0,
    growth: 0
  },
  metrics: {},
  financials: {
    grossMargin: 0,
    netMargin: 0,
    ebitda: 0,
    expenses: {
      monthly: 0,
      breakdown: {
        salaries: 0,
        marketing: 0,
        infrastructure: 0,
        other: 0
      }
    }
  },
  team: {
    size: 1,
    keyPersons: []
  },
  technology: {
    stack: [],
    infrastructure: '',
    scalability: ''
  },
  assets: {
    domain: '',
    ip: [],
    contracts: []
  },
  tags: []
};

export default function NewBusinessListingPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<BusinessFormData>(initialFormData);
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const steps = [
    { id: 1, title: 'Basic Info', icon: <InformationCircleIcon className="h-5 w-5" /> },
    { id: 2, title: 'Financials', icon: <CurrencyDollarIcon className="h-5 w-5" /> },
    { id: 3, title: 'Metrics', icon: <ChartBarIcon className="h-5 w-5" /> },
    { id: 4, title: 'Team', icon: <UsersIcon className="h-5 w-5" /> },
    { id: 5, title: 'Technology', icon: <CogIcon className="h-5 w-5" /> },
    { id: 6, title: 'Assets', icon: <DocumentTextIcon className="h-5 w-5" /> },
    { id: 7, title: 'Review', icon: <PhotoIcon className="h-5 w-5" /> }
  ];

  const updateFormData = (section: string, data: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: { ...prev[section as keyof BusinessFormData], ...data }
    }));
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.title) newErrors.title = 'Title is required';
        if (!formData.description) newErrors.description = 'Description is required';
        if (!formData.industry) newErrors.industry = 'Industry is required';
        if (formData.askingPrice <= 0) newErrors.askingPrice = 'Asking price must be greater than 0';
        break;
      case 2:
        if (formData.revenue.annual <= 0) newErrors.annual = 'Annual revenue is required';
        if (formData.revenue.monthly <= 0) newErrors.monthly = 'Monthly revenue is required';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setLoading(true);
    try {
      const response = await fetch('/api/abnfund/businesses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          seller: {
            id: 'user-001', // In a real app, this would come from auth
            name: 'Current User',
            verified: true,
            previousSales: 0
          }
        }),
      });

      if (response.ok) {
        const newBusiness = await response.json();
        router.push(`/web/abnfund/marketplace/${newBusiness.id}`);
      } else {
        throw new Error('Failed to create listing');
      }
    } catch (error) {
      console.error('Error creating listing:', error);
      alert('Failed to create listing. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Business Title *</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="e.g., SaaS Analytics Platform"
              />
              {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
              <textarea
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${errors.description ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="Describe your business, what it does, and what makes it unique..."
              />
              {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="SaaS">SaaS</option>
                  <option value="E-commerce">E-commerce</option>
                  <option value="Mobile App">Mobile App</option>
                  <option value="Content">Content</option>
                  <option value="Agency">Agency</option>
                  <option value="Marketplace">Marketplace</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Industry *</label>
                <input
                  type="text"
                  value={formData.industry}
                  onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${errors.industry ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="e.g., Analytics, E-commerce, FinTech"
                />
                {errors.industry && <p className="text-red-500 text-sm mt-1">{errors.industry}</p>}
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Asking Price *</label>
                <input
                  type="number"
                  value={formData.askingPrice}
                  onChange={(e) => setFormData(prev => ({ ...prev, askingPrice: Number(e.target.value) }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${errors.askingPrice ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="2500000"
                />
                {errors.askingPrice && <p className="text-red-500 text-sm mt-1">{errors.askingPrice}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                <select
                  value={formData.currency}
                  onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="AUD">AUD</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Monthly Revenue *</label>
                <input
                  type="number"
                  value={formData.revenue.monthly}
                  onChange={(e) => updateFormData('revenue', { monthly: Number(e.target.value) })}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${errors.monthly ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="85000"
                />
                {errors.monthly && <p className="text-red-500 text-sm mt-1">{errors.monthly}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Annual Revenue *</label>
                <input
                  type="number"
                  value={formData.revenue.annual}
                  onChange={(e) => updateFormData('revenue', { annual: Number(e.target.value) })}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${errors.annual ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="1020000"
                />
                {errors.annual && <p className="text-red-500 text-sm mt-1">{errors.annual}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Growth Rate (%)</label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.revenue.growth}
                  onChange={(e) => updateFormData('revenue', { growth: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="15.5"
                />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Gross Margin (%)</label>
                <input
                  type="number"
                  value={formData.financials.grossMargin}
                  onChange={(e) => updateFormData('financials', { grossMargin: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="85"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Net Margin (%)</label>
                <input
                  type="number"
                  value={formData.financials.netMargin}
                  onChange={(e) => updateFormData('financials', { netMargin: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="22"
                />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">EBITDA</label>
                <input
                  type="number"
                  value={formData.financials.ebitda}
                  onChange={(e) => updateFormData('financials', { ebitda: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="224400"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Monthly Expenses</label>
                <input
                  type="number"
                  value={formData.financials.expenses.monthly}
                  onChange={(e) => updateFormData('financials', {
                    expenses: { ...formData.financials.expenses, monthly: Number(e.target.value) }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="68000"
                />
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-12">
            <p className="text-gray-500">Step {currentStep} content will be implemented here</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-4 mb-6">
            <Link href="/web/abnfund/sell" className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition">
              <ArrowLeftIcon className="h-5 w-5" />
              Back to Dashboard
            </Link>
          </div>

          <div>
            <h1 className="text-3xl font-bold text-gray-900">List Your Business</h1>
            <p className="mt-2 text-gray-600">Create a compelling listing to attract qualified buyers</p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition ${
                  currentStep === step.id
                    ? 'bg-blue-100 text-blue-700'
                    : currentStep > step.id
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-500'
                }`}>
                  {step.icon}
                  <span className="hidden sm:inline">{step.title}</span>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-8 h-0.5 mx-2 ${currentStep > step.id ? 'bg-green-500' : 'bg-gray-300'}`} />
                )}
              </div>
            ))}
          </nav>
        </div>
      </div>

      {/* Form Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Step {currentStep}: {steps[currentStep - 1].title}
            </h2>
            <p className="text-gray-600">
              {currentStep === 1 && "Let's start with the basic information about your business"}
              {currentStep === 2 && "Provide financial details to help buyers understand your business performance"}
              {currentStep > 2 && `Complete step ${currentStep} of your business listing`}
            </p>
          </div>

          {renderStepContent()}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition"
            >
              Previous
            </button>

            {currentStep < steps.length ? (
              <button
                onClick={handleNext}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition disabled:opacity-50"
              >
                {loading ? 'Creating Listing...' : 'Create Listing'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}