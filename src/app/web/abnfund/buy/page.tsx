'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  MagnifyingGlassIcon,
  BellIcon,
  HeartIcon,
  EyeIcon,
  ChatBubbleLeftIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  FunnelIcon,
  BookmarkIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

interface Business {
  id: string;
  title: string;
  description: string;
  category: string;
  industry: string;
  askingPrice: number;
  currency: string;
  revenue: {
    monthly: number;
    annual: number;
    growth: number;
  };
  listing: {
    views: number;
    inquiries: number;
    offers: number;
    listedDate: string;
  };
  tags: string[];
}

interface SavedSearch {
  id: string;
  name: string;
  criteria: {
    category?: string;
    priceRange?: string;
    revenueRange?: string;
  };
  alertsEnabled: boolean;
  newMatches: number;
}

const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export default function BuyPage() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [savedBusinesses, setSavedBusinesses] = useState<string[]>([]);
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('discover');

  useEffect(() => {
    fetchBuyerData();
  }, []);

  const fetchBuyerData = async () => {
    try {
      const response = await fetch('/api/abnfund/businesses');
      const data = await response.json();
      setBusinesses(data.businesses || []);
      
      // Mock saved businesses and searches
      setSavedBusinesses(['biz-001']);
      setSavedSearches([
        {
          id: 'search-1',
          name: 'SaaS under $5M',
          criteria: { category: 'SaaS', priceRange: 'under-5m' },
          alertsEnabled: true,
          newMatches: 2
        },
        {
          id: 'search-2',
          name: 'E-commerce businesses',
          criteria: { category: 'E-commerce' },
          alertsEnabled: false,
          newMatches: 0
        }
      ]);
    } catch (error) {
      console.error('Error fetching buyer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleSaved = (businessId: string) => {
    setSavedBusinesses(prev => 
      prev.includes(businessId) 
        ? prev.filter(id => id !== businessId)
        : [...prev, businessId]
    );
  };

  const featuredBusinesses = businesses.filter(b => b.listing && 'featured' in b.listing);
  const recentBusinesses = businesses.slice(0, 3);
  const savedBusinessesList = businesses.filter(b => savedBusinesses.includes(b.id));

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading buyer dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Buyer Dashboard</h1>
              <p className="mt-2 text-gray-600">Discover and track businesses that match your investment criteria</p>
            </div>
            <div className="mt-4 sm:mt-0 flex gap-3">
              <Link 
                href="/web/abnfund/marketplace"
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition"
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
                Browse All
              </Link>
              <button className="inline-flex items-center gap-2 bg-white hover:bg-gray-50 text-gray-700 font-semibold px-6 py-3 rounded-lg border border-gray-300 transition">
                <BellIcon className="h-5 w-5" />
                Alerts
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'discover', label: 'Discover', count: businesses.length },
              { id: 'saved', label: 'Saved', count: savedBusinesses.length },
              { id: 'searches', label: 'Saved Searches', count: savedSearches.length },
              { id: 'activity', label: 'Activity', count: 0 }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                {tab.count > 0 && (
                  <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'discover' && (
          <div className="space-y-8">
            {/* Quick Search */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Quick Search</h2>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search businesses..."
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <select className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option>All Categories</option>
                  <option>SaaS</option>
                  <option>E-commerce</option>
                  <option>Mobile App</option>
                </select>
                <select className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option>All Prices</option>
                  <option>Under $1M</option>
                  <option>$1M - $5M</option>
                  <option>Over $5M</option>
                </select>
                <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg transition">
                  Search
                </button>
              </div>
            </div>

            {/* Featured Businesses */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Featured Opportunities</h2>
                <Link href="/web/abnfund/marketplace" className="text-blue-600 hover:text-blue-700 font-medium">
                  View all →
                </Link>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {recentBusinesses.map((business) => (
                  <div key={business.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition">
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <span className="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                          {business.category}
                        </span>
                        <button
                          onClick={() => toggleSaved(business.id)}
                          className="text-gray-400 hover:text-red-500 transition"
                        >
                          {savedBusinesses.includes(business.id) ? 
                            <HeartIconSolid className="h-5 w-5 text-red-500" /> : 
                            <HeartIcon className="h-5 w-5" />
                          }
                        </button>
                      </div>

                      <h3 className="text-lg font-bold text-gray-900 mb-2">{business.title}</h3>
                      <p className="text-gray-600 mb-4 line-clamp-2">{business.description}</p>

                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Price</span>
                          <span className="font-bold">{formatCurrency(business.askingPrice)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Revenue</span>
                          <span className="font-semibold">{formatCurrency(business.revenue.annual)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Growth</span>
                          <span className="font-semibold text-green-600">+{business.revenue.growth}%</span>
                        </div>
                      </div>

                      <Link 
                        href={`/web/abnfund/marketplace/${business.id}`}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition text-center block"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Investment Preferences */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Set Your Investment Preferences</h2>
              <p className="text-gray-600 mb-6">Help us show you the most relevant opportunities</p>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option>$500K - $2M</option>
                    <option>$2M - $5M</option>
                    <option>$5M - $10M</option>
                    <option>$10M+</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Categories</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option>SaaS</option>
                    <option>E-commerce</option>
                    <option>Mobile Apps</option>
                    <option>All Categories</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Investment Type</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option>Strategic Acquisition</option>
                    <option>Financial Investment</option>
                    <option>Portfolio Addition</option>
                  </select>
                </div>
              </div>
              <button className="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg transition">
                Save Preferences
              </button>
            </div>
          </div>
        )}

        {activeTab === 'saved' && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Saved Businesses</h2>
            {savedBusinessesList.length === 0 ? (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
                <BookmarkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No saved businesses yet</h3>
                <p className="text-gray-500 mb-6">Save businesses you're interested in to track them here</p>
                <Link 
                  href="/web/abnfund/marketplace"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition"
                >
                  Browse Businesses
                </Link>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {savedBusinessesList.map((business) => (
                  <div key={business.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition">
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <span className="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                          {business.category}
                        </span>
                        <button
                          onClick={() => toggleSaved(business.id)}
                          className="text-red-500 hover:text-red-600 transition"
                        >
                          <HeartIconSolid className="h-5 w-5" />
                        </button>
                      </div>

                      <h3 className="text-lg font-bold text-gray-900 mb-2">{business.title}</h3>
                      <p className="text-gray-600 mb-4 line-clamp-2">{business.description}</p>

                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Price</span>
                          <span className="font-bold">{formatCurrency(business.askingPrice)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Revenue</span>
                          <span className="font-semibold">{formatCurrency(business.revenue.annual)}</span>
                        </div>
                      </div>

                      <Link 
                        href={`/web/abnfund/marketplace/${business.id}`}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition text-center block"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'searches' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Saved Searches</h2>
              <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition">
                Create New Search
              </button>
            </div>
            
            <div className="space-y-4">
              {savedSearches.map((search) => (
                <div key={search.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{search.name}</h3>
                        {search.newMatches > 0 && (
                          <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2 py-1 rounded-full">
                            {search.newMatches} new matches
                          </span>
                        )}
                        {search.alertsEnabled && (
                          <span className="bg-green-100 text-green-800 text-sm font-medium px-2 py-1 rounded-full flex items-center gap-1">
                            <BellIcon className="h-3 w-3" />
                            Alerts On
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        {search.criteria.category && <span>Category: {search.criteria.category}</span>}
                        {search.criteria.priceRange && <span>Price: {search.criteria.priceRange}</span>}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button className="bg-blue-100 hover:bg-blue-200 text-blue-700 font-medium px-4 py-2 rounded-lg transition">
                        View Results
                      </button>
                      <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium px-4 py-2 rounded-lg transition">
                        Edit
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'activity' && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Recent Activity</h2>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No recent activity</h3>
              <p className="text-gray-500">Your interactions with businesses will appear here</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
