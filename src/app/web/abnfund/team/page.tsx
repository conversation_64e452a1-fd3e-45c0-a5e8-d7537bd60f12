import React from 'react';
import <PERSON> from 'next/link';
import {
  UserCircleIcon,
  UsersIcon,
  AcademicCapIcon,
  LightBulbIcon,
  BuildingOffice2Icon,
  ChartBarIcon,
  CpuChipIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

const team = [
  { name: '<PERSON>', role: 'Managing General Partner' },
  { name: '<PERSON>', role: 'Partner, COO, General Counsel' },
  { name: '<PERSON>', role: 'Talent Partner' },
  { name: '<PERSON>', role: 'Partner' },
  { name: '<PERSON><PERSON>', role: 'Principal Builder' },
  { name: '<PERSON>', role: 'CTO' },
  { name: '<PERSON>', role: 'Sr. Marketing Manager' },
  { name: '<PERSON>', role: 'CFO' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Machine Learning Engineer' },
  { name: '<PERSON>', role: 'General Manager, Taiwan' },
  { name: '<PERSON>', role: 'Sr. Executive Recruiter' },
  { name: '<PERSON>', role: 'Sr. Recruiter' },
  { name: '<PERSON>', role: 'Senior Associate Builder' },
  { name: '<PERSON>', role: 'Talent' },
  { name: '<PERSON>', role: 'Operations Manager of Colombia' },
  { name: '<PERSON><PERSON>', role: 'Assistant General Counsel' },
  { name: '<PERSON> <PERSON>co', role: 'Sr. Executive Assistant' },
  { name: '<PERSON> <PERSON>der<PERSON>', role: 'Sr. A<PERSON>untant' },
];

const advisors = [
  { name: '<PERSON> <PERSON>hoo', role: '<PERSON> Advisor' },
  { name: '<PERSON> <PERSON><PERSON><PERSON><PERSON>', role: 'AI Fund Fellow' },
  { name: '<PERSON> Luman', role: 'Venture Advisor' },
  { name: '<PERSON> <PERSON>lin', role: 'Venture Advisor' },
  { name: 'David Sobie', role: 'Venture Advisor' },
  { name: 'Dean Sysman', role: 'Venture Advisor' },
  { name: 'Emil Stefanutti', role: 'Venture Advisor' },
  { name: 'Gene Wade', role: 'Venture Advisor' },
  { name: 'Johnny Lee', role: 'AI Fund Fellow' },
  { name: 'Jon Miller', role: 'Venture Advisor' },
  { name: 'Laurence Moroney', role: 'AI Fund Fellow' },
  { name: 'Miguel Paredes', role: 'AI Fund Fellow' },
  { name: 'Philippe Chambadal', role: 'Venture Advisor' },
  { name: 'Ross Saario', role: 'Venture Advisor' },
  { name: 'Ryan Howard', role: 'Venture Advisor' },
  { name: 'Sarah Daniels', role: 'Venture Advisor' },
  { name: 'Scott C. Taylor', role: 'Venture Advisor' },
  { name: 'Shan Sinha', role: 'Venture Advisor' },
  { name: 'Sharbani Roy', role: 'AI Fund Fellow' },
  { name: 'Tim Westergren', role: 'Venture Advisor' },
  { name: 'TJ Kennedy', role: 'Venture Advisor' },
];

export default function TeamPage() {
  return (
    <div className="min-h-screen bg-white pb-20">
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>
        <div className="max-w-4xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-2 rounded-full mb-6">
            <UsersIcon className="h-4 w-4" />
            Meet Our Team
          </div>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 text-slate-900">
            <span className="block">Passionate Innovators</span>
            <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Building Tomorrow</span>
          </h1>
          <p className="text-xl text-slate-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            We are a diverse team of AI pioneers, seasoned operators, successful founders, and strategic investors.
            Our mission is to help visionary entrepreneurs bring their ideas to life and scale them globally.
          </p>
          <div className="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
              <div className="text-2xl font-bold text-blue-600 mb-1">50+</div>
              <div className="text-slate-600 font-medium">Team Members</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
              <div className="text-2xl font-bold text-blue-600 mb-1">20+</div>
              <div className="text-slate-600 font-medium">Industry Experts</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
              <div className="text-2xl font-bold text-blue-600 mb-1">15+</div>
              <div className="text-slate-600 font-medium">Years Experience</div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Grid */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-slate-900 mb-4">Leadership Team</h2>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            Experienced leaders driving innovation and growth across all aspects of our venture studio
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {team.map(({ name, role }) => (
            <div key={name} className="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
              <div className="flex flex-col items-center text-center">
                <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl mb-4 group-hover:scale-110 transition-transform">
                  <UserCircleIcon className="h-10 w-10 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-slate-900 mb-1 group-hover:text-blue-600 transition-colors">{name}</h3>
                <p className="text-sm text-slate-500 leading-relaxed">{role}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Advisors Grid */}
      <section className="bg-gradient-to-br from-slate-50 to-blue-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 mb-4">Strategic Advisors</h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Industry veterans and domain experts providing strategic guidance and mentorship to our portfolio companies
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {advisors.map(({ name, role }) => (
              <div key={name} className="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300">
                <div className="flex flex-col items-center text-center">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-2xl mb-4 group-hover:scale-110 transition-transform">
                    <AcademicCapIcon className="h-10 w-10 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-1 group-hover:text-purple-600 transition-colors">{name}</h3>
                  <p className="text-sm text-slate-500 leading-relaxed">{role}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Join Our Mission</h2>
          <p className="text-xl text-blue-100 mb-10 max-w-2xl mx-auto leading-relaxed">
            We're always looking for exceptional talent to join our team and help build the future of AI-powered businesses.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/web/abnfund/advisory" className="inline-flex items-center gap-2 bg-white hover:bg-gray-100 text-slate-900 font-semibold px-8 py-4 rounded-xl transition shadow-lg hover:shadow-xl">
              <BuildingOffice2Icon className="h-5 w-5" />
              Work With Us
            </Link>
            <Link href="/web/abnfund/portfolio" className="inline-flex items-center gap-2 bg-transparent hover:bg-white hover:text-slate-900 text-white font-semibold px-8 py-4 rounded-xl border-2 border-white transition">
              <ChartBarIcon className="h-5 w-5" />
              View Portfolio
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
} 