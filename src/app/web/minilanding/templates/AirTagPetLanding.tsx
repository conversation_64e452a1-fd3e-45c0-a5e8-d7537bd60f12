'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Heart, Phone, Mail, MessageCircle, MapPin, AlertTriangle, Gift } from 'lucide-react';

interface AirTagPetLandingConfig {
  title: string;
  message: string;
  petName: string;
  ownerName: string;
  ownerContact: string;
  contactType: 'email' | 'phone';
  airTagId: string;
  petType?: string;
  petBreed?: string;
  petAge?: string;
  petDescription?: string;
  medicalInfo?: string;
  showOwnerName: boolean;
  showContactInfo: boolean;
  allowDirectContact: boolean;
  allowAnonymousMessage: boolean;
  customInstructions?: string;
  backgroundColor?: string;
  textColor?: string;
  buttonColor?: string;
  showRewardInfo?: boolean;
  rewardOffered?: boolean;
  rewardAmount?: string;
  emergencyVet?: string;
  lastSeenLocation?: string;
}

interface AirTagPetLandingProps {
  config: AirTagPetLandingConfig;
}

export default function AirTagPetLanding({ config }: AirTagPetLandingProps) {
  const [contactForm, setContactForm] = useState({
    finderName: '',
    finderContact: '',
    message: '',
    location: '',
    petCondition: '',
    isAnonymous: false
  });
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!contactForm.message.trim()) {
      setError('Please enter a message');
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/platforms/airtag/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          airTagId: config.airTagId,
          finderName: contactForm.isAnonymous ? null : contactForm.finderName,
          finderContact: contactForm.isAnonymous ? null : contactForm.finderContact,
          message: `Pet Condition: ${contactForm.petCondition}\nLocation: ${contactForm.location}\nMessage: ${contactForm.message}`,
          location: contactForm.location
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }

      setSubmitted(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDirectContact = () => {
    if (config.contactType === 'email') {
      window.location.href = `mailto:${config.ownerContact}?subject=Found: ${config.petName}&body=Hi ${config.ownerName}, I found your pet ${config.petName}. Please contact me immediately to arrange reunion.`;
    } else if (config.contactType === 'phone') {
      window.location.href = `tel:${config.ownerContact}`;
    }
  };

  const backgroundColor = config.backgroundColor || '#e7f3ff';
  const textColor = config.textColor || '#0066cc';
  const buttonColor = config.buttonColor || '#0066cc';

  if (submitted) {
    return (
      <div 
        className="min-h-screen flex items-center justify-center p-4"
        style={{ backgroundColor, color: textColor }}
      >
        <Card className="w-full max-w-md">
          <CardContent className="text-center p-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Heart className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Message Sent!</h2>
            <p className="text-gray-600 mb-4">
              Thank you for finding {config.petName}! The owner has been notified and will contact you immediately.
            </p>
            <p className="text-sm text-gray-500">
              Please keep {config.petName} safe until the owner arrives.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div 
      className="min-h-screen p-4"
      style={{ backgroundColor, color: textColor }}
    >
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Heart className="w-10 h-10 text-red-500" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            {config.title}
          </h1>
          <p className="text-lg md:text-xl mb-6">
            {config.message}
          </p>
          
          {config.showRewardInfo && config.rewardOffered && (
            <div className="inline-flex items-center gap-2 bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full mb-6">
              <Gift className="w-6 h-6" />
              <span className="font-medium text-lg">
                Reward Offered: {config.rewardAmount || 'Thank you gift'}
              </span>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Pet Information */}
          <Card className="border-2" style={{ borderColor: buttonColor }}>
            <CardHeader className="bg-blue-50">
              <CardTitle className="flex items-center gap-2 text-xl">
                <Heart className="w-6 h-6" />
                About {config.petName}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div>
                <Label className="text-sm font-medium text-gray-600">Pet Name</Label>
                <p className="text-xl font-bold">{config.petName}</p>
              </div>
              
              {config.petType && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Type</Label>
                  <p className="text-lg">{config.petType}</p>
                </div>
              )}

              {config.petBreed && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Breed</Label>
                  <p className="text-lg">{config.petBreed}</p>
                </div>
              )}

              {config.petAge && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Age</Label>
                  <p className="text-lg">{config.petAge}</p>
                </div>
              )}

              {config.petDescription && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Description</Label>
                  <p className="text-sm">{config.petDescription}</p>
                </div>
              )}

              {config.showOwnerName && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Owner</Label>
                  <p className="text-lg font-semibold">{config.ownerName}</p>
                </div>
              )}

              {config.lastSeenLocation && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Last Seen</Label>
                  <p className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {config.lastSeenLocation}
                  </p>
                </div>
              )}

              {config.medicalInfo && (
                <div className="bg-red-50 p-4 rounded-md border border-red-200">
                  <Label className="text-sm font-medium text-red-700">Medical Information</Label>
                  <p className="text-sm text-red-600 mt-1">{config.medicalInfo}</p>
                </div>
              )}

              {config.customInstructions && (
                <div className="bg-blue-50 p-4 rounded-md">
                  <Label className="text-sm font-medium text-blue-700">Special Instructions</Label>
                  <p className="text-sm text-blue-600 mt-1">{config.customInstructions}</p>
                </div>
              )}

              {config.showContactInfo && config.allowDirectContact && (
                <div className="pt-4">
                  <Button 
                    onClick={handleDirectContact}
                    className="w-full text-lg py-3"
                    style={{ backgroundColor: buttonColor }}
                  >
                    {config.contactType === 'email' ? (
                      <>
                        <Mail className="w-5 h-5 mr-2" />
                        Email Owner Now
                      </>
                    ) : (
                      <>
                        <Phone className="w-5 h-5 mr-2" />
                        Call Owner Now
                      </>
                    )}
                  </Button>
                  {config.showContactInfo && (
                    <p className="text-sm text-gray-600 mt-2 text-center font-medium">
                      {config.ownerContact}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contact Form */}
          {config.allowAnonymousMessage && (
            <Card className="border-2" style={{ borderColor: buttonColor }}>
              <CardHeader className="bg-green-50">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <MessageCircle className="w-6 h-6" />
                  Report Finding {config.petName}
                </CardTitle>
                <CardDescription className="text-base">
                  Please provide details about finding this beloved pet
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="anonymous"
                      checked={contactForm.isAnonymous}
                      onCheckedChange={(checked) => 
                        setContactForm(prev => ({ ...prev, isAnonymous: checked }))
                      }
                    />
                    <Label htmlFor="anonymous" className="text-sm">
                      Send anonymously
                    </Label>
                  </div>

                  {!contactForm.isAnonymous && (
                    <>
                      <div>
                        <Label htmlFor="finderName">Your Name</Label>
                        <Input
                          id="finderName"
                          value={contactForm.finderName}
                          onChange={(e) => 
                            setContactForm(prev => ({ ...prev, finderName: e.target.value }))
                          }
                          placeholder="Enter your name"
                        />
                      </div>

                      <div>
                        <Label htmlFor="finderContact">Your Contact</Label>
                        <Input
                          id="finderContact"
                          value={contactForm.finderContact}
                          onChange={(e) => 
                            setContactForm(prev => ({ ...prev, finderContact: e.target.value }))
                          }
                          placeholder="Email or phone number"
                        />
                      </div>
                    </>
                  )}

                  <div>
                    <Label htmlFor="location">Where did you find {config.petName}?</Label>
                    <Input
                      id="location"
                      value={contactForm.location}
                      onChange={(e) => 
                        setContactForm(prev => ({ ...prev, location: e.target.value }))
                      }
                      placeholder="Exact location where you found the pet"
                    />
                  </div>

                  <div>
                    <Label htmlFor="petCondition">Pet's Condition</Label>
                    <Input
                      id="petCondition"
                      value={contactForm.petCondition}
                      onChange={(e) => 
                        setContactForm(prev => ({ ...prev, petCondition: e.target.value }))
                      }
                      placeholder="e.g., Healthy, Scared, Injured, Hungry"
                    />
                  </div>

                  <div>
                    <Label htmlFor="message">Additional Details *</Label>
                    <Textarea
                      id="message"
                      value={contactForm.message}
                      onChange={(e) => 
                        setContactForm(prev => ({ ...prev, message: e.target.value }))
                      }
                      placeholder="Describe how you found the pet, their behavior, any injuries, etc."
                      rows={4}
                      required
                    />
                  </div>

                  {error && (
                    <div className="text-red-600 text-sm">{error}</div>
                  )}

                  <Button 
                    type="submit" 
                    disabled={submitting}
                    className="w-full text-lg py-3"
                    style={{ backgroundColor: buttonColor }}
                  >
                    {submitting ? 'Sending...' : `Report Finding ${config.petName}`}
                  </Button>
                </form>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Emergency Vet */}
        {config.emergencyVet && (
          <Card className="mt-8 border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="flex items-center gap-2 text-red-700 mb-2">
                <AlertTriangle className="w-6 h-6" />
                <span className="font-bold text-lg">Emergency Veterinary Contact</span>
              </div>
              <p className="text-red-600 text-base">
                If {config.petName} needs immediate medical attention: {config.emergencyVet}
              </p>
            </CardContent>
          </Card>
        )}

        {/* Important Notice */}
        <Card className="mt-8 bg-yellow-50 border-yellow-200">
          <CardContent className="p-6 text-center">
            <h3 className="font-bold text-lg mb-2">Important Notice</h3>
            <p className="text-sm text-gray-700">
              Please keep {config.petName} safe and secure until the owner arrives. 
              Pets can be scared and may try to run away. Provide water and a quiet space if possible.
            </p>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Powered by ABN AirTag - Helping reunite pets with their families</p>
        </div>
      </div>
    </div>
  );
}
