'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { MapPin, Phone, Mail, MessageCircle, Gift, AlertTriangle } from 'lucide-react';

interface AirTagLandingConfig {
  title: string;
  message: string;
  itemName: string;
  ownerName: string;
  ownerContact: string;
  contactType: 'email' | 'phone';
  airTagId: string;
  showOwnerName: boolean;
  showContactInfo: boolean;
  allowDirectContact: boolean;
  allowAnonymousMessage: boolean;
  customInstructions?: string;
  backgroundColor?: string;
  textColor?: string;
  buttonColor?: string;
  showRewardInfo?: boolean;
  rewardOffered?: boolean;
  rewardAmount?: string;
  emergencyContact?: string;
  location?: string;
}

interface AirTagLandingProps {
  config: AirTagLandingConfig;
}

export default function AirTagLanding({ config }: AirTagLandingProps) {
  const [contactForm, setContactForm] = useState({
    finderName: '',
    finderContact: '',
    message: '',
    location: '',
    isAnonymous: false
  });
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!contactForm.message.trim()) {
      setError('Please enter a message');
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/platforms/airtag/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          airTagId: config.airTagId,
          finderName: contactForm.isAnonymous ? null : contactForm.finderName,
          finderContact: contactForm.isAnonymous ? null : contactForm.finderContact,
          message: contactForm.message,
          location: contactForm.location
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }

      setSubmitted(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDirectContact = () => {
    if (config.contactType === 'email') {
      window.location.href = `mailto:${config.ownerContact}?subject=Found: ${config.itemName}&body=Hi ${config.ownerName}, I found your ${config.itemName}. Please contact me to arrange return.`;
    } else if (config.contactType === 'phone') {
      window.location.href = `tel:${config.ownerContact}`;
    }
  };

  const backgroundColor = config.backgroundColor || '#ffffff';
  const textColor = config.textColor || '#333333';
  const buttonColor = config.buttonColor || '#007bff';

  if (submitted) {
    return (
      <div 
        className="min-h-screen flex items-center justify-center p-4"
        style={{ backgroundColor, color: textColor }}
      >
        <Card className="w-full max-w-md">
          <CardContent className="text-center p-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MessageCircle className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Message Sent!</h2>
            <p className="text-gray-600 mb-4">
              Thank you for finding this item. The owner has been notified and will contact you soon.
            </p>
            <p className="text-sm text-gray-500">
              You can close this page now.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div 
      className="min-h-screen p-4"
      style={{ backgroundColor, color: textColor }}
    >
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            {config.title}
          </h1>
          <p className="text-lg md:text-xl mb-6">
            {config.message}
          </p>
          
          {config.showRewardInfo && config.rewardOffered && (
            <div className="inline-flex items-center gap-2 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full mb-4">
              <Gift className="w-5 h-5" />
              <span className="font-medium">
                Reward Offered: {config.rewardAmount || 'Thank you gift'}
              </span>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Item Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Item Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">Item</Label>
                <p className="text-lg font-semibold">{config.itemName}</p>
              </div>
              
              {config.showOwnerName && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Owner</Label>
                  <p className="text-lg">{config.ownerName}</p>
                </div>
              )}

              {config.location && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Last Known Location</Label>
                  <p className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {config.location}
                  </p>
                </div>
              )}

              {config.customInstructions && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Instructions</Label>
                  <p className="text-sm">{config.customInstructions}</p>
                </div>
              )}

              {config.showContactInfo && config.allowDirectContact && (
                <div className="pt-4">
                  <Button 
                    onClick={handleDirectContact}
                    className="w-full"
                    style={{ backgroundColor: buttonColor }}
                  >
                    {config.contactType === 'email' ? (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        Email Owner
                      </>
                    ) : (
                      <>
                        <Phone className="w-4 h-4 mr-2" />
                        Call Owner
                      </>
                    )}
                  </Button>
                  {config.showContactInfo && (
                    <p className="text-sm text-gray-600 mt-2 text-center">
                      {config.ownerContact}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contact Form */}
          {config.allowAnonymousMessage && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Contact Owner
                </CardTitle>
                <CardDescription>
                  Send a message to the owner about finding this item
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="anonymous"
                      checked={contactForm.isAnonymous}
                      onCheckedChange={(checked) => 
                        setContactForm(prev => ({ ...prev, isAnonymous: checked }))
                      }
                    />
                    <Label htmlFor="anonymous" className="text-sm">
                      Send anonymously
                    </Label>
                  </div>

                  {!contactForm.isAnonymous && (
                    <>
                      <div>
                        <Label htmlFor="finderName">Your Name</Label>
                        <Input
                          id="finderName"
                          value={contactForm.finderName}
                          onChange={(e) => 
                            setContactForm(prev => ({ ...prev, finderName: e.target.value }))
                          }
                          placeholder="Enter your name"
                        />
                      </div>

                      <div>
                        <Label htmlFor="finderContact">Your Contact</Label>
                        <Input
                          id="finderContact"
                          value={contactForm.finderContact}
                          onChange={(e) => 
                            setContactForm(prev => ({ ...prev, finderContact: e.target.value }))
                          }
                          placeholder="Email or phone number"
                        />
                      </div>
                    </>
                  )}

                  <div>
                    <Label htmlFor="location">Where did you find it?</Label>
                    <Input
                      id="location"
                      value={contactForm.location}
                      onChange={(e) => 
                        setContactForm(prev => ({ ...prev, location: e.target.value }))
                      }
                      placeholder="Location where you found the item"
                    />
                  </div>

                  <div>
                    <Label htmlFor="message">Message *</Label>
                    <Textarea
                      id="message"
                      value={contactForm.message}
                      onChange={(e) => 
                        setContactForm(prev => ({ ...prev, message: e.target.value }))
                      }
                      placeholder="Tell the owner about finding their item..."
                      rows={4}
                      required
                    />
                  </div>

                  {error && (
                    <div className="text-red-600 text-sm">{error}</div>
                  )}

                  <Button 
                    type="submit" 
                    disabled={submitting}
                    className="w-full"
                    style={{ backgroundColor: buttonColor }}
                  >
                    {submitting ? 'Sending...' : 'Send Message'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Emergency Contact */}
        {config.emergencyContact && (
          <Card className="mt-8 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-medium">Emergency Contact</span>
              </div>
              <p className="text-red-600 mt-2">
                If this is an emergency situation, please contact: {config.emergencyContact}
              </p>
            </CardContent>
          </Card>
        )}

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Powered by ABN AirTag - Web-based Object Tracking</p>
        </div>
      </div>
    </div>
  );
}
