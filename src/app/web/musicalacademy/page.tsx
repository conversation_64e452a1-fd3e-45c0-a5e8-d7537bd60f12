import React from 'react';
import Image from 'next/image';
import { Metadata } from 'next';
import ProductionGallery from './page-components/ProductionGallery';
import ProductionInfo from './page-components/ProductionInfo';

export const metadata: Metadata = {
  title: 'KHOA PIANO - Học viện Âm nhạc Quốc gia Việt Nam',
  description: 'Khoa Piano - Nơi ươm mầm cho những tài năng trẻ không chỉ cho đất nước mà còn mang tầm quốc tế',
};

export default function MusicalAcademyPage() {
  return (
    <div className="bg-white min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col items-center mb-4">
          <img src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.logo.webp" alt="Piano Academy Logo" className="w-24 h-24 object-contain mb-2" />
        </div>
        {/* <h1 className="text-4xl font-bold text-center mb-8">KHOA PIANO</h1> */}
        
        <ProductionGallery />
        
        {/* <div className="mt-12">
          <ProductionInfo />
        </div> */}
      </div>
    </div>
  );
} 