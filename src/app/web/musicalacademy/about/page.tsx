import React from 'react';
import { Metadata } from 'next';
import { Music, Award, Users, Globe, Star, Trophy, GraduationCap, Piano } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Giới thiệu - KHOA PIANO',
  description: 'Thông tin về Khoa Piano - Học viện Âm nhạc Quốc gia Việt Nam',
};

export default function AboutPage() {
  return (
    <div className="bg-gradient-to-br from-white to-blue-50 min-h-screen">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 flex flex-col md:flex-row items-center gap-8">
        <div className="flex-1 text-center md:text-left">
          <h1 className="text-5xl font-extrabold mb-4 text-blue-900 flex items-center justify-center md:justify-start gap-2">
            <span className="inline-block align-middle">🎹</span> KHOA PIANO
          </h1>
          <p className="text-xl text-blue-700 mb-6 font-medium">N<PERSON><PERSON> ươm mầm tài năng piano hàng đầu Việt Nam & quốc tế</p>
          <div className="flex flex-wrap gap-4 justify-center md:justify-start">
            <span className="inline-flex items-center bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full font-semibold text-sm shadow"><Trophy className="w-5 h-5 mr-2" />Giải thưởng quốc tế</span>
            <span className="inline-flex items-center bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-semibold text-sm shadow"><GraduationCap className="w-5 h-5 mr-2" />Đào tạo đa cấp</span>
            <span className="inline-flex items-center bg-pink-100 text-pink-800 px-4 py-2 rounded-full font-semibold text-sm shadow"><Users className="w-5 h-5 mr-2" />24 giảng viên xuất sắc</span>
          </div>
        </div>
        <div className="flex-1 flex justify-center">
          {/* Piano Illustration */}
          <div className="relative w-64 h-48 flex items-center justify-center">
            <img src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.logo.webp" alt="Piano Illustration" className="w-full h-full object-contain drop-shadow-lg" />
            {/* Fallback icon if image not found */}
            {/* <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <Piano className="w-32 h-32 text-gray-300" />
            </div> */}
          </div>
        </div>
      </div>

      {/* Icon Cards Section */}
      <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-8">
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center border-t-4 border-blue-400">
          <Users className="w-12 h-12 text-blue-500 mb-3" />
          <h2 className="text-xl font-bold mb-2">Đội ngũ giảng viên</h2>
          <p className="text-blue-900 font-semibold">24 giảng viên, 2 PGS, 6 TS, 16 ThS</p>
          <p className="text-gray-500 text-sm mt-2">Phần lớn đào tạo ở nước ngoài, giàu kinh nghiệm & đam mê</p>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center border-t-4 border-yellow-400">
          <Trophy className="w-12 h-12 text-yellow-500 mb-3" />
          <h2 className="text-xl font-bold mb-2">Thành tựu nổi bật</h2>
          <ul className="text-blue-900 font-semibold space-y-1">
            <li>🏆 Đặng Thái Sơn: Giải Nhất Chopin 1980</li>
            <li>🏆 Nhiều giải quốc tế tại Nhật, Úc, Séc...</li>
            <li>🏆 Học sinh đạt giải tại 15+ quốc gia</li>
          </ul>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center border-t-4 border-pink-400">
          <Music className="w-12 h-12 text-pink-500 mb-3" />
          <h2 className="text-xl font-bold mb-2">Sự kiện & Cuộc thi</h2>
          <p className="text-blue-900 font-semibold">Tổ chức Piano Quốc tế Hà Nội 2010, 2012, 2015</p>
          <p className="text-gray-500 text-sm mt-2">Cuộc thi âm nhạc quốc tế đầu tiên tại Việt Nam</p>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center border-t-4 border-green-400">
          <Award className="w-12 h-12 text-green-500 mb-3" />
          <h2 className="text-xl font-bold mb-2">Khen thưởng</h2>
          <ul className="text-blue-900 font-semibold space-y-1">
            <li>🏅 Bằng khen Bộ VH-TT-DL</li>
            <li>🏅 Tập thể lao động xuất sắc</li>
            <li>🏅 Chi bộ hoàn thành xuất sắc</li>
          </ul>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center border-t-4 border-purple-400">
          <Globe className="w-12 h-12 text-purple-500 mb-3" />
          <h2 className="text-xl font-bold mb-2">Vươn tầm quốc tế</h2>
          <p className="text-blue-900 font-semibold">Sinh viên đạt giải tại Mỹ, Ý, Úc, Áo, Bỉ, Nhật, Hàn...</p>
          <p className="text-gray-500 text-sm mt-2">Nhiều sinh viên là thủ khoa, được vinh danh tại Văn Miếu</p>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center border-t-4 border-red-400">
          <Star className="w-12 h-12 text-red-500 mb-3" />
          <h2 className="text-xl font-bold mb-2">Tự hào Việt Nam</h2>
          <p className="text-blue-900 font-semibold">Đào tạo nghệ sĩ trẻ tài năng cho đất nước & thế giới</p>
          <p className="text-gray-500 text-sm mt-2">Liên tục đổi mới, sáng tạo, hội nhập quốc tế</p>
        </div>
      </div>

      {/* Call to Action */}
      <div className="max-w-3xl mx-auto py-12 text-center">
        <div className="relative bg-white px-8 py-10 rounded-2xl shadow-md border border-blue-100 mb-8 flex flex-col items-center">
          {/* Decorative curved border and quotes */}
          <svg width="120" height="120" className="absolute left-0 top-0 -translate-x-1/2 -translate-y-1/2 text-blue-800 opacity-30" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M100 20 Q20 20 20 100" stroke="currentColor" strokeWidth="6" fill="none" />
          </svg>
          <svg width="120" height="120" className="absolute right-0 bottom-0 translate-x-1/2 translate-y-1/2 text-blue-800 opacity-30" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 100 Q100 100 100 20" stroke="currentColor" strokeWidth="6" fill="none" />
          </svg>
          <span className="absolute left-6 top-2 text-6xl text-blue-800 opacity-70 select-none">“</span>
          <span className="absolute right-6 bottom-2 text-6xl text-blue-800 opacity-70 select-none">”</span>
          <h2 className="text-2xl font-bold mb-4 text-blue-900 relative z-10">Khám phá tài năng piano cùng chúng tôi!</h2>
          <p className="text-blue-700 mb-6 relative z-10">Cũng như thuốc, không thể uống thuốc rẻ và uống nhiều được. Để phát triển hãy chọn người thầy đúng. Chọn người dìu dắt, tham gia các lớp học, sự kiện và cuộc thi để phát triển đam mê âm nhạc và cuộc sống.</p>
        </div>
        {/* <button className="bg-blue-600 text-white px-8 py-3 rounded-full font-semibold text-lg shadow hover:bg-blue-700 transition">Đăng ký ngay</button> */}
      </div>
    </div>
  );
} 