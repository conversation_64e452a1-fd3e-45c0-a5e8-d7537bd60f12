import React from 'react';
import Link from 'next/link';

export default function Navigation() {
  const navItems = [
    { name: 'Trang chủ', href: '/web/musicalacademy' },
    { name: '<PERSON><PERSON><PERSON><PERSON> thiệu', href: '/web/musicalacademy/about' },
    { name: '<PERSON><PERSON><PERSON> ngũ giảng viên', href: '/web/musicalacademy/faculty' },
    /* { name: '<PERSON><PERSON><PERSON>ng trình đào tạo', href: '/web/musicalacademy/programs' },
    { name: 'Thành tựu', href: '/web/musicalacademy/achievements' },
    { name: '<PERSON><PERSON><PERSON> h<PERSON>', href: '/web/musicalacademy/contact' }, */
  ];

  return (
    <div className="w-full bg-white/80 backdrop-blur border-b border-emerald-100 shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            {/* <div className="flex-shrink-0">
              <Link href="/web/musicalacademy" className="text-xl font-bold">
                KHOA PIANO
              </Link>
            </div> */}
          </div>
          <div className="md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="px-4 py-2 rounded-full text-sm font-semibold transition-all duration-200 hover:bg-emerald-600 hover:text-white focus:bg-emerald-700 focus:text-white shadow-sm border border-transparent hover:border-emerald-700"
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 