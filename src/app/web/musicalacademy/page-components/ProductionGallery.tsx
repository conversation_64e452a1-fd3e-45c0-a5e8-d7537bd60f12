import React from 'react';
import Image from 'next/image';

export default function ProductionGallery() {
  return (
    <div className="relative">
      {/* Musical Note Decorations */}
      <div className="absolute top-10 left-5 text-blue-300 opacity-20 transform rotate-12 text-7xl">♪</div>
      <div className="absolute top-20 right-10 text-blue-300 opacity-20 transform -rotate-12 text-8xl">♫</div>
      <div className="absolute bottom-10 left-20 text-blue-300 opacity-20 transform -rotate-45 text-7xl">♩</div>
      <div className="absolute bottom-40 right-5 text-blue-300 opacity-20 transform rotate-45 text-7xl">♬</div>
      
      {/* Piano Keys Background */}
      <div className="absolute inset-x-0 top-0 h-6 flex justify-center opacity-10">
        {Array.from({ length: 40 }).map((_, i) => (
          <div key={i} className={`w-3 h-24 ${i % 2 === 0 ? 'bg-black' : 'bg-white border border-gray-300'}`}></div>
        ))}
      </div>
      <div className="absolute inset-x-0 bottom-0 h-6 flex justify-center opacity-10">
        {Array.from({ length: 40 }).map((_, i) => (
          <div key={i} className={`w-3 h-24 ${i % 2 === 0 ? 'bg-black' : 'bg-white border border-gray-300'}`}></div>
        ))}
      </div>

      {/* Main Title with Piano Icon */}
      <div className="text-center mb-8 relative">
        <h2 className="text-3xl font-bold text-blue-900 inline-flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 3H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2Z"></path>
            <path d="M9 11V5h10v14H9"></path>
            <path d="M9 11H7"></path>
            <path d="M13 11h-2"></path>
            <path d="M17 11h-2"></path>
            <path d="M9 15H7"></path>
            <path d="M13 15h-2"></path>
            <path d="M17 15h-2"></path>
          </svg>
          Khoa Piano - Học viện Âm nhạc Quốc gia
        </h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 relative z-10">
        {/* Left column */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 relative">
            <div className="absolute top-2 right-2 text-blue-500 opacity-70">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M9 18V5l12-2v13"></path>
                <circle cx="6" cy="18" r="3"></circle>
                <circle cx="18" cy="16" r="3"></circle>
              </svg>
            </div>
            <div className="relative h-64 w-full">
              <Image 
                src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.piano.webp" 
                alt="Khoa Piano - Học viện Âm nhạc Quốc gia Việt Nam" 
                fill 
                className="object-cover"
              />
            </div>
            <div className="p-4 border-t border-blue-50">
              <p className="text-sm text-gray-700 flex items-center">
                <span className="text-blue-500 mr-2">♪</span>
                Khoa Piano - Nơi ươm mầm tài năng âm nhạc
              </p>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 relative">
            <div className="absolute top-2 right-2 text-blue-500 opacity-70">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M9 17V9"></path>
                <path d="M9 17a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h1"></path>
                <path d="M9 9V7a3 3 0 0 1 6 0v2"></path>
                <path d="M9 9h3"></path>
                <path d="M15 17V9"></path>
                <path d="M15 17a3 3 0 0 0 3 3h1a3 3 0 0 0 3-3v-2a3 3 0 0 0-3-3h-1"></path>
              </svg>
            </div>
            <div className="relative h-64 w-full">
              <Image 
                src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.piano.webp" 
                alt="Giảng viên và học viên Khoa Piano" 
                fill 
                className="object-cover"
              />
            </div>
            <div className="p-4 border-t border-blue-50">
              <p className="text-sm text-gray-700 flex items-center">
                <span className="text-blue-500 mr-2">♫</span>
                Giảng viên và học viên Khoa Piano trong buổi biểu diễn
              </p>
            </div>
          </div>
        </div>
        
        {/* Center column */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 relative">
            <div className="absolute top-2 right-2 text-blue-500 opacity-70">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m12 8-9.04 9.06a2.82 2.82 0 1 0 3.98 3.98L16 12"></path>
                <circle cx="17" cy="7" r="5"></circle>
              </svg>
            </div>
            <div className="relative h-[400px] w-full">
              <Image 
                src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.piano.webp" 
                alt="Cuộc thi Piano Quốc tế Hà Nội" 
                fill 
                className="object-cover"
              />
            </div>
            <div className="p-4 border-t border-blue-50">
              <p className="text-sm text-gray-700 flex items-center">
                <span className="text-blue-500 mr-2">♬</span>
                Cuộc thi Piano Quốc tế Hà Nội - Sân chơi cho tài năng trẻ
              </p>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div className="p-6 border border-blue-100 relative">
              <div className="absolute -top-3 -left-3 w-10 h-10 bg-blue-50 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 8a2 2 0 0 1 4 0v8a2 2 0 0 1-4 0"></path>
                  <path d="M16 8a2 2 0 0 1 4 0v8a2 2 0 0 1-4 0"></path>
                  <path d="M8 9.5a2 2 0 0 1 4 0V16"></path>
                  <path d="M8 16a2 2 0 0 1 4 0"></path>
                  <path d="M7 10h1"></path>
                  <path d="M7 16h1"></path>
                </svg>
              </div>
              <div className="absolute -bottom-3 -right-3 w-10 h-10 bg-blue-50 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m12 8-9.04 9.06a2.82 2.82 0 1 0 3.98 3.98L16 12"></path>
                  <circle cx="17" cy="7" r="5"></circle>
                </svg>
              </div>
              <div className="text-center">
                <p className="mb-1 font-bold text-blue-900 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-yellow-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20.42 4.58a5.4 5.4 0 0 0-7.65 0l-.77.78-.77-.78a5.4 5.4 0 0 0-7.65 0C1.46 6.7 1.33 10.28 4 13l8 8 8-8c2.67-2.72 2.54-6.3.42-8.42z"></path>
                  </svg>
                  Thành tích nổi bật:
                </p>
                <p className="mb-4 flex items-center">
                  <span className="text-blue-500 mr-2">♪</span>
                  - Giải Nhất cuộc thi Chopin quốc tế 1980 (NSND. Đặng Thái Sơn)
                </p>
                <p className="mb-4 flex items-center">
                  <span className="text-blue-500 mr-2">♪</span>
                  - Giải Ba cuộc thi Piano quốc tế Smetana 1980 (NS. Tôn Nữ Nguyệt Minh)
                </p>
                <p className="mb-4 flex items-center">
                  <span className="text-blue-500 mr-2">♪</span>
                  - Giải Nhất cuộc thi Tài năng trẻ dương cầm quốc tế 1999 (TS. Nguyễn Hoàng Phương)
                </p>
                <p className="mb-4 flex items-center">
                  <span className="text-blue-500 mr-2">♪</span>
                  - Giải Nhất cuộc thi Lev Vlapssenko 2011 (NS. Lưu Hồng Quang)
                </p>
                <p className="flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                  </svg>
                  Đội ngũ giảng viên: 24 giảng viên, trong đó có 2 PGS, 6 TS và 16 ThS
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right column */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 relative">
            <div className="absolute top-2 right-2 text-blue-500 opacity-70">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="8"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </div>
            <div className="relative h-64 w-full">
              <Image 
                src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.piano.webp" 
                alt="Buổi biểu diễn của học viên Khoa Piano" 
                fill 
                className="object-cover"
              />
            </div>
            <div className="p-4 border-t border-blue-50">
              <p className="text-sm text-gray-700 flex items-center">
                <span className="text-blue-500 mr-2">♩</span>
                Buổi biểu diễn của học viên Khoa Piano
              </p>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 relative">
            <div className="absolute top-2 right-2 text-blue-500 opacity-70">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="9 11 12 14 22 4"></polyline>
                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
              </svg>
            </div>
            <div className="relative h-64 w-full">
              <Image 
                src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.piano.webp" 
                alt="Lớp học Piano tại Học viện" 
                fill 
                className="object-cover"
              />
            </div>
            <div className="p-4 border-t border-blue-50">
              <p className="text-sm text-gray-700 flex items-center">
                <span className="text-blue-500 mr-2">♬</span>
                Lớp học Piano tại Học viện Âm nhạc Quốc gia
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Piano Keyboard Decoration */}
      <div className="mt-8 flex justify-center">
        {Array.from({ length: 14 }).map((_, i) => (
          <div key={i} className={`h-10 ${i % 2 === 0 ? 'w-8 bg-black rounded-b-md' : 'w-7 bg-white border border-gray-300 rounded-b-md'} mx-[1px]`}></div>
        ))}
      </div>
    </div>
  );
}