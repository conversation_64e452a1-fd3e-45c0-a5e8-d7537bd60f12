import React from 'react';
import { Metadata } from 'next';
import Navigation from './page-components/Navigation';

export const metadata: Metadata = {
  title: 'KHOA PIANO - Học viện Âm nhạc Quốc gia Việt Nam',
  description: 'Khoa Piano - Nơi ươm mầm cho những tài năng trẻ không chỉ cho đất nước mà còn mang tầm quốc tế',
};

export default function MusicalAcademyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen">
      <Navigation />
      
      {/* <header className="bg-emerald-800 text-white py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold">KHOA PIANO</h1>
          <p className="mt-1 text-lg">H<PERSON><PERSON> viện Âm nhạc Quốc gia Việt Nam</p>
        </div>
      </header> */}
      
      <main>{children}</main>
      
      <footer className="bg-gray-100 py-6 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-lg font-semibold mb-2">KHOA PIANO</h3>
              <p className="text-gray-600">Nơi ươm mầm cho những tài năng trẻ</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Liên hệ</h3>
              <p className="text-gray-600"><EMAIL></p>
            </div>
          </div>
          <div className="mt-8 pt-4 border-t border-gray-200">
            <p className="text-center text-gray-600">
              © {new Date().getFullYear()} Khoa Piano - Học viện Âm nhạc Quốc gia Việt Nam. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
} 