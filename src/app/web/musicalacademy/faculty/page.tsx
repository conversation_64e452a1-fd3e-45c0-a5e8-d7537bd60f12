'use client';
import React, { useState } from 'react';
import { Music, Users, Piano, Award, Star, User, GraduationCap, ChevronDown, ChevronUp, Mail, Phone, BookOpen } from 'lucide-react';

export default function FacultyPage() {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const facultyMembers = [
    { name: 'PGS.TS. Nguyễn <PERSON>', role: 'Giảng viên chính', specialty: 'Piano cổ điển' },
    { name: 'PGS.TS. Nguyễn <PERSON>', role: '<PERSON><PERSON><PERSON>ng viên chính', specialty: 'Piano hiện đại' },
    { name: 'ThS. Trần Th<PERSON>', role: 'Gi<PERSON>ng viên', specialty: 'Piano cổ điển' },
    { name: 'TS. Tr<PERSON><PERSON>', role: '<PERSON><PERSON><PERSON><PERSON> viên', specialty: 'Piano hiện đại' },
    { name: 'Th<PERSON>. <PERSON><PERSON>ương Mai', role: '<PERSON><PERSON><PERSON><PERSON> viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Nguyễn Thị Thanh Vân', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Ngô Lan Hương', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Trần Thị Ngọc Bích', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Phạm Quỳnh Trang', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Phạm Linh Chi', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'TS. Triệu Tú My', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Vũ Ngọc Linh', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Nguyễn Thanh Hằng', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Nguyễn Hương Giang', role: 'Trưởng bộ môn Piano phổ thông', specialty: 'Piano hiện đại' },
    { name: 'ThS. Chu Ngân Hà', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'CN. Lê Phương Anh', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Đoàn Diễm Hương', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Trần Thị Trang', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Chử Hải Ly', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Nguyễn Thùy Linh', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Trần Thị Tâm Ngọc', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Trần Hương Giang', role: 'Giảng viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Phan Diệu Linh', role: 'Giảng viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Trần Hà Mi', role: 'Giảng viên', specialty: 'Piano hiện đại' },
  ];

  const collaborators = [
    { name: 'GS.TS.NGND. Trần Thị Thu Hà', role: 'Cố vấn chuyên môn', specialty: 'Piano cổ điển' },
    { name: 'ThS. Trần Thục Anh', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'NSUT. Hoàng Kim Dung', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'NSUT. Trần Thị Tuyết Minh', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS.NSUT. Hà Thị Ngọc Thoa', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Nguyễn Thị Lan', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Vũ Thị Phương Mai', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Nguyễn Thị Thanh Vân', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Ngô Lan Hương', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Trần Thanh Ly', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Nguyễn Phương Ly', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Lưu Đức Anh', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Nguyễn Đức Thịnh', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Nguyễn Lệ Thuyên Hà', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Lê Thị Yến', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Trần Thái Linh', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Trần Vân Cơ', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Dương Hồng Thạch', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'ThS. Hoàng Hồ Thu', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'ThS. Phạm Phương Hoa', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
    { name: 'CN. Trần Minh Anh', role: 'Cộng tác viên', specialty: 'Piano cổ điển' },
    { name: 'CN. Nguyễn Minh Thảo', role: 'Cộng tác viên', specialty: 'Piano hiện đại' },
  ];

  return (
    <div className="bg-gradient-to-br from-white to-blue-50 min-h-screen">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 flex flex-col md:flex-row items-center gap-8">
        <div className="flex-1 text-center md:text-left">
          <h1 className="text-5xl font-extrabold mb-4 text-blue-900 flex items-center justify-center md:justify-start gap-2">
            <span className="inline-block align-middle">🎹</span> Đội ngũ giảng viên
          </h1>
          <p className="text-xl text-blue-700 mb-6 font-medium flex items-center gap-2 justify-center md:justify-start">
            <Users className="w-7 h-7 text-blue-400" /> 24 giảng viên xuất sắc, tận tâm & sáng tạo
          </p>
          <div className="flex flex-wrap gap-4 justify-center md:justify-start">
            <span className="inline-flex items-center bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full font-semibold text-sm shadow"><Award className="w-5 h-5 mr-2" />Nhiều giải thưởng quốc tế</span>
            <span className="inline-flex items-center bg-pink-100 text-pink-800 px-4 py-2 rounded-full font-semibold text-sm shadow"><Music className="w-5 h-5 mr-2" />Đam mê âm nhạc</span>
          </div>
        </div>
        <div className="flex-1 flex justify-center">
          {/* Piano Illustration */}
          <div className="relative w-64 h-48 flex items-center justify-center">
            <img src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/vnam.piano.webp" alt="Piano Illustration" className="w-full h-full object-contain drop-shadow-lg" />
            {/* <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <Piano className="w-48 h-48 text-gray-300/50" />
            </div> */}
          </div>
        </div>
      </div>

      {/* Divider with musical notes */}
      <div className="flex justify-center my-8">
        <span className="text-3xl mx-2">🎶</span>
        <span className="text-3xl mx-2">🎵</span>
        <span className="text-3xl mx-2">🎼</span>
        <span className="text-3xl mx-2">🎹</span>
      </div>

      {/* Leadership Section */}
      <div className="max-w-5xl mx-auto mb-12">
        <h2 className="text-2xl font-bold mb-6 flex items-center gap-2 text-blue-900"><Star className="w-7 h-7 text-yellow-400" />Ban lãnh đạo đương nhiệm</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-2xl shadow-lg flex flex-col items-center text-center border-t-4 border-blue-400">
            <User className="w-12 h-12 text-blue-500 mb-2" />
            <h3 className="text-xl font-bold mb-1">TS. Đào Trọng Tuyên</h3>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-semibold mb-2">Trưởng khoa</span>
            <p className="text-gray-600 text-sm">Tiến sĩ âm nhạc, chuyên gia piano với nhiều năm kinh nghiệm giảng dạy và biểu diễn quốc tế.</p>
          </div>
          <div className="bg-white p-6 rounded-2xl shadow-lg flex flex-col items-center text-center border-t-4 border-pink-400">
            <User className="w-12 h-12 text-pink-500 mb-2" />
            <h3 className="text-xl font-bold mb-1">ThS. Nguyễn Trinh Hương</h3>
            <span className="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-xs font-semibold mb-2">Phó trưởng khoa</span>
            <p className="text-gray-600 text-sm">Thạc sĩ piano với chuyên môn sâu về kỹ thuật biểu diễn và phương pháp giảng dạy hiện đại.</p>
          </div>
          <div className="bg-white p-6 rounded-2xl shadow-lg flex flex-col items-center text-center border-t-4 border-yellow-400">
            <User className="w-12 h-12 text-yellow-500 mb-2" />
            <h3 className="text-xl font-bold mb-1">TS. Nguyễn Hoàng Phương</h3>
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs font-semibold mb-2">Phó trưởng khoa</span>
            <p className="text-gray-600 text-sm">Tiến sĩ piano, từng đạt giải nhất cuộc thi Tài năng trẻ dương cầm quốc tế năm 1999 tại Nhật Bản.</p>
          </div>
        </div>
      </div>

      {/* Divider with piano keys */}
      <div className="flex justify-center my-8">
        {Array.from({ length: 14 }).map((_, i) => (
          <span key={i} className={`inline-block w-3 h-8 mx-0.5 rounded ${i % 2 === 0 ? 'bg-black' : 'bg-white border border-gray-300'}`}></span>
        ))}
      </div>

      {/* Faculty and Collaborators Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
          {/* Faculty Section */}
          <div>
            <div 
              className="bg-white rounded-xl shadow-md overflow-hidden mb-6 cursor-pointer"
              onClick={() => toggleSection('faculty')}
            >
              <div className="p-6 flex justify-between items-center">
                <h2 className="text-2xl font-bold flex items-center gap-2 text-blue-900">
                  <GraduationCap className="w-7 h-7 text-blue-400" />
                  Các giảng viên hiện nay
                </h2>
                {expandedSection === 'faculty' ? (
                  <ChevronUp className="w-6 h-6 text-blue-500" />
                ) : (
                  <ChevronDown className="w-6 h-6 text-blue-500" />
                )}
              </div>
              
              {expandedSection === 'faculty' && (
                <div className="p-6 pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {facultyMembers.map((member, idx) => (
                      <div key={idx} className="bg-blue-50 rounded-lg p-4 hover:shadow-md transition-shadow duration-300">
                        <div className="flex items-start gap-3">
                          <div className="bg-blue-100 p-2 rounded-full">
                            <User className="w-5 h-5 text-blue-500" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-blue-900">{member.name}</h3>
                            <p className="text-sm text-blue-700">{member.role}</p>
                            {/* <p className="text-xs text-blue-600 mt-1">{member.specialty}</p> */}
                            <div className="flex gap-2 mt-2">
                              <button className="text-blue-500 hover:text-blue-700">
                                <Mail className="w-4 h-4" />
                              </button>
                              <button className="text-blue-500 hover:text-blue-700">
                                <Phone className="w-4 h-4" />
                              </button>
                              <button className="text-blue-500 hover:text-blue-700">
                                <BookOpen className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Collaborators Section */}
          <div>
            <div 
              className="bg-white rounded-xl shadow-md overflow-hidden mb-6 cursor-pointer"
              onClick={() => toggleSection('collaborators')}
            >
              <div className="p-6 flex justify-between items-center">
                <h2 className="text-2xl font-bold flex items-center gap-2 text-blue-900">
                  <Star className="w-7 h-7 text-yellow-400" />
                  Các Cộng tác viên
                </h2>
                {expandedSection === 'collaborators' ? (
                  <ChevronUp className="w-6 h-6 text-blue-500" />
                ) : (
                  <ChevronDown className="w-6 h-6 text-blue-500" />
                )}
              </div>
              
              {expandedSection === 'collaborators' && (
                <div className="p-6 pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {collaborators.map((member, idx) => (
                      <div key={idx} className="bg-yellow-50 rounded-lg p-4 hover:shadow-md transition-shadow duration-300">
                        <div className="flex items-start gap-3">
                          <div className="bg-yellow-100 p-2 rounded-full">
                            <User className="w-5 h-5 text-yellow-500" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-blue-900">{member.name}</h3>
                            <p className="text-sm text-blue-700">{member.role}</p>
                            {/* <p className="text-xs text-blue-600 mt-1">{member.specialty}</p> */}
                            <div className="flex gap-2 mt-2">
                              <button className="text-yellow-500 hover:text-yellow-700">
                                <Mail className="w-4 h-4" />
                              </button>
                              <button className="text-yellow-500 hover:text-yellow-700">
                                <Phone className="w-4 h-4" />
                              </button>
                              <button className="text-yellow-500 hover:text-yellow-700">
                                <BookOpen className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 