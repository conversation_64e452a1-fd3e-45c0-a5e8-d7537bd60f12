'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Star, ArrowRight, Upload, Spark<PERSON>, <PERSON>, Shirt, Zap } from 'lucide-react'

export default function MeModelHomePage() {
  const [email, setEmail] = useState('')

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Header */}
        <header className="relative z-10 px-4 py-6">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-purple-400" />
              <span className="text-2xl font-bold text-white">MeModel AI</span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/web/memodel/gallery" className="text-white hover:text-purple-300 transition-colors">
                Gallery
              </Link>
              <Link href="/web/memodel/photo-packs" className="text-white hover:text-purple-300 transition-colors">
                Photo Packs
              </Link>
              <Link href="/web/memodel/marketplace" className="text-white hover:text-purple-300 transition-colors">
                Avatar Marketplace
              </Link>
              <Link href="/web/memodel/pricing" className="text-white hover:text-purple-300 transition-colors">
                Pricing
              </Link>
              <Link href="/web/memodel/faq" className="text-white hover:text-purple-300 transition-colors">
                FAQ
              </Link>
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-full transition-colors">
                Sign In
              </button>
            </nav>
          </div>
        </header>

        {/* Hero Content */}
        <div className="relative z-10 px-4 py-16 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
              🔥 Fire your photographer
            </h1>
            
            <div className="text-xl md:text-2xl text-purple-200 mb-8 space-y-2">
              <div className="flex items-center justify-center space-x-2">
                <Upload className="h-6 w-6" />
                <span>Upload your selfies → Create an AI model of yourself</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Sparkles className="h-6 w-6" />
                <span>Or create 100% AI influencers to monetize them</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Shirt className="h-6 w-6" />
                <span>License &amp; use others&apos; avatars with creator revenue sharing</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Video className="h-6 w-6" />
                <span>Take 100% AI photos with yourself in any pose, place or action</span>
              </div>
            </div>

            {/* Email Signup */}
            <div className="max-w-md mx-auto mb-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-1 px-4 py-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder:text-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                />
                <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-lg font-semibold flex items-center space-x-2 transition-all">
                  <span>Start creating AI photos</span>
                  <ArrowRight className="h-5 w-5" />
                </button>
              </div>
              <p className="text-sm text-purple-200 mt-2">
                <Zap className="h-4 w-4 inline mr-1" />
                1,015,351 photos generated this month
              </p>
            </div>

            {/* Social Proof */}
            <div className="text-center mb-16">
              <p className="text-purple-200 mb-4">as seen on</p>
              <div className="flex items-center justify-center space-x-8 opacity-60">
                <div className="bg-white/10 px-4 py-2 rounded">TechCrunch</div>
                <div className="bg-white/10 px-4 py-2 rounded">The Verge</div>
                <div className="bg-white/10 px-4 py-2 rounded">Wired</div>
                <div className="bg-white/10 px-4 py-2 rounded">Forbes</div>
              </div>
            </div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute top-40 left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 px-4 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            The most detailed AI photo generator ever with next-level realism
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-gradient-to-br from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Upload className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Upload &amp; Train</h3>
              <p className="text-purple-200">Upload 10-20 selfies and we&apos;ll create your AI model in 1-2 hours</p>
            </div>
            
            <div className="text-center">
              <div className="bg-gradient-to-br from-blue-600 to-cyan-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Generate Photos</h3>
              <p className="text-purple-200">Create unlimited AI photos in any pose, place, or style in ~20 seconds</p>
            </div>
            
            <div className="text-center">
              <div className="bg-gradient-to-br from-green-600 to-emerald-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Video className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Create Videos</h3>
              <p className="text-purple-200">Turn any AI photo into a video with talking scripts and animations</p>
            </div>
          </div>
        </div>
      </div>

      {/* Avatar Marketplace Section */}
      <div className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-8">
            Avatar Marketplace: Share &amp; Earn
          </h2>
          <p className="text-xl text-purple-200 text-center mb-16 max-w-3xl mx-auto">
            License your avatar to others or use verified creator avatars with transparent revenue sharing
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-gradient-to-br from-yellow-600 to-orange-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shirt className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">List Your Avatar</h3>
              <p className="text-purple-200">Upload your model to the marketplace and set licensing terms &amp; pricing</p>
            </div>
            
            <div className="text-center">
              <div className="bg-gradient-to-br from-emerald-600 to-teal-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Browse Avatars</h3>
              <p className="text-purple-200">Discover verified creator avatars and license them for your projects</p>
            </div>
            
            <div className="text-center">
              <div className="bg-gradient-to-br from-pink-600 to-rose-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Secure Payments</h3>
              <p className="text-purple-200">Automated revenue sharing with transparent tracking &amp; instant payouts</p>
            </div>
            
            <div className="text-center">
              <div className="bg-gradient-to-br from-indigo-600 to-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Upload className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Creator Protection</h3>
              <p className="text-purple-200">Owner approval required for all outputs, consent verification &amp; usage rights management</p>
            </div>
          </div>

          {/* Revenue Sharing Info */}
          <div className="mt-16 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-4">Fair Revenue Sharing</h3>
              <p className="text-purple-200 max-w-2xl mx-auto">
                Creators earn 70% of licensing fees when others use their avatars. All outputs require owner approval before payment. If rejected, credits are fully refunded.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-green-400 mb-2">70%</div>
                <div className="text-white font-semibold mb-1">Creator Share</div>
                <div className="text-sm text-purple-200">Of every licensing fee</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400 mb-2">98%</div>
                <div className="text-white font-semibold mb-1">Approval Rate</div>
                <div className="text-sm text-purple-200">Owners approve outputs</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">24hrs</div>
                <div className="text-white font-semibold mb-1">Review Time</div>
                <div className="text-sm text-purple-200">Fast approval process</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Photo Examples Grid */}
      <div className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Upload your selfies and start taking stunning AI photos now
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <div key={i} className="aspect-square bg-gradient-to-br from-purple-600/20 to-pink-600/20 rounded-lg border border-white/10 flex items-center justify-center">
                <div className="text-white/60 text-sm">AI Photo {i}</div>
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 text-purple-300 mb-4">
              <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
              <span>MEMODEL AI GENERATED</span>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="py-20 px-4 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Customers can&apos;t stop raving about the photos they took
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-white mb-4">
                &ldquo;MeModel AI is just fantastic! I take amazing photos of my family and friends. As a photographer I use it to test ideas before creating a real photoshoot. I strongly recommend!&rdquo;
              </p>
              <p className="text-purple-300 font-semibold">Sarah ✅ VERIFIED PURCHASE</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-white mb-4">
                &ldquo;Cool AI tool for image generation! I could create a lot of truly amazing pictures in different locations with different outfits! All my friends were surprised and loved my pictures!&rdquo;
              </p>
              <p className="text-purple-300 font-semibold">Michael ✅ VERIFIED PURCHASE</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-white mb-4">
                &ldquo;Good input = good output. Very fun! Took me some effort to get the models to feel accurate but once I got the right input it was amazing. MeModel AI was very responsive to my questions.&rdquo;
              </p>
              <p className="text-purple-300 font-semibold">Jordan ✅ VERIFIED PURCHASE</p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 px-4 text-center">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-8">
            Ready to create your AI model?
          </h2>
          <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-12 py-4 rounded-full text-xl font-semibold flex items-center space-x-3 mx-auto transition-all">
            <span>Start taking AI photos now</span>
            <ArrowRight className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-sm py-12 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Sparkles className="h-6 w-6 text-purple-400" />
                <span className="text-xl font-bold text-white">MeModel AI</span>
              </div>
              <p className="text-purple-200 text-sm">
                Create stunning AI photos and videos of yourself with the power of artificial intelligence.
              </p>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Product</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/ai-photography" className="block text-purple-200 hover:text-white text-sm">AI Photography</Link>
                <Link href="/web/memodel/marketplace" className="block text-purple-200 hover:text-white text-sm">Avatar Marketplace</Link>
                <Link href="/web/memodel/instant-camera" className="block text-purple-200 hover:text-white text-sm">Instant Camera</Link>
                <Link href="/web/memodel/ai-selfies" className="block text-purple-200 hover:text-white text-sm">AI Selfies</Link>
                <Link href="/web/memodel/video-generator" className="block text-purple-200 hover:text-white text-sm">Video Generator</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Support</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/faq" className="block text-purple-200 hover:text-white text-sm">FAQ</Link>
                <Link href="/web/memodel/help" className="block text-purple-200 hover:text-white text-sm">Help Center</Link>
                <Link href="/web/memodel/contact" className="block text-purple-200 hover:text-white text-sm">Contact</Link>
                <Link href="/web/memodel/billing" className="block text-purple-200 hover:text-white text-sm">Billing</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Legal</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/privacy" className="block text-purple-200 hover:text-white text-sm">Privacy Policy</Link>
                <Link href="/web/memodel/terms" className="block text-purple-200 hover:text-white text-sm">Terms of Service</Link>
                <Link href="/web/memodel/license" className="block text-purple-200 hover:text-white text-sm">Commercial License</Link>
              </div>
            </div>
          </div>
          
          <div className="border-t border-white/10 mt-8 pt-8 text-center">
            <p className="text-purple-200 text-sm">
              © 2025 MeModel AI. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
