'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Shield, Eye, Lock, Database, Users, FileText, Download, Mail } from 'lucide-react';
import Link from 'next/link';

export default function PrivacyPage() {
  const sections = [
    {
      id: 'information-collection',
      title: 'Information We Collect',
      icon: Database,
      content: [
        'Account information: Email address, name, and profile details',
        'Photos you upload: Training images and generated AI photos',
        'Usage data: How you interact with our service and features',
        'Payment information: Billing details processed by secure payment providers',
        'Technical data: IP address, browser type, and device information'
      ]
    },
    {
      id: 'information-use',
      title: 'How We Use Your Information',
      icon: Eye,
      content: [
        'Create and train your personalized AI models',
        'Generate AI photos based on your requests',
        'Provide customer support and respond to inquiries',
        'Process payments and manage subscriptions',
        'Improve our service and develop new features',
        'Send important updates and notifications'
      ]
    },
    {
      id: 'data-protection',
      title: 'Data Protection & Security',
      icon: Shield,
      content: [
        'All photos are encrypted during upload and storage',
        'AI models are created in secure, isolated environments',
        'We use industry-standard security measures and protocols',
        'Regular security audits and vulnerability assessments',
        'Access to your data is strictly limited to authorized personnel',
        'Original training photos are automatically deleted after 30 days'
      ]
    },
    {
      id: 'data-sharing',
      title: 'Data Sharing & Disclosure',
      icon: Users,
      content: [
        'We never sell your personal information to third parties',
        'Photos are never shared without your explicit consent',
        'Limited data sharing with service providers (payment processing, hosting)',
        'Legal compliance: We may disclose data when required by law',
        'Business transfers: Data may be transferred if we are acquired',
        'Anonymous analytics: We may share aggregated, non-personal statistics'
      ]
    }
  ];

  const rights = [
    'Access your personal data and download your information',
    'Correct inaccurate or incomplete information',
    'Delete your account and associated data',
    'Restrict processing of your data',
    'Data portability - export your data in a standard format',
    'Object to certain types of data processing',
    'Withdraw consent for data processing at any time'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/web/memodel" className="text-2xl font-bold text-white">
              MeModel AI
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/web/memodel" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <Link href="/web/memodel/photo-packs" className="text-gray-300 hover:text-white transition-colors">
                Photo Packs
              </Link>
              <Link href="/web/memodel/gallery" className="text-gray-300 hover:text-white transition-colors">
                Gallery
              </Link>
              <Link href="/web/memodel/pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
            </nav>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              Get Started
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Shield className="h-8 w-8 text-purple-400" />
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Privacy Policy
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-4">
            Your privacy is important to us. This policy explains how we collect, use, and protect your personal information when you use MeModel AI.
          </p>
          <p className="text-sm text-gray-400">
            Last updated: December 2024
          </p>
        </div>

        {/* Quick Summary */}
        <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <h2 className="text-2xl font-bold text-white mb-4">Quick Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">What We Collect</h3>
              <p className="text-gray-300 text-sm">Photos you upload, account details, and usage data to provide our AI photo generation service.</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">How We Protect It</h3>
              <p className="text-gray-300 text-sm">End-to-end encryption, secure storage, and automatic deletion of training photos after 30 days.</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Your Control</h3>
              <p className="text-gray-300 text-sm">You can access, modify, or delete your data at any time through your account settings.</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">No Selling</h3>
              <p className="text-gray-300 text-sm">We never sell your personal information or photos to third parties. Your data stays private.</p>
            </div>
          </div>
        </div>

        {/* Main Sections */}
        <div className="space-y-8 mb-12">
          {sections.map((section, index) => (
            <div key={index} id={section.id} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
              <div className="flex items-center gap-3 mb-6">
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl">
                  <section.icon className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white">{section.title}</h2>
              </div>
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start gap-3 text-gray-300">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Your Rights */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl">
              <Lock className="h-6 w-6 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white">Your Privacy Rights</h2>
          </div>
          <p className="text-gray-300 mb-6">
            Under applicable privacy laws, you have the following rights regarding your personal data:
          </p>
          <ul className="space-y-3">
            {rights.map((right, index) => (
              <li key={index} className="flex items-start gap-3 text-gray-300">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0" />
                <span>{right}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Data Retention */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white">Data Retention</h2>
          </div>
          <div className="space-y-4 text-gray-300">
            <p>
              <strong className="text-white">Training Photos:</strong> Automatically deleted 30 days after model creation for your privacy and security.
            </p>
            <p>
              <strong className="text-white">Generated Photos:</strong> Stored indefinitely while your account is active. You can delete them anytime.
            </p>
            <p>
              <strong className="text-white">Account Data:</strong> Retained until you request deletion or close your account.
            </p>
            <p>
              <strong className="text-white">Usage Analytics:</strong> Anonymized data may be retained for service improvement purposes.
            </p>
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
          <Mail className="h-12 w-12 text-purple-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-4">Questions About Privacy?</h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            If you have any questions about this Privacy Policy or how we handle your data, please don&apos;t hesitate to contact us.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/web/memodel/contact">
              <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                <Mail className="mr-2 h-4 w-4" />
                Contact Privacy Team
              </Button>
            </Link>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Download className="mr-2 h-4 w-4" />
              Download Policy (PDF)
            </Button>
          </div>
          <p className="text-sm text-gray-400 mt-6">
            Email us <NAME_EMAIL> for privacy-related inquiries
          </p>
        </div>

        {/* Navigation */}
        <div className="mt-12 text-center">
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/web/memodel/terms" className="text-purple-300 hover:text-purple-200 transition-colors">
              Terms of Service
            </Link>
            <span className="text-gray-500">•</span>
            <Link href="/web/memodel/cookies" className="text-purple-300 hover:text-purple-200 transition-colors">
              Cookie Policy
            </Link>
            <span className="text-gray-500">•</span>
            <Link href="/web/memodel/security" className="text-purple-300 hover:text-purple-200 transition-colors">
              Security
            </Link>
            <span className="text-gray-500">•</span>
            <Link href="/web/memodel/contact" className="text-purple-300 hover:text-purple-200 transition-colors">
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
