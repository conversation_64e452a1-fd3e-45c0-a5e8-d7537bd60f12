'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Star, Search, TrendingUp, Users, DollarSign, Shield, Eye, Zap, Award } from 'lucide-react';
import Link from 'next/link';

export default function MarketplacePage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('popular');

  const categories = [
    { id: 'all', label: 'All Avatars', count: 2847 },
    { id: 'influencers', label: 'Influencers', count: 456 },
    { id: 'models', label: 'Models', count: 732 },
    { id: 'actors', label: 'Actors', count: 189 },
    { id: 'creators', label: 'Content Creators', count: 891 },
    { id: 'professionals', label: 'Professionals', count: 324 },
    { id: 'characters', label: 'Characters', count: 255 }
  ];

  const avatars = [
    {
      id: 1,
      name: 'Emma Lifestyle',
      creator: 'EmmaCreates',
      category: 'Influencers',
      price: 25,
      usage: 12500,
      rating: 4.9,
      reviews: 234,
      verified: true,
      featured: true,
      revenue: 8750,
      description: 'Lifestyle influencer avatar perfect for fashion, travel, and everyday content',
      tags: ['Fashion', 'Travel', 'Lifestyle'],
      thumbnail: '/avatars/emma-preview.jpg'
    },
    {
      id: 2,
      name: 'Alex Fitness Pro',
      creator: 'FitWithAlex',
      category: 'Models',
      price: 35,
      usage: 8900,
      rating: 4.8,
      reviews: 156,
      verified: true,
      featured: false,
      revenue: 6230,
      description: 'Athletic model avatar for fitness, sports, and wellness content',
      tags: ['Fitness', 'Sports', 'Wellness'],
      thumbnail: '/avatars/alex-preview.jpg'
    },
    {
      id: 3,
      name: 'Maya Professional',
      creator: 'BizMaya',
      category: 'Professionals',
      price: 40,
      usage: 5600,
      rating: 4.95,
      reviews: 89,
      verified: true,
      featured: true,
      revenue: 3920,
      description: 'Business professional avatar for corporate and executive content',
      tags: ['Business', 'Corporate', 'Executive'],
      thumbnail: '/avatars/maya-preview.jpg'
    },
    {
      id: 4,
      name: 'Jake Adventure',
      creator: 'AdventureJake',
      category: 'Creators',
      price: 30,
      usage: 7200,
      rating: 4.7,
      reviews: 178,
      verified: false,
      featured: false,
      revenue: 5040,
      description: 'Adventure creator avatar for outdoor and extreme sports content',
      tags: ['Adventure', 'Outdoors', 'Travel'],
      thumbnail: '/avatars/jake-preview.jpg'
    },
    {
      id: 5,
      name: 'Sofia Elegant',
      creator: 'ElegantSofia',
      category: 'Models',
      price: 45,
      usage: 4300,
      rating: 4.85,
      reviews: 112,
      verified: true,
      featured: true,
      revenue: 3010,
      description: 'Elegant fashion model avatar for luxury and high-end content',
      tags: ['Fashion', 'Luxury', 'Elegant'],
      thumbnail: '/avatars/sofia-preview.jpg'
    },
    {
      id: 6,
      name: 'Ryan Tech',
      creator: 'TechRyan',
      category: 'Professionals',
      price: 28,
      usage: 6800,
      rating: 4.6,
      reviews: 145,
      verified: true,
      featured: false,
      revenue: 4760,
      description: 'Tech professional avatar for startup and innovation content',
      tags: ['Technology', 'Startup', 'Innovation'],
      thumbnail: '/avatars/ryan-preview.jpg'
    }
  ];

  const topCreators = [
    { name: 'EmmaCreates', earnings: '$15,420', avatars: 3, rating: 4.9 },
    { name: 'FitWithAlex', earnings: '$12,890', avatars: 2, rating: 4.8 },
    { name: 'BizMaya', earnings: '$8,950', avatars: 1, rating: 4.95 },
    { name: 'TechRyan', earnings: '$7,640', avatars: 2, rating: 4.7 }
  ];

  const filteredAvatars = avatars.filter(avatar => {
    if (selectedCategory !== 'all' && avatar.category.toLowerCase() !== selectedCategory) return false;
    if (searchQuery && !avatar.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !avatar.creator.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !avatar.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))) return false;
    return true;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/web/memodel" className="text-2xl font-bold text-white">
              MeModel AI
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/web/memodel" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <Link href="/web/memodel/gallery" className="text-gray-300 hover:text-white transition-colors">
                Gallery
              </Link>
              <Link href="/web/memodel/marketplace" className="text-white font-semibold">
                Avatar Marketplace
              </Link>
              <Link href="/web/memodel/pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
            </nav>
            <div className="flex gap-3">
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                List Your Avatar
              </Button>
              <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                Sign In
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Users className="h-8 w-8 text-purple-400" />
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Avatar Marketplace
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Discover and license verified creator avatars, or monetize your own. Fair revenue sharing, transparent tracking, and secure transactions.
          </p>
          <div className="flex items-center justify-center gap-8 text-sm text-gray-400">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>2,847 Avatars</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              <span>$125K+ Creator Earnings</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>Verified &amp; Protected</span>
            </div>
          </div>
        </div>          {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div className="bg-gradient-to-r from-green-600/20 to-emerald-600/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10 text-center">
            <DollarSign className="h-8 w-8 text-green-400 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">70%</div>
            <div className="text-sm text-gray-300">Creator Revenue Share</div>
          </div>
          <div className="bg-gradient-to-r from-blue-600/20 to-cyan-600/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10 text-center">
            <Shield className="h-8 w-8 text-blue-400 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">98%</div>
            <div className="text-sm text-gray-300">Approval Rate</div>
          </div>
          <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10 text-center">
            <Award className="h-8 w-8 text-purple-400 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">4.8</div>
            <div className="text-sm text-gray-300">Average Rating</div>
          </div>
          <div className="bg-gradient-to-r from-orange-600/20 to-red-600/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10 text-center">
            <Zap className="h-8 w-8 text-orange-400 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">24/7</div>
            <div className="text-sm text-gray-300">Usage Tracking</div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                      : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  {category.label} ({category.count})
                </button>
              ))}
            </div>
            <div className="flex gap-3 w-full lg:w-auto">
              <div className="relative flex-1 lg:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search avatars, creators, tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 pl-10"
                />
              </div>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm"
              >
                <option value="popular">Most Popular</option>
                <option value="newest">Newest</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
              </select>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Avatar Grid */}
          <div className="lg:col-span-3">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredAvatars.map((avatar) => (
                <div key={avatar.id} className="group bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/10 hover:bg-white/15 transition-all duration-300">
                  <div className="aspect-square bg-gradient-to-br from-purple-400/20 to-pink-400/20 relative">
                    <div className="absolute inset-0 flex items-center justify-center text-white/60">
                      Avatar Preview
                    </div>
                    
                    {avatar.featured && (
                      <div className="absolute top-3 left-3">
                        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                          <Star className="h-3 w-3 fill-current" />
                          Featured
                        </div>
                      </div>
                    )}

                    {avatar.verified && (
                      <div className="absolute top-3 right-3">
                        <div className="bg-blue-600 p-1.5 rounded-full">
                          <Shield className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    )}

                    <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button size="sm" className="bg-black/70 backdrop-blur-sm hover:bg-black/90">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">{avatar.name}</h3>
                        <p className="text-sm text-gray-400">by {avatar.creator}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-400">${avatar.price}</div>
                        <div className="text-xs text-gray-400">per use</div>
                      </div>
                    </div>

                    <p className="text-sm text-gray-300 mb-4 line-clamp-2">{avatar.description}</p>

                    <div className="flex flex-wrap gap-1 mb-4">
                      {avatar.tags.map((tag, index) => (
                        <span key={index} className="text-xs bg-purple-600/20 text-purple-300 px-2 py-1 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span>{avatar.rating}</span>
                        <span>({avatar.reviews})</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-4 w-4" />
                        <span>{avatar.usage.toLocaleString()} uses</span>
                      </div>
                    </div>

                    <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                      License Avatar
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* How It Works - Approval Process */}
            <div className="bg-gradient-to-r from-blue-600/20 to-cyan-600/20 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">How Avatar Licensing Works</h3>
              <div className="grid md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 text-white font-bold">1</div>
                  <h4 className="text-white font-semibold mb-2">Generate Photo</h4>
                  <p className="text-sm text-gray-300">Create your AI photo using the licensed avatar</p>
                </div>
                <div className="text-center">
                  <div className="bg-yellow-600 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 text-white font-bold">2</div>
                  <h4 className="text-white font-semibold mb-2">Owner Reviews</h4>
                  <p className="text-sm text-gray-300">Avatar owner approves the final output within 24 hours</p>
                </div>
                <div className="text-center">
                  <div className="bg-green-600 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 text-white font-bold">3</div>
                  <h4 className="text-white font-semibold mb-2">Payment Processed</h4>
                  <p className="text-sm text-gray-300">Once approved, payment is split and photo is yours</p>
                </div>
                <div className="text-center">
                  <div className="bg-purple-600 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 text-white font-bold">✓</div>
                  <h4 className="text-white font-semibold mb-2">Credits Returned</h4>
                  <p className="text-sm text-gray-300">If rejected, your credits are fully refunded</p>
                </div>
              </div>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-300">
                  <Shield className="h-4 w-4 inline mr-1 text-green-400" />
                  98% approval rate • No payment until approved • Full refund if rejected
                </p>
              </div>
            </div>

            {/* Load More */}
            <div className="text-center mt-12">
              <Button className="bg-white/10 hover:bg-white/20 text-white border border-white/20">
                Load More Avatars
              </Button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Top Creators */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-400" />
                Top Creators
              </h3>
              <div className="space-y-4">
                {topCreators.map((creator, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-white text-sm">{creator.name}</div>
                      <div className="text-xs text-gray-400">{creator.avatars} avatars</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-green-400">{creator.earnings}</div>
                      <div className="flex items-center gap-1 text-xs text-gray-400">
                        <Star className="h-3 w-3 text-yellow-400 fill-current" />
                        {creator.rating}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Become a Creator */}
            <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-2">Become a Creator</h3>
              <p className="text-sm text-gray-300 mb-4">
                Share your avatar and earn 70% of every licensing fee. Full analytics and monthly payouts.
              </p>
              <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700" size="sm">
                List Your Avatar
              </Button>
            </div>

            {/* Safety & Trust */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-400" />
                Safety &amp; Trust
              </h3>
              <div className="space-y-3 text-sm text-gray-300">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                  <span>Owner approval required for all outputs</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                  <span>No payment until content is approved</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                  <span>Full credit refund if rejected</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                  <span>24-hour approval window</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                  <span>Content moderation &amp; usage rights</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                  <span>Secure payments &amp; data protection</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
