'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON>les, ChevronDown, ChevronUp, Search } from 'lucide-react'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'Generate AI photos and videos of yourself',
    answer: 'Creating realistic AI-generated photos of yourself used to require serious technical chops: GPUs, Python scripts, and days of setup. Now, thanks to platforms like MeModel AI, it&apos;s as simple as uploading some selfies and in less than a minute later you have your own highly photorealistic AI photos indistinguishable from real life.',
    category: 'Getting Started'
  },
  {
    id: '2',
    question: 'Turn images into stunning AI videos with MeModel AI',
    answer: 'With AI-generated video technology rapidly evolving, it&apos;s now possible to turn a single image into a cinematic video clip in seconds, with no editing skills. Whether you&apos;re a content creator, marketer, or just experimenting, MeModel AI makes the process fast, easy, and insanely fun.',
    category: 'Video Generation'
  },
  {
    id: '3',
    question: 'Uncrop & expand images with MeModel AI&apos;s Zoom Out feature',
    answer: 'Ever wish you could zoom out of a photo that was shot too tight or extend the background beyond the frame? With MeModel AI&apos;s Zoom Out feature, you can do exactly that. It uses generative AI to uncrop and expand your images realistically, filling in extra space that looks like it was always there.',
    category: 'Photo Editing'
  },
  {
    id: '4',
    question: 'How to create AI Thumbnails for YouTube with MeModel AI',
    answer: 'Want MrBeast-style thumbnails without hiring a designer? With MeModel AI, you can generate custom YouTube thumbnails in minutes featuring yourself anywhere using AI. Step 1: Create or Select your model. Step 2: Choose the thumbnail photo pack. Step 3: Generate and download your thumbnails.',
    category: 'Content Creation'
  },
  {
    id: '5',
    question: 'Is MeModel AI free?',
    answer: 'Every Pro, Premium and Ultra subscription to MeModel AI now gives you a big set of free photos for every new model you create! That means thousands of free photos for Ultra plan subscribers. If you become a yearly subscriber, you get 6+ months free.',
    category: 'Pricing'
  },
  {
    id: '6',
    question: 'What type of photos should I upload for creating an AI model?',
    answer: 'We recommend uploading photos with high variety, a mix of close-up selfies and full body shots in a variety of places, angles, clothes and expressions. Do not upload photos with low variety, group photos, other people, sunglasses, hats, photos where your face is cut off or not visible.',
    category: 'Getting Started'
  },
  {
    id: '7',
    question: 'How does MeModel AI&apos;s AI Photo & Video Generator work?',
    answer: 'MeModel AI lets you upload selfies, create AI models and then generate AI photos and videos with them. We teach the AI how you look and then it&apos;s able to generate photorealistic images and videos of you.',
    category: 'How It Works'
  },
  {
    id: '8',
    question: 'What AI model do you use?',
    answer: 'MeModel AI uses its own generation pipeline which is trained for high photorealism. The main generation model we use now is Flux.',
    category: 'Technical'
  },
  {
    id: '9',
    question: 'How long does it take to take an AI photo?',
    answer: 'Very fast. Right now it&apos;s about 15 seconds! And you can take many photos in parallel especially on the higher plans.',
    category: 'Performance'
  },
  {
    id: '10',
    question: 'How much does MeModel AI cost? What are the subscription plans?',
    answer: 'You can check current pricing on our pricing page. You get 6+ months free if you pay yearly. We offer Starter ($15/month), Pro ($39/month), Premium ($59/month), and Ultra ($119/month) plans.',
    category: 'Pricing'
  },
  {
    id: '11',
    question: 'How much will my AI photos look like me?',
    answer: 'Yes. MeModel AI has the highest resemblance/likeness of any AI apps right now.',
    category: 'Quality'
  },
  {
    id: '12',
    question: 'How long will it take to create my AI model?',
    answer: 'Premium and Ultra subscribers now have extremely fast training at just 90 seconds. For lower plans it can take 30 minutes to 2 hours.',
    category: 'Performance'
  },
  {
    id: '13',
    question: 'What file formats of photos do you accept for creating an AI model?',
    answer: 'We accept JPG, PNG, WebP and AVIF files only.',
    category: 'Technical'
  },
  {
    id: '14',
    question: 'Will my AI photos have artifacts?',
    answer: 'Sometimes yes! Depending on the quality of the uploaded photos, and the type of photos you take, you might see some AI artifacts. With the new Flux model we use, generally stuff like strange anatomy, limbs and especially hands (!) is mostly fixed now.',
    category: 'Quality'
  },
  {
    id: '15',
    question: 'Can I delete my data?',
    answer: 'Yes, you can delete your photos in the gallery: hover over a photo, and click the trash bin in the top right to delete it. Deleted photos are permanently deleted after 30 days.',
    category: 'Privacy'
  },
  {
    id: '16',
    question: 'Can I get a refund?',
    answer: 'Yes. Please login, then tap the top left MeModel AI logo, and in the dropdown select [ Request a refund ].',
    category: 'Billing'
  },
  {
    id: '17',
    question: 'How do I cancel my subscription?',
    answer: 'You can cancel your subscription at any time. Go to the top of MeModel AI when you are logged in, tap the logo and in the drop down tap Billing, you will be redirected to Stripe&apos;s billing portal where you can download invoices, switch plans and cancel your subscription.',
    category: 'Billing'
  },
  {
    id: '18',
    question: 'Can I use MeModel AI photos commercially?',
    answer: 'Yes, the Pro, Premium and Ultra plans give you a commercial use license. The Starter plan is only for personal use.',
    category: 'Licensing'
  }
]

const categories = ['All', 'Getting Started', 'How It Works', 'Pricing', 'Technical', 'Performance', 'Quality', 'Video Generation', 'Photo Editing', 'Content Creation', 'Privacy', 'Billing', 'Licensing']

export default function FAQPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [openItems, setOpenItems] = useState<string[]>([])

  const filteredFAQs = faqData.filter(faq => {
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/web/memodel" className="flex items-center space-x-2">
            <Sparkles className="h-8 w-8 text-purple-400" />
            <span className="text-2xl font-bold text-white">MeModel AI</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/web/memodel/gallery" className="text-white hover:text-purple-300 transition-colors">
              Gallery
            </Link>
            <Link href="/web/memodel/photo-packs" className="text-white hover:text-purple-300 transition-colors">
              Photo Packs
            </Link>
            <Link href="/web/memodel/pricing" className="text-white hover:text-purple-300 transition-colors">
              Pricing
            </Link>
            <Link href="/web/memodel/faq" className="text-purple-400 font-semibold">
              FAQ
            </Link>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-full transition-colors">
              Sign In
            </button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <div className="px-4 py-16 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            AI Image Generation on MeModel AI
          </h1>
          <p className="text-xl text-purple-200 mb-8">
            Explore our comprehensive guides on MeModel AI&apos;s powerful image generation capabilities. Learn how to master our tools, fine-tune your settings, and create breathtaking photorealistic AI-generated photos and videos with confidence.
          </p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-purple-400" />
              <input
                type="text"
                placeholder="Search FAQ..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder:text-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-purple-600 text-white'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Content */}
      <div className="px-4 pb-20">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {filteredFAQs.map((faq) => (
              <div
                key={faq.id}
                className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden"
              >
                <button
                  onClick={() => toggleItem(faq.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-1">
                      <h3 className="text-lg font-semibold text-white">{faq.question}</h3>
                      <span className="bg-purple-600/20 text-purple-300 text-xs px-2 py-1 rounded-full">
                        {faq.category}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    {openItems.includes(faq.id) ? (
                      <ChevronUp className="h-5 w-5 text-purple-400" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-purple-400" />
                    )}
                  </div>
                </button>
                
                {openItems.includes(faq.id) && (
                  <div className="px-6 pb-4">
                    <div className="border-t border-white/10 pt-4">
                      <p className="text-purple-200 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {filteredFAQs.length === 0 && (
            <div className="text-center py-12">
              <p className="text-purple-200 text-lg">
                No FAQ items found matching your search criteria.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Contact Section */}
      <div className="py-20 px-4 bg-white/5 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-8">
            Still have questions?
          </h2>
          <p className="text-xl text-purple-200 mb-8">
            Can&apos;t find the answer you&apos;re looking for? Please chat with our friendly team.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/web/memodel/contact"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-lg font-semibold transition-all"
            >
              Contact Support
            </Link>
            <Link
              href="/web/memodel/help"
              className="bg-white/10 hover:bg-white/20 text-white px-8 py-3 rounded-lg font-semibold border border-white/20 transition-all"
            >
              Help Center
            </Link>
          </div>
        </div>
      </div>

      {/* Popular Topics */}
      <div className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Popular topics
          </h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <Link
              href="#"
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/15 transition-all group"
            >
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300">
                Getting Started Guide
              </h3>
              <p className="text-purple-200">
                Learn how to upload photos, create your first AI model, and generate your first photos.
              </p>
            </Link>
            
            <Link
              href="#"
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/15 transition-all group"
            >
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300">
                Photo Quality Tips
              </h3>
              <p className="text-purple-200">
                Best practices for uploading photos to get the highest quality AI-generated results.
              </p>
            </Link>
            
            <Link
              href="#"
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/15 transition-all group"
            >
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300">
                Billing & Subscriptions
              </h3>
              <p className="text-purple-200">
                Everything you need to know about plans, billing, and managing your subscription.
              </p>
            </Link>
            
            <Link
              href="#"
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/15 transition-all group"
            >
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300">
                Commercial Usage
              </h3>
              <p className="text-purple-200">
                Learn about commercial licenses and how to use your AI photos for business purposes.
              </p>
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-sm py-12 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Sparkles className="h-6 w-6 text-purple-400" />
            <span className="text-xl font-bold text-white">MeModel AI</span>
          </div>
          <p className="text-purple-200 text-sm mb-8">
            Create stunning AI photos and videos of yourself with the power of artificial intelligence.
          </p>
          
          <div className="grid md:grid-cols-4 gap-8 text-left">
            <div>
              <h4 className="text-white font-semibold mb-4">Getting Started</h4>
              <div className="space-y-2">
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Upload Photos</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Create AI Model</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Generate Photos</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Best Practices</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Features</h4>
              <div className="space-y-2">
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">AI Photography</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Video Generation</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Photo Editing</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Photo Packs</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Support</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/help" className="block text-purple-200 hover:text-white text-sm">Help Center</Link>
                <Link href="/web/memodel/contact" className="block text-purple-200 hover:text-white text-sm">Contact</Link>
                <Link href="/web/memodel/billing" className="block text-purple-200 hover:text-white text-sm">Billing</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Report Bug</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Legal</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/privacy" className="block text-purple-200 hover:text-white text-sm">Privacy Policy</Link>
                <Link href="/web/memodel/terms" className="block text-purple-200 hover:text-white text-sm">Terms of Service</Link>
                <Link href="/web/memodel/license" className="block text-purple-200 hover:text-white text-sm">Commercial License</Link>
                <Link href="#" className="block text-purple-200 hover:text-white text-sm">Refund Policy</Link>
              </div>
            </div>
          </div>
          
          <div className="border-t border-white/10 mt-8 pt-8 text-center">
            <p className="text-purple-200 text-sm">
              © 2025 MeModel AI. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
