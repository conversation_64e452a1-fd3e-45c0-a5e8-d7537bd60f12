'use client';

import { But<PERSON> } from '@/components/ui/button';
import { BookOpen, Search, MessageCircle, Video, FileText, ExternalLink, ArrowRight, HelpCircle, Users, Lightbulb, Zap } from 'lucide-react';
import Link from 'next/link';

export default function HelpPage() {
  const helpCategories = [
    {
      icon: Zap,
      title: 'Getting Started',
      description: 'Learn the basics of creating your first AI model and generating photos',
      articles: [
        'How to create your first AI model',
        'Best practices for uploading photos',
        'Understanding photo quality settings',
        'Your first AI photo generation'
      ],
      color: 'from-green-500 to-emerald-600'
    },
    {
      icon: Users,
      title: 'Account & Billing',
      description: 'Manage your subscription, billing, and account settings',
      articles: [
        'Managing your subscription',
        'Understanding photo credits',
        'Billing and payment methods',
        'Account security settings'
      ],
      color: 'from-blue-500 to-cyan-600'
    },
    {
      icon: Lightbulb,
      title: 'Photo Generation',
      description: 'Tips and tricks for creating amazing AI photos',
      articles: [
        'Choosing the right photo pack',
        'Writing effective custom prompts',
        'Understanding likeness settings',
        'Photo editing and enhancement'
      ],
      color: 'from-purple-500 to-violet-600'
    },
    {
      icon: HelpCircle,
      title: 'Troubleshooting',
      description: 'Solve common issues and technical problems',
      articles: [
        'Why my photos look different',
        'Model creation failed',
        'Photo generation is slow',
        'Upload errors and solutions'
      ],
      color: 'from-orange-500 to-red-600'
    }
  ];

  const quickActions = [
    {
      icon: MessageCircle,
      title: 'Live Chat Support',
      description: 'Get instant help from our support team',
      action: 'Start Chat',
      available: true
    },
    {
      icon: Video,
      title: 'Video Tutorials',
      description: 'Watch step-by-step guides and tutorials',
      action: 'Watch Now',
      available: true
    },
    {
      icon: FileText,
      title: 'API Documentation',
      description: 'Technical documentation for developers',
      action: 'View Docs',
      available: true
    },
    {
      icon: Users,
      title: 'Community Forum',
      description: 'Connect with other MeModel AI users',
      action: 'Join Forum',
      available: false
    }
  ];

  const popularArticles = [
    {
      title: 'How to get the best AI photo results',
      category: 'Photo Generation',
      readTime: '5 min read',
      views: '12.5K'
    },
    {
      title: 'Understanding photo pack categories',
      category: 'Getting Started',
      readTime: '3 min read',
      views: '8.2K'
    },
    {
      title: 'Subscription plans comparison',
      category: 'Account & Billing',
      readTime: '4 min read',
      views: '7.8K'
    },
    {
      title: 'Why my model creation is taking too long',
      category: 'Troubleshooting',
      readTime: '2 min read',
      views: '6.1K'
    },
    {
      title: 'Commercial usage guidelines',
      category: 'Legal & Policy',
      readTime: '6 min read',
      views: '5.9K'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/web/memodel" className="text-2xl font-bold text-white">
              MeModel AI
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/web/memodel" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <Link href="/web/memodel/photo-packs" className="text-gray-300 hover:text-white transition-colors">
                Photo Packs
              </Link>
              <Link href="/web/memodel/gallery" className="text-gray-300 hover:text-white transition-colors">
                Gallery
              </Link>
              <Link href="/web/memodel/pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
              <Link href="/web/memodel/help" className="text-white font-semibold">
                Help
              </Link>
            </nav>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              Get Started
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-2 mb-4">
            <BookOpen className="h-8 w-8 text-purple-400" />
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Help Center
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Find answers to your questions, learn how to use MeModel AI effectively, and get the support you need to create amazing AI photos.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search help articles..."
              className="w-full bg-white/10 border border-white/20 rounded-xl pl-12 pr-4 py-4 text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {quickActions.map((action, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10 text-center group hover:bg-white/15 transition-all">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl inline-flex mb-4">
                <action.icon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{action.title}</h3>
              <p className="text-gray-300 text-sm mb-4">{action.description}</p>
              <Button 
                size="sm"
                disabled={!action.available}
                className={action.available 
                  ? "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700" 
                  : "bg-gray-600 cursor-not-allowed"
                }
              >
                {action.action}
                {action.available && <ArrowRight className="ml-2 h-4 w-4" />}
              </Button>
            </div>
          ))}
        </div>

        {/* Help Categories */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Browse by Category</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {helpCategories.map((category, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 group hover:bg-white/15 transition-all">
                <div className={`bg-gradient-to-r ${category.color} p-3 rounded-xl inline-flex mb-4`}>
                  <category.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{category.title}</h3>
                <p className="text-gray-300 mb-6">{category.description}</p>
                <div className="space-y-2">
                  {category.articles.map((article, articleIndex) => (
                    <div key={articleIndex} className="flex items-center gap-2 text-purple-300 hover:text-purple-200 cursor-pointer transition-colors">
                      <ExternalLink className="h-4 w-4" />
                      <span className="text-sm">{article}</span>
                    </div>
                  ))}
                </div>
                <Button variant="outline" className="w-full mt-6 border-white/20 text-white hover:bg-white/10">
                  View All Articles
                </Button>
              </div>
            ))}
          </div>
        </div>

        {/* Popular Articles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Popular Articles</h2>
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden">
            {popularArticles.map((article, index) => (
              <div key={index} className={`p-6 hover:bg-white/10 transition-colors cursor-pointer ${index !== popularArticles.length - 1 ? 'border-b border-white/10' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-2">{article.title}</h3>
                    <div className="flex items-center gap-4 text-sm text-gray-400">
                      <span className="bg-purple-600/20 text-purple-300 px-2 py-1 rounded-full">
                        {article.category}
                      </span>
                      <span>{article.readTime}</span>
                      <span>{article.views} views</span>
                    </div>
                  </div>
                  <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-white transition-colors" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Support */}
        <div className="text-center bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-3xl p-12 border border-white/10">
          <MessageCircle className="h-12 w-12 text-purple-400 mx-auto mb-4" />
          <h2 className="text-3xl font-bold text-white mb-4">Still Need Help?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Can&apos;t find what you&apos;re looking for? Our support team is here to help you with any questions or issues you might have.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/web/memodel/contact">
              <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700" size="lg">
                <MessageCircle className="mr-2 h-5 w-5" />
                Contact Support
              </Button>
            </Link>
            <Link href="/web/memodel/faq">
              <Button variant="outline" size="lg" className="border-white/20 text-white hover:bg-white/10">
                <HelpCircle className="mr-2 h-5 w-5" />
                View FAQ
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
