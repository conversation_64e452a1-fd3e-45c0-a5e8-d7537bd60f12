'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { <PERSON>rkles, Camera, Heart, Briefcase, Palette, Plane, ArrowRight } from 'lucide-react'

interface PhotoPack {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  photoCount: number
  weeklyRuns: number
  category: string
  featured?: boolean
}

const photoPacks: PhotoPack[] = [
  {
    id: 'ai-photography',
    title: '📸 AI Photography',
    description: 'Create stunning artistic photography portraits with AI. Professional lighting, creative compositions, and artistic flair bring your photos to life',
    icon: <Camera className="h-6 w-6" />,
    photoCount: 46,
    weeklyRuns: 102,
    category: 'Photography'
  },
  {
    id: 'instant-camera',
    title: '📸 Instant Camera',
    description: 'Instant film camera photographs with a classic analog camera for a vintage, timeless look. Capture moments with an authentic, nostalgic feel',
    icon: <Camera className="h-6 w-6" />,
    photoCount: 32,
    weeklyRuns: 74,
    category: 'Photography'
  },
  {
    id: 'ai-selfies',
    title: '🤳 AI Selfies',
    description: 'Create AI-generated selfies that capture your best angles and expressions. Perfect selfies every time without the hassle',
    icon: <Camera className="h-6 w-6" />,
    photoCount: 32,
    weeklyRuns: 69,
    category: 'Photography',
    featured: true
  },
  {
    id: 'luxury-lifestyle',
    title: '💎 Luxury Lifestyle',
    description: 'Enter into a world of elegance and sophistication. Showcase expensive outfits, lavish settings, and infinite opulence',
    icon: <Sparkles className="h-6 w-6" />,
    photoCount: 30,
    weeklyRuns: 52,
    category: 'Lifestyle'
  },
  {
    id: 'linkedin-headshots',
    title: '📸 LinkedIn Headshots',
    description: 'Professional headshots that enhance your credibility and personal brand. Studio-quality photos for career advancement',
    icon: <Briefcase className="h-6 w-6" />,
    photoCount: 32,
    weeklyRuns: 35,
    category: 'Professional'
  },
  {
    id: 'dating-photos',
    title: '❤️ Dating Photos',
    description: 'Look your best while staying true to who you are. Attract more matches on Tinder, Bumble, and Hinge',
    icon: <Heart className="h-6 w-6" />,
    photoCount: 28,
    weeklyRuns: 44,
    category: 'Dating'
  },
  {
    id: 'travel',
    title: '🌎 Travel',
    description: 'Travel the world and capture stunning photos from Paris to Tokyo. Showcase your global adventures',
    icon: <Plane className="h-6 w-6" />,
    photoCount: 31,
    weeklyRuns: 11,
    category: 'Travel'
  },
  {
    id: 'instagram',
    title: '📸 Instagram',
    description: 'Take engaging and visually stunning photos as an Instagram influencer. Boost your confidence, likes and followers',
    icon: <Camera className="h-6 w-6" />,
    photoCount: 34,
    weeklyRuns: 43,
    category: 'Social Media'
  },
  {
    id: 'fitness',
    title: '🏋️‍♀️ Fitness',
    description: 'Show off your hard work and dedication with our fitness photo shoot. Capture your fitness journey',
    icon: <Sparkles className="h-6 w-6" />,
    photoCount: 32,
    weeklyRuns: 6,
    category: 'Fitness'
  },
  {
    id: 'fashion',
    title: '👗 Fashion',
    description: 'Showcase the latest fashion trends and styles. Perfect for fashion enthusiasts and style influencers',
    icon: <Palette className="h-6 w-6" />,
    photoCount: 40,
    weeklyRuns: 18,
    category: 'Fashion'
  }
]

const categories = ['All', 'Photography', 'Professional', 'Lifestyle', 'Dating', 'Travel', 'Social Media', 'Fitness', 'Fashion']

export default function PhotoPacksPage() {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [searchTerm, setSearchTerm] = useState('')

  const filteredPacks = photoPacks.filter(pack => {
    const matchesCategory = selectedCategory === 'All' || pack.category === selectedCategory
    const matchesSearch = pack.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pack.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/web/memodel" className="flex items-center space-x-2">
            <Sparkles className="h-8 w-8 text-purple-400" />
            <span className="text-2xl font-bold text-white">MeModel AI</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/web/memodel/gallery" className="text-white hover:text-purple-300 transition-colors">
              Gallery
            </Link>
            <Link href="/web/memodel/photo-packs" className="text-purple-400 font-semibold">
              Photo Packs
            </Link>
            <Link href="/web/memodel/pricing" className="text-white hover:text-purple-300 transition-colors">
              Pricing
            </Link>
            <Link href="/web/memodel/faq" className="text-white hover:text-purple-300 transition-colors">
              FAQ
            </Link>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-full transition-colors">
              Sign In
            </button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <div className="px-4 py-16 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            🔥 New photo packs just dropped
          </h1>
          <p className="text-xl text-purple-200 mb-8">
            Photo packs are themed collections of AI-generated photos featuring your own AI model that you can use to create different styles, moods, and effects. All photo packs are included in your membership!
          </p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <input
              type="text"
              placeholder="Search photo packs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 px-4 py-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder:text-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
            />
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-purple-600 text-white'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Photo Packs Grid */}
      <div className="px-4 pb-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPacks.map((pack) => (
              <div
                key={pack.id}
                className={`bg-white/10 backdrop-blur-sm rounded-lg p-6 border transition-all hover:bg-white/15 hover:scale-105 cursor-pointer ${
                  pack.featured ? 'border-purple-400 ring-2 ring-purple-400/20' : 'border-white/20'
                }`}
              >
                {pack.featured && (
                  <div className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full inline-block mb-3">
                    FEATURED
                  </div>
                )}
                
                <div className="flex items-start space-x-3 mb-4">
                  <div className="bg-gradient-to-br from-purple-600 to-pink-600 w-12 h-12 rounded-lg flex items-center justify-center">
                    {pack.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-white mb-2">{pack.title}</h3>
                    <span className="bg-white/10 text-purple-300 text-xs px-2 py-1 rounded-full">
                      {pack.category}
                    </span>
                  </div>
                </div>
                
                <p className="text-purple-200 text-sm mb-4 line-clamp-3">
                  {pack.description}
                </p>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-purple-300">
                    {pack.photoCount} PHOTOS
                  </span>
                  <span className="text-purple-300">
                    {pack.weeklyRuns}X RAN THIS WEEK
                  </span>
                </div>
                
                <div className="mt-4 pt-4 border-t border-white/10">
                  <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-2 rounded-lg text-sm font-medium flex items-center justify-center space-x-2 transition-all">
                    <span>Try this pack</span>
                    <ArrowRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 px-4 bg-white/5 backdrop-blur-sm text-center">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-8">
            Pick from 135+ photo packs
          </h2>
          <p className="text-xl text-purple-200 mb-8">
            With MeModel AI&apos;s preset photo packs, you don&apos;t need to do any of the hard work of writing prompts or setting parameters. Instead, with just one click, MeModel AI takes a set of photos for you.
          </p>
          <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-12 py-4 rounded-full text-xl font-semibold flex items-center space-x-3 mx-auto transition-all">
            <span>Start creating now</span>
            <ArrowRight className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-sm py-12 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Sparkles className="h-6 w-6 text-purple-400" />
            <span className="text-xl font-bold text-white">MeModel AI</span>
          </div>
          <p className="text-purple-200 text-sm mb-8">
            Create stunning AI photos and videos of yourself with the power of artificial intelligence.
          </p>
          <div className="grid md:grid-cols-4 gap-8 text-left">
            <div>
              <h4 className="text-white font-semibold mb-4">Popular Packs</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/ai-photography" className="block text-purple-200 hover:text-white text-sm">AI Photography</Link>
                <Link href="/web/memodel/instant-camera" className="block text-purple-200 hover:text-white text-sm">Instant Camera</Link>
                <Link href="/web/memodel/ai-selfies" className="block text-purple-200 hover:text-white text-sm">AI Selfies</Link>
                <Link href="/web/memodel/luxury-lifestyle" className="block text-purple-200 hover:text-white text-sm">Luxury Lifestyle</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Professional</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/linkedin-headshots" className="block text-purple-200 hover:text-white text-sm">LinkedIn Headshots</Link>
                <Link href="/web/memodel/ceo-headshots" className="block text-purple-200 hover:text-white text-sm">CEO Headshots</Link>
                <Link href="/web/memodel/startup-founder" className="block text-purple-200 hover:text-white text-sm">Startup Founder</Link>
                <Link href="/web/memodel/corporate-headshots" className="block text-purple-200 hover:text-white text-sm">Corporate Headshots</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Lifestyle</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/dating-photos" className="block text-purple-200 hover:text-white text-sm">Dating Photos</Link>
                <Link href="/web/memodel/instagram" className="block text-purple-200 hover:text-white text-sm">Instagram</Link>
                <Link href="/web/memodel/travel" className="block text-purple-200 hover:text-white text-sm">Travel</Link>
                <Link href="/web/memodel/fitness" className="block text-purple-200 hover:text-white text-sm">Fitness</Link>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Support</h4>
              <div className="space-y-2">
                <Link href="/web/memodel/faq" className="block text-purple-200 hover:text-white text-sm">FAQ</Link>
                <Link href="/web/memodel/help" className="block text-purple-200 hover:text-white text-sm">Help Center</Link>
                <Link href="/web/memodel/contact" className="block text-purple-200 hover:text-white text-sm">Contact</Link>
                <Link href="/web/memodel/billing" className="block text-purple-200 hover:text-white text-sm">Billing</Link>
              </div>
            </div>
          </div>
          
          <div className="border-t border-white/10 mt-8 pt-8 text-center">
            <p className="text-purple-200 text-sm">
              © 2025 MeModel AI. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
