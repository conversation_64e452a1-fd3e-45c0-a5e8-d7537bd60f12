'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON>les, Check, Zap, Crown, Star, ArrowR<PERSON> } from 'lucide-react'

interface PricingPlan {
  id: string
  name: string
  monthlyPrice: number
  yearlyPrice: number
  description: string
  features: string[]
  photosIncluded: number
  modelsPerMonth: number
  isPopular?: boolean
  isPremium?: boolean
}

const pricingPlans: PricingPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    monthlyPrice: 15,
    yearlyPrice: 108,
    description: 'Get started with basic AI photos, create your first model, and begin your AI photography journey',
    photosIncluded: 50,
    modelsPerMonth: 1,
    features: [
      'Take 50 AI Photos (credits)',
      'Create 1 AI Model per month',
      'Flux™ photorealistic model',
      'Low quality photos',
      'Low likeness',
      'Take 1 photo at a time',
      'Slow processing',
      'For personal use only',
      'No free auto-generated photos'
    ]
  },
  {
    id: 'pro',
    name: 'Pro',
    monthlyPrice: 39,
    yearlyPrice: 419,
    description: 'Boost your creativity with higher quality photos, parallel processing, and commercial usage rights',
    photosIncluded: 1000,
    modelsPerMonth: 3,
    isPopular: true,
    features: [
      'Take 1,000 AI Photos (credits)',
      'Create 3 AI Models per month',
      'Flux™ model',
      'Medium quality photos',
      'Medium likeness',
      'Take up to 4 photos in parallel',
      'Import photos',
      'Write your own prompts',
      'Remix any photo',
      'Commercial use license'
    ]
  },
  {
    id: 'premium',
    name: 'Premium',
    monthlyPrice: 59,
    yearlyPrice: 709,
    description: 'Get more photos, more models, more features, and higher quality photos with our most popular plan',
    photosIncluded: 3000,
    modelsPerMonth: 10,
    features: [
      'Take 3,000 AI Photos (credits)',
      'Create 10 AI Models per month',
      'Flux™ HD photorealistic model',
      'High quality photos',
      'High likeness',
      'Take up to 8 photos in parallel',
      'Edit photos',
      'Crop photos',
      'Zoom out photos',
      'Shoot AI videos',
      'Use LoRas from Civitai',
      'Relight photos',
      'Combine photos',
      'Use the magic upscaler',
      'Try on clothes (for Shopify)',
      'Early access to new features'
    ]
  },
  {
    id: 'ultra',
    name: 'Ultra',
    monthlyPrice: 119,
    yearlyPrice: 1427,
    description: 'Get our highest level of access with ultra-fast processing and enterprise-level performance',
    photosIncluded: 10000,
    modelsPerMonth: 50,
    isPremium: true,
    features: [
      'Take 10,000 AI Photos (credits)',
      'Create 50 AI Models per month',
      'Flux™ HD photorealistic model',
      'Ultra quality photos',
      'Ultra-high likeness',
      'Take up to 16 photos in parallel',
      'Unlimited photo storage',
      'Priority: faster response times',
      'Export your models',
      'All Premium features included'
    ]
  }
]

export default function PricingPage() {
  const [isYearly, setIsYearly] = useState(true)

  const calculateSavings = (monthly: number, yearly: number) => {
    const monthlyCost = monthly * 12
    const savings = monthlyCost - yearly
    const percentage = Math.round((savings / monthlyCost) * 100)
    return { savings, percentage }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/web/memodel" className="flex items-center space-x-2">
            <Sparkles className="h-8 w-8 text-purple-400" />
            <span className="text-2xl font-bold text-white">MeModel AI</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/web/memodel/gallery" className="text-white hover:text-purple-300 transition-colors">
              Gallery
            </Link>
            <Link href="/web/memodel/photo-packs" className="text-white hover:text-purple-300 transition-colors">
              Photo Packs
            </Link>
            <Link href="/web/memodel/pricing" className="text-purple-400 font-semibold">
              Pricing
            </Link>
            <Link href="/web/memodel/faq" className="text-white hover:text-purple-300 transition-colors">
              FAQ
            </Link>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-full transition-colors">
              Sign In
            </button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <div className="px-4 py-16 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Pricing
          </h1>
          <p className="text-xl text-purple-200 mb-8">
            Choose the perfect plan for your AI photo generation needs. All plans include access to our extensive photo pack library.
          </p>
        </div>
      </div>

      {/* Billing Toggle */}
      <div className="px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-full p-1 border border-white/20">
              <div className="flex items-center">
                <button
                  onClick={() => setIsYearly(false)}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                    !isYearly ? 'bg-purple-600 text-white' : 'text-white hover:text-purple-300'
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setIsYearly(true)}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all flex items-center space-x-2 ${
                    isYearly ? 'bg-purple-600 text-white' : 'text-white hover:text-purple-300'
                  }`}
                >
                  <span>Yearly</span>
                  <span className="bg-gradient-to-r from-pink-500 to-yellow-500 text-xs px-2 py-1 rounded-full text-black font-bold">
                    🔥 get 6+ months free
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="px-4 pb-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {pricingPlans.map((plan) => {
              const { savings, percentage } = calculateSavings(plan.monthlyPrice, plan.yearlyPrice)
              const displayPrice = isYearly ? plan.yearlyPrice : plan.monthlyPrice

              return (
                <div
                  key={plan.id}
                  className={`relative bg-white/10 backdrop-blur-sm rounded-lg p-6 border transition-all hover:bg-white/15 ${
                    plan.isPopular 
                      ? 'border-purple-400 ring-2 ring-purple-400/20 scale-105' 
                      : plan.isPremium
                      ? 'border-yellow-400 ring-2 ring-yellow-400/20'
                      : 'border-white/20'
                  }`}
                >
                  {plan.isPopular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-purple-600 text-white text-xs px-3 py-1 rounded-full flex items-center space-x-1">
                        <Star className="h-3 w-3" />
                        <span>Most popular</span>
                      </div>
                    </div>
                  )}

                  {plan.isPremium && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-black text-xs px-3 py-1 rounded-full flex items-center space-x-1">
                        <Crown className="h-3 w-3" />
                        <span>Premium</span>
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                    
                    <div className="mb-4">
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-white">
                          ${isYearly ? Math.round(displayPrice / 12) : displayPrice}
                        </span>
                        <span className="text-purple-200 ml-2">
                          {isYearly ? 'billed annually' : 'per month'}
                        </span>
                      </div>
                      
                      {isYearly && (
                        <div className="text-sm text-purple-300 mt-1">
                          ${displayPrice} {percentage}% off • {Math.round(savings/plan.monthlyPrice)} months free
                        </div>
                      )}
                    </div>

                    <p className="text-sm text-purple-200 mb-6">
                      {plan.description}
                    </p>
                  </div>

                  <button className={`w-full py-3 rounded-lg font-semibold mb-6 transition-all flex items-center justify-center space-x-2 ${
                    plan.isPopular || plan.isPremium
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white'
                      : 'bg-white/10 hover:bg-white/20 text-white border border-white/20'
                  }`}>
                    <span>Subscribe</span>
                    <ArrowRight className="h-4 w-4" />
                  </button>

                  <div className="space-y-3">
                    <div className="text-center pb-3 border-b border-white/10">
                      <div className="text-white font-semibold">
                        Take {plan.photosIncluded.toLocaleString()} AI Photos (credits)
                      </div>
                      <div className="text-purple-300 text-sm">
                        Create {plan.modelsPerMonth} AI Model{plan.modelsPerMonth > 1 ? 's' : ''} per month
                      </div>
                    </div>

                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-start space-x-2 text-sm">
                        <Check className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                        <span className="text-purple-200">{feature}</span>
                      </div>
                    ))}

                    <div className="pt-3 border-t border-white/10 text-center">
                      <div className="text-purple-300 text-sm">
                        For each model you create, you get 48 free photos auto-generated:
                      </div>
                      <div className="text-xs text-purple-400 mt-1">
                        8x profile pics • 8x headshots • 8x dating photos • 8x outfit ideas • 8x social posts
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Features Comparison */}
      <div className="py-20 px-4 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            What&apos;s included in each plan
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-gradient-to-br from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Lightning Fast</h3>
              <p className="text-purple-200">Generate photos in ~20 seconds with our optimized AI models</p>
            </div>
            
            <div className="text-center">
              <div className="bg-gradient-to-br from-blue-600 to-cyan-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">135+ Photo Packs</h3>
              <p className="text-purple-200">Access to all photo packs including new releases and exclusive content</p>
            </div>
            
            <div className="text-center">
              <div className="bg-gradient-to-br from-green-600 to-emerald-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Commercial Rights</h3>
              <p className="text-purple-200">Use your AI photos for business, social media, and commercial purposes</p>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Frequently asked questions
          </h2>
          
          <div className="space-y-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-2">Is MeModel AI free?</h3>
              <p className="text-purple-200">
                We offer a free trial with limited credits. All paid plans come with generous free photo allocations for each model you create.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-2">Can I cancel anytime?</h3>
              <p className="text-purple-200">
                Yes, you can cancel your subscription at any time. You&apos;ll continue to have access until the end of your billing period.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-2">What payment methods do you accept?</h3>
              <p className="text-purple-200">
                We accept all major credit cards, PayPal, and various regional payment methods through our secure payment processor.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-2">How long does it take to create my AI model?</h3>
              <p className="text-purple-200">
                Premium and Ultra subscribers get extremely fast training at just 90 seconds. For lower plans it can take 30 minutes to 2 hours.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 px-4 text-center">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-8">
            Ready to create your AI model?
          </h2>
          <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-12 py-4 rounded-full text-xl font-semibold flex items-center space-x-3 mx-auto transition-all">
            <span>Start taking AI photos now</span>
            <ArrowRight className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-sm py-12 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Sparkles className="h-6 w-6 text-purple-400" />
            <span className="text-xl font-bold text-white">MeModel AI</span>
          </div>
          <p className="text-purple-200 text-sm">
            © 2025 MeModel AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
