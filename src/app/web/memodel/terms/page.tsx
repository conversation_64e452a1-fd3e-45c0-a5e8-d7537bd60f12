'use client';

import { Button } from '@/components/ui/button';
import { Scale, AlertTriangle, Users, CreditCard, Shield, FileText, Download, Mail } from 'lucide-react';
import Link from 'next/link';

export default function TermsPage() {
  const sections = [
    {
      id: 'acceptance',
      title: 'Acceptance of Terms',
      icon: Scale,
      content: [
        'By accessing and using MeModel AI, you accept and agree to be bound by these Terms of Service',
        'If you do not agree to these terms, you may not use our service',
        'We may update these terms from time to time, and your continued use constitutes acceptance',
        'You must be at least 18 years old to use our service',
        'If you are under 18, you must have parental consent to use MeModel AI'
      ]
    },
    {
      id: 'service-description',
      title: 'Service Description',
      icon: Users,
      content: [
        'MeModel AI provides AI-powered photo generation services using machine learning technology',
        'Users can upload photos to create personalized AI models for generating new images',
        'We offer various subscription plans with different features and usage limits',
        'Service availability may vary and is subject to maintenance and updates',
        'We reserve the right to modify or discontinue features at any time'
      ]
    },
    {
      id: 'user-obligations',
      title: 'User Obligations & Restrictions',
      icon: AlertTriangle,
      content: [
        'You must only upload photos of yourself or photos you have permission to use',
        'You may not upload photos of minors, copyrighted material, or illegal content',
        'Generated photos must not be used for illegal, harmful, or deceptive purposes',
        'You may not attempt to reverse engineer, hack, or abuse our service',
        'Sharing account credentials or circumventing usage limits is prohibited',
        'You are responsible for maintaining the security of your account'
      ]
    },
    {
      id: 'payment-terms',
      title: 'Payment & Subscription Terms',
      icon: CreditCard,
      content: [
        'Subscription fees are charged monthly or annually as selected',
        'All fees are non-refundable except as required by law or our refund policy',
        'We offer a 7-day money-back guarantee for new subscribers',
        'Price changes will be communicated 30 days in advance',
        'You can cancel your subscription at any time from your account settings',
        'Upon cancellation, service continues until the end of your billing period'
      ]
    }
  ];

  const prohibitedUses = [
    'Creating deepfakes or non-consensual imagery',
    'Generating content that violates intellectual property rights',
    'Creating images for fraudulent or deceptive purposes',
    'Uploading photos of other people without their consent',
    'Generating illegal, harmful, or offensive content',
    'Using the service to harass, intimidate, or harm others',
    'Attempting to identify or recreate real people without permission',
    'Commercial use of content generated with personal plans'
  ];

  const liability = [
    'MeModel AI is provided "as is" without warranties of any kind',
    'We do not guarantee the accuracy, quality, or availability of generated photos',
    'Our liability is limited to the amount you paid for the service in the last 12 months',
    'We are not responsible for any indirect, incidental, or consequential damages',
    'You agree to indemnify us against claims arising from your use of the service',
    'Some jurisdictions do not allow limitation of liability, so these limits may not apply to you'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/web/memodel" className="text-2xl font-bold text-white">
              MeModel AI
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/web/memodel" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <Link href="/web/memodel/photo-packs" className="text-gray-300 hover:text-white transition-colors">
                Photo Packs
              </Link>
              <Link href="/web/memodel/gallery" className="text-gray-300 hover:text-white transition-colors">
                Gallery
              </Link>
              <Link href="/web/memodel/pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
            </nav>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              Get Started
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Scale className="h-8 w-8 text-purple-400" />
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Terms of Service
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-4">
            These terms govern your use of MeModel AI. Please read them carefully before using our service.
          </p>
          <p className="text-sm text-gray-400">
            Last updated: December 2024 • Effective Date: December 1, 2024
          </p>
        </div>

        {/* Quick Summary */}
        <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <h2 className="text-2xl font-bold text-white mb-4">Quick Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Your Rights</h3>
              <p className="text-gray-300 text-sm">Use our AI photo generation service according to your subscription plan and create amazing content.</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Your Responsibilities</h3>
              <p className="text-gray-300 text-sm">Upload only photos you own, respect others&apos; rights, and use the service lawfully and ethically.</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Our Commitment</h3>
              <p className="text-gray-300 text-sm">Provide reliable AI photo generation service while protecting your privacy and data security.</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Important Limits</h3>
              <p className="text-gray-300 text-sm">Service provided &quot;as is&quot; with usage limits based on your subscription plan and applicable laws.</p>
            </div>
          </div>
        </div>

        {/* Main Sections */}
        <div className="space-y-8 mb-12">
          {sections.map((section, index) => (
            <div key={index} id={section.id} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
              <div className="flex items-center gap-3 mb-6">
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl">
                  <section.icon className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white">{section.title}</h2>
              </div>
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start gap-3 text-gray-300">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Prohibited Uses */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-gradient-to-r from-red-600 to-orange-600 p-3 rounded-xl">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white">Prohibited Uses</h2>
          </div>
          <p className="text-gray-300 mb-6">
            The following uses of MeModel AI are strictly prohibited and may result in immediate account termination:
          </p>
          <ul className="space-y-3">
            {prohibitedUses.map((use, index) => (
              <li key={index} className="flex items-start gap-3 text-gray-300">
                <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                <span>{use}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Intellectual Property */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white">Intellectual Property & Content Rights</h2>
          </div>
          <div className="space-y-4 text-gray-300">
            <p>
              <strong className="text-white">Your Content:</strong> You retain ownership of photos you upload. By using our service, you grant us a limited license to process your photos for AI model creation and photo generation.
            </p>
            <p>
              <strong className="text-white">Generated Content:</strong> You own the AI-generated photos created from your models. Commercial use rights depend on your subscription plan.
            </p>
            <p>
              <strong className="text-white">Our Technology:</strong> MeModel AI&apos;s algorithms, software, and technology are our proprietary intellectual property and are protected by copyright and patent laws.
            </p>
            <p>
              <strong className="text-white">Third-Party Content:</strong> You must respect third-party intellectual property rights and only upload content you own or have permission to use.
            </p>
          </div>
        </div>

        {/* Liability and Disclaimers */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white">Limitation of Liability & Disclaimers</h2>
          </div>
          <ul className="space-y-3">
            {liability.map((item, index) => (
              <li key={index} className="flex items-start gap-3 text-gray-300">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0" />
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Termination */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Account Termination</h2>
          <div className="space-y-4 text-gray-300">
            <p>
              <strong className="text-white">By You:</strong> You may terminate your account at any time through your account settings. Your subscription will remain active until the end of your billing period.
            </p>
            <p>
              <strong className="text-white">By Us:</strong> We may suspend or terminate your account for violations of these terms, illegal activities, or abuse of our service. We will provide notice when possible.
            </p>
            <p>
              <strong className="text-white">Effect of Termination:</strong> Upon termination, your access to the service will cease, and your data may be deleted according to our data retention policy.
            </p>
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
          <Mail className="h-12 w-12 text-purple-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-4">Questions About These Terms?</h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            If you have any questions about these Terms of Service or need clarification on any provisions, please contact our legal team.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/web/memodel/contact">
              <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                <Mail className="mr-2 h-4 w-4" />
                Contact Legal Team
              </Button>
            </Link>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Download className="mr-2 h-4 w-4" />
              Download Terms (PDF)
            </Button>
          </div>
          <p className="text-sm text-gray-400 mt-6">
            Email us <NAME_EMAIL> for terms-related inquiries
          </p>
        </div>

        {/* Navigation */}
        <div className="mt-12 text-center">
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/web/memodel/privacy" className="text-purple-300 hover:text-purple-200 transition-colors">
              Privacy Policy
            </Link>
            <span className="text-gray-500">•</span>
            <Link href="/web/memodel/cookies" className="text-purple-300 hover:text-purple-200 transition-colors">
              Cookie Policy
            </Link>
            <span className="text-gray-500">•</span>
            <Link href="/web/memodel/refund" className="text-purple-300 hover:text-purple-200 transition-colors">
              Refund Policy
            </Link>
            <span className="text-gray-500">•</span>
            <Link href="/web/memodel/contact" className="text-purple-300 hover:text-purple-200 transition-colors">
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
