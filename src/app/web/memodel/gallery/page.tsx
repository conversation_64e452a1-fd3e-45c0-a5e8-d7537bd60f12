'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowRight, Upload, Sparkles, Download, Star, Users, Heart, Camera } from 'lucide-react';
import Link from 'next/link';

export default function GalleryPage() {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const filters = [
    { id: 'all', label: 'All Photos', count: 1247 },
    { id: 'trending', label: 'Trending', count: 89 },
    { id: 'recent', label: 'Recent', count: 156 },
    { id: 'popular', label: 'Most Popular', count: 234 },
    { id: 'professional', label: 'Professional', count: 178 },
    { id: 'lifestyle', label: 'Lifestyle', count: 201 },
    { id: 'creative', label: 'Creative', count: 145 }
  ];

  const galleryPhotos = [
    {
      id: 1,
      url: '/gallery/photo-1.jpg',
      title: 'Professional Headshot',
      creator: '<PERSON>',
      likes: 234,
      pack: 'Professional',
      featured: true
    },
    {
      id: 2, 
      url: '/gallery/photo-2.jpg',
      title: 'Fantasy Warrior',
      creator: '<PERSON> <PERSON>.',
      likes: 567,
      pack: 'Fantasy Adventure',
      featured: true
    },
    {
      id: 3,
      url: '/gallery/photo-3.jpg', 
      title: 'Elegant Portrait',
      creator: 'Emma L.',
      likes: 345,
      pack: 'Glamour Fashion',
      featured: false
    },
    {
      id: 4,
      url: '/gallery/photo-4.jpg',
      title: 'Travel Explorer', 
      creator: 'Alex P.',
      likes: 189,
      pack: 'Travel Explorer',
      featured: false
    },
    {
      id: 5,
      url: '/gallery/photo-5.jpg',
      title: 'Fitness Inspiration',
      creator: 'Jordan K.',
      likes: 412,
      pack: 'Fitness & Workout',
      featured: true
    },
    {
      id: 6,
      url: '/gallery/photo-6.jpg',
      title: 'Vintage Classic',
      creator: 'Taylor B.',
      likes: 298,
      pack: 'Vintage Classic',
      featured: false
    }
  ];

  const filteredPhotos = galleryPhotos.filter(photo => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'trending') return photo.featured;
    if (selectedFilter === 'popular') return photo.likes > 300;
    if (selectedFilter === 'recent') return photo.id > 3;
    return photo.pack.toLowerCase().includes(selectedFilter);
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/web/memodel" className="text-2xl font-bold text-white">
              MeModel AI
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/web/memodel" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <Link href="/web/memodel/photo-packs" className="text-gray-300 hover:text-white transition-colors">
                Photo Packs
              </Link>
              <Link href="/web/memodel/gallery" className="text-white font-semibold">
                Gallery
              </Link>
              <Link href="/web/memodel/pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
            </nav>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              Get Started
            </Button>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Camera className="h-8 w-8 text-purple-400" />
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              AI Photo Gallery
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Discover stunning AI-generated photos created by our community. Get inspired by amazing transformations and see what&apos;s possible with MeModel AI.
          </p>
          <div className="flex items-center justify-center gap-8 text-sm text-gray-400">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>50K+ Creators</span>
            </div>
            <div className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              <span>1M+ Photos Generated</span>
            </div>
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              <span>2M+ Likes</span>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6">
            <div className="flex flex-wrap gap-2">
              {filters.map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setSelectedFilter(filter.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                    selectedFilter === filter.id
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                      : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>
            <div className="w-full lg:w-80">
              <Input
                type="text"
                placeholder="Search photos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              />
            </div>
          </div>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredPhotos.map((photo) => (
            <div key={photo.id} className="group relative bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
              <div className="aspect-square bg-gradient-to-br from-purple-400/20 to-pink-400/20 flex items-center justify-center">
                <Camera className="h-24 w-24 text-white/30" />
              </div>
              
              {photo.featured && (
                <div className="absolute top-4 left-4">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    Featured
                  </div>
                </div>
              )}

              <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button size="sm" className="bg-black/50 backdrop-blur-sm hover:bg-black/70">
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-1">{photo.title}</h3>
                <p className="text-sm text-gray-400 mb-3">by {photo.creator}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xs bg-purple-600/20 text-purple-300 px-2 py-1 rounded-full">
                    {photo.pack}
                  </span>
                  <div className="flex items-center gap-1 text-gray-400">
                    <Heart className="h-4 w-4" />
                    <span className="text-sm">{photo.likes}</span>
                  </div>
                </div>
              </div>

              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center">
          <Button className="bg-white/10 hover:bg-white/20 text-white border border-white/20">
            Load More Photos
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-3xl p-12 border border-white/10">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-6 w-6 text-purple-400" />
            <h2 className="text-3xl font-bold text-white">Ready to Create Your Own?</h2>
          </div>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of creators who are transforming their photos with AI. Upload your photos and start generating stunning images in minutes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700" size="lg">
              <Upload className="mr-2 h-5 w-5" />
              Start Creating
            </Button>
            <Button variant="outline" size="lg" className="border-white/20 text-white hover:bg-white/10">
              View Photo Packs
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
