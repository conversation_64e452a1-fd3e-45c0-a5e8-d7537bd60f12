'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Mail, MapPin, Phone, Clock, Send, MessageCircle, HelpCircle, Users } from 'lucide-react';
import Link from 'next/link';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const contactOptions = [
    {
      icon: MessageCircle,
      title: 'General Support',
      description: 'Questions about your account, billing, or general inquiries',
      email: '<EMAIL>',
      response: '24 hours'
    },
    {
      icon: HelpCircle,
      title: 'Technical Help',
      description: 'Issues with photo generation, model creation, or technical problems',
      email: '<EMAIL>',
      response: '12 hours'
    },
    {
      icon: Users,
      title: 'Business & Partnerships',
      description: 'Enterprise solutions, API access, and partnership opportunities',
      email: '<EMAIL>',
      response: '2 business days'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/web/memodel" className="text-2xl font-bold text-white">
              MeModel AI
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/web/memodel" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <Link href="/web/memodel/photo-packs" className="text-gray-300 hover:text-white transition-colors">
                Photo Packs
              </Link>
              <Link href="/web/memodel/gallery" className="text-gray-300 hover:text-white transition-colors">
                Gallery
              </Link>
              <Link href="/web/memodel/pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
              <Link href="/web/memodel/contact" className="text-white font-semibold">
                Contact
              </Link>
            </nav>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              Get Started
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Get in Touch
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Have questions about MeModel AI? Need technical support? Want to explore business partnerships? 
            We&apos;re here to help and would love to hear from you.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/10">
            <h2 className="text-2xl font-bold text-white mb-6">Send us a Message</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Full Name
                </label>
                <Input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email Address
                </label>
                <Input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  placeholder="Enter your email address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Inquiry Type
                </label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                >
                  <option value="general">General Support</option>
                  <option value="technical">Technical Help</option>
                  <option value="business">Business & Partnerships</option>
                  <option value="billing">Billing & Payments</option>
                  <option value="feature">Feature Request</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Subject
                </label>
                <Input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  placeholder="Brief description of your inquiry"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Message
                </label>
                <Textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={5}
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 resize-none"
                  placeholder="Please provide details about your inquiry..."
                />
              </div>

              <Button 
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                size="lg"
              >
                <Send className="mr-2 h-4 w-4" />
                Send Message
              </Button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            {/* Contact Options */}
            <div className="space-y-6">
              {contactOptions.map((option, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                  <div className="flex items-start gap-4">
                    <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-3 rounded-xl">
                      <option.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">{option.title}</h3>
                      <p className="text-gray-300 text-sm mb-3">{option.description}</p>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-2 text-purple-300">
                          <Mail className="h-4 w-4" />
                          <span>{option.email}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400">
                          <Clock className="h-4 w-4" />
                          <span>{option.response}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Company Info */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4">Company Information</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-gray-300">
                  <MapPin className="h-5 w-5 text-purple-400" />
                  <span>San Francisco, CA 94103, United States</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Phone className="h-5 w-5 text-purple-400" />
                  <span>+****************</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Mail className="h-5 w-5 text-purple-400" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Clock className="h-5 w-5 text-purple-400" />
                  <span>Mon-Fri 9AM-6PM PST</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Links</h3>
              <div className="space-y-2">
                <Link href="/web/memodel/faq" className="block text-purple-300 hover:text-purple-200 transition-colors">
                  Frequently Asked Questions
                </Link>
                <Link href="/web/memodel/help" className="block text-purple-300 hover:text-purple-200 transition-colors">
                  Help Center
                </Link>
                <Link href="/web/memodel/privacy" className="block text-purple-300 hover:text-purple-200 transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/web/memodel/terms" className="block text-purple-300 hover:text-purple-200 transition-colors">
                  Terms of Service
                </Link>
                <Link href="/web/memodel/api" className="block text-purple-300 hover:text-purple-200 transition-colors">
                  API Documentation
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Preview */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Still have questions?</h2>
          <p className="text-gray-300 mb-6">
            Check out our comprehensive FAQ section for instant answers to common questions.
          </p>
          <Link href="/web/memodel/faq">
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <HelpCircle className="mr-2 h-4 w-4" />
              Visit FAQ Center
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
