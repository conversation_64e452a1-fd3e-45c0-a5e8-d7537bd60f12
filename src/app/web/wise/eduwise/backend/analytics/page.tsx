'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { User, Course, Quiz, Lesson, UserScore, UserEnrollment, UserActivity } from '../../types';

interface AnalyticsData {
  users: User[];
  courses: Course[];
  lessons: Lesson[];
  quizzes: Quiz[];
  scores: UserScore[];
  enrollments: UserEnrollment[];
  activities: UserActivity[];
}

export default function Analytics() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      const [usersRes, coursesRes, lessonsRes, quizzesRes, scoresRes, enrollmentsRes, activitiesRes] = await Promise.all([
        fetch('/web/wise/eduwise/backend/api/users'),
        fetch('/web/wise/eduwise/backend/api/courses'),
        fetch('/web/wise/eduwise/backend/api/lessons'),
        fetch('/web/wise/eduwise/backend/api/quizzes'),
        fetch('/web/wise/eduwise/backend/api/scores'),
        fetch('/web/wise/eduwise/backend/api/enrollments'),
        fetch('/web/wise/eduwise/backend/api/activities')
      ]);

      const [users, courses, lessons, quizzes, scores, enrollments, activities] = await Promise.all([
        usersRes.json(),
        coursesRes.json(),
        lessonsRes.json(),
        quizzesRes.json(),
        scoresRes.json(),
        enrollmentsRes.json().catch(() => []),
        activitiesRes.json().catch(() => [])
      ]);

      setData({ users, courses, lessons, quizzes, scores, enrollments, activities });
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Không thể tải dữ liệu phân tích</p>
        </div>
      </div>
    );
  }

  // Calculate analytics
  const totalUsers = data.users.length;
  const activeUsers = data.users.filter(user => user.status === 1).length;
  const totalCourses = data.courses.length;
  const activeCourses = data.courses.filter(course => course.status === 1).length;
  const totalLessons = data.lessons.length;
  const activeLessons = data.lessons.filter(lesson => lesson.status === 1).length;
  const totalEnrollments = data.enrollments.length;
  const completedEnrollments = data.enrollments.filter(enrollment => enrollment.status === 'completed').length;
  const totalScores = data.scores.length;
  const passedScores = data.scores.filter(score => score.passed).length;
  const averageScore = totalScores > 0
    ? Math.round(data.scores.reduce((sum, score) => sum + score.percentage, 0) / totalScores)
    : 0;

  // Course popularity
  const courseEnrollments = data.enrollments.reduce((acc, enrollment) => {
    acc[enrollment.course_id] = (acc[enrollment.course_id] || 0) + 1;
    return acc;
  }, {} as Record<number, number>);

  const popularCourses = data.courses
    .map(course => ({
      ...course,
      enrollmentCount: courseEnrollments[course.id] || 0
    }))
    .sort((a, b) => b.enrollmentCount - a.enrollmentCount)
    .slice(0, 5);

  // User performance
  const userPerformance = data.users
    .map(user => {
      const userScores = data.scores.filter(score => score.user_id === user.id);
      const userEnrollments = data.enrollments.filter(enrollment => enrollment.user_id === user.id);
      const avgScore = userScores.length > 0 
        ? Math.round(userScores.reduce((sum, score) => sum + score.percentage, 0) / userScores.length)
        : 0;
      
      return {
        ...user,
        totalEnrollments: userEnrollments.length,
        completedCourses: userEnrollments.filter(e => e.status === 'completed').length,
        averageScore: avgScore,
        totalQuizzes: userScores.length,
        passedQuizzes: userScores.filter(score => score.passed).length
      };
    })
    .filter(user => user.totalEnrollments > 0)
    .sort((a, b) => b.averageScore - a.averageScore)
    .slice(0, 10);

  // Quiz performance
  const quizPerformance = data.quizzes
    .map(quiz => {
      const quizScores = data.scores.filter(score => score.quiz_id === quiz.id);
      const avgScore = quizScores.length > 0 
        ? Math.round(quizScores.reduce((sum, score) => sum + score.percentage, 0) / quizScores.length)
        : 0;
      
      return {
        ...quiz,
        totalAttempts: quizScores.length,
        passedAttempts: quizScores.filter(score => score.passed).length,
        averageScore: avgScore,
        passRate: quizScores.length > 0 
          ? Math.round((quizScores.filter(score => score.passed).length / quizScores.length) * 100)
          : 0
      };
    })
    .filter(quiz => quiz.totalAttempts > 0)
    .sort((a, b) => b.totalAttempts - a.totalAttempts);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link 
                href="/web/wise/eduwise/backend" 
                className="text-blue-600 hover:text-blue-800 mr-4"
              >
                ← Quay lại
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">Báo cáo & Thống kê</h1>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
              >
                <option value="7d">7 ngày qua</option>
                <option value="30d">30 ngày qua</option>
                <option value="90d">90 ngày qua</option>
                <option value="all">Tất cả</option>
              </select>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Người dùng hoạt động</dt>
                    <dd className="text-lg font-medium text-gray-900">{activeUsers}/{totalUsers}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 20 12 16.77 5.82 20 7 13.87 2 9l6.91-.74L12 2z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Khóa học hoạt động</dt>
                    <dd className="text-lg font-medium text-gray-900">{activeCourses}/{totalCourses}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Bài học hoạt động</dt>
                    <dd className="text-lg font-medium text-gray-900">{activeLessons}/{totalLessons}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Tỷ lệ hoàn thành</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {totalEnrollments > 0 ? Math.round((completedEnrollments / totalEnrollments) * 100) : 0}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Điểm trung bình</dt>
                    <dd className="text-lg font-medium text-gray-900">{averageScore}%</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Charts and Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Popular Courses */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Khóa học phổ biến
              </h3>
              <div className="space-y-3">
                {popularCourses.map((course, index) => (
                  <div key={course.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-500 w-6">
                        #{index + 1}
                      </span>
                      <span className="ml-3 text-sm font-medium text-gray-900 truncate">
                        {course.name}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500">
                        {course.enrollmentCount} đăng ký
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quiz Performance */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Hiệu suất bài kiểm tra
              </h3>
              <div className="space-y-3">
                {quizPerformance.slice(0, 5).map((quiz) => (
                  <div key={quiz.id} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {quiz.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {quiz.totalAttempts} lượt làm
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">
                        {quiz.passRate}% đạt
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {quiz.averageScore}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Top Performers */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Người học xuất sắc
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Người dùng
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Khóa học
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Bài kiểm tra
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Điểm TB
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {userPerformance.map((user) => (
                    <tr key={user.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-red-500 flex items-center justify-center">
                              <span className="text-white font-medium">
                                {user.fullName.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {user.fullName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {user.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {user.completedCourses}/{user.totalEnrollments}
                        </div>
                        <div className="text-sm text-gray-500">
                          hoàn thành
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {user.passedQuizzes}/{user.totalQuizzes}
                        </div>
                        <div className="text-sm text-gray-500">
                          đạt yêu cầu
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.averageScore >= 80 
                            ? 'bg-green-100 text-green-800' 
                            : user.averageScore >= 60
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {user.averageScore}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
