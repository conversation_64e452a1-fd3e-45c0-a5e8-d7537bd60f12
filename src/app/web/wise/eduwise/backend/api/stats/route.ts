import { NextRequest, NextResponse } from 'next/server';
import { getBackendStats } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const stats = getBackendStats();
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching backend stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    );
  }
}
