import { NextRequest, NextResponse } from 'next/server';
import { getAllUserEnrollments, createUserEnrollment } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const enrollments = getAllUserEnrollments();
    return NextResponse.json(enrollments);
  } catch (error) {
    console.error('Error fetching enrollments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch enrollments' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const enrollmentData = await request.json();
    
    // Validate required fields
    const requiredFields = ['user_id', 'course_id'];
    for (const field of requiredFields) {
      if (!enrollmentData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const newEnrollment = createUserEnrollment({
      user_id: enrollmentData.user_id,
      course_id: enrollmentData.course_id,
      enrolled_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      progress: enrollmentData.progress || 0,
      status: enrollmentData.status || 'enrolled',
      completed_at: enrollmentData.completed_at
    });

    return NextResponse.json(newEnrollment, { status: 201 });
  } catch (error) {
    console.error('Error creating enrollment:', error);
    return NextResponse.json(
      { error: 'Failed to create enrollment' },
      { status: 500 }
    );
  }
}
