import { NextRequest, NextResponse } from 'next/server';
import { getAllUserActivities, createUserActivity } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const activities = getAllUserActivities();
    return NextResponse.json(activities);
  } catch (error) {
    console.error('Error fetching activities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activities' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const activityData = await request.json();
    
    // Validate required fields
    const requiredFields = ['user_id', 'activity_type'];
    for (const field of requiredFields) {
      if (!activityData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const newActivity = createUserActivity({
      user_id: activityData.user_id,
      activity_type: activityData.activity_type,
      course_id: activityData.course_id,
      lesson_id: activityData.lesson_id,
      quiz_id: activityData.quiz_id,
      timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      details: activityData.details
    });

    return NextResponse.json(newActivity, { status: 201 });
  } catch (error) {
    console.error('Error creating activity:', error);
    return NextResponse.json(
      { error: 'Failed to create activity' },
      { status: 500 }
    );
  }
}
