import { NextRequest, NextResponse } from 'next/server';
import { getAllLessons, createLesson } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const lessons = getAllLessons();
    return NextResponse.json(lessons);
  } catch (error) {
    console.error('Error fetching lessons:', error);
    return NextResponse.json(
      { error: 'Failed to fetch lessons' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const lessonData = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'slug'];
    for (const field of requiredFields) {
      if (!lessonData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const newLesson = createLesson({
      name: lessonData.name,
      slug: lessonData.slug,
      description: lessonData.description || '',
      content: lessonData.content || '',
      video_url: lessonData.video_url || '',
      intro: lessonData.intro || '',
      duration: lessonData.duration || 0,
      duration_type: lessonData.duration_type || 1,
      status: lessonData.status || 1
    });

    return NextResponse.json(newLesson, { status: 201 });
  } catch (error) {
    console.error('Error creating lesson:', error);
    return NextResponse.json(
      { error: 'Failed to create lesson' },
      { status: 500 }
    );
  }
}
