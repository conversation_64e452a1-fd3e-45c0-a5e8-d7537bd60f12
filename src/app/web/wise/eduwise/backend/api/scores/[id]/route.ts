import { NextRequest, NextResponse } from 'next/server';
import { getAllUserScores } from '../../../lib/data-access';
import fs from 'fs';
import path from 'path';

const DATA_DIR = path.join(process.cwd(), 'data/apps/web/wise/eduwise');
const SCORES_FILE = path.join(DATA_DIR, 'user-scores.json');

function writeScores(scores: any[]) {
  try {
    if (!fs.existsSync(DATA_DIR)) {
      fs.mkdirSync(DATA_DIR, { recursive: true });
    }
    fs.writeFileSync(SCORES_FILE, JSON.stringify(scores, null, 2), 'utf8');
  } catch (error) {
    console.error('Error writing scores file:', error);
    throw error;
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const scores = getAllUserScores();
    const filteredScores = scores.filter(score => score.id !== id);
    
    if (filteredScores.length === scores.length) {
      return NextResponse.json(
        { error: 'Score not found' },
        { status: 404 }
      );
    }
    
    writeScores(filteredScores);
    return NextResponse.json({ message: 'Score deleted successfully' });
  } catch (error) {
    console.error('Error deleting score:', error);
    return NextResponse.json(
      { error: 'Failed to delete score' },
      { status: 500 }
    );
  }
}
