import { NextRequest, NextResponse } from 'next/server';
import { getAllUserScores, createUserScore } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const scores = getAllUserScores();
    return NextResponse.json(scores);
  } catch (error) {
    console.error('Error fetching scores:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scores' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const scoreData = await request.json();
    
    // Validate required fields
    const requiredFields = ['user_id', 'course_id', 'quiz_id', 'score', 'max_score'];
    for (const field of requiredFields) {
      if (scoreData[field] === undefined || scoreData[field] === null) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Calculate percentage and passed status
    const percentage = Math.round((scoreData.score / scoreData.max_score) * 100);
    const passed = percentage >= (scoreData.passing_grade || 70);

    const newScore = createUserScore({
      user_id: scoreData.user_id,
      course_id: scoreData.course_id,
      quiz_id: scoreData.quiz_id,
      score: scoreData.score,
      max_score: scoreData.max_score,
      percentage,
      passed,
      completed_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      attempts: scoreData.attempts || 1
    });

    return NextResponse.json(newScore, { status: 201 });
  } catch (error) {
    console.error('Error creating score:', error);
    return NextResponse.json(
      { error: 'Failed to create score' },
      { status: 500 }
    );
  }
}
