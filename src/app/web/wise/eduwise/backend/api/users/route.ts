import { NextRequest, NextResponse } from 'next/server';
import { getAllUsers, createUser } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const users = getAllUsers();
    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();
    
    // Validate required fields
    const requiredFields = ['username', 'email', 'fullName', 'password'];
    for (const field of requiredFields) {
      if (!userData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const newUser = createUser({
      username: userData.username,
      email: userData.email,
      fullName: userData.fullName,
      phone: userData.phone || '',
      password: userData.password,
      organization: userData.organization || '',
      business_areas: userData.business_areas || '',
      references: userData.references || '',
      point: userData.point || 0,
      status: userData.status || 1
    });

    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
