import { NextRequest, NextResponse } from 'next/server';
import { getQuizById, updateQuiz, deleteQuiz, getQuestionsByQuizId, getAnswersByQuestionId } from '../../../lib/data-access';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const quizId = parseInt(resolvedParams.id);
    const quiz = getQuizById(quizId);
    
    if (!quiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      );
    }

    // Add questions to quiz
    const questions = getQuestionsByQuizId(quizId);
    const quizWithQuestions = {
      ...quiz,
      questions: questions.map(question => ({
        ...question,
        answers: getAnswersByQuestionId(question.id)
      }))
    };

    return NextResponse.json(quizWithQuestions);
  } catch (error) {
    console.error('Error fetching quiz:', error);
    return NextResponse.json(
      { error: 'Failed to fetch quiz' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    const quizData = await request.json();
    const updatedQuiz = updateQuiz(quizId, quizData);
    
    if (!updatedQuiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedQuiz);
  } catch (error) {
    console.error('Error updating quiz:', error);
    return NextResponse.json(
      { error: 'Failed to update quiz' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    const success = deleteQuiz(quizId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Quiz deleted successfully' });
  } catch (error) {
    console.error('Error deleting quiz:', error);
    return NextResponse.json(
      { error: 'Failed to delete quiz' },
      { status: 500 }
    );
  }
}
