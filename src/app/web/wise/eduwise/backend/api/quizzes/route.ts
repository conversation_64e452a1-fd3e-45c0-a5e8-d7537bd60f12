import { NextRequest, NextResponse } from 'next/server';
import { getAllQuizzes, createQuiz, getAllQuestions, getAnswersByQuestionId } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const quizzes = getAllQuizzes();
    const questions = getAllQuestions();
    
    // Add question count to each quiz
    const quizzesWithQuestions = quizzes.map(quiz => ({
      ...quiz,
      questions: questions.filter(q => q.quiz_id === quiz.id).map(question => ({
        ...question,
        answers: getAnswersByQuestionId(question.id)
      }))
    }));
    
    return NextResponse.json(quizzesWithQuestions);
  } catch (error) {
    console.error('Error fetching quizzes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch quizzes' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const quizData = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'slug'];
    for (const field of requiredFields) {
      if (!quizData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const newQuiz = createQuiz({
      name: quizData.name,
      slug: quizData.slug,
      description: quizData.description || '',
      content: quizData.content || '',
      duration: quizData.duration || 0,
      duration_type: quizData.duration_type || 1,
      passing_grade: quizData.passing_grade || 70,
      status: quizData.status || 1
    });

    return NextResponse.json(newQuiz, { status: 201 });
  } catch (error) {
    console.error('Error creating quiz:', error);
    return NextResponse.json(
      { error: 'Failed to create quiz' },
      { status: 500 }
    );
  }
}
