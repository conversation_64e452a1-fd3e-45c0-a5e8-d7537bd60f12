import { NextRequest, NextResponse } from 'next/server';
import { getAllCourses, createCourse } from '../../lib/data-access';

export async function GET(request: NextRequest) {
  try {
    const courses = getAllCourses();
    return NextResponse.json(courses);
  } catch (error) {
    console.error('Error fetching courses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch courses' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const courseData = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'slug', 'description'];
    for (const field of requiredFields) {
      if (!courseData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const newCourse = createCourse({
      name: courseData.name,
      slug: courseData.slug,
      description: courseData.description,
      content: courseData.content || '',
      thumbnail: courseData.thumbnail || null,
      duration: courseData.duration || 0,
      duration_type: courseData.duration_type || 1,
      regular_price: courseData.regular_price || null,
      sale_price: courseData.sale_price || null,
      status: courseData.status || 1,
      total_lessons: courseData.total_lessons || 0,
      total_quizzes: courseData.total_quizzes || 0,
      instructor_id: courseData.instructor_id || 1
    });

    return NextResponse.json(newCourse, { status: 201 });
  } catch (error) {
    console.error('Error creating course:', error);
    return NextResponse.json(
      { error: 'Failed to create course' },
      { status: 500 }
    );
  }
}
