import fs from 'fs';
import path from 'path';
import { User, Course, Quiz, Question, Answer, UserScore, UserEnrollment, UserActivity, BackendStats, CourseSection, CourseSectionItem, Lesson } from '../../types';

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data/apps/web/wise/eduwise');

const DATA_FILES = {
  users: path.join(DATA_DIR, 'users.json'),
  courses: path.join(DATA_DIR, 'courses.json'),
  courseSections: path.join(DATA_DIR, 'course-sections.json'),
  courseSectionItems: path.join(DATA_DIR, 'course-section-items.json'),
  lessons: path.join(DATA_DIR, 'lessons.json'),
  quizzes: path.join(DATA_DIR, 'quizzes.json'),
  questions: path.join(DATA_DIR, 'questions.json'),
  answers: path.join(DATA_DIR, 'answers.json'),
  userScores: path.join(DATA_DIR, 'user-scores.json'),
  userEnrollments: path.join(DATA_DIR, 'user-enrollments.json'),
  userActivities: path.join(DATA_DIR, 'user-activities.json'),
  faq: path.join(DATA_DIR, 'faq.json'),
};

// Generic file operations
function readJsonFile<T>(filePath: string): T[] {
  try {
    if (!fs.existsSync(filePath)) {
      return [];
    }
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data) || [];
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return [];
  }
}

function writeJsonFile<T>(filePath: string, data: T[]): void {
  try {
    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
  } catch (error) {
    console.error(`Error writing file ${filePath}:`, error);
    throw error;
  }
}

// User management functions
export function getAllUsers(): User[] {
  return readJsonFile<User>(DATA_FILES.users);
}

export function getUserById(id: string): User | null {
  const users = getAllUsers();
  return users.find(user => user.id === id) || null;
}

export function createUser(userData: Omit<User, 'id' | 'createdAt'>): User {
  const users = getAllUsers();
  const newUser: User = {
    ...userData,
    id: generateId(),
    createdAt: new Date().toISOString().slice(0, 19).replace('T', ' ')
  };
  
  users.push(newUser);
  writeJsonFile(DATA_FILES.users, users);
  return newUser;
}

export function updateUser(id: string, userData: Partial<User>): User | null {
  const users = getAllUsers();
  const userIndex = users.findIndex(user => user.id === id);
  
  if (userIndex === -1) return null;
  
  users[userIndex] = { ...users[userIndex], ...userData };
  writeJsonFile(DATA_FILES.users, users);
  return users[userIndex];
}

export function deleteUser(id: string): boolean {
  const users = getAllUsers();
  const filteredUsers = users.filter(user => user.id !== id);
  
  if (filteredUsers.length === users.length) return false;
  
  writeJsonFile(DATA_FILES.users, filteredUsers);
  return true;
}

// Course management functions
export function getAllCourses(): Course[] {
  const courses = readJsonFile<Course>(DATA_FILES.courses);
  const courseSections = getAllCourseSections();

  // Add sections to each course
  return courses.map(course => ({
    ...course,
    sections: courseSections.filter(section => section.course_id === course.id)
  }));
}

export function getCourseById(id: number): Course | null {
  const courses = readJsonFile<Course>(DATA_FILES.courses);
  const course = courses.find(course => course.id === id);
  if (!course) return null;

  const courseSections = getAllCourseSections();
  return {
    ...course,
    sections: courseSections.filter(section => section.course_id === course.id)
  };
}

export function createCourse(courseData: Omit<Course, 'id' | 'created_at' | 'updated_at'>): Course {
  const courses = readJsonFile<Course>(DATA_FILES.courses);
  const newCourse: Course = {
    ...courseData,
    id: Math.max(...courses.map(c => c.id), 0) + 1,
    created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
    updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
  };

  courses.push(newCourse);
  writeJsonFile(DATA_FILES.courses, courses);
  return newCourse;
}

export function updateCourse(id: number, courseData: Partial<Course>): Course | null {
  const courses = readJsonFile<Course>(DATA_FILES.courses);
  const courseIndex = courses.findIndex(course => course.id === id);

  if (courseIndex === -1) return null;

  courses[courseIndex] = {
    ...courses[courseIndex],
    ...courseData,
    updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
  };
  writeJsonFile(DATA_FILES.courses, courses);
  return courses[courseIndex];
}

export function deleteCourse(id: number): boolean {
  const courses = readJsonFile<Course>(DATA_FILES.courses);
  const filteredCourses = courses.filter(course => course.id !== id);

  if (filteredCourses.length === courses.length) return false;

  writeJsonFile(DATA_FILES.courses, filteredCourses);
  return true;
}

// Course section management functions
export function getAllCourseSections(): CourseSection[] {
  return readJsonFile<CourseSection>(DATA_FILES.courseSections);
}

export function getCourseSectionsByCourseId(courseId: number): CourseSection[] {
  const sections = getAllCourseSections();
  return sections.filter(section => section.course_id === courseId);
}

// Course section items management functions
export function getAllCourseSectionItems(): CourseSectionItem[] {
  return readJsonFile<CourseSectionItem>(DATA_FILES.courseSectionItems);
}

export function getCourseSectionItemsBySectionId(sectionId: number): CourseSectionItem[] {
  const items = getAllCourseSectionItems();
  return items.filter(item => item.course_section_id === sectionId);
}

// Lesson management functions
export function getAllLessons(): Lesson[] {
  return readJsonFile<Lesson>(DATA_FILES.lessons);
}

export function getLessonById(id: number): Lesson | null {
  const lessons = getAllLessons();
  return lessons.find(lesson => lesson.id === id) || null;
}

export function createLesson(lessonData: Omit<Lesson, 'id'>): Lesson {
  const lessons = getAllLessons();
  const newLesson: Lesson = {
    ...lessonData,
    id: Math.max(...lessons.map(l => l.id), 0) + 1
  };

  lessons.push(newLesson);
  writeJsonFile(DATA_FILES.lessons, lessons);
  return newLesson;
}

export function updateLesson(id: number, lessonData: Partial<Lesson>): Lesson | null {
  const lessons = getAllLessons();
  const lessonIndex = lessons.findIndex(lesson => lesson.id === id);

  if (lessonIndex === -1) return null;

  lessons[lessonIndex] = { ...lessons[lessonIndex], ...lessonData };
  writeJsonFile(DATA_FILES.lessons, lessons);
  return lessons[lessonIndex];
}

export function deleteLesson(id: number): boolean {
  const lessons = getAllLessons();
  const filteredLessons = lessons.filter(lesson => lesson.id !== id);

  if (filteredLessons.length === lessons.length) return false;

  writeJsonFile(DATA_FILES.lessons, filteredLessons);
  return true;
}

// Quiz management functions
export function getAllQuizzes(): Quiz[] {
  return readJsonFile<Quiz>(DATA_FILES.quizzes);
}

export function getQuizById(id: number): Quiz | null {
  const quizzes = getAllQuizzes();
  return quizzes.find(quiz => quiz.id === id) || null;
}

export function createQuiz(quizData: Omit<Quiz, 'id'>): Quiz {
  const quizzes = getAllQuizzes();
  const newQuiz: Quiz = {
    ...quizData,
    id: Math.max(...quizzes.map(q => q.id), 0) + 1
  };
  
  quizzes.push(newQuiz);
  writeJsonFile(DATA_FILES.quizzes, quizzes);
  return newQuiz;
}

export function updateQuiz(id: number, quizData: Partial<Quiz>): Quiz | null {
  const quizzes = getAllQuizzes();
  const quizIndex = quizzes.findIndex(quiz => quiz.id === id);
  
  if (quizIndex === -1) return null;
  
  quizzes[quizIndex] = { ...quizzes[quizIndex], ...quizData };
  writeJsonFile(DATA_FILES.quizzes, quizzes);
  return quizzes[quizIndex];
}

export function deleteQuiz(id: number): boolean {
  const quizzes = getAllQuizzes();
  const filteredQuizzes = quizzes.filter(quiz => quiz.id !== id);
  
  if (filteredQuizzes.length === quizzes.length) return false;
  
  writeJsonFile(DATA_FILES.quizzes, filteredQuizzes);
  return true;
}

// Question management functions
export function getAllQuestions(): Question[] {
  return readJsonFile<Question>(DATA_FILES.questions);
}

export function getQuestionsByQuizId(quizId: number): Question[] {
  const questions = getAllQuestions();
  return questions.filter(question => question.quiz_id === quizId);
}

export function createQuestion(questionData: Omit<Question, 'id'>): Question {
  const questions = getAllQuestions();
  const newQuestion: Question = {
    ...questionData,
    id: Math.max(...questions.map(q => q.id), 0) + 1
  };
  
  questions.push(newQuestion);
  writeJsonFile(DATA_FILES.questions, questions);
  return newQuestion;
}

export function updateQuestion(id: number, questionData: Partial<Question>): Question | null {
  const questions = getAllQuestions();
  const questionIndex = questions.findIndex(question => question.id === id);
  
  if (questionIndex === -1) return null;
  
  questions[questionIndex] = { ...questions[questionIndex], ...questionData };
  writeJsonFile(DATA_FILES.questions, questions);
  return questions[questionIndex];
}

export function deleteQuestion(id: number): boolean {
  const questions = getAllQuestions();
  const filteredQuestions = questions.filter(question => question.id !== id);
  
  if (filteredQuestions.length === questions.length) return false;
  
  writeJsonFile(DATA_FILES.questions, filteredQuestions);
  return true;
}

// Answer management functions
export function getAllAnswers(): Answer[] {
  return readJsonFile<Answer>(DATA_FILES.answers);
}

export function getAnswersByQuestionId(questionId: number): Answer[] {
  const answers = getAllAnswers();
  return answers.filter(answer => answer.question_id === questionId);
}

export function createAnswer(answerData: Omit<Answer, 'id'>): Answer {
  const answers = getAllAnswers();
  const newAnswer: Answer = {
    ...answerData,
    id: Math.max(...answers.map(a => a.id), 0) + 1
  };
  
  answers.push(newAnswer);
  writeJsonFile(DATA_FILES.answers, answers);
  return newAnswer;
}

export function updateAnswer(id: number, answerData: Partial<Answer>): Answer | null {
  const answers = getAllAnswers();
  const answerIndex = answers.findIndex(answer => answer.id === id);
  
  if (answerIndex === -1) return null;
  
  answers[answerIndex] = { ...answers[answerIndex], ...answerData };
  writeJsonFile(DATA_FILES.answers, answers);
  return answers[answerIndex];
}

export function deleteAnswer(id: number): boolean {
  const answers = getAllAnswers();
  const filteredAnswers = answers.filter(answer => answer.id !== id);
  
  if (filteredAnswers.length === answers.length) return false;
  
  writeJsonFile(DATA_FILES.answers, filteredAnswers);
  return true;
}

// User score management
export function getAllUserScores(): UserScore[] {
  return readJsonFile<UserScore>(DATA_FILES.userScores);
}

export function getUserScores(userId: string): UserScore[] {
  const scores = getAllUserScores();
  return scores.filter(score => score.user_id === userId);
}

export function createUserScore(scoreData: Omit<UserScore, 'id'>): UserScore {
  const scores = getAllUserScores();
  const newScore: UserScore = {
    ...scoreData,
    id: generateId()
  };
  
  scores.push(newScore);
  writeJsonFile(DATA_FILES.userScores, scores);
  return newScore;
}

// User enrollment management
export function getAllUserEnrollments(): UserEnrollment[] {
  return readJsonFile<UserEnrollment>(DATA_FILES.userEnrollments);
}

export function getUserEnrollments(userId: string): UserEnrollment[] {
  const enrollments = getAllUserEnrollments();
  return enrollments.filter(enrollment => enrollment.user_id === userId);
}

export function createUserEnrollment(enrollmentData: Omit<UserEnrollment, 'id'>): UserEnrollment {
  const enrollments = getAllUserEnrollments();
  const newEnrollment: UserEnrollment = {
    ...enrollmentData,
    id: generateId()
  };
  
  enrollments.push(newEnrollment);
  writeJsonFile(DATA_FILES.userEnrollments, enrollments);
  return newEnrollment;
}

// User activity tracking
export function getAllUserActivities(): UserActivity[] {
  return readJsonFile<UserActivity>(DATA_FILES.userActivities);
}

export function getUserActivities(userId: string): UserActivity[] {
  const activities = getAllUserActivities();
  return activities.filter(activity => activity.user_id === userId);
}

export function createUserActivity(activityData: UserActivity): UserActivity {
  const activities = getAllUserActivities();
  activities.push(activityData);
  writeJsonFile(DATA_FILES.userActivities, activities);
  return activityData;
}

// Statistics and analytics
export function getBackendStats(): BackendStats {
  const users = getAllUsers();
  const courses = readJsonFile<Course>(DATA_FILES.courses); // Get raw courses without sections
  const lessons = getAllLessons();
  const quizzes = getAllQuizzes();
  const enrollments = getAllUserEnrollments();

  return {
    total_users: users.length,
    total_courses: courses.length,
    total_lessons: lessons.length,
    total_quizzes: quizzes.length,
    total_enrollments: enrollments.length,
    active_users: users.filter(user => user.status === 1).length,
    completed_courses: enrollments.filter(enrollment => enrollment.status === 'completed').length
  };
}

// Utility functions
function generateId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}
