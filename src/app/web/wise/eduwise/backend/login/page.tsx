"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useOneIDAuth } from "../../hooks/useOneIDAuth";

export default function BackendLoginPage() {
  const router = useRouter();
  const { login, isLoading: authLoading, error: authError, clearError, user } = useOneIDAuth();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Check if user is already logged in and has admin access
  useEffect(() => {
    if (user) {
      // For backend access, we need to check OneID user metadata
      const checkAdminAccess = async () => {
        try {
          const response = await fetch('/api/backbone/oneid/auth/validate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              sessionId: sessionStorage.getItem('eduwise_session') || localStorage.getItem('eduwise_session'),
              source: 'eduwise' 
            }),
          });
          
          const result = await response.json();
          
          if (result.success && result.user?.metadata?.eduwise?.role === 'admin') {
            router.push('/web/wise/eduwise/backend');
          }
        } catch (error) {
          console.error('Admin access check failed:', error);
        }
      };
      
      checkAdminAccess();
    }
  }, [user, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
    
    // Clear OneID auth error
    if (authError) {
      clearError();
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = "Tên tài khoản không được để trống";
    }

    if (!formData.password) {
      newErrors.password = "Mật khẩu không được để trống";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await login(formData);

      if (result.success) {
        // Additional check for admin role
        const response = await fetch('/api/backbone/oneid/auth/validate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            sessionId: sessionStorage.getItem('eduwise_session') || localStorage.getItem('eduwise_session'),
            source: 'eduwise' 
          }),
        });
        
        const validateResult = await response.json();
        
        if (validateResult.success && validateResult.user?.metadata?.eduwise?.role === 'admin') {
          alert("Đăng nhập thành công!");
          router.push("/web/wise/eduwise/backend");
        } else {
          alert("Chỉ quản trị viên Eduwise mới có thể truy cập backend!");
          // Logout the user since they don't have admin access
          await fetch('/api/backbone/oneid/auth/logout', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              sessionId: sessionStorage.getItem('eduwise_session') || localStorage.getItem('eduwise_session'),
              source: 'eduwise' 
            }),
          });
          sessionStorage.removeItem('eduwise_session');
          localStorage.removeItem('eduwise_session');
        }
      } else {
        // Error message will be shown via authError state
        console.error("Login failed:", result.message);
      }

    } catch (error) {
      console.error("Login error:", error);
      alert("Đã xảy ra lỗi khi đăng nhập. Vui lòng thử lại.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-8">
      <div className="max-w-md w-full space-y-6">
        {/* Header */}
        <div className="text-center">
          <Link
            href="/web/wise/eduwise"
            className="inline-block mb-6 text-blue-600 hover:text-blue-800 transition-colors text-sm"
          >
            ← VỀ TRANG CHỦ EDUWISE
          </Link>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Eduwise Backend</h2>
          <p className="text-gray-600">Đăng nhập quản trị</p>
        </div>

        {/* Login Form */}
        <div className="bg-white shadow-lg rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Show OneID authentication error */}
            {authError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-700">
                {authError}
              </div>
            )}
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tên tài khoản
              </label>
              <input
                type="text"
                name="username"
                placeholder="Nhập tên tài khoản"
                value={formData.username}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 ${
                  errors.username ? 'border-red-500' : ''
                }`}
              />
              {errors.username && <p className="mt-1 text-sm text-red-600">{errors.username}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mật khẩu
              </label>
              <input
                type="password"
                name="password"
                placeholder="Nhập mật khẩu"
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 ${
                  errors.password ? 'border-red-500' : ''
                }`}
              />
              {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 text-gray-600">
                  Nhớ tài khoản
                </label>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading || authLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading || authLoading ? "Đang đăng nhập..." : "Đăng nhập Backend"}
            </button>
          </form>

          {/* Test Credentials */}
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Tài khoản test:</h3>
            <div className="text-xs text-gray-600 space-y-1">
              <div><strong>Admin:</strong> eduwise_admin / admin123</div>
              <div><strong>Teacher:</strong> eduwise_teacher / password123</div>
              <div><strong>Student:</strong> eduwise_student / password123</div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              * Chỉ tài khoản Admin mới có thể truy cập backend
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}