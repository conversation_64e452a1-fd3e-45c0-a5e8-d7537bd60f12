import { NextRequest, NextResponse } from 'next/server';

export interface AuthUser {
  id: string;
  username: string;
  email: string;
  fullName: string;
  phone: string;
  role?: string;
}

export interface AuthenticatedRequest extends NextRequest {
  user?: AuthUser;
}

export async function verifyOneIDSession(sessionId: string): Promise<AuthUser | null> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/backbone/oneid/auth/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ sessionId, source: 'eduwise' }),
    });

    if (!response.ok) {
      return null;
    }

    const result = await response.json();
    
    if (result.success && result.user) {
      return {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        fullName: result.user.fullName || result.user.name || '',
        phone: result.user.phone || '',
        role: result.user.role || 'user'
      };
    }

    return null;
  } catch (error) {
    console.error('OneID session verification error:', error);
    return null;
  }
}

export async function authenticateRequest(request: NextRequest): Promise<AuthUser | null> {
  // Check for session in cookies
  const sessionCookie = request.cookies.get('eduwise_session');
  if (sessionCookie) {
    try {
      const sessionData = JSON.parse(sessionCookie.value);
      const user = await verifyOneIDSession(sessionData.id);
      if (user) {
        return user;
      }
    } catch (error) {
      console.error('Session cookie parsing error:', error);
    }
  }

  // Check for Authorization header
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    try {
      // Verify token with OneID
      const user = await verifyOneIDSession(token);
      if (user) {
        return user;
      }
    } catch (error) {
      console.error('Token verification error:', error);
    }
  }

  return null;
}

export function requireAuth(handler: (request: AuthenticatedRequest, ...args: any[]) => Promise<NextResponse>) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const user = await authenticateRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Add user to request object
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = user;

    return handler(authenticatedRequest, ...args);
  };
}

export function requireAdminAuth(handler: (request: AuthenticatedRequest, ...args: any[]) => Promise<NextResponse>) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const user = await authenticateRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin role
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Add user to request object
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = user;

    return handler(authenticatedRequest, ...args);
  };
}

export function optionalAuth(handler: (request: AuthenticatedRequest, ...args: any[]) => Promise<NextResponse>) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const user = await authenticateRequest(request);
    
    // Add user to request object (can be null)
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = user;

    return handler(authenticatedRequest, ...args);
  };
}