import { NextRequest, NextResponse } from 'next/server';

export interface EduwiseAuthUser {
  id: string;
  username: string;
  email: string;
  fullName: string;
  phone: string;
  role: string;
  permissions: string[];
}

export interface EduwiseAuthenticatedRequest extends NextRequest {
  user?: EduwiseAuthUser;
}

export async function verifyEduwiseSession(sessionId: string): Promise<EduwiseAuthUser | null> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/backbone/oneid/auth/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ sessionId, source: 'eduwise' }),
    });

    if (!response.ok) {
      return null;
    }

    const result = await response.json();
    
    if (result.success && result.user && result.user.metadata?.eduwise) {
      const eduwiseData = result.user.metadata.eduwise;
      return {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        fullName: result.user.fullName || result.user.name || '',
        phone: result.user.phone || '',
        role: eduwiseData.role || 'student',
        permissions: eduwiseData.permissions || []
      };
    }

    return null;
  } catch (error) {
    console.error('Eduwise session verification error:', error);
    return null;
  }
}

export async function authenticateEduwiseRequest(request: NextRequest): Promise<EduwiseAuthUser | null> {
  // Check for session in cookies
  const sessionCookie = request.cookies.get('eduwise_session');
  if (sessionCookie) {
    try {
      const sessionData = JSON.parse(sessionCookie.value);
      const user = await verifyEduwiseSession(sessionData.id);
      if (user) {
        return user;
      }
    } catch (error) {
      console.error('Session cookie parsing error:', error);
    }
  }

  // Check for Authorization header
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    try {
      // Verify token with OneID
      const user = await verifyEduwiseSession(token);
      if (user) {
        return user;
      }
    } catch (error) {
      console.error('Token verification error:', error);
    }
  }

  return null;
}

export function requireEduwiseAuth(handler: (request: EduwiseAuthenticatedRequest, ...args: any[]) => Promise<NextResponse>) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const user = await authenticateEduwiseRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Eduwise authentication required' },
        { status: 401 }
      );
    }

    // Add user to request object
    const authenticatedRequest = request as EduwiseAuthenticatedRequest;
    authenticatedRequest.user = user;

    return handler(authenticatedRequest, ...args);
  };
}

export function requireEduwiseAdminAuth(handler: (request: EduwiseAuthenticatedRequest, ...args: any[]) => Promise<NextResponse>) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const user = await authenticateEduwiseRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Eduwise authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin role for Eduwise
    if (user.role !== 'admin' && user.role !== 'teacher') {
      return NextResponse.json(
        { error: 'Eduwise admin or teacher access required' },
        { status: 403 }
      );
    }

    // Add user to request object
    const authenticatedRequest = request as EduwiseAuthenticatedRequest;
    authenticatedRequest.user = user;

    return handler(authenticatedRequest, ...args);
  };
}

export function requireEduwiseBackendAuth(handler: (request: EduwiseAuthenticatedRequest, ...args: any[]) => Promise<NextResponse>) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const user = await authenticateEduwiseRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Eduwise authentication required' },
        { status: 401 }
      );
    }

    // Only admin users can access backend
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Eduwise backend admin access required' },
        { status: 403 }
      );
    }

    // Add user to request object
    const authenticatedRequest = request as EduwiseAuthenticatedRequest;
    authenticatedRequest.user = user;

    return handler(authenticatedRequest, ...args);
  };
}