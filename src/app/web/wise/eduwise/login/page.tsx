"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useOneIDAuth } from "../hooks/useOneIDAuth";
import LoginPromoSlider from "../components/LoginPromoSlider";

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get('redirect');
  const errorParam = searchParams.get('error');
  const { login, isLoading: authLoading, error: authError, clearError } = useOneIDAuth();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Show error message from URL parameters
  useEffect(() => {
    if (errorParam === 'insufficient_permissions') {
      setStatusMessage({ type: 'error', message: 'Bạn không có quyền truy cập vào backend. Chỉ admin mới có thể truy cập.' });
    }
    
    // Debug: log redirect URL
    console.log('Redirect URL from params:', redirectUrl);
  }, [errorParam, redirectUrl]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
    
    // Clear OneID auth error
    if (authError) {
      clearError();
    }
    
    // Clear status message
    if (statusMessage) {
      setStatusMessage(null);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = "Tên tài khoản không được để trống";
    }

    if (!formData.password) {
      newErrors.password = "Mật khẩu không được để trống";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await login(formData);

      if (result.success) {
        // Show success message and redirect
        setStatusMessage({ type: 'success', message: 'Đăng nhập thành công! Đang chuyển hướng...' });
        
        // Debug: log redirect information
        console.log('Login successful, redirecting...');
        console.log('redirectUrl from params:', redirectUrl);
        const targetUrl = redirectUrl || "/web/wise/eduwise";
        console.log('Target URL for redirect:', targetUrl);
        
        setTimeout(() => {
          // Redirect to the intended page or default to home
          console.log('Executing router.push to:', targetUrl);
          router.push(targetUrl);
        }, 1500);
      } else {
        // Error message will be shown via authError state
        console.error("Login failed:", result.message);
      }

    } catch (error) {
      console.error("Login error:", error);
      setStatusMessage({ type: 'error', message: 'Đã xảy ra lỗi khi đăng nhập. Vui lòng thử lại.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white flex">
      {/* Left side - Promotional content */}
      <LoginPromoSlider />

      {/* Right side - Login form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <div className="max-w-md w-full space-y-6">
          {/* Header */}
          <div className="text-center">
            <Link
              href="/web/wise/eduwise"
              className="inline-block mb-6 text-wise-primary hover:text-wise-primary-dark transition-colors text-sm"
            >
              VỀ TRANG CHỦ
            </Link>
            <h2 className="text-2xl font-bold text-wise-neutral-900 mb-6">Đăng nhập</h2>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Show status messages */}
            {statusMessage && (
              <div className={`border rounded-md p-3 text-sm ${
                statusMessage.type === 'success' 
                  ? 'bg-green-50 border-green-200 text-green-700'
                  : 'bg-red-50 border-red-200 text-red-700'
              }`}>
                {statusMessage.message}
              </div>
            )}
            
            {/* Show OneID authentication error */}
            {authError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-700">
                {authError}
              </div>
            )}
            
            <div>
              <input
                type="text"
                name="username"
                placeholder="Tên tài khoản"
                value={formData.username}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.username ? 'border-wise-error' : ''
                }`}
              />
              {errors.username && <p className="mt-1 text-sm text-red-600">{errors.username}</p>}
            </div>

            <div>
              <input
                type="password"
                name="password"
                placeholder="Mật khẩu"
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.password ? 'border-wise-error' : ''
                }`}
              />
              {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-wise-primary focus:ring-wise-primary border-wise-neutral-300 rounded"
                />
                <label className="ml-2 text-wise-neutral-600">
                  Nhớ tài khoản
                </label>
              </div>
              <Link
                href="/web/wise/eduwise/forgot-password"
                className="text-wise-primary hover:text-wise-primary-dark"
              >
                Quên mật khẩu ?
              </Link>
            </div>

            <button
              type="submit"
              disabled={isLoading || authLoading}
              className="w-full bg-wise-primary text-white py-2 px-4 rounded hover:bg-wise-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading || authLoading ? "Đang đăng nhập..." : "Đăng nhập"}
            </button>
          </form>

          {/* Backend Access Note */}
          {redirectUrl?.includes('/backend') && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700">
              <strong>Lưu ý:</strong> Bạn đang cố gắng truy cập vào hệ thống quản lý backend. 
              Chỉ tài khoản với quyền admin mới có thể truy cập.
            </div>
          )}

          {/* Social Login */}
          {/* <div className="mt-4">
            <div className="text-center text-wise-neutral-500 mb-3 text-sm">Hoặc đăng nhập</div>
            <div className="space-y-2">
              <button className="w-full flex items-center justify-start px-3 py-2 border border-wise-neutral-300 rounded hover:bg-wise-neutral-50 transition-colors text-sm">
                <Image
                  src="/net-icon-01.png"
                  alt="Google"
                  width={16}
                  height={16}
                  className="mr-2"
                />
                Đăng nhập bằng google
              </button>
              <button className="w-full flex items-center justify-start px-3 py-2 border border-wise-neutral-300 rounded hover:bg-wise-neutral-50 transition-colors text-sm">
                <Image
                  src="/net-icon-02.png"
                  alt="Facebook"
                  width={16}
                  height={16}
                  className="mr-2"
                />
                Đăng nhập bằng facebook
              </button>
            </div> 
          </div>
          */}

          {/* Register Link */}
          <div className="text-center">
            <p className="text-wise-neutral-600 text-sm">
              Bạn chưa có tài khoản?{" "}
              <Link href="/web/wise/eduwise/register" className="text-wise-primary hover:text-wise-primary-dark">
                Tạo tài khoản
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
