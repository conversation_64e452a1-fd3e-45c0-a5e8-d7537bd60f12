"use client";

import { useState, useEffect, useCallback } from 'react';
import { User, LoginData, RegisterData, authService } from '../lib/auth';

export interface UseOneIDAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (data: LoginData) => Promise<{ success: boolean; message: string; user?: User }>;
  register: (data: RegisterData) => Promise<{ success: boolean; message: string; user?: User }>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<{ success: boolean; message: string }>;
  clearError: () => void;
  refreshAuth: () => Promise<void>;
}

export function useOneIDAuth(): UseOneIDAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshAuth = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const currentUser = authService.getCurrentUser();
      
      if (currentUser) {
        // Validate session with OneID
        const isValid = await authService.validateSession();
        if (isValid) {
          setUser(currentUser);
        } else {
          setUser(null);
          // Don't set error for expired sessions, just silently logout
        }
      } else {
        setUser(null);
      }
    } catch (err) {
      console.error('Auth refresh error:', err);
      setUser(null);
      // Don't show error on refresh failure
    } finally {
      setIsLoading(false);
    }
  }, []);

  const login = useCallback(async (data: LoginData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await authService.login(data);
      
      if (result.success && result.user) {
        setUser(result.user);
      } else {
        setError(result.message);
      }
      
      return result;
    } catch (err) {
      const errorMessage = 'Đã xảy ra lỗi khi đăng nhập';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (data: RegisterData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await authService.register(data);
      
      if (!result.success) {
        setError(result.message);
      }
      
      return result;
    } catch (err) {
      const errorMessage = 'Đã xảy ra lỗi khi đăng ký';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      setError(null);
    } catch (err) {
      console.error('Logout error:', err);
      // Still clear local state even if API call fails
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const forgotPassword = useCallback(async (email: string) => {
    try {
      setError(null);
      const result = await authService.forgotPassword(email);
      
      if (!result.success) {
        setError(result.message);
      }
      
      return result;
    } catch (err) {
      const errorMessage = 'Đã xảy ra lỗi khi gửi email đặt lại mật khẩu';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    }
  }, []);

  // Initialize auth state on mount
  useEffect(() => {
    refreshAuth();
  }, [refreshAuth]);

  // Set up periodic session validation
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(async () => {
      try {
        const isValid = await authService.validateSession();
        if (!isValid) {
          setUser(null);
          // Silently logout on session expiry
        }
      } catch (err) {
        console.error('Session validation error:', err);
      }
    }, 10 * 60 * 1000); // Check every 10 minutes

    return () => clearInterval(interval);
  }, [user]);

  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    register,
    logout,
    forgotPassword,
    clearError,
    refreshAuth,
  };
}