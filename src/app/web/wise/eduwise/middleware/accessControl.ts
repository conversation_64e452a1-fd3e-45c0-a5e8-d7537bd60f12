// WISE Eduwise Access Control Middleware
// This middleware ensures that users have the proper OneID permissions to access the eduwise system

import { NextRequest, NextResponse } from 'next/server';
import { initializeOneID } from '@/app/backbone/oneid';
import { getUserContext } from '@/app/backbone/oneid/middleware/AuthContext';

const EDUWISE_PERMISSION = 'eduwise_access';
const EDUWISE_ROLE = 'eduwise_user';

export interface EduwiseAccessResult {
  success: boolean;
  user?: unknown;
  hasAccess: boolean;
  redirectUrl?: string;
  error?: string;
}

/**
 * Check if the current user has access to the eduwise system
 * This function can be used both in API routes and client-side
 */
export async function checkEduwiseAccess(req: NextRequest): Promise<EduwiseAccessResult> {
  try {
    // Get user context from OneID
    const userContext = await getUserContext(req);
    
    if (!userContext.success || !userContext.user) {
      return {
        success: false,
        hasAccess: false,
        redirectUrl: '/web/wise/eduwise/login?returnUrl=' + encodeURIComponent(req.url),
        error: 'User not authenticated'
      };
    }

    const user = userContext.user;
    const oneID = await initializeOneID();

    // Check if user has the eduwise role
    const userRoles = await oneID.accessControl.getUserRoles(user.id);
    const hasEduwiseRole = userRoles.some(role => role.name === EDUWISE_ROLE);

    if (!hasEduwiseRole) {
      // Check if user has the specific permission
      const userPermissions = await oneID.accessControl.getUserPermissions(user.id);
      const hasEduwisePermission = userPermissions.some(permission => 
        permission.name === EDUWISE_PERMISSION
      );

      if (!hasEduwisePermission) {
        return {
          success: true,
          user,
          hasAccess: false,
          error: 'User does not have permission to access eduwise system'
        };
      }
    }

    return {
      success: true,
      user,
      hasAccess: true
    };

  } catch (error) {
    console.error('Error checking eduwise access:', error);
    return {
      success: false,
      hasAccess: false,
      error: 'Failed to check user access'
    };
  }
}

/**
 * Middleware for protecting eduwise routes
 * Usage: Add this to your API routes or page components that need protection
 */
export async function requireEduwiseAccess(
  req: NextRequest,
  handler: (req: NextRequest, user: unknown) => Promise<NextResponse>
): Promise<NextResponse> {
  const accessResult = await checkEduwiseAccess(req);

  if (!accessResult.success) {
    return NextResponse.json(
      { 
        success: false, 
        error: accessResult.error || 'Access check failed',
        redirectUrl: accessResult.redirectUrl
      },
      { status: 401 }
    );
  }

  if (!accessResult.hasAccess) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Access denied: You do not have permission to access the eduwise system',
        requiresPermission: EDUWISE_PERMISSION
      },
      { status: 403 }
    );
  }

  if (!accessResult.user) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'User data not available'
      },
      { status: 500 }
    );
  }

  return await handler(req, accessResult.user);
}

/**
 * Client-side hook for checking eduwise access
 * This can be used in React components to conditionally render content
 */
export function useEduwiseAccess() {
  // This would typically use React hooks to check authentication status
  // For now, we'll create a simple version that can be expanded
  
  return {
    checkAccess: async (): Promise<EduwiseAccessResult> => {
      try {
        const response = await fetch('/api/backbone/oneid/access/check', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            resource: 'module',
            action: 'read',
            resourceId: 'eduwise'
          })
        });

        const result = await response.json();
        
        if (!result.success) {
          return {
            success: false,
            hasAccess: false,
            error: result.error
          };
        }

        return {
          success: true,
          hasAccess: result.hasAccess,
          user: result.user
        };

      } catch {
        return {
          success: false,
          hasAccess: false,
          error: 'Failed to check access'
        };
      }
    }
  };
}

/**
 * Utility function to grant eduwise access to a user
 * This can be used by administrators to grant access
 */
export async function grantEduwiseAccess(userId: string, grantedBy: string): Promise<boolean> {
  try {
    const oneID = await initializeOneID();

    // Get the eduwise role
    const roles = await oneID.accessControl.listRoles();
    const eduwiseRole = roles.find(role => role.name === EDUWISE_ROLE);

    if (!eduwiseRole) {
      console.error('Eduwise role not found');
      return false;
    }

    // Assign the role to the user
    await oneID.accessControl.assignRole(userId, eduwiseRole.id, grantedBy, undefined, undefined);
    
    console.log(`Granted eduwise access to user ${userId}`);
    return true;

  } catch (error) {
    console.error('Failed to grant eduwise access:', error);
    return false;
  }
}

/**
 * Utility function to revoke eduwise access from a user
 */
export async function revokeEduwiseAccess(userId: string): Promise<boolean> {
  try {
    const oneID = await initializeOneID();

    // Get the eduwise role
    const roles = await oneID.accessControl.listRoles();
    const eduwiseRole = roles.find(role => role.name === EDUWISE_ROLE);

    if (!eduwiseRole) {
      console.error('Eduwise role not found');
      return false;
    }

    // Revoke the role from the user
    await oneID.accessControl.revokeRole(userId, eduwiseRole.id);
    
    console.log(`Revoked eduwise access from user ${userId}`);
    return true;

  } catch (error) {
    console.error('Failed to revoke eduwise access:', error);
    return false;
  }
}

const eduwiseAccessControl = {
  checkEduwiseAccess,
  requireEduwiseAccess,
  useEduwiseAccess,
  grantEduwiseAccess,
  revokeEduwiseAccess
};

export default eduwiseAccessControl;
