"use client";

export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  phone: string;
  createdAt: string;
}

export interface LoginData {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  username: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  phone: string;
  email: string;
  agreeToTerms: boolean;
}

// OneID-integrated authentication service
class AuthService {
  private currentUserKey = 'eduwise_current_user';
  private sessionKey = 'eduwise_session';

  private async callOneIDAPI(endpoint: string, data: any): Promise<any> {
    try {
      const response = await fetch(`/api/backbone/oneid${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('OneID API call failed:', error);
      throw error;
    }
  }

  private mapOneIDUserToEduwiseUser(oneIDUser: any): User {
    return {
      id: oneIDUser.id,
      username: oneIDUser.username,
      email: oneIDUser.email,
      fullName: oneIDUser.fullName || oneIDUser.name || '',
      phone: oneIDUser.phone || '',
      createdAt: oneIDUser.createdAt || new Date().toISOString(),
    };
  }

  private getUserRole(user: User): string {
    // Determine user role based on username or other criteria
    // This matches the test accounts mentioned in the backend login page
    if (user.username === 'eduwise_admin') {
      return 'admin';
    } else if (user.username === 'eduwise_teacher') {
      return 'teacher';
    } else if (user.username === 'eduwise_student') {
      return 'student';
    } else {
      // Default role for other users
      return 'student';
    }
  }

  private saveSession(user: User, session: any, rememberMe: boolean = false) {
    if (typeof window === 'undefined') return;
    
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(this.currentUserKey, JSON.stringify(user));
    storage.setItem(this.sessionKey, JSON.stringify(session));
    
    // Also save session in the other storage for backup
    const backupStorage = rememberMe ? sessionStorage : localStorage;
    backupStorage.setItem(this.currentUserKey, JSON.stringify(user));
    backupStorage.setItem(this.sessionKey, JSON.stringify(session));
    
    // Create token for middleware access
    // The middleware expects a base64 encoded JSON token with user metadata
    const tokenData = {
      username: user.username,
      id: user.id,
      exp: Math.floor(Date.now() / 1000) + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60), // expiry timestamp
      metadata: {
        eduwise: {
          role: this.getUserRole(user) // We need to determine the user's role
        }
      }
    };
    
    const token = btoa(JSON.stringify(tokenData));
    const maxAge = rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60; // 30 days or 1 day
    document.cookie = `edu_oneid_token=${token}; max-age=${maxAge}; path=/; samesite=lax`;
    console.log('Token saved to cookie for user:', user.username, 'with role:', tokenData.metadata.eduwise.role);
  }

  private clearSession() {
    if (typeof window === 'undefined') return;
    sessionStorage.removeItem(this.currentUserKey);
    sessionStorage.removeItem(this.sessionKey);
    localStorage.removeItem(this.currentUserKey);
    localStorage.removeItem(this.sessionKey);
    
    // Clear the cookie
    document.cookie = 'edu_oneid_token=; max-age=0; path=/; samesite=lax';
    console.log('Token cookie cleared');
  }

  async register(data: RegisterData): Promise<{ success: boolean; message: string; user?: User }> {
    try {
      const response = await this.callOneIDAPI('/auth/register/individual', {
        username: data.username,
        password: data.password,
        email: data.email,
        fullName: data.fullName,
        phone: data.phone,
        name: data.fullName,
        metadata: {
          source: 'eduwise',
          agreeToTerms: data.agreeToTerms
        }
      });

      if (response.success) {
        const user = this.mapOneIDUserToEduwiseUser(response.user);
        return {
          success: true,
          message: 'Đăng ký thành công!',
          user
        };
      } else {
        return {
          success: false,
          message: response.message || 'Đăng ký thất bại'
        };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: 'Đã xảy ra lỗi khi đăng ký. Vui lòng thử lại.'
      };
    }
  }

  async login(data: LoginData): Promise<{ success: boolean; message: string; user?: User }> {
    try {
      const response = await this.callOneIDAPI('/auth/login', {
        username: data.username,
        password: data.password,
        source: 'eduwise'
      });

      if (response.success) {
        const user = this.mapOneIDUserToEduwiseUser(response.user);
        this.saveSession(user, response.session, data.rememberMe);
        
        return {
          success: true,
          message: 'Đăng nhập thành công!',
          user
        };
      } else {
        return {
          success: false,
          message: response.message || 'Đăng nhập thất bại'
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Đã xảy ra lỗi khi đăng nhập. Vui lòng thử lại.'
      };
    }
  }

  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;
    
    // Check session storage first, then local storage
    let userData = sessionStorage.getItem(this.currentUserKey);
    if (!userData) {
      userData = localStorage.getItem(this.currentUserKey);
    }
    
    return userData ? JSON.parse(userData) : null;
  }

  async logout() {
    if (typeof window === 'undefined') return;
    
    try {
      // Get current session
      let sessionData = sessionStorage.getItem(this.sessionKey);
      if (!sessionData) {
        sessionData = localStorage.getItem(this.sessionKey);
      }
      
      if (sessionData) {
        const session = JSON.parse(sessionData);
        // Call OneID logout API
        await this.callOneIDAPI('/auth/logout', {
          sessionId: session.id,
          source: 'eduwise'
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearSession();
    }
  }

  async forgotPassword(email: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.callOneIDAPI('/auth/forgot-password', {
        email,
        source: 'eduwise'
      });
      
      if (response.success) {
        return {
          success: true,
          message: 'Email đặt lại mật khẩu đã được gửi'
        };
      } else {
        return {
          success: false,
          message: response.message || 'Email không tồn tại trong hệ thống'
        };
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      return {
        success: false,
        message: 'Đã xảy ra lỗi. Vui lòng thử lại.'
      };
    }
  }

  async validateSession(): Promise<boolean> {
    if (typeof window === 'undefined') return false;
    
    try {
      let sessionData = sessionStorage.getItem(this.sessionKey);
      if (!sessionData) {
        sessionData = localStorage.getItem(this.sessionKey);
      }
      
      if (!sessionData) return false;
      
      const session = JSON.parse(sessionData);
      
      // Check if session has expired locally first
      if (session.expiresAt && new Date() > new Date(session.expiresAt)) {
        this.clearSession();
        return false;
      }
      
      const response = await this.callOneIDAPI('/auth/validate', {
        sessionId: session.id,
        source: 'eduwise'
      });
      
      if (!response.success) {
        this.clearSession();
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Session validation error:', error);
      // Don't clear session on validation error - it might be a network issue
      return true;
    }
  }
}

export const authService = new AuthService();
