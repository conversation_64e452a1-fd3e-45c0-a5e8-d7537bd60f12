import { Course, CourseSection, Lesson, Quiz, Question, Answer, Tag } from '../types';

// Import JSON data
import coursesData from '../../../../../../data/apps/web/wise/eduwise/courses.json';
import courseSectionsData from '../../../../../../data/apps/web/wise/eduwise/course-sections.json';
import lessonsData from '../../../../../../data/apps/web/wise/eduwise/lessons.json';
import quizzesData from '../../../../../../data/apps/web/wise/eduwise/quizzes.json';
import questionsData from '../../../../../../data/apps/web/wise/eduwise/questions.json';
import answersData from '../../../../../../data/apps/web/wise/eduwise/answers.json';
import tagsData from '../../../../../../data/apps/web/wise/eduwise/tags.json';

// Type the imported data
const courses = coursesData as Course[];
const courseSections = courseSectionsData as CourseSection[];
const lessons = lessonsData as Lesson[];
const quizzes = quizzesData as Quiz[];
const questions = questionsData as Question[];
const answers = answersData as Answer[];
const tags = tagsData as Tag[];

// Simulate delay to mimic real API calls
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Course functions
export async function getAllCourses(): Promise<Course[]> {
  await delay(100); // Simulate network latency
  return courses.map(course => ({
    ...course,
    sections: getCourseSections(course.id)
  }));
}

export async function getCourseBySlug(slug: string): Promise<Course | null> {
  await delay(100); // Simulate network latency
  const course = courses.find(c => c.slug === slug);
  if (!course) return null;
  
  return {
    ...course,
    sections: getCourseSections(course.id)
  };
}

export async function getCourseById(id: number): Promise<Course | null> {
  await delay(100); // Simulate network latency
  const course = courses.find(c => c.id === id);
  if (!course) return null;

  return {
    ...course,
    sections: getCourseSections(course.id)
  };
}

// Synchronous version for navigation helpers
function getCourseByIdSync(id: number): Course | null {
  const course = courses.find(c => c.id === id);
  if (!course) return null;

  return {
    ...course,
    sections: getCourseSections(course.id)
  };
}

export function getCourseSections(courseId: number): CourseSection[] {
  return courseSections
    .filter(section => section.course_id === courseId)
    .sort((a, b) => a.sort_order - b.sort_order)
    .map(section => ({
      ...section,
      items: getSectionItems(section.id)
    }));
}

export function getSectionItems(sectionId: number) {
  // For now, we'll create mock section items based on lessons and quizzes
  // In a real implementation, this would come from course_section_items table
  const sectionLessons = lessons.filter(() => {
    // Mock relationship - in real app this would be properly linked
    return true; // Return all lessons for now
  }).slice(0, 3); // Limit to 3 lessons per section

  const sectionQuizzes = quizzes.filter(() => {
    // Mock relationship - in real app this would be properly linked
    return true; // Return all quizzes for now
  }).slice(0, 1); // Limit to 1 quiz per section
  
  const items: Array<{
    id: number;
    course_section_id: number;
    item_id: number;
    sort_order: number;
    type: number;
    lesson?: Lesson;
    quiz?: Quiz;
  }> = [];
  
  // Add lessons
  sectionLessons.forEach((lesson, index) => {
    items.push({
      id: lesson.id,
      course_section_id: sectionId,
      item_id: lesson.id,
      sort_order: index + 1,
      type: 1, // Lesson
      lesson
    });
  });
  
  // Add quizzes
  sectionQuizzes.forEach((quiz, index) => {
    items.push({
      id: quiz.id + 1000, // Offset to avoid ID conflicts
      course_section_id: sectionId,
      item_id: quiz.id,
      sort_order: sectionLessons.length + index + 1,
      type: 2, // Quiz
      quiz: {
        ...quiz,
        questions: getQuizQuestions(quiz.id)
      }
    });
  });
  
  return items.sort((a, b) => a.sort_order - b.sort_order);
}

// Lesson functions
export function getAllLessons(): Lesson[] {
  return lessons;
}

export function getLessonBySlug(slug: string): Lesson | null {
  return lessons.find(lesson => lesson.slug === slug) || null;
}

export function getLessonById(id: number): Lesson | null {
  return lessons.find(lesson => lesson.id === id) || null;
}

// Quiz functions
export function getAllQuizzes(): Quiz[] {
  return quizzes.map(quiz => ({
    ...quiz,
    questions: getQuizQuestions(quiz.id)
  }));
}

export function getQuizBySlug(slug: string): Quiz | null {
  const quiz = quizzes.find(q => q.slug === slug);
  if (!quiz) return null;
  
  return {
    ...quiz,
    questions: getQuizQuestions(quiz.id)
  };
}

export function getQuizById(id: number): Quiz | null {
  const quiz = quizzes.find(q => q.id === id);
  if (!quiz) return null;
  
  return {
    ...quiz,
    questions: getQuizQuestions(quiz.id)
  };
}

export function getQuizQuestions(quizId: number): Question[] {
  return questions
    .filter(question => question.quiz_id === quizId)
    .sort((a, b) => a.order - b.order)
    .map(question => ({
      ...question,
      answers: getQuestionAnswers(question.id)
    }));
}

export function getQuestionAnswers(questionId: number): Answer[] {
  return answers.filter(answer => answer.question_id === questionId);
}

// Search and filter functions
export function searchCourses(query: string): Course[] {
  const lowercaseQuery = query.toLowerCase();
  return courses.filter(course => 
    course.name.toLowerCase().includes(lowercaseQuery) ||
    course.description.toLowerCase().includes(lowercaseQuery)
  ).map(course => ({
    ...course,
    sections: getCourseSections(course.id)
  }));
}

export function getPopularCourses(limit: number = 6): Course[] {
  // Mock popular courses - in real app this would be based on enrollment data
  return courses.slice(0, limit).map(course => ({
    ...course,
    sections: getCourseSections(course.id)
  }));
}

export function getFeaturedCourses(limit: number = 3): Course[] {
  // Mock featured courses - in real app this would be based on featured flag
  return courses.slice(0, limit).map(course => ({
    ...course,
    sections: getCourseSections(course.id)
  }));
}

// Utility functions
export function formatDuration(duration: number, durationType: number): string {
  const types = {
    1: 'phút',
    2: 'giờ', 
    3: 'ngày',
    4: 'tuần'
  };
  
  return `${duration} ${types[durationType as keyof typeof types] || 'phút'}`;
}

export function calculateCourseProgress(course: Course, completedLessons: number[] = []): number {
  const totalLessons = course.total_lessons;
  if (totalLessons === 0) return 0;
  
  const completed = completedLessons.length;
  return Math.round((completed / totalLessons) * 100);
}

export function isQuizPassed(score: number, passingGrade: number): boolean {
  return score >= passingGrade;
}

// Navigation helpers
export function getNextLesson(currentLessonId: number, courseId: number): Lesson | null {
  const course = getCourseByIdSync(courseId);
  if (!course) return null;

  const allLessons: Lesson[] = [];
  course.sections?.forEach(section => {
    section.items?.forEach(item => {
      if (item.type === 1 && item.lesson) {
        allLessons.push(item.lesson);
      }
    });
  });

  const currentIndex = allLessons.findIndex(lesson => lesson.id === currentLessonId);
  if (currentIndex === -1 || currentIndex === allLessons.length - 1) return null;

  return allLessons[currentIndex + 1];
}

export function getPreviousLesson(currentLessonId: number, courseId: number): Lesson | null {
  const course = getCourseByIdSync(courseId);
  if (!course) return null;

  const allLessons: Lesson[] = [];
  course.sections?.forEach(section => {
    section.items?.forEach(item => {
      if (item.type === 1 && item.lesson) {
        allLessons.push(item.lesson);
      }
    });
  });

  const currentIndex = allLessons.findIndex(lesson => lesson.id === currentLessonId);
  if (currentIndex <= 0) return null;

  return allLessons[currentIndex - 1];
}

// Tag functions
export function getAllTags(): Tag[] {
  return tags.filter(tag => tag.deleted_at === null);
}

export function getTagById(id: string): Tag | null {
  return tags.find(tag => tag.id === id && tag.deleted_at === null) || null;
}

export function getTagsByIds(ids: string[]): Tag[] {
  return tags.filter(tag => ids.includes(tag.id) && tag.deleted_at === null);
}

export function getLessonTags(lesson: Lesson): Tag[] {
  if (!lesson.tags || lesson.tags.length === 0) return [];
  return getTagsByIds(lesson.tags);
}
