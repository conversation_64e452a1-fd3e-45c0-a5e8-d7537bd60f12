import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format functions
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function formatTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} phút`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} giờ`;
  }
  
  return `${hours} giờ ${remainingMinutes} phút`;
}

export function formatPrice(price: number | null): string {
  if (price === null || price === 0) {
    return 'Miễn phí';
  }
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price);
}

export function formatProgress(progress: number): string {
  return `${Math.round(progress)}%`;
}

// String utilities
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

export function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '');
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Validation utilities
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPassword(password: string): boolean {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

// Array utilities
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

export function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {
  return [...array].sort((a, b) => {
    const aVal = a[key];
    const bVal = b[key];
    
    if (aVal < bVal) return direction === 'asc' ? -1 : 1;
    if (aVal > bVal) return direction === 'asc' ? 1 : -1;
    return 0;
  });
}

export function uniqueBy<T>(array: T[], key: keyof T): T[] {
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) return false;
    seen.add(value);
    return true;
  });
}

// Local storage utilities
export function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') return defaultValue;
  
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage:`, error);
    return defaultValue;
  }
}

export function setToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error writing to localStorage:`, error);
  }
}

export function removeFromStorage(key: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing from localStorage:`, error);
  }
}

// URL utilities
export function buildUrl(base: string, params: Record<string, string | number | boolean>): string {
  const url = new URL(base, window.location.origin);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      url.searchParams.set(key, String(value));
    }
  });
  
  return url.toString();
}

export function getQueryParam(param: string): string | null {
  if (typeof window === 'undefined') return null;
  
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(param);
}

// Debounce utility
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Random utilities
export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export function randomFromArray<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// Progress calculation
export function calculatePercentage(current: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((current / total) * 100);
}

export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

// Error handling
export function handleError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'Đã xảy ra lỗi không xác định';
}

// Type guards
export function isNotNull<T>(value: T | null): value is T {
  return value !== null;
}

export function isNotUndefined<T>(value: T | undefined): value is T {
  return value !== undefined;
}

export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

// YouTube utilities
/**
 * Extract YouTube video ID from various YouTube URL formats
 */
export function extractYouTubeVideoId(url: string): string | null {
  if (!url) return null;

  // If it's already just a video ID (11 characters, alphanumeric + underscore + dash)
  if (/^[a-zA-Z0-9_-]{11}$/.test(url)) {
    return url;
  }

  // Various YouTube URL patterns
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/,
    /youtu\.be\/([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/v\/([a-zA-Z0-9_-]{11})/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }

  return null;
}

/**
 * Get YouTube video ID from lesson data
 * Checks content, intro, and video_url fields in that order for better accuracy
 */
interface LessonWithVideoFields {
  content?: string;
  intro?: string;
  video_url?: string | null;
}

export function getYouTubeVideoId(lesson: LessonWithVideoFields): string | null {
  // First check content field for [embed] tags and YouTube URLs
  if (lesson.content) {
    // Look for [embed]https://youtu.be/VIDEO_ID[/embed] pattern
    const embedMatch = lesson.content.match(/\[embed\](.*?)\[\/embed\]/);
    if (embedMatch) {
      const videoId = extractYouTubeVideoId(embedMatch[1]);
      if (videoId) return videoId;
    }

    // Look for YouTube URLs in href attributes (for Google Sheets data)
    const hrefMatches = lesson.content.match(/href="(https?:\/\/(?:www\.)?(?:youtube\.com|youtu\.be)[^"]*)"[^>]*>/g);
    if (hrefMatches) {
      for (const hrefMatch of hrefMatches) {
        const urlMatch = hrefMatch.match(/href="([^"]*)"/)
        if (urlMatch) {
          const videoId = extractYouTubeVideoId(urlMatch[1]);
          if (videoId) return videoId;
        }
      }
    }

    // Look for YouTube URLs in data-sheets-hyperlink attributes
    const sheetsHyperlinkMatch = lesson.content.match(/data-sheets-hyperlink="(https?:\/\/(?:www\.)?(?:youtube\.com|youtu\.be)[^"]*)"/)
    if (sheetsHyperlinkMatch) {
      const videoId = extractYouTubeVideoId(sheetsHyperlinkMatch[1]);
      if (videoId) return videoId;
    }

    // Look for YouTube URLs in data-sheets-value attributes
    const sheetsValueMatches = lesson.content.match(/data-sheets-value="[^"]*https?:\/\/(?:www\.)?(?:youtube\.com|youtu\.be)[^"]*"/g);
    if (sheetsValueMatches) {
      for (const sheetsValueMatch of sheetsValueMatches) {
        // Extract the URL from the JSON-like structure
        const urlMatches = sheetsValueMatch.match(/https?:\/\/(?:www\.)?(?:youtube\.com|youtu\.be)[^"\\]*/g);
        if (urlMatches) {
          for (const url of urlMatches) {
            const videoId = extractYouTubeVideoId(url);
            if (videoId) return videoId;
          }
        }
      }
    }

    // Also check for any other YouTube URLs in content
    const videoId = extractYouTubeVideoId(lesson.content);
    if (videoId) return videoId;
  }

  // Check intro field for iframe embed
  if (lesson.intro) {
    const videoId = extractYouTubeVideoId(lesson.intro);
    if (videoId) return videoId;
  }

  // Finally check video_url field (least reliable)
  if (lesson.video_url) {
    const videoId = extractYouTubeVideoId(lesson.video_url);
    if (videoId) return videoId;
  }

  return null;
}

/**
 * Generate YouTube thumbnail URL from video ID
 * Quality options:
 * - default: 120x90 (default.jpg)
 * - medium: 320x180 (mqdefault.jpg)
 * - high: 480x360 (hqdefault.jpg)
 * - standard: 640x480 (sddefault.jpg)
 * - maxres: 1280x720 (maxresdefault.jpg)
 */
export function getYouTubeThumbnail(videoId: string, quality: 'default' | 'medium' | 'high' | 'standard' | 'maxres' = 'medium'): string {
  const qualityMap = {
    'default': 'default.jpg',
    'medium': 'mqdefault.jpg',
    'high': 'hqdefault.jpg',
    'standard': 'sddefault.jpg',
    'maxres': 'maxresdefault.jpg'
  };

  return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}`;
}

/**
 * Get multiple YouTube thumbnail URLs for fallback purposes
 */
export function getYouTubeThumbnailWithFallbacks(videoId: string): string[] {
  return [
    getYouTubeThumbnail(videoId, 'maxres'),
    getYouTubeThumbnail(videoId, 'standard'),
    getYouTubeThumbnail(videoId, 'high'),
    getYouTubeThumbnail(videoId, 'medium'),
    getYouTubeThumbnail(videoId, 'default')
  ];
}
