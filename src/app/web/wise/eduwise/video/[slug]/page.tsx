"use client";

import { notFound } from "next/navigation";
import Link from "next/link";
import { useState, useEffect } from "react";
import { BookO<PERSON>, Clock, PlayCircle, Eye, ThumbsUp } from "lucide-react";
import { getLessonBySlug, getAllLessons } from "../../lib/data";
import { formatTime, getYouTubeVideoId } from "../../lib/utils";
import YouTubePlayer, { YouTubeThumbnail } from "../../components/YouTubePlayer";

interface VideoPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function VideoPage({ params }: VideoPageProps) {
  const [slug, setSlug] = useState<string>("");
  const [showPlayer, setShowPlayer] = useState(false);

  useEffect(() => {
    params.then(({ slug }) => setSlug(slug));
  }, [params]);

  if (!slug) {
    return <div>Loading...</div>;
  }

  const lesson = getLessonBySlug(slug);
  const allLessons = getAllLessons();

  if (!lesson) {
    notFound();
  }

  // Get YouTube video ID for this lesson
  const videoId = getYouTubeVideoId(lesson);

  // Get related videos (other lessons)
  const relatedVideos = allLessons
    .filter(l => l.id !== lesson.id)
    .slice(0, 6);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <h1 className="ml-2 text-2xl font-bold text-gray-900">
                <Link href="/" className="hover:text-blue-600 transition-colors">
                  Đào tạo Wise Vietnam
                </Link>
              </h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="/" className="text-gray-900 hover:text-blue-600 transition-colors">
                Trang chủ
              </Link>
              <Link href="/video" className="text-blue-600 font-semibold">
                Video
              </Link>
              <Link href="/courses" className="text-gray-900 hover:text-blue-600 transition-colors">
                Khóa học
              </Link>
              <Link href="/faq" className="text-gray-900 hover:text-blue-600 transition-colors">
                FAQ
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <Link href="/" className="text-gray-500 hover:text-gray-700">
                Trang chủ
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li>
              <Link href="/video" className="text-gray-500 hover:text-gray-700">
                Video
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900 font-medium truncate">
              {lesson.name}
            </li>
          </ol>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Video Content */}
          <div className="lg:col-span-2">
            {/* Video Player */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
              {videoId ? (
                showPlayer ? (
                  <YouTubePlayer
                    videoId={videoId}
                    title={lesson.name}
                    className="rounded-lg overflow-hidden"
                  />
                ) : (
                  <div className="relative cursor-pointer" onClick={() => setShowPlayer(true)}>
                    <YouTubeThumbnail
                      videoId={videoId}
                      title={lesson.name}
                      className="rounded-lg overflow-hidden"
                    />
                    {/* Duration badge */}
                    <div className="absolute bottom-4 right-4 bg-black bg-opacity-75 text-white text-sm px-3 py-1 rounded">
                      {formatTime(lesson.duration || 15)}
                    </div>
                  </div>
                )
              ) : (
                <div className="aspect-video bg-gray-900 relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <PlayCircle className="h-20 w-20 mx-auto mb-4" />
                      <p className="text-lg font-medium">Video không khả dụng</p>
                      <p className="text-sm text-gray-300 mt-2">
                        Không tìm thấy video cho bài học này
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Video Info */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                {lesson.name}
              </h1>
              
              <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
                <div className="flex items-center space-x-6">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{formatTime(lesson.duration || 15)}</span>
                  </div>
                  <div className="flex items-center">
                    <Eye className="h-4 w-4 mr-1" />
                    <span>1,234 lượt xem</span>
                  </div>
                  <div className="flex items-center">
                    <ThumbsUp className="h-4 w-4 mr-1" />
                    <span>98% thích</span>
                  </div>
                </div>
              </div>

              <p className="text-gray-600 mb-6 leading-relaxed">
                {lesson.description}
              </p>

              {/* Action Buttons */}
              <div className="flex items-center space-x-4">
                <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  Thích
                </button>
                <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  Chia sẻ
                </button>
              </div>
            </div>

            {/* Video Description */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                Mô tả chi tiết
              </h2>
              <div 
                className="prose max-w-none text-gray-700"
                dangerouslySetInnerHTML={{ __html: lesson.content }}
              />
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Video liên quan
              </h3>
              
              <div className="space-y-4">
                {relatedVideos.map((video) => {
                  const relatedVideoId = getYouTubeVideoId(video);

                  return (
                  <Link
                    key={video.id}
                    href={`/video/${video.slug}`}
                    className="block group"
                  >
                    <div className="flex space-x-3">
                      <div className="flex-shrink-0">
                        {relatedVideoId ? (
                          <YouTubeThumbnail
                            videoId={relatedVideoId}
                            title={video.name}
                            className="w-24 h-16"
                          />
                        ) : (
                          <div className="w-24 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded flex items-center justify-center">
                            <PlayCircle className="h-6 w-6 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                          {video.name}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatTime(video.duration || 15)}
                        </p>
                      </div>
                    </div>
                  </Link>
                  );
                })}
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <Link
                  href="/video"
                  className="block w-full text-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Xem tất cả video
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <BookOpen className="h-8 w-8 text-blue-400" />
                <span className="ml-2 text-xl font-bold">SÁNG KIẾN HỖ TRỢ PHỤ NỮ KHỞI NGHIỆP VÀ KINH DOANH</span>
              </div>
              <div className="space-y-2 text-gray-400">
                <p>88 Phạm Ngọc Thạch, Đống Đa, Hà Nội</p>
                <p>Email: <EMAIL></p>
                <p>033 772 9889</p>
              </div>
            </div>
            <div>
              <h5 className="text-lg font-semibold mb-4">Liên kết nhanh</h5>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/video" className="hover:text-white transition-colors">Video</Link></li>
                <li><Link href="/courses" className="hover:text-white transition-colors">Khóa học</Link></li>
                <li><Link href="/faq" className="hover:text-white transition-colors">FAQ</Link></li>
              </ul>
            </div>
            <div>
              <h5 className="text-lg font-semibold mb-4">Liên hệ với chúng tôi</h5>
              <p className="text-gray-400 text-sm">
                © 2024 WISE Kết nối cơ hội - Dẫn lối thành công.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
