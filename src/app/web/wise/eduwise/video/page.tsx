"use client";

import { useState, useMemo } from "react";
import Link from "next/link";
import { PlayCircle, Clock, Search } from "lucide-react";
import { getAllLessons, getAllTags, getLessonTags } from "../lib/data";
import { formatTime, getYouTubeVideoId } from "../lib/utils";
import Layout, { Breadcrumb } from "../components/Layout";
import { YouTubeThumbnail } from "../components/YouTubePlayer";

type SortOption = "title-asc" | "title-desc" | "newest" | "oldest";

export default function VideoPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("newest");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedLevel, setSelectedLevel] = useState<string[]>([]);

  const allLessons = getAllLessons();
  const allTags = getAllTags();

  // Available tags for videos - using actual tag names
  const availableTags = allTags.map(tag => tag.name);

  const levels = ["Tổng quan", "Test"];

  const filteredAndSortedLessons = useMemo(() => {
    let lessons = allLessons;
    
    // Apply search filter
    if (searchQuery.trim() !== "") {
      lessons = lessons.filter(lesson =>
        lesson.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lesson.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply tag filter
    if (selectedTags.length > 0) {
      lessons = lessons.filter(lesson => {
        const lessonTags = getLessonTags(lesson);
        return selectedTags.some(selectedTag => 
          lessonTags.some(tag => tag.name === selectedTag)
        );
      });
    }
    
    // Apply sorting
    lessons = [...lessons].sort((a, b) => {
      switch (sortBy) {
        case "title-asc":
          return a.name.localeCompare(b.name, 'vi');
        case "title-desc":
          return b.name.localeCompare(a.name, 'vi');
        case "newest":
          return b.id - a.id; // Assuming higher ID means newer
        case "oldest":
          return a.id - b.id;
        default:
          return 0;
      }
    });
    
    return lessons;
  }, [searchQuery, sortBy, selectedTags, allLessons]);

  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const toggleLevel = (level: string) => {
    setSelectedLevel(prev =>
      prev.includes(level)
        ? prev.filter(l => l !== level)
        : [...prev, level]
    );
  };

  return (
    <Layout currentPage="video">
      <Breadcrumb
        items={[
          { label: 'Trang chủ', href: '/' },
          { label: 'Video' }
        ]}
      />

      {/* Main Content */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Filters */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                {/* Sort Options */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Sắp xếp theo</h4>
                  <div className="space-y-2">
                    {[
                      { value: "title-asc", label: "Tiêu đề A-Z" },
                      { value: "title-desc", label: "Tiêu đề Z-A" },
                      { value: "oldest", label: "Nội dung Cũ nhất" },
                      { value: "newest", label: "Nội dung Mới nhất" }
                    ].map(option => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="sort"
                          value={option.value}
                          checked={sortBy === option.value}
                          onChange={(e) => setSortBy(e.target.value as SortOption)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Level Filter */}
                {/* <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Mức độ chuyên sâu</h4>
                  <div className="space-y-2">
                    {levels.map(level => (
                      <label key={level} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedLevel.includes(level)}
                          onChange={() => toggleLevel(level)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">{level}</span>
                      </label>
                    ))}
                  </div>
                </div> */}

                {/* Tags Filter */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Tags</h4>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {availableTags.map(tag => (
                      <label key={tag} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedTags.includes(tag)}
                          onChange={() => toggleTag(tag)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">{tag}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Video Grid */}
            <div className="lg:col-span-3">
              {/* Search Bar */}
              <div className="mb-6">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Tìm kiếm video..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Results Info */}
              <div className="flex justify-between items-center mb-6">
                <p className="text-gray-600">
                  Hiển thị {filteredAndSortedLessons.length} video
                  {searchQuery && ` cho "${searchQuery}"`}
                </p>
              </div>

              {/* Videos Grid */}
              {filteredAndSortedLessons.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {filteredAndSortedLessons.map((lesson) => {
                    const videoId = getYouTubeVideoId(lesson);

                    return (
                    <div
                      key={lesson.id}
                      className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                    >
                      {/* Video Thumbnail */}
                      <div className="relative h-48">
                        {videoId ? (
                          <Link href={`/video/${lesson.slug}`}>
                            <YouTubeThumbnail
                              videoId={videoId}
                              title={lesson.name}
                              className="h-full"
                            />
                          </Link>
                        ) : (
                          <div className="h-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                            <PlayCircle className="h-12 w-12 text-white" />
                          </div>
                        )}
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                          {formatTime(lesson.duration || 15)}
                        </div>
                      </div>

                      {/* Video Content */}
                      <div className="p-4">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                          <Link 
                            href={`/video/${lesson.slug}`} 
                            className="hover:text-blue-600 transition-colors"
                          >
                            {lesson.name}
                          </Link>
                        </h3>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-3">
                          {lesson.description}
                        </p>
                        
                        {/* Tags */}
                        {lesson.tags && lesson.tags.length > 0 && (
                          <div className="mb-3">
                            <div className="flex flex-wrap gap-1">
                              {getLessonTags(lesson).map(tag => (
                                <span
                                  key={tag.id}
                                  className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                                >
                                  {tag.name}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            <span>{formatTime(lesson.duration || 15)}</span>
                          </div>
                          <Link
                            href={`/video/${lesson.slug}`}
                            className="text-blue-600 hover:text-blue-700 font-medium"
                          >
                            Xem video
                          </Link>
                        </div>
                      </div>
                    </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <PlayCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Không tìm thấy video
                  </h3>
                  <p className="text-gray-600">
                    Thử tìm kiếm với từ khóa khác hoặc{" "}
                    <button
                      onClick={() => {
                        setSearchQuery("");
                        setSelectedTags([]);
                      }}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      xem tất cả video
                    </button>
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

    </Layout>
  );
}
