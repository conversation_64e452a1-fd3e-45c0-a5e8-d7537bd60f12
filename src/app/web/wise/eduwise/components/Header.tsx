"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { User } from "../lib/auth";
import { useOneIDAuth } from "../hooks/useOneIDAuth";

interface HeaderProps {
  user?: User | null;
}

export default function Header({ user: propUser }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user: authUser, logout } = useOneIDAuth();
  const router = useRouter();
  
  // Use auth hook user if available, otherwise fall back to prop user
  const user = authUser || propUser;
  
  const handleLogout = async () => {
    await logout();
    router.push('/web/wise/eduwise');
  };

  return (
    <header className="header header-page bg-wise-primary" style={{ background: 'linear-gradient(135deg, var(--wise-neutral-800) 0%, var(--wise-neutral-900) 100%)', color: '#fff' }}>
      <nav className="navbar-expand-lg header-nav scroll-sticky">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="d-flex align-items-center w-100" style={{ padding: '15px 0' }}>
            {/* Left side: Logo and Navigation Menu */}
            <div className="d-flex align-items-center">
              <div className="navbar-header">
                <button
                  id="mobile_btn"
                  className="d-md-none border-0 bg-transparent"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  style={{ color: 'white', marginRight: '15px' }}
                  aria-label="Toggle mobile menu"
                >
                  <span className="bar-icon">
                    <span style={{ display: 'block', width: '25px', height: '3px', backgroundColor: 'white', margin: '5px 0' }}></span>
                    <span style={{ display: 'block', width: '25px', height: '3px', backgroundColor: 'white', margin: '5px 0' }}></span>
                    <span style={{ display: 'block', width: '25px', height: '3px', backgroundColor: 'white', margin: '5px 0' }}></span>
                  </span>
                </button>
                <Link href="/web/wise/eduwise" className="navbar-brand logo" style={{ marginRight: '50px' }}>
                  <Image
                    src="/wise-logo.png"
                    /* src="/bv2-e1639024615824.jpeg" */
                    alt="WISE"
                    width={100}
                    height={120}
                    className="img-fluid"
                    style={{ maxHeight: '85px' }}
                  />
                </Link>
              </div>
              <div className="main-menu-wrapper">
                <div className="menu-header d-none">
                  <Link href="/web/wise/eduwise" className="menu-logo">
                    <Image
                      src="/wise-logo.png.webp"
                      alt="Logo"
                      width={130}
                      height={40}
                      className="img-fluid"
                    />
                  </Link>
                  <button
                    id="menu_close"
                    className="menu-close border-0 bg-transparent"
                    onClick={() => setIsMobileMenuOpen(false)}
                    aria-label="Close mobile menu"
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
                <ul className="main-nav d-none d-md-flex align-items-center" style={{ margin: 0, padding: 0, listStyle: 'none' }}>
                  <li className="has-submenu" style={{ marginRight: '40px' }}>
                    <Link
                      href="/web/wise/eduwise"
                      style={{
                        color: 'white',
                        fontSize: '15px',
                        fontWeight: '500',
                        textDecoration: 'none',
                        display: 'block',
                        padding: '15px 0',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      Trang chủ
                    </Link>
                  </li>
                  <li className="has-submenu" style={{ marginRight: '40px' }}>
                    <Link
                      href="/web/wise/eduwise/video"
                      style={{
                        color: 'white',
                        fontSize: '15px',
                        fontWeight: '500',
                        textDecoration: 'none',
                        display: 'block',
                        padding: '15px 0',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      Video
                    </Link>
                  </li>
                  <li className="has-submenu" style={{ marginRight: '40px' }}>
                    <Link
                      href="/web/wise/eduwise/courses"
                      style={{
                        color: 'white',
                        fontSize: '15px',
                        fontWeight: '500',
                        textDecoration: 'none',
                        display: 'block',
                        padding: '15px 0',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      Khóa học
                    </Link>
                  </li>
                  <li className="has-submenu">
                    <Link
                      href="/web/wise/eduwise/faq"
                      style={{
                        color: 'white',
                        fontSize: '15px',
                        fontWeight: '500',
                        textDecoration: 'none',
                        display: 'block',
                        padding: '15px 0',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      Faq
                    </Link>
                  </li>
                </ul>
              </div>
            </div>

            {/* Right side: Login/Logout buttons */}
            <div className="d-flex align-items-center" style={{ marginLeft: 'auto' }}>
              <ul className="nav header-navbar-rht d-flex align-items-center" style={{ margin: 0, padding: 0, listStyle: 'none' }}>
                {user ? (
                  <>
                    <li style={{ marginRight: '15px' }}>
                      <span style={{ color: 'white', fontSize: '14px', fontWeight: '500', whiteSpace: 'nowrap' }}>
                        Xin chào, {user.fullName || user.username}
                      </span>
                    </li>
                    <li style={{ marginRight: '15px' }}>
                      <Link
                        href="/web/wise/eduwise/profile"
                        style={{
                          color: '#26292c',
                          fontSize: '14px',
                          fontWeight: '500',
                          backgroundColor: '#fff',
                          borderRadius: '25px',
                          padding: '10px 20px',
                          textDecoration: 'none',
                          display: 'inline-block',
                          whiteSpace: 'nowrap'
                        }}
                        className="header-login"
                      >
                        Tài khoản
                      </Link>
                    </li>
                    <li>
                      <button
                        onClick={handleLogout}
                        style={{
                          color: '#26292c',
                          fontSize: '14px',
                          fontWeight: '500',
                          backgroundColor: '#f8f9fa',
                          borderRadius: '25px',
                          padding: '10px 20px',
                          border: 'none',
                          cursor: 'pointer',
                          whiteSpace: 'nowrap'
                        }}
                        className="header-logout"
                      >
                        Đăng xuất
                      </button>
                    </li>
                  </>
                ) : (
                  <>
                    <li style={{ marginRight: '15px' }}>
                      <Link
                        href="/web/wise/eduwise/login"
                        style={{
                          color: '#26292c',
                          fontSize: '16px',
                          fontWeight: '500',
                          backgroundColor: '#fff',
                          borderRadius: '25px',
                          padding: '12px 25px',
                          textDecoration: 'none',
                          display: 'inline-block',
                          whiteSpace: 'nowrap'
                        }}
                        className="header-login"
                      >
                        Đăng nhập
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/web/wise/eduwise/register"
                        style={{
                          color: '#26292c',
                          fontSize: '16px',
                          fontWeight: '500',
                          backgroundColor: '#fff',
                          borderRadius: '25px',
                          padding: '12px 25px',
                          textDecoration: 'none',
                          display: 'inline-block',
                          whiteSpace: 'nowrap'
                        }}
                        className="header-sign"
                      >
                        Đăng ký
                      </Link>
                    </li>
                  </>
                )}
              </ul>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="d-md-none" style={{ backgroundColor: '#273044', borderTop: '1px solid #f0f0f0' }}>
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="row">
                  <div className="col-12">
                    <ul className="main-nav flex-column py-3">
                      <li className="has-submenu mb-3">
                        <Link
                          href="/web/wise/eduwise"
                          style={{
                            color: 'white',
                            fontSize: '15px',
                            fontWeight: '500',
                            textDecoration: 'none',
                            display: 'block'
                          }}
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          Trang chủ
                        </Link>
                      </li>
                      <li className="has-submenu mb-3">
                        <Link
                          href="/web/wise/eduwise/video"
                          style={{
                            color: 'white',
                            fontSize: '15px',
                            fontWeight: '500',
                            textDecoration: 'none',
                            display: 'block'
                          }}
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          Video
                        </Link>
                      </li>
                      <li className="has-submenu mb-3">
                        <Link
                          href="/web/wise/eduwise/courses"
                          style={{
                            color: 'white',
                            fontSize: '15px',
                            fontWeight: '500',
                            textDecoration: 'none',
                            display: 'block'
                          }}
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          Khóa học
                        </Link>
                      </li>
                      <li className="has-submenu mb-3">
                        <Link
                          href="/web/wise/eduwise/faq"
                          style={{
                            color: 'white',
                            fontSize: '15px',
                            fontWeight: '500',
                            textDecoration: 'none',
                            display: 'block'
                          }}
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          Faq
                        </Link>
                      </li>
                      <li className="border-top pt-3">
                        {user ? (
                          <>
                            <div style={{ color: 'white', fontWeight: '500', marginBottom: '10px' }}>
                              Xin chào, {user.fullName || user.username}
                            </div>
                            <Link
                              href="/web/wise/eduwise/profile"
                              style={{
                                color: 'white',
                                fontSize: '15px',
                                fontWeight: '500',
                                textDecoration: 'none',
                                display: 'block',
                                marginBottom: '10px'
                              }}
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              Tài khoản
                            </Link>
                            <button
                              onClick={() => {
                                setIsMobileMenuOpen(false);
                                handleLogout();
                              }}
                              style={{
                                color: 'white',
                                fontSize: '15px',
                                fontWeight: '500',
                                textDecoration: 'none',
                                display: 'block',
                                background: 'none',
                                border: 'none',
                                cursor: 'pointer',
                                padding: 0
                              }}
                            >
                              Đăng xuất
                            </button>
                          </>
                        ) : (
                          <>
                            <Link
                              href="/web/wise/eduwise/login"
                              style={{
                                color: 'white',
                                fontSize: '15px',
                                fontWeight: '500',
                                textDecoration: 'none',
                                display: 'block',
                                marginBottom: '10px'
                              }}
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              Đăng nhập
                            </Link>
                            <Link
                              href="/web/wise/eduwise/register"
                              style={{
                                color: 'white',
                                fontSize: '15px',
                                fontWeight: '500',
                                textDecoration: 'none',
                                display: 'block'
                              }}
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              Đăng ký
                            </Link>
                          </>
                        )}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>
    </header>
  );
}
