"use client";

import { useState } from "react";
import { PlayCircle } from "lucide-react";
import Image from "next/image";

interface YouTubePlayerProps {
  videoId: string;
  title?: string;
  className?: string;
  autoplay?: boolean;
  showThumbnail?: boolean;
}

export default function YouTubePlayer({
  videoId,
  title = "Video",
  className = "",
  autoplay = false,
  showThumbnail = true
}: YouTubePlayerProps) {
  const [isPlaying, setIsPlaying] = useState(autoplay);
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [thumbnailLoading, setThumbnailLoading] = useState(true);

  const handlePlay = () => {
    setIsLoading(true);
    setIsPlaying(true);
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const handleThumbnailLoad = () => {
    setThumbnailLoading(false);
    setImageError(false);
  };

  if (!videoId) {
    return (
      <div className={`aspect-video bg-gray-900 flex items-center justify-center ${className}`}>
        <div className="text-center text-white">
          <PlayCircle className="h-20 w-20 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">Video không khả dụng</p>
        </div>
      </div>
    );
  }

  if (!isPlaying && showThumbnail) {
    const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    const fallbackUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;

    return (
      <div className={`aspect-video relative cursor-pointer group ${className}`} onClick={handlePlay}>
        {/* Loading placeholder */}
        {thumbnailLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse z-0" />
        )}

        {/* YouTube Thumbnail */}
        {!imageError ? (
          <Image
            src={thumbnailUrl}
            alt={title}
            className="w-full h-full object-cover"
            onError={(e) => {
              const img = e.target as HTMLImageElement;
              if (img.src === thumbnailUrl) {
                img.src = fallbackUrl;
              } else {
                setImageError(true);
                setThumbnailLoading(false);
              }
            }}
            onLoad={handleThumbnailLoad}
            style={{ display: thumbnailLoading ? 'none' : 'block' }}
            width={1280}
            height={720}
            unoptimized
            priority
          />
        ) : (
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <div className="text-center text-white">
              <PlayCircle className="h-16 w-16 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Video Thumbnail</p>
            </div>
          </div>
        )}

        {/* Play Button Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-40 transition-all duration-200 z-10">
          <div className="bg-red-600 rounded-full p-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <PlayCircle className="h-16 w-16 text-white fill-current" />
          </div>
        </div>

        {/* Loading indicator */}
        {isLoading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
          </div>
        )}
      </div>
    );
  }

  // YouTube iframe parameters for better experience
  const iframeParams = new URLSearchParams({
    autoplay: isPlaying ? '1' : '0',
    rel: '0', // Don't show related videos from other channels
    modestbranding: '1', // Reduce YouTube branding
    playsinline: '1', // Play inline on mobile
    controls: '1', // Show player controls
    enablejsapi: '1', // Enable JavaScript API
    origin: typeof window !== 'undefined' ? window.location.origin : ''
  });

  return (
    <div className={`aspect-video ${className}`}>
      <iframe
        src={`https://www.youtube.com/embed/${videoId}?${iframeParams.toString()}`}
        title={title}
        className="w-full h-full"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
        onLoad={handleIframeLoad}
      />
    </div>
  );
}

// Simplified version for thumbnails/previews
export function YouTubeThumbnail({
  videoId,
  title = "Video",
  className = "",
  onClick
}: {
  videoId: string;
  title?: string;
  className?: string;
  onClick?: () => void;
}) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  console.log('YouTubeThumbnail rendering with videoId:', videoId);

  if (!videoId) {
    console.log('No videoId provided to YouTubeThumbnail');
    return (
      <div className={`aspect-video bg-gray-200 flex items-center justify-center ${className}`}>
        <PlayCircle className="h-8 w-8 text-gray-400" />
      </div>
    );
  }

  // Use the highest quality thumbnail available
  const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
  const fallbackUrl = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const img = e.target as HTMLImageElement;
    console.log('Image error for videoId:', videoId, 'URL:', img.src);
    if (img.src === thumbnailUrl) {
      console.log('Trying fallback URL for videoId:', videoId);
      // Try fallback URL
      img.src = fallbackUrl;
    } else {
      console.log('Both URLs failed for videoId:', videoId, 'showing error state');
      // Both failed, show error state
      setImageError(true);
      setIsLoading(false);
    }
  };

  const handleImageLoad = () => {
    console.log('Image loaded successfully for videoId:', videoId);
    setIsLoading(false);
    setImageError(false);
  };

  if (imageError) {
    return (
      <div className={`aspect-video bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center ${className}`}>
        <div className="text-center text-white">
          <PlayCircle className="h-8 w-8 mx-auto mb-2" />
          <p className="text-xs">Video</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`aspect-video relative cursor-pointer group ${className}`}
      onClick={onClick}
    >
      {/* Loading placeholder */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded z-0" />
      )}

      <Image
        src={thumbnailUrl}
        alt={title}
        className="w-full h-full object-cover rounded"
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{ display: isLoading ? 'none' : 'block' }}
        width={480}
        height={270}
        unoptimized
        priority
      />

    </div>
  );
}
