"use client";

import { useState, useEffect } from "react";
import Image from "next/image";

interface SlideData {
  id: number;
  image: string;
  title: string;
  subtitle: string;
  description: string;
}

const slides: SlideData[] = [
  {
    id: 1,
    image: "/login-img.png",
    title: "<PERSON>h<PERSON><PERSON> học trực tuyến",
    subtitle: "miễn phí.",
    description: "Với các nội dung đa dạng và liên tục đ<PERSON> cập nhật"
  },
  {
    id: 2,
    image: "/login-img.png",
    title: "Chuyên gia hàng đầu",
    subtitle: "trong nhiều lĩnh vực.",
    description: "Giảng viên là những chuyên gia thực chiến và chủ những doanh nghiệp thành công"
  },
  {
    id: 3,
    image: "/login-img.png",
    title: "<PERSON><PERSON><PERSON> th<PERSON><PERSON> và kinh nghiệm",
    subtitle: "thự<PERSON> tiễn.",
    description: "Những chia sẻ thực tiễn, dễ dàng áp dụng vào công việc kinh doanh của bạn"
  }
];

export default function LoginPromoSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-advance slides every 5 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <div className="hidden lg:flex lg:w-1/2 bg-white p-8 flex-col justify-center">
      <div className="max-w-md mx-auto relative">
        {/* Slider container */}
        <div className="overflow-hidden">
          <div 
            className="flex transition-transform duration-500 ease-in-out"
            style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          >
            {slides.map((slide) => (
              <div key={slide.id} className="w-full flex-shrink-0">
                <div className="text-center">
                  <div className="mb-6">
                    <Image
                      src={slide.image}
                      alt={`${slide.title} Image`}
                      width={120}
                      height={120}
                      className="mx-auto"
                    />
                  </div>
                  <h2 className="text-xl font-bold mb-2 text-wise-neutral-800">{slide.title}</h2>
                  <h3 className="text-xl font-bold mb-4 text-wise-primary">{slide.subtitle}</h3>
                  <hr className="border-wise-primary mb-4" />
                  <p className="text-wise-neutral-600 text-sm">{slide.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation arrows */}
        <button
          onClick={prevSlide}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-all hover:bg-wise-primary hover:text-white"
          aria-label="Previous slide"
        >
          <svg className="w-5 h-5 text-wise-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <button
          onClick={nextSlide}
          className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-all hover:bg-wise-primary hover:text-white"
          aria-label="Next slide"
        >
          <svg className="w-5 h-5 text-wise-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>

        {/* Dots indicator */}
        <div className="flex justify-center mt-8 space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentSlide ? 'bg-wise-primary' : 'bg-wise-neutral-300'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
