import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, PlayCircle } from "lucide-react";
import { Course } from "../types";
import { formatTime, formatPrice, truncateText, stripHtml } from "../lib/utils";

interface CourseCardProps {
  course: Course;
  variant?: 'default' | 'compact' | 'featured';
  showProgress?: boolean;
  progress?: number;
}

export default function CourseCard({ 
  course, 
  variant = 'default', 
  showProgress = false, 
  progress = 0 
}: CourseCardProps) {
  const renderCourseImage = () => (
    <div className={`relative ${
      variant === 'featured' ? 'h-64' : variant === 'compact' ? 'h-32' : 'h-48'
    } bg-gradient-to-r from-wise-primary to-wise-primary-dark overflow-hidden`}>
      <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
        <PlayCircle className={`text-white ${
          variant === 'featured' ? 'h-16 w-16' : variant === 'compact' ? 'h-8 w-8' : 'h-12 w-12'
        }`} />
      </div>
      {course.thumbnail && (
        <Image
          src={course.thumbnail}
          alt={course.name}
          fill
          className="object-cover"
        />
      )}
    </div>
  );

  const renderCourseStats = () => (
    <div className={`flex items-center justify-between text-sm text-wise-neutral-500 ${
      variant === 'compact' ? 'mb-2' : 'mb-4'
    }`}>
      <div className="flex items-center space-x-4">
        <div className="flex items-center">
          <BookOpen className="h-4 w-4 mr-1" />
          <span>{course.total_lessons} bài học</span>
        </div>
        {variant !== 'compact' && (
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1" />
            <span>{formatTime(course.duration || 60)}</span>
          </div>
        )}
      </div>
      {variant === 'featured' && (
        <div className="flex items-center">
          <Star className="h-4 w-4 text-wise-secondary mr-1" />
          <span>4.8</span>
        </div>
      )}
    </div>
  );

  const renderPrice = () => (
    <div className="flex items-center justify-between">
      <div className="flex flex-col">
        <span className={`font-bold text-wise-primary ${
          variant === 'featured' ? 'text-3xl' : variant === 'compact' ? 'text-lg' : 'text-2xl'
        }`}>
          {formatPrice(course.sale_price || course.regular_price || 0)}
        </span>
        {course.regular_price && course.sale_price && course.regular_price !== course.sale_price && (
          <span className="text-sm text-wise-neutral-500 line-through">
            {formatPrice(course.regular_price)}
          </span>
        )}
      </div>
      <Link
        href={`/courses/${course.slug}`}
        className={`bg-wise-primary text-white rounded-md hover:bg-wise-primary-dark transition-colors font-medium ${
          variant === 'featured'
            ? 'px-6 py-3 text-base'
            : variant === 'compact'
            ? 'px-3 py-1 text-sm'
            : 'px-4 py-2 text-sm'
        }`}
      >
        {variant === 'compact' ? 'Xem' : 'Xem chi tiết'}
      </Link>
    </div>
  );

  const renderProgress = () => {
    if (!showProgress) return null;
    
    return (
      <div className="mt-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-wise-neutral-600">Tiến độ</span>
          <span className="text-sm font-medium text-wise-primary">{progress}%</span>
        </div>
        <div className="w-full bg-wise-neutral-200 rounded-full h-2">
          <div
            className="bg-wise-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>
    );
  };

  if (variant === 'compact') {
    return (
      <div className="bg-white border border-wise-neutral-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        <div className="flex space-x-4">
          <div className="flex-shrink-0">
            {renderCourseImage()}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="text-lg font-semibold text-wise-neutral-900 mb-2 line-clamp-2">
              <Link href={`/courses/${course.slug}`} className="hover:text-wise-primary transition-colors">
                {course.name}
              </Link>
            </h4>
            <p className="text-wise-neutral-600 text-sm mb-3 line-clamp-2">
              {truncateText(stripHtml(course.description), 100)}
            </p>
            {renderCourseStats()}
            {renderProgress()}
          </div>
          <div className="flex-shrink-0 text-right">
            <div className="text-lg font-bold text-wise-primary mb-2">
              {formatPrice(course.sale_price || course.regular_price || 0)}
            </div>
            <Link
              href={`/courses/${course.slug}`}
              className="inline-block px-3 py-1 bg-wise-primary text-white rounded text-sm hover:bg-wise-primary-dark transition-colors"
            >
              Xem
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${
      variant === 'featured' ? 'transform hover:scale-105' : ''
    }`}>
      {renderCourseImage()}
      
      <div className={variant === 'featured' ? 'p-8' : 'p-6'}>
        <h4 className={`font-semibold text-wise-neutral-900 mb-2 line-clamp-2 ${
          variant === 'featured' ? 'text-2xl' : 'text-xl'
        }`}>
          <Link href={`/courses/${course.slug}`} className="hover:text-wise-primary transition-colors">
            {course.name}
          </Link>
        </h4>
        
        <p className={`text-wise-neutral-600 mb-4 line-clamp-3 ${
          variant === 'featured' ? 'text-base' : 'text-sm'
        }`}>
          {truncateText(stripHtml(course.description), variant === 'featured' ? 150 : 100)}
        </p>

        {renderCourseStats()}
        {renderPrice()}
        {renderProgress()}
      </div>
    </div>
  );
}

// Course grid component
interface CourseGridProps {
  courses: Course[];
  variant?: 'default' | 'compact' | 'featured';
  showProgress?: boolean;
  emptyMessage?: string;
}

export function CourseGrid({ 
  courses, 
  variant = 'default', 
  showProgress = false,
  emptyMessage = "Không có khóa học nào."
}: CourseGridProps) {
  if (courses.length === 0) {
    return (
      <div className="text-center py-12">
        <BookOpen className="h-12 w-12 text-wise-neutral-400 mx-auto mb-4" />
        <p className="text-wise-neutral-600">{emptyMessage}</p>
      </div>
    );
  }

  const gridClasses = variant === 'compact' 
    ? 'space-y-4'
    : variant === 'featured'
    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
    : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';

  return (
    <div className={gridClasses}>
      {courses.map((course) => (
        <CourseCard
          key={course.id}
          course={course}
          variant={variant}
          showProgress={showProgress}
          progress={course.progress}
        />
      ))}
    </div>
  );
}

// Course list item for mobile
interface CourseListItemProps {
  course: Course;
  showProgress?: boolean;
}

export function CourseListItem({ course, showProgress = false }: CourseListItemProps) {
  return (
    <div className="bg-white border-b border-gray-200 p-4">
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <BookOpen className="h-8 w-8 text-white" />
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-base font-semibold text-gray-900 truncate">
            <Link href={`/courses/${course.slug}`} className="hover:text-blue-600 transition-colors">
              {course.name}
            </Link>
          </h4>
          <p className="text-sm text-gray-600 line-clamp-2 mt-1">
            {truncateText(stripHtml(course.description), 80)}
          </p>
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center text-xs text-gray-500">
              <BookOpen className="h-3 w-3 mr-1" />
              <span>{course.total_lessons} bài</span>
              <Clock className="h-3 w-3 ml-3 mr-1" />
              <span>{formatTime(course.duration || 30)}</span>
            </div>
            <span className="text-sm font-bold text-blue-600">
              {formatPrice(course.sale_price || course.regular_price || 0)}
            </span>
          </div>
          {showProgress && course.progress !== undefined && (
            <div className="mt-2">
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div 
                  className="bg-blue-600 h-1 rounded-full"
                  style={{ width: `${course.progress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
