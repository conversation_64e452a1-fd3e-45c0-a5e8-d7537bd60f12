"use client";

export default function Footer() {
  return (
    <footer className="footer" style={{ 
      background: 'linear-gradient(135deg, var(--wise-neutral-800) 0%, var(--wise-neutral-900) 100%)', 
      color: '#fff' 
    }}>
      {/* Footer Top */}
      <div className="footer-top" style={{ 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        position: 'relative', 
        zIndex: 9, 
        fontSize: '15px' 
      }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            {/* Left Column - Company Info */}
            <div className="footer-widget footer-about">
              <h2 className="footer-title" style={{ 
                color: '#fff', 
                fontSize: '18px', 
                fontWeight: '700', 
                marginBottom: '24px', 
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                lineHeight: '1.3'
              }}>
                SÁNG KIẾN HỖ TRỢ PHỤ NỮ KHỞI NGHIỆP VÀ KINH DOANH
              </h2>
              
              <div className="footer-about-content">
                <ul style={{ listStyle: 'none', margin: 0, padding: 0, outline: 'none' }}>
                  <li style={{ marginBottom: '12px', display: 'flex', alignItems: 'center' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-geo-alt-fill" viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0, color: '#fbbf24' }}>
                      <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
                    </svg>
                    88 Phạm Ngọc Thạch, Đống Đa, Hà Nội
                  </li>
                  <li style={{ marginBottom: '12px', display: 'flex', alignItems: 'center' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-envelope-fill" viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0, color: '#fbbf24' }}>
                      <path d="M.05 3.555A2 2 0 0 1 2 2h12a2 2 0 0 1 1.95 1.555L8 8.414.05 3.555ZM0 4.697v7.104l5.803-3.558L0 4.697ZM6.761 8.83l-6.57 4.027A2 2 0 0 0 2 14h12a2 2 0 0 0 1.808-1.144l-6.57-4.027L8 9.586l-1.239-.757Zm3.436-.586L16 11.801V4.697l-5.803 3.546Z"/>
                    </svg>
                    Email: <a href="mailto:<EMAIL>" style={{ color: '#fff', textDecoration: 'none', marginLeft: '5px' }}><EMAIL></a>
                  </li>
                  <li style={{ marginBottom: '12px', display: 'flex', alignItems: 'center' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-telephone-fill" viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0, color: '#fbbf24' }}>
                      <path fillRule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.678.678 0 0 0 .178.643l2.457 2.457a.678.678 0 0 0 .644.178l2.189-.547a1.745 1.745 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.634 18.634 0 0 1-7.01-4.42 18.634 18.634 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877L1.885.511z"/>
                    </svg>
                    <a href="tel:************" style={{ color: '#fff', textDecoration: 'none' }}>************</a>
                  </li>
                </ul>
              </div>
              
              <div className="social-icon-three" style={{ marginTop: '24px' }}>
                <ul style={{ listStyle: 'none', margin: 0, padding: 0, display: 'flex', gap: '12px' }}>
                  <li>
                    <a href="https://www.facebook.com/wisenets/" target="_blank" rel="noopener noreferrer" style={{ 
                      backgroundColor: '#3b5998',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#fff',
                      transition: 'transform 0.2s'
                    }}>
                      <svg width="14" height="14" fill="currentColor" viewBox="0 0 320 512">
                        <path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/>
                      </svg>
                    </a>
                  </li>
                  <li>
                    <a href="#" target="_blank" rel="noopener noreferrer" style={{ 
                      backgroundColor: '#1da1f2',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#fff',
                      transition: 'transform 0.2s'
                    }}>
                      <svg width="14" height="14" fill="currentColor" viewBox="0 0 512 512">
                        <path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"/>
                      </svg>
                    </a>
                  </li>
                  <li>
                    <a href="https://www.linkedin.com/company/wise-vietnam/" target="_blank" rel="noopener noreferrer" style={{ 
                      backgroundColor: '#0077b5',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#fff',
                      transition: 'transform 0.2s'
                    }}>
                      <svg width="14" height="14" fill="currentColor" viewBox="0 0 448 512">
                        <path d="M100.28 448H7.4V148.9h92.88zM53.79 108.1C24.09 108.1 0 83.5 0 53.8a53.79 53.79 0 0 1 107.58 0c0 29.7-24.1 54.3-53.79 54.3zM447.9 448h-92.68V302.4c0-34.7-.7-79.2-48.29-79.2-48.29 0-55.69 37.7-55.69 76.7V448h-92.78V148.9h89.08v40.8h1.3c12.4-23.5 42.69-48.3 87.88-48.3 94 0 111.28 61.9 111.28 142.3V448z"/>
                      </svg>
                    </a>
                  </li>
                  <li>
                    <a href="#" target="_blank" rel="noopener noreferrer" style={{ 
                      backgroundColor: '#ff0000',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#fff',
                      transition: 'transform 0.2s'
                    }}>
                      <svg width="14" height="14" fill="currentColor" viewBox="0 0 576 512">
                        <path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"/>
                      </svg>
                    </a>
                  </li>
                </ul>
              </div>
            </div>

            {/* Right Column - Contact Form */}
            <div className="footer-widget footer-contact">
              <h2 className="footer-title" style={{ 
                color: '#fff', 
                fontSize: '18px', 
                fontWeight: '700', 
                marginBottom: '24px', 
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}>
                LIÊN HỆ VỚI CHÚNG TÔI
              </h2>
              
              <div className="news-letter">
                <form id="contact_form" method="post" action="">
                  {/* First Row - Name and Email */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                    <input
                      type="text"
                      name="name"
                      className="form-control"
                      placeholder="Họ tên *"
                      style={{
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        color: '#fff',
                        borderRadius: '4px',
                        padding: '10px 12px',
                        width: '100%',
                        fontSize: '14px'
                      }}
                    />
                    <input
                      type="email"
                      name="email"
                      className="form-control"
                      placeholder="Email *"
                      style={{
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        color: '#fff',
                        borderRadius: '4px',
                        padding: '10px 12px',
                        width: '100%',
                        fontSize: '14px'
                      }}
                    />
                  </div>
                  
                  {/* Second Row - Subject */}
                  <div className="form-item mb-3">
                    <input
                      type="text"
                      name="subject"
                      className="form-control"
                      placeholder="Tiêu đề *"
                      style={{
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        color: '#fff',
                        borderRadius: '4px',
                        padding: '10px 12px',
                        width: '100%',
                        fontSize: '14px'
                      }}
                    />
                  </div>
                  
                  {/* Third Row - Message */}
                  <div className="form-item mb-4">
                    <textarea
                      name="content"
                      className="form-control"
                      placeholder="Nội dung *"
                      rows={3}
                      style={{
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        color: '#fff',
                        borderRadius: '4px',
                        padding: '10px 12px',
                        width: '100%',
                        resize: 'vertical',
                        fontSize: '14px'
                      }}
                    ></textarea>
                  </div>
                  
                  {/* Submit Button */}
                  <div className="form-button">
                    <button
                      type="submit"
                      className="btn btn-submit"
                      style={{
                        backgroundColor: '#a50f34',
                        border: '1px solid #a50f34',
                        color: '#fff',
                        padding: '10px 24px',
                        borderRadius: '4px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        fontSize: '14px',
                        textTransform: 'uppercase',
                        transition: 'background-color 0.2s'
                      }}
                    >
                      Gửi
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Footer Bottom */}
      <div className="footer-bottom" style={{ 
        borderTop: '1px solid rgba(255,255,255,0.1)', 
        paddingTop: '20px', 
        paddingBottom: '20px' 
      }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="copyright">
            <div className="text-center">
              <p className="mb-0" style={{ color: '#fff', fontSize: '14px' }}>
                © 2025 WISE Kết nối cơ hội - Dẫn lối thành công.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
