"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useOneIDAuth } from "../hooks/useOneIDAuth";
import LoginPromoSlider from "../components/LoginPromoSlider";

export default function RegisterPage() {
  const router = useRouter();
  const { register, isLoading: authLoading, error: authError, clearError } = useOneIDAuth();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    fullName: "",
    phone: "",
    email: "",
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
    
    // Clear OneID auth error
    if (authError) {
      clearError();
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = "Tên đăng nhập không được để trống";
    }

    if (!formData.password) {
      newErrors.password = "Mật khẩu không được để trống";
    } else if (formData.password.length < 6) {
      newErrors.password = "Mật khẩu phải có ít nhất 6 ký tự";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Vui lòng xác nhận mật khẩu";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Mật khẩu xác nhận không khớp";
    }

    if (!formData.fullName.trim()) {
      newErrors.fullName = "Họ tên không được để trống";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Số điện thoại không được để trống";
    } else if (!/^[0-9]{10,11}$/.test(formData.phone)) {
      newErrors.phone = "Số điện thoại không hợp lệ";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email không được để trống";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email không hợp lệ";
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "Bạn phải đồng ý với điều khoản sử dụng";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await register(formData);

      if (result.success) {
        alert(result.message);
        router.push("/web/wise/eduwise/login");
      } else {
        // Error message will be shown via authError state
        console.error("Registration failed:", result.message);
      }

    } catch (error) {
      console.error("Registration error:", error);
      alert("Đã xảy ra lỗi khi đăng ký. Vui lòng thử lại.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white flex">
      {/* Left side - Promotional content */}
      <LoginPromoSlider />

      {/* Right side - Registration form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-wise-neutral-50">
        <div className="max-w-md w-full space-y-6">
          {/* Header */}
          <div className="text-center">
            <Link
              href="/web/wise/eduwise"
              className="inline-block mb-6 text-wise-primary hover:text-wise-primary-dark transition-colors text-sm"
            >
              VỀ TRANG CHỦ
            </Link>
            <h2 className="text-2xl font-bold text-wise-neutral-900 mb-6">Tạo tài khoản</h2>
          </div>

          {/* Registration Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Show OneID authentication error */}
            {authError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-700">
                {authError}
              </div>
            )}
            
            <div>
              <input
                type="text"
                name="username"
                placeholder="Tên đăng nhập"
                value={formData.username}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.username ? 'border-wise-error' : ''
                }`}
              />
              {errors.username && <p className="mt-1 text-sm text-wise-error">{errors.username}</p>}
            </div>

            <div>
              <input
                type="password"
                name="password"
                placeholder="Mật khẩu"
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.password ? 'border-wise-error' : ''
                }`}
              />
              {errors.password && <p className="mt-1 text-sm text-wise-error">{errors.password}</p>}
            </div>

            <div>
              <input
                type="password"
                name="confirmPassword"
                placeholder="Xác nhận mật khẩu"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.confirmPassword ? 'border-wise-error' : ''
                }`}
              />
              {errors.confirmPassword && <p className="mt-1 text-sm text-wise-error">{errors.confirmPassword}</p>}
            </div>

            <div>
              <input
                type="text"
                name="fullName"
                placeholder="Họ tên"
                value={formData.fullName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.fullName ? 'border-wise-error' : ''
                }`}
              />
              {errors.fullName && <p className="mt-1 text-sm text-wise-error">{errors.fullName}</p>}
            </div>

            <div>
              <input
                type="tel"
                name="phone"
                placeholder="Số điện thoại"
                value={formData.phone}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.phone ? 'border-wise-error' : ''
                }`}
              />
              {errors.phone && <p className="mt-1 text-sm text-wise-error">{errors.phone}</p>}
            </div>

            <div>
              <input
                type="email"
                name="email"
                placeholder="Email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border border-wise-neutral-300 rounded focus:outline-none focus:border-wise-primary ${
                  errors.email ? 'border-wise-error' : ''
                }`}
              />
              {errors.email && <p className="mt-1 text-sm text-wise-error">{errors.email}</p>}
            </div>

            <div className="flex items-start">
              <input
                type="checkbox"
                name="agreeToTerms"
                checked={formData.agreeToTerms}
                onChange={handleInputChange}
                className="mt-1 mr-2 h-4 w-4 text-wise-primary focus:ring-wise-primary border-wise-neutral-300 rounded"
              />
              <label className="text-sm text-wise-neutral-600">
                Bằng cách nhấp vào <strong>Đăng ký</strong>, bạn đồng ý với{" "}
                <strong>Điều khoản, Quy chế hoạt động</strong> và{" "}
                <strong>Chính sách bảo mật</strong> của chúng tôi.
              </label>
            </div>
            {errors.agreeToTerms && <p className="text-sm text-wise-error">{errors.agreeToTerms}</p>}

            <button
              type="submit"
              disabled={isLoading || authLoading}
              className="w-full bg-wise-primary text-white py-2 px-4 rounded hover:bg-wise-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading || authLoading ? "Đang đăng ký..." : "Đăng ký"}
            </button>
          </form>

          {/* Social Login */}
          {/* <div className="mt-4">
            <div className="text-center text-wise-neutral-500 mb-3 text-sm">Hoặc đăng nhập</div>
            <div className="space-y-2">
              <button className="w-full flex items-center justify-start px-3 py-2 border border-wise-neutral-300 rounded hover:bg-wise-neutral-50 transition-colors text-sm">
                <Image
                  src="/net-icon-01.png"
                  alt="Google"
                  width={16}
                  height={16}
                  className="mr-2"
                />
                Đăng nhập bằng google
              </button>
              <button className="w-full flex items-center justify-start px-3 py-2 border border-wise-neutral-300 rounded hover:bg-wise-neutral-50 transition-colors text-sm">
                <Image
                  src="/net-icon-02.png"
                  alt="Facebook"
                  width={16}
                  height={16}
                  className="mr-2"
                />
                Đăng nhập bằng facebook
              </button>
            </div>
          </div> */}

          {/* Login Link */}
          <div className="text-center">
            <p className="text-wise-neutral-600 text-sm">
              Bạn đã có tài khoản?{" "}
              <Link href="/web/wise/eduwise/login" className="text-wise-primary hover:text-wise-primary-dark">
                Đăng nhập
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
