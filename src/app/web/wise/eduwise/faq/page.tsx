"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, Search, HelpCircle } from "lucide-react";
import Layout, { Breadcrumb } from "../components/Layout";

interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
}

// FAQ data from the original site
const faqData: FAQ[] = [
  {
    id: 1,
    question: "Trang web này cung cấp những bài học về gì?",
    answer: "Nền tảng học tập của Sáng kiến Hỗ trợ Phụ nữ Khởi nghiệp và Kinh doanh (WISE) cung cấp các bài học miễn phí về kỹ năng khởi nghiệp và kinh doanh dưới dạng video. C<PERSON><PERSON> bài học này bao gồm nhiều chủ đề khác nhau từ việc lập kế hoạch kinh doanh, quản lý tài ch<PERSON>h, marketing, cho đến kỹ năng lãnh đạo và quản lý nhân sự.",
    category: "general"
  },
  {
    id: 2,
    question: "<PERSON><PERSON><PERSON> khóa học có thực sự miễn phí không?",
    answer: "Tất cả các bài học và khóa học trên nền tảng này đều hoàn toàn miễn phí.",
    category: "pricing"
  },
  {
    id: 3,
    question: "Làm thế nào để bắt đầu học?",
    answer: "Để bắt đầu học, bạn chỉ cần đăng ký một tài khoản miễn phí bằng tài khoản email của mình. Sau khi đăng ký thành công, bạn có thể truy cập vào tất cả các bài học và bắt đầu học ngay lập tức.",
    category: "getting-started"
  },
  {
    id: 4,
    question: "Tôi có thể tải video về máy tính hoặc thiết bị di động của mình không?",
    answer: "Hiện tại, nền tảng không hỗ trợ việc tải video trực tiếp từ trang web. Tuy nhiên, bạn có thể xem các video trực tuyến bất kỳ lúc nào khi bạn có kết nối internet.",
    category: "technical"
  },
  {
    id: 5,
    question: "Làm thế nào để theo dõi tiến độ học tập của tôi?",
    answer: "Khi bạn đăng nhập vào tài khoản của mình, bạn sẽ thấy một bảng điều khiển cá nhân hiển thị tiến độ học tập, các khóa học bạn đã hoàn thành và những khóa học bạn đang theo dõi.",
    category: "progress"
  },
  {
    id: 6,
    question: "Các bài học có được cập nhật thường xuyên không?",
    answer: "WISE thường xuyên cập nhật các bài học mới và nâng cấp nội dung hiện có để đảm bảo bạn luôn nhận được những kiến thức và kỹ năng mới nhất.",
    category: "content"
  },
  {
    id: 7,
    question: "Có giới hạn về số lượng khóa học mà tôi có thể tham gia không?",
    answer: "Không, bạn có thể tham gia bất kỳ số lượng khóa học nào mà bạn muốn. Không có giới hạn về số lượng khóa học bạn có thể theo dõi cùng một lúc.",
    category: "access"
  },
  {
    id: 8,
    question: "Tôi có cần phải hoàn thành các bài học theo một thứ tự nhất định không?",
    answer: "Bạn có thể hoàn thành các bài học theo thứ tự mà bạn cảm thấy phù hợp nhất với nhu cầu và thời gian của mình.",
    category: "learning"
  },
  {
    id: 9,
    question: "Có các bài kiểm tra hoặc bài tập nào đi kèm với các bài học không?",
    answer: "Nhiều khóa học của chúng tôi bao gồm các bài kiểm tra và bài tập thực hành để giúp bạn củng cố kiến thức và kỹ năng đã học. Bạn có thể kiểm tra tiến độ của mình và nhận phản hồi ngay lập tức.",
    category: "assessment"
  },
  {
    id: 10,
    question: "Có giới hạn độ tuổi để tham gia các khóa học không?",
    answer: "Các khóa học của chúng tôi mở rộng cho mọi lứa tuổi. Tuy nhiên, một số nội dung có thể phù hợp hơn với người trưởng thành hoặc những người đã có kiến thức cơ bản về khởi nghiệp và kinh doanh.",
    category: "eligibility"
  },
  {
    id: 11,
    question: "Tôi có thể tham gia cộng đồng học tập để trao đổi kinh nghiệm không?",
    answer: "Chúng tôi có một cộng đồng trực tuyến nơi bạn có thể kết nối, trao đổi kinh nghiệm và học hỏi từ những người học khác. Ngoài ra, bạn cũng có thể cập nhật thông tin về những hoạt động hỗ trợ doanh nghiệp khởi nghiệp và kinh doanh mới nhất tại https://www.facebook.com/groups/wisenets",
    category: "community"
  },
  {
    id: 12,
    question: "Làm thế nào nếu tôi gặp vấn đề khi xem video hoặc truy cập trang web?",
    answer: "Nếu bạn gặp bất kỳ vấn đề gì khi xem video hoặc truy cập trang web, vui lòng liên hệ với hotline 0337729889 hoặc email: <EMAIL>. Bạn cũng có thể nhắn tin tới Fanpage của WISE tại https://www.facebook.com/wisenets",
    category: "support"
  }
];

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const faqs = faqData;

  // Filter FAQs based on search and category
  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "all" || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Get unique categories
  const categories = ["all", ...Array.from(new Set(faqs.map(faq => faq.category)))];
  const categoryLabels: Record<string, string> = {
    all: "Tất cả",
    general: "Tổng quan",
    pricing: "Giá cả",
    "getting-started": "Bắt đầu",
    technical: "Kỹ thuật",
    progress: "Tiến độ",
    content: "Nội dung",
    access: "Truy cập",
    learning: "Học tập",
    assessment: "Đánh giá",
    eligibility: "Điều kiện",
    community: "Cộng đồng",
    support: "Hỗ trợ"
  };

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <Layout currentPage="faq">
      <Breadcrumb
        items={[
          { label: 'Trang chủ', href: '/' },
          { label: 'FAQ' }
        ]}
      />

      {/* Page Header */}
      <section className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <HelpCircle className="h-16 w-16 text-blue-600 mx-auto mb-6" />
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Câu hỏi Thường gặp (FAQ)
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Tìm câu trả lời cho những câu hỏi phổ biến về nền tảng học tập của chúng tôi
            </p>

            {/* Search Bar */}
            <div className="max-w-md mx-auto relative mb-8">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Tìm kiếm câu hỏi..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {categoryLabels[category] || category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredFAQs.length > 0 ? (
            <div className="space-y-4">
              {filteredFAQs.map((faq) => (
                <div
                  key={faq.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                >
                  <button
                    onClick={() => toggleItem(faq.id)}
                    className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                  >
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">
                      {faq.question}
                    </h3>
                    {openItems.includes(faq.id) ? (
                      <ChevronUp className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    )}
                  </button>
                  
                  {openItems.includes(faq.id) && (
                    <div className="px-6 pb-4">
                      <div className="border-t border-gray-200 pt-4">
                        <p className="text-gray-700 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Không tìm thấy câu hỏi nào
              </h3>
              <p className="text-gray-600 mb-4">
                Thử tìm kiếm với từ khóa khác hoặc chọn danh mục khác
              </p>
              <button
                onClick={() => {
                  setSearchQuery("");
                  setSelectedCategory("all");
                }}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Xem tất cả câu hỏi
              </button>
            </div>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section className="bg-blue-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Không tìm thấy câu trả lời?
          </h3>
          <p className="text-gray-600 mb-6">
            Liên hệ với chúng tôi để được hỗ trợ trực tiếp
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="tel:0337729889"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Hotline: ************
            </a>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            >
              Email: <EMAIL>
            </a>
          </div>
        </div>
      </section>

    </Layout>
  );
}
