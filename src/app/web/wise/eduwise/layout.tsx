import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./eduwise.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "WISE Đào tạo",
  description: "NÂNG CẤP BẢN THÂN. NÂNG TẦM DOANH NGHIỆP",
};

export default function EduWiseLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className={`${inter.variable} antialiased`}>
      <script
        src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        async
      />
      {children}
    </div>
  );
}
