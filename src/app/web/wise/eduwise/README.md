# EduWise - WISE Vietnam Learning Platform

EduWise is a comprehensive online learning platform for the Women's Initiative for Startup and Entrepreneurship (WISE) Vietnam. This platform provides free courses and resources to help women entrepreneurs develop their business skills.

**Location**: `src/app/web/wise/eduwise/`
**Data Storage**: `data/apps/web/wise/eduwise/`

## Features

- 🎓 **Course Management**: Browse and view detailed course information
- 📚 **Lesson Viewer**: Interactive lesson pages with video support
- 📝 **Quiz System**: Complete quiz functionality with scoring and results
- 📱 **Responsive Design**: Fully responsive across all devices
- 🎨 **Modern UI**: Clean, professional design with Tailwind CSS
- ⚡ **Fast Performance**: Built with Next.js 15 for optimal performance
- 🔍 **Search**: Search courses by name and description
- 📊 **Progress Tracking**: Visual progress indicators for courses

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Data Storage**: JSON files (pure data storage as requested)
- **Package Manager**: Yarn

## Getting Started

### Prerequisites

- Node.js 18+
- Yarn package manager

### Installation

1. Navigate to the project directory:
```bash
cd nextjs
```

2. Install dependencies:
```bash
yarn install
```

3. Start the development server:
```bash
yarn start
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Available Scripts

- `yarn start` - Start development server
- `yarn dev` - Start development server (alternative)
- `yarn build` - Build for production
- `yarn start:prod` - Start production server
- `yarn lint` - Run ESLint

## Project Structure

```
nextjs/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── courses/           # Course-related pages
│   │   │   ├── [slug]/        # Individual course pages
│   │   │   │   ├── lessons/   # Lesson viewer pages
│   │   │   │   └── quizzes/   # Quiz pages
│   │   │   └── page.tsx       # Course listing page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # Reusable React components
│   │   ├── CourseCard.tsx     # Course display components
│   │   └── Layout.tsx         # Layout components
│   ├── data/                  # JSON data files
│   │   ├── courses.json       # Course data
│   │   ├── lessons.json       # Lesson data
│   │   ├── quizzes.json       # Quiz data
│   │   ├── questions.json     # Question data
│   │   └── answers.json       # Answer data
│   ├── lib/                   # Utility functions
│   │   ├── data.ts           # Data access functions
│   │   └── utils.ts          # General utilities
│   └── types/                 # TypeScript type definitions
│       └── index.ts          # Type definitions
├── scripts/                   # Build and utility scripts
│   └── extract-data.js       # Data extraction script
├── package.json              # Dependencies and scripts
└── README.md                 # This file
```

## Data Structure

The application uses JSON files for data storage:

- **Courses**: Main course information including name, description, pricing
- **Lessons**: Individual lesson content with video URLs and descriptions
- **Quizzes**: Quiz metadata including duration and passing grades
- **Questions**: Quiz questions with types (single/multiple choice)
- **Answers**: Answer options with correct/incorrect flags

## Features Overview

### Course Listing
- Grid and list view of all courses
- Search functionality
- Course filtering and sorting
- Responsive design for mobile and desktop

### Course Details
- Comprehensive course information
- Course curriculum with sections and lessons
- Enrollment information and pricing
- Progress tracking

### Lesson Viewer
- Video player integration
- Lesson content display
- Navigation between lessons
- Course sidebar for easy navigation

### Quiz System
- Timed quizzes with countdown
- Multiple question types (single choice, multiple choice)
- Real-time progress tracking
- Instant results with pass/fail status
- Retake functionality for failed attempts

### Responsive Design
- Mobile-first approach
- Tablet and desktop optimizations
- Touch-friendly interface
- Accessible navigation

## Customization

### Adding New Courses
1. Add course data to `src/data/courses.json`
2. Add related lessons to `src/data/lessons.json`
3. Add quizzes and questions as needed
4. The application will automatically display the new content

### Styling
- Modify `src/app/globals.css` for global styles
- Update Tailwind classes in components for design changes
- Customize color scheme in CSS variables

### Data Sources
- Replace JSON files with API calls in `src/lib/data.ts`
- Modify type definitions in `src/types/index.ts` as needed

## Performance

- Server-side rendering with Next.js
- Optimized images and assets
- Efficient component rendering
- Minimal JavaScript bundle size

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact:
- Email: <EMAIL>
- Website: [wisevietnam.org](https://wisevietnam.org)
