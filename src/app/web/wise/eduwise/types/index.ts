// Course related types
export interface Course {
  id: number;
  name: string;
  slug: string;
  description: string;
  content: string;
  thumbnail?: string | null;
  duration: number;
  duration_type: number; // 1: minute, 2: hour, 3: day, 4: week
  regular_price?: number | null;
  sale_price?: number | null;
  status: number; // 1: active, 2: draft
  total_lessons: number;
  total_quizzes: number;
  instructor_id: number;
  created_at: string;
  updated_at: string;
  // Computed properties
  sections?: CourseSection[];
  progress?: number;
  is_enrolled?: boolean;
}

export interface CourseSection {
  id: number;
  name: string;
  course_id: number;
  description: string;
  sort_order: number;
  total_lessons: number;
  total_quizzes: number;
  // Computed properties
  items?: CourseSectionItem[];
}

export interface CourseSectionItem {
  id: number;
  course_section_id: number;
  item_id: number;
  sort_order: number;
  type: number; // 1: Lesson, 2: Quiz
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  // Computed properties
  lesson?: Lesson;
  quiz?: Quiz;
}

// Tag related types
export interface Tag {
  id: string;
  name: string;
  slug: string;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

// Lesson related types
export interface Lesson {
  id: number;
  name: string;
  slug: string;
  description: string;
  content: string;
  video_url?: string | null;
  intro?: string;
  duration: number;
  duration_type: number; // 1: minute, 2: hour, 3: day, 4: week
  status: number;
  tags?: string[]; // Array of tag IDs
  // Computed properties
  is_completed?: boolean;
  progress?: number;
}

// Quiz related types
export interface Quiz {
  id: number;
  name: string;
  slug: string;
  description: string;
  content: string;
  duration: number;
  duration_type: number; // 1: minute, 2: hour, 3: day, 4: week
  passing_grade: number;
  status: number;
  // Computed properties
  questions?: Question[];
  is_completed?: boolean;
  score?: number;
  passed?: boolean;
}

export interface Question {
  id: number;
  question: string;
  type: number; // 1: boolean, 2: single choice, 3: multiple choice, 4: fill blank
  quiz_id: number;
  order: number;
  // Computed properties
  answers?: Answer[];
  selected_answers?: number[];
  is_correct?: boolean;
}

export interface Answer {
  id: number;
  question_id: number;
  answer: string;
  is_correct: number; // 1: correct, 0: incorrect
}

// User progress and enrollment types
export interface CourseEnrollment {
  id: number;
  course_id: number;
  customer_id: number;
  status: number; // 1: enrolled, 2: in_progress, 3: completed
  progress: number; // 0-100
  enrolled_at: string;
  completed_at?: string | null;
}

export interface LessonProgress {
  id: number;
  lesson_id: number;
  customer_id: number;
  status: number; // 1: in_progress, 2: completed
  progress: number; // 0-100
  started_at: string;
  completed_at?: string | null;
}

export interface QuizResult {
  id: number;
  quiz_id: number;
  customer_id: number;
  score: number;
  passed: boolean;
  answers: QuizAnswer[];
  completed_at: string;
}

export interface QuizAnswer {
  question_id: number;
  selected_answers: number[];
  is_correct: boolean;
}

// UI and navigation types
export interface BreadcrumbItem {
  label: string;
  href?: string;
}

export interface NavigationItem {
  id: number;
  title: string;
  type: 'lesson' | 'quiz';
  slug: string;
  is_completed?: boolean;
  is_current?: boolean;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Search and filter types
export interface CourseFilters {
  search?: string;
  category?: string;
  level?: string;
  price?: 'free' | 'paid';
  duration?: string;
  sort?: 'newest' | 'oldest' | 'popular' | 'name';
}

export interface SearchResult {
  courses: Course[];
  total: number;
  filters: CourseFilters;
}

// Constants
export const DURATION_TYPES = {
  MINUTE: 1,
  HOUR: 2,
  DAY: 3,
  WEEK: 4
} as const;

export const COURSE_STATUS = {
  DRAFT: 2,
  ACTIVE: 1
} as const;

export const QUESTION_TYPES = {
  BOOLEAN: 1,
  SINGLE_CHOICE: 2,
  MULTIPLE_CHOICE: 3,
  FILL_BLANK: 4
} as const;

export const ENROLLMENT_STATUS = {
  ENROLLED: 1,
  IN_PROGRESS: 2,
  COMPLETED: 3
} as const;

export const LESSON_STATUS = {
  IN_PROGRESS: 1,
  COMPLETED: 2
} as const;

// User types for backend management
export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  phone: string;
  password: string;
  organization: string;
  business_areas: string;
  references: string;
  point: number;
  status: number;
  createdAt: string;
}

// Score tracking types
export interface UserScore {
  id: string;
  user_id: string;
  course_id: number;
  quiz_id: number;
  score: number;
  max_score: number;
  percentage: number;
  passed: boolean;
  completed_at: string;
  attempts: number;
}

// Course enrollment tracking
export interface UserEnrollment {
  id: string;
  user_id: string;
  course_id: number;
  enrolled_at: string;
  progress: number;
  status: 'enrolled' | 'in_progress' | 'completed' | 'dropped';
  completed_at?: string;
}




// Backend management types
export interface BackendStats {
  total_users: number;
  total_courses: number;
  total_quizzes: number;
  total_lessons: number;
  total_enrollments: number;
  active_users: number;
  completed_courses: number;
}

export interface UserActivity {
  user_id: string;
  activity_type: 'login' | 'course_start' | 'lesson_complete' | 'quiz_complete' | 'course_complete';
  course_id?: number;
  lesson_id?: number;
  quiz_id?: number;
  timestamp: string;
  details?: any;
}
