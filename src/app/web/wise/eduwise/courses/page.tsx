"use client";

import { useState, useMemo, useEffect } from "react";
import Link from "next/link";
import { Clock, Search, BookOpen } from "lucide-react";
import { getAllCourses, searchCourses } from "../lib/data";
import { formatTime, formatPrice } from "../lib/utils";
import Layout, { Breadcrumb } from "../components/Layout";
import Image from "next/image";
import { Course } from "../types";

type SortOption = "title-asc" | "title-desc" | "newest" | "oldest";
type LevelFilter = "all" | "overview" | "test";

export default function CoursesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("newest");
  const [levelFilter, setLevelFilter] = useState<LevelFilter>("all");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [allCourses, setAllCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadCourses() {
      try {
        const courses = await getAllCourses();
        setAllCourses(courses);
      } catch (error) {
        console.error('Error loading courses:', error);
      } finally {
        setLoading(false);
      }
    }

    loadCourses();
  }, []);

  // Available tags (extracted from course content/descriptions)
  const availableTags = [
    "Marketing",
    "Thương hiệu",
    "Chuyển đổi số",
    "Quản lý tài chính",
    "Chiến lược kinh doanh",
    "Lợi nhuận",
    "Thuế",
    "Định giá"
  ];

  const filteredAndSortedCourses = useMemo(() => {
    let courses = searchQuery.trim() === "" ? allCourses : searchCourses(searchQuery);

    // Apply level filter
    if (levelFilter !== "all") {
      // For demo purposes, we'll filter based on course content
      courses = courses.filter(course => {
        if (levelFilter === "overview") {
          return course.description.toLowerCase().includes("tổng quan") ||
                 course.description.toLowerCase().includes("cơ bản") ||
                 course.description.toLowerCase().includes("hiểu về");
        }
        if (levelFilter === "test") {
          return course.total_quizzes > 0;
        }
        return true;
      });
    }

    // Apply tag filter
    if (selectedTags.length > 0) {
      courses = courses.filter(course => {
        return selectedTags.some(tag =>
          course.name.toLowerCase().includes(tag.toLowerCase()) ||
          course.description.toLowerCase().includes(tag.toLowerCase()) ||
          course.content.toLowerCase().includes(tag.toLowerCase())
        );
      });
    }

    // Apply sorting
    courses = [...courses].sort((a, b) => {
      switch (sortBy) {
        case "title-asc":
          return a.name.localeCompare(b.name, 'vi');
        case "title-desc":
          return b.name.localeCompare(a.name, 'vi');
        case "newest":
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case "oldest":
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        default:
          return 0;
      }
    });

    return courses;
  }, [searchQuery, sortBy, levelFilter, selectedTags, allCourses]);

  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  if (loading) {
    return (
      <Layout currentPage="courses">
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải khóa học...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout currentPage="courses">
      <Breadcrumb
        items={[
          { label: 'Trang chủ', href: '/' },
          { label: 'Khóa học' }
        ]}
      />

      {/* Main Content */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Filters */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                {/* Search */}
                <div className="mb-6">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-wise-neutral-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Tìm kiếm khóa học..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="block w-full pl-10 pr-3 py-2 border border-wise-neutral-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-wise-primary focus:border-wise-primary"
                    />
                  </div>
                </div>

                {/* Sort Options */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-wise-neutral-900 mb-3">Sắp xếp theo</h4>
                  <div className="space-y-2">
                    {[
                      { value: "title-asc", label: "Tiêu đề A-Z" },
                      { value: "title-desc", label: "Tiêu đề Z-A" },
                      { value: "oldest", label: "Nội dung Cũ nhất" },
                      { value: "newest", label: "Nội dung Mới nhất" }
                    ].map(option => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="sort"
                          value={option.value}
                          checked={sortBy === option.value}
                          onChange={(e) => setSortBy(e.target.value as SortOption)}
                          className="h-4 w-4 text-wise-primary focus:ring-wise-primary border-wise-neutral-300"
                        />
                        <span className="ml-2 text-sm text-wise-neutral-700">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Level Filter */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-wise-neutral-900 mb-3">Mức độ chuyên sâu</h4>
                  <div className="space-y-2">
                    {[
                      { value: "all", label: "Tất cả" },
                      { value: "overview", label: "Tổng quan" },
                      { value: "test", label: "Test" }
                    ].map(option => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="level"
                          value={option.value}
                          checked={levelFilter === option.value}
                          onChange={(e) => setLevelFilter(e.target.value as LevelFilter)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Tags Filter */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Tags</h4>
                  <div className="space-y-2">
                    {availableTags.map(tag => (
                      <label key={tag} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedTags.includes(tag)}
                          onChange={() => toggleTag(tag)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">{tag}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Course Grid */}
            <div className="lg:col-span-3">
              {/* Results Info */}
              <div className="flex justify-between items-center mb-6">
                <p className="text-gray-600">
                  Hiển thị {filteredAndSortedCourses.length} khóa học
                  {searchQuery && ` cho "${searchQuery}"`}
                </p>
              </div>

              {/* Courses Grid */}
              {filteredAndSortedCourses.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {filteredAndSortedCourses.map((course) => (
                <div
                  key={course.id}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                >
                  {/* Course Image */}
                  <div className="h-48 bg-gradient-to-r from-wise-primary to-wise-primary-dark relative">
                    <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                      <Image
                        src={course.thumbnail || "/course_default.png"}
                        alt={course.name}
                        width={300}
                        height={192}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  </div>

                  {/* Course Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                      {course.name}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3 text-sm">
                      {course.description}
                    </p>

                    {/* Course Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                        <BookOpen className="h-4 w-4 mr-1" />
                        <span>{course.total_lessons} bài học</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{formatTime(course.duration || 60)}</span>
                      </div>
                    </div>

                    {/* Course Footer */}
                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="text-2xl font-bold text-blue-600">
                          {formatPrice(course.sale_price || course.regular_price || 0)}
                        </span>
                        {course.regular_price && course.sale_price && course.regular_price !== course.sale_price && (
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(course.regular_price)}
                          </span>
                        )}
                      </div>
                      <Link
                        href={`/courses/${course.slug}`}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                      >
                        Xem chi tiết
                      </Link>
                    </div>
                  </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Không tìm thấy khóa học
                  </h3>
                  <p className="text-gray-600">
                    Thử tìm kiếm với từ khóa khác hoặc{" "}
                    <button
                      onClick={() => {
                        setSearchQuery("");
                        setLevelFilter("all");
                        setSelectedTags([]);
                      }}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      xem tất cả khóa học
                    </button>
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

    </Layout>
  );
}
