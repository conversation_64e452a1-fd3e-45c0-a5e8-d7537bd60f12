"use client";

import { useState, useEffect } from "react";
import { notFound } from "next/navigation";
import Link from "next/link";
import { BookO<PERSON>, Clock, XCircle, AlertCircle, Trophy } from "lucide-react";
import { getCourseBySlug, getQuizBySlug } from "../../../../lib/data";
import { Course, Quiz } from "../../../../types";

interface QuizPageProps {
  params: Promise<{
    slug: string;
    quizSlug: string;
  }>;
}

function QuizPageContent({ slug, quizSlug }: { slug: string; quizSlug: string }) {
  const [course, setCourse] = useState<Course | null>(null);
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(true);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<Record<number, number[]>>({});
  const [showResults, setShowResults] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [quizStarted, setQuizStarted] = useState(false);

  useEffect(() => {
    async function loadData() {
      try {
        const [courseData, quizData] = await Promise.all([
          getCourseBySlug(slug),
          Promise.resolve(getQuizBySlug(quizSlug))
        ]);
        setCourse(courseData);
        setQuiz(quizData);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [slug, quizSlug]);

  useEffect(() => {
    if (quizStarted && quiz && timeRemaining === null) {
      setTimeRemaining((quiz.duration || 15) * 60); // Convert minutes to seconds
    }
  }, [quizStarted, quiz, timeRemaining]);

  useEffect(() => {
    if (timeRemaining !== null && timeRemaining > 0 && quizStarted && !showResults) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeRemaining === 0 && !showResults) {
      handleSubmitQuiz();
    }
  }, [timeRemaining, quizStarted, showResults]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (!course || !quiz) {
    notFound();
  }

  const currentQuestion = quiz.questions?.[currentQuestionIndex];
  const totalQuestions = quiz.questions?.length || 0;

  const handleAnswerSelect = (questionId: number, answerId: number, isMultiple: boolean) => {
    setSelectedAnswers(prev => {
      if (isMultiple) {
        const current = prev[questionId] || [];
        const newAnswers = current.includes(answerId)
          ? current.filter(id => id !== answerId)
          : [...current, answerId];
        return { ...prev, [questionId]: newAnswers };
      } else {
        return { ...prev, [questionId]: [answerId] };
      }
    });
  };

  const calculateScore = () => {
    if (!quiz.questions) return 0;
    
    let correctAnswers = 0;
    quiz.questions.forEach(question => {
      const userAnswers = selectedAnswers[question.id] || [];
      const correctAnswerIds = question.answers?.filter(a => a.is_correct === 1).map(a => a.id) || [];
      
      if (question.type === 2) { // Single choice
        if (userAnswers.length === 1 && correctAnswerIds.includes(userAnswers[0])) {
          correctAnswers++;
        }
      } else if (question.type === 3) { // Multiple choice
        const userSet = new Set(userAnswers);
        const correctSet = new Set(correctAnswerIds);
        if (userSet.size === correctSet.size && [...userSet].every(id => correctSet.has(id))) {
          correctAnswers++;
        }
      }
    });
    
    return Math.round((correctAnswers / quiz.questions.length) * 100);
  };

  const handleSubmitQuiz = () => {
    setShowResults(true);
  };

  const formatTimeRemaining = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const score = showResults ? calculateScore() : 0;
  const passed = score >= (quiz.passing_grade || 70);

  if (!quizStarted) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                <BookOpen className="h-6 w-6 text-blue-600" />
                <h1 className="ml-2 text-lg font-bold text-gray-900">
                  <Link href="/">Đào tạo Wise Vietnam</Link>
                </h1>
              </div>
              <Link
                href={`/courses/${course.slug}`}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Quay lại khóa học
              </Link>
            </div>
          </div>
        </header>

        {/* Quiz Introduction */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="mb-6">
              <AlertCircle className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {quiz.name}
              </h1>
              <p className="text-lg text-gray-600">
                {quiz.description}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{totalQuestions}</div>
                <div className="text-sm text-gray-600">Câu hỏi</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{quiz.duration || 15}</div>
                <div className="text-sm text-gray-600">Phút</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{quiz.passing_grade || 70}%</div>
                <div className="text-sm text-gray-600">Điểm đạt</div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-yellow-800 mb-2">Lưu ý quan trọng:</h3>
              <ul className="text-sm text-yellow-700 text-left space-y-1">
                <li>• Bạn có {quiz.duration || 15} phút để hoàn thành bài kiểm tra</li>
                <li>• Cần đạt tối thiểu {quiz.passing_grade || 70}% để vượt qua</li>
                <li>• Không thể quay lại sau khi đã bắt đầu</li>
                <li>• Hãy đảm bảo kết nối internet ổn định</li>
              </ul>
            </div>

            <button
              onClick={() => setQuizStarted(true)}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Bắt đầu làm bài
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (showResults) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                <BookOpen className="h-6 w-6 text-blue-600" />
                <h1 className="ml-2 text-lg font-bold text-gray-900">
                  <Link href="/">Đào tạo Wise Vietnam</Link>
                </h1>
              </div>
            </div>
          </div>
        </header>

        {/* Results */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="mb-6">
              {passed ? (
                <Trophy className="h-16 w-16 text-green-600 mx-auto mb-4" />
              ) : (
                <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              )}
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {passed ? "Chúc mừng!" : "Chưa đạt yêu cầu"}
              </h1>
              
              <div className="text-6xl font-bold mb-4">
                <span className={passed ? "text-green-600" : "text-red-600"}>
                  {score}%
                </span>
              </div>
              
              <p className="text-lg text-gray-600 mb-6">
                {passed 
                  ? "Bạn đã hoàn thành xuất sắc bài kiểm tra!"
                  : `Bạn cần đạt tối thiểu ${quiz.passing_grade || 70}% để vượt qua.`
                }
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round((score / 100) * totalQuestions)}/{totalQuestions}
                </div>
                <div className="text-sm text-gray-600">Câu đúng</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {quiz.passing_grade || 70}%
                </div>
                <div className="text-sm text-gray-600">Điểm yêu cầu</div>
              </div>
            </div>

            <div className="space-y-4">
              <Link
                href={`/courses/${course.slug}`}
                className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors mr-4"
              >
                Quay lại khóa học
              </Link>
              
              {!passed && (
                <button
                  onClick={() => {
                    setQuizStarted(false);
                    setShowResults(false);
                    setCurrentQuestionIndex(0);
                    setSelectedAnswers({});
                    setTimeRemaining(null);
                  }}
                  className="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors"
                >
                  Làm lại
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Quiz Taking Interface
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <BookOpen className="h-6 w-6 text-blue-600" />
              <h1 className="ml-2 text-lg font-bold text-gray-900">
                {quiz.name}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {timeRemaining !== null && (
                <div className="flex items-center text-sm">
                  <Clock className="h-4 w-4 mr-1 text-red-600" />
                  <span className={`font-mono ${timeRemaining < 300 ? 'text-red-600' : 'text-gray-600'}`}>
                    {formatTimeRemaining(timeRemaining)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Bar */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Câu hỏi {currentQuestionIndex + 1} / {totalQuestions}
            </h2>
            <span className="text-sm text-gray-600">
              {Math.round(((currentQuestionIndex + 1) / totalQuestions) * 100)}% hoàn thành
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / totalQuestions) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Question */}
        {currentQuestion && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">
              {currentQuestion.question}
            </h3>

            <div className="space-y-3">
              {currentQuestion.answers?.map((answer) => (
                <label
                  key={answer.id}
                  className="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                >
                  <input
                    type={currentQuestion.type === 3 ? "checkbox" : "radio"}
                    name={`question-${currentQuestion.id}`}
                    value={answer.id}
                    checked={(selectedAnswers[currentQuestion.id] || []).includes(answer.id)}
                    onChange={() => handleAnswerSelect(
                      currentQuestion.id,
                      answer.id,
                      currentQuestion.type === 3
                    )}
                    className="mt-1 mr-3"
                  />
                  <span className="text-gray-900">{answer.answer}</span>
                </label>
              ))}
            </div>

            {currentQuestion.type === 3 && (
              <p className="text-sm text-gray-600 mt-4">
                * Có thể chọn nhiều đáp án
              </p>
            )}
          </div>
        )}

        {/* Navigation */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex justify-between items-center">
            <button
              onClick={() => setCurrentQuestionIndex(Math.max(0, currentQuestionIndex - 1))}
              disabled={currentQuestionIndex === 0}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Câu trước
            </button>

            <div className="flex space-x-2">
              {Array.from({ length: totalQuestions }, (_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentQuestionIndex(index)}
                  className={`w-8 h-8 rounded text-xs font-medium ${
                    index === currentQuestionIndex
                      ? 'bg-blue-600 text-white'
                      : selectedAnswers[quiz.questions?.[index]?.id || 0]?.length > 0
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {index + 1}
                </button>
              ))}
            </div>

            {currentQuestionIndex === totalQuestions - 1 ? (
              <button
                onClick={handleSubmitQuiz}
                className="px-6 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
              >
                Nộp bài
              </button>
            ) : (
              <button
                onClick={() => setCurrentQuestionIndex(Math.min(totalQuestions - 1, currentQuestionIndex + 1))}
                className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
              >
                Câu tiếp theo
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function QuizPage({ params }: QuizPageProps) {
  const [slug, setSlug] = useState<string>("");
  const [quizSlug, setQuizSlug] = useState<string>("");

  useEffect(() => {
    params.then(({ slug, quizSlug }) => {
      setSlug(slug);
      setQuizSlug(quizSlug);
    });
  }, [params]);

  if (!slug || !quizSlug) {
    return <div>Loading...</div>;
  }

  return <QuizPageContent slug={slug} quizSlug={quizSlug} />;
}
