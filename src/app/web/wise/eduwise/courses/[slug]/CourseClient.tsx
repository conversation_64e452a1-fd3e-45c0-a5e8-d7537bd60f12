"use client";

import Link from "next/link";
import { BookOpen, Clock, PlayCircle, FileText, CheckCircle, Users } from "lucide-react";
import { formatTime, formatPrice, stripHtml, truncateText } from "../../lib/utils";
import Header from "../../components/Header";
import { authService, User } from "../../lib/auth";
import { useState, useEffect } from "react";
import { Course, CourseSection, CourseSectionItem } from "../../types";

interface CourseClientProps {
  course: Course;
}

export default function CourseClient({ course }: CourseClientProps) {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header user={user} />
      
      {/* Breadcrumb */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <Link href="/" className="text-gray-500 hover:text-gray-700">
                Trang chủ
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li>
              <Link href="/courses" className="text-gray-500 hover:text-gray-700">
                Khóa học
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900 font-medium truncate">
              {course.name}
            </li>
          </ol>
        </div>
      </nav>

      {/* Course Content */}
      {/* Course Header */}
      <section className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Course Info */}
            <div className="lg:col-span-2">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {course.name}
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                {course.description}
              </p>

              {/* Course Stats */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-8">
                <div className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-2" />
                  <span>{course.total_lessons} bài học</span>
                </div>
                <div className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  <span>{course.total_quizzes} bài kiểm tra</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>{formatTime(course.duration || 120)}</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  <span>Không giới hạn học viên</span>
                </div>
              </div>

              {/* Course Description */}
              <div className="prose max-w-none">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Mô tả khóa học
                </h2>
                <div 
                  className="text-gray-700 leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: course.content }}
                />
              </div>
            </div>

            {/* Course Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white border border-gray-200 rounded-lg p-6 sticky top-6">
                {/* Course Image */}
                <div className="h-48 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mb-6 relative">
                  <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center rounded-lg">
                    <PlayCircle className="h-16 w-16 text-white" />
                  </div>
                </div>

                {/* Price */}
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {formatPrice(course.sale_price || course.regular_price || 0)}
                  </div>
                  {course.regular_price && course.sale_price && course.regular_price !== course.sale_price && (
                    <div className="text-lg text-gray-500 line-through">
                      {formatPrice(course.regular_price)}
                    </div>
                  )}
                </div>

                {/* Enroll Button */}
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors mb-4">
                  Đăng ký học ngay
                </button>

                {/* Course Features */}
                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Truy cập trọn đời</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Học theo tiến độ cá nhân</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Chứng chỉ hoàn thành</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Hỗ trợ trực tuyến</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Course Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">
            Nội dung khóa học
          </h2>

          <div className="space-y-6">
            {course.sections?.map((section: CourseSection, sectionIndex: number) => (
              <div key={section.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {sectionIndex + 1}. {section.name}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {section.total_lessons} bài học • {section.total_quizzes} bài kiểm tra
                  </p>
                </div>
                
                <div className="divide-y divide-gray-200">
                  {section.items?.map((item: CourseSectionItem) => (
                    <div key={item.id} className="px-6 py-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {item.type === 1 ? (
                            <PlayCircle className="h-5 w-5 text-blue-600 mr-3" />
                          ) : (
                            <FileText className="h-5 w-5 text-green-600 mr-3" />
                          )}
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">
                              {item.lesson?.name || item.quiz?.name}
                            </h4>
                            {item.lesson?.description && (
                              <p className="text-xs text-gray-600 mt-1">
                                {truncateText(stripHtml(item.lesson.description), 100)}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          {item.lesson && formatTime(item.lesson.duration || 15)}
                          {item.quiz && `${item.quiz.duration || 15} phút`}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <BookOpen className="h-8 w-8 text-blue-400" />
                <span className="ml-2 text-xl font-bold">Đào tạo Wise Vietnam</span>
              </div>
              <p className="text-gray-400">
                Nền tảng học tập trực tuyến hàng đầu cho doanh nghiệp và cá nhân.
              </p>
            </div>
            <div>
              <h5 className="text-lg font-semibold mb-4">Liên kết</h5>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/courses" className="hover:text-white">Khóa học</Link></li>
                <li><Link href="/about" className="hover:text-white">Giới thiệu</Link></li>
                <li><Link href="/contact" className="hover:text-white">Liên hệ</Link></li>
              </ul>
            </div>
            <div>
              <h5 className="text-lg font-semibold mb-4">Thông tin</h5>
              <p className="text-gray-400 text-sm">
                © 2024 Wise Vietnam. Tất cả quyền được bảo lưu.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
