import { notFound } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Clock, PlayCircle, ChevronLeft, ChevronRight, CheckCircle } from "lucide-react";
import { getCourseBySlug, getLessonBySlug, getNextLesson, getPreviousLesson } from "../../../../lib/data";
import { formatTime } from "../../../../lib/utils";

interface LessonPageProps {
  params: Promise<{
    slug: string;
    lessonSlug: string;
  }>;
}

export default async function LessonPage({ params }: LessonPageProps) {
  const { slug, lessonSlug } = await params;
  const course = await getCourseBySlug(slug);
  const lesson = getLessonBySlug(lessonSlug);

  if (!course || !lesson) {
    notFound();
  }

  const nextLesson = getNextLesson(lesson.id, course.id);
  const previousLesson = getPreviousLesson(lesson.id, course.id);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <BookOpen className="h-6 w-6 text-blue-600" />
              <h1 className="ml-2 text-lg font-bold text-gray-900">
                <Link href="/">Đào tạo Wise Vietnam</Link>
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/courses/${course.slug}`}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Quay lại khóa học
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <Link href="/" className="text-gray-500 hover:text-gray-700">
                Trang chủ
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li>
              <Link href="/courses" className="text-gray-500 hover:text-gray-700">
                Khóa học
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li>
              <Link href={`/courses/${course.slug}`} className="text-gray-500 hover:text-gray-700">
                {course.name}
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900 font-medium truncate">
              {lesson.name}
            </li>
          </ol>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Lesson Header */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                {lesson.name}
              </h1>
              <p className="text-gray-600 mb-4">
                {lesson.description}
              </p>
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="h-4 w-4 mr-1" />
                <span>{formatTime(lesson.duration || 15)}</span>
              </div>
            </div>

            {/* Video Player */}
            {lesson.video_url && (
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center">
                  <div className="text-center text-white">
                    <PlayCircle className="h-16 w-16 mx-auto mb-4" />
                    <p className="text-lg">Video Player</p>
                    <p className="text-sm text-gray-300 mt-2">
                      URL: {lesson.video_url}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Lesson Content */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                Nội dung bài học
              </h2>
              <div 
                className="prose max-w-none text-gray-700"
                dangerouslySetInnerHTML={{ __html: lesson.content }}
              />
            </div>

            {/* Navigation */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-center">
                <div>
                  {previousLesson ? (
                    <Link
                      href={`/courses/${course.slug}/lessons/${previousLesson.slug}`}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <ChevronLeft className="h-4 w-4 mr-2" />
                      Bài trước
                    </Link>
                  ) : (
                    <div></div>
                  )}
                </div>
                
                <div className="text-center">
                  <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Hoàn thành bài học
                  </button>
                </div>

                <div>
                  {nextLesson ? (
                    <Link
                      href={`/courses/${course.slug}/lessons/${nextLesson.slug}`}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                    >
                      Bài tiếp theo
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Link>
                  ) : (
                    <div></div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Nội dung khóa học
              </h3>
              
              <div className="space-y-4">
                {course.sections?.map((section, sectionIndex) => (
                  <div key={section.id}>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      {sectionIndex + 1}. {section.name}
                    </h4>
                    <div className="space-y-1 ml-4">
                      {section.items?.map((item) => (
                        <div key={item.id}>
                          {item.lesson && (
                            <Link
                              href={`/courses/${course.slug}/lessons/${item.lesson.slug}`}
                              className={`block text-sm py-1 px-2 rounded hover:bg-gray-100 ${
                                item.lesson.id === lesson.id 
                                  ? 'bg-blue-100 text-blue-700 font-medium' 
                                  : 'text-gray-600'
                              }`}
                            >
                              <div className="flex items-center">
                                <PlayCircle className="h-3 w-3 mr-2" />
                                <span className="truncate">{item.lesson.name}</span>
                              </div>
                            </Link>
                          )}
                          {item.quiz && (
                            <Link
                              href={`/courses/${course.slug}/quizzes/${item.quiz.slug}`}
                              className="block text-sm py-1 px-2 rounded hover:bg-gray-100 text-gray-600"
                            >
                              <div className="flex items-center">
                                <CheckCircle className="h-3 w-3 mr-2" />
                                <span className="truncate">{item.quiz.name}</span>
                              </div>
                            </Link>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
