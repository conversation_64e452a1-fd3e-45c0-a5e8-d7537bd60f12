import Link from "next/link";
import Image from "next/image";
import { getAllCourses } from "./lib/data";
import Layout from "./components/Layout";
import CourseCard from "./components/CourseCard";

export default async function Home() {
  const allCourses = await getAllCourses();

  return (
    <Layout>
      {/* Hero Section */}
{/*       <section className="bg-gradient-to-r from-wise-primary to-wise-primary-dark text-white py-20"> */}
      <section className="bg-white text-black py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl lg:text-5xl font-bold mb-8 leading-tight text-wise-neutral-800">
                NÂNG CẤP BẢN THÂN.<br />
                NÂNG TẦM DOANH NGHIỆP.
              </h1>
              
              {/* Feature Icons */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                {/* Feature 1 */}
                <div className="flex flex-col items-center text-center p-6 bg-wise-neutral-50 rounded-lg border border-wise-neutral-200 hover:shadow-md transition-shadow">
                  <div className="w-16 h-16 mb-4 flex items-center justify-center">
                    <Image
                      src="/icon-1.svg"
                      alt="Free Online Courses"
                      width={48}
                      height={48}
                      className="w-12 h-12"
                    />
                  </div>
                  <p className="text-sm font-medium text-wise-neutral-700">
                    Khóa học trực tuyến miễn phí.
                  </p>
                </div>

                {/* Feature 2 */}
                <div className="flex flex-col items-center text-center p-6 bg-wise-neutral-50 rounded-lg border border-wise-neutral-200 hover:shadow-md transition-shadow">
                  <div className="w-16 h-16 mb-4 flex items-center justify-center">
                    <Image
                      src="/icon-2.svg"
                      alt="Expert Instructors"
                      width={48}
                      height={48}
                      className="w-12 h-12"
                    />
                  </div>
                  <p className="text-sm font-medium text-wise-neutral-700">
                    Chuyên gia hàng đầu trong nhiều lĩnh vực.
                  </p>
                </div>

                {/* Feature 3 */}
                <div className="flex flex-col items-center text-center p-6 bg-wise-neutral-50 rounded-lg border border-wise-neutral-200 hover:shadow-md transition-shadow">
                  <div className="w-16 h-16 mb-4 flex items-center justify-center">
                    <Image
                      src="/icon-3.svg"
                      alt="Practical Knowledge"
                      width={48}
                      height={48}
                      className="w-12 h-12"
                    />
                  </div>
                  <p className="text-sm font-medium text-wise-neutral-700">
                    Kiến thức và kinh nghiệm thực tiễn.
                  </p>
                </div>

                {/* Feature 4 */}
                <div className="flex flex-col items-center text-center p-6 bg-wise-neutral-50 rounded-lg border border-wise-neutral-200 hover:shadow-md transition-shadow">
                  <div className="w-16 h-16 mb-4 flex items-center justify-center">
                    <Image
                      src="/icon-4.svg"
                      alt="Connect Opportunities"
                      width={48}
                      height={48}
                      className="w-12 h-12"
                    />
                  </div>
                  <p className="text-sm font-medium text-wise-neutral-700">
                    Kết nối cơ hội. Dẫn lối thành công.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <Image
                src="/bv2-e1639024615824.jpeg"
                alt="WISE Vietnam Training"
                width={600}
                height={400}
                className="rounded-lg shadow-2xl"
                priority
              />
            </div>
          </div>
        </div>
      </section>
      
      {/* Courses Section */}
      <section className="py-16 bg-wise-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-wise-neutral-900 mb-4">Khóa học</h2>
            <p className="text-lg text-wise-neutral-600 max-w-2xl mx-auto">
              Khoá học khởi nghiệp dành cho bất kỳ ai có mong muốn và dự định khởi sự kinh doanh
            </p>
          </div>
          
          {/* Courses Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {allCourses.slice(0, 9).map((course) => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
          
          {/* View All Courses Button */}
          <div className="text-center mt-12">
            <Link
              href="/web/wise/eduwise/courses"
              className="inline-flex items-center px-8 py-3 bg-wise-primary text-white font-semibold rounded-lg hover:bg-wise-primary-dark transition-colors"
            >
              Xem tất cả khóa học
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-wise-neutral-900 mb-4">Ý kiến học viên</h2>
            <p className="text-lg text-wise-neutral-600">
              Chúng tôi rất vui vì chúng tôi có một khách hàng hài lòng
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-wise-neutral-50 rounded-lg p-6 border border-wise-neutral-200">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-wise-secondary fill-current" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                ))}
              </div>
              <p className="text-wise-neutral-700 mb-4">
                Các khóa học của WISE rất hữu ích và thực tế. Tôi đã học được nhiều kiến thức bổ ích cho việc khởi nghiệp của mình.
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-wise-primary rounded-full mr-3"></div>
                <div>
                  <p className="text-sm text-wise-neutral-600">Doanh nhân</p>
                </div>
              </div>
            </div>
            
            {/* Testimonial 2 */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                ))}
              </div>
              <p className="text-gray-700 mb-4">
                Nội dung khóa học rất chất lượng và dễ hiểu. Tôi đã áp dụng được nhiều kiến thức vào công việc kinh doanh của mình.
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                <div>
                  <p className="text-sm text-gray-600">Chủ cửa hàng</p>
                </div>
              </div>
            </div>
            
            {/* Testimonial 3 */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                ))}
              </div>
              <p className="text-gray-700 mb-4">
                WISE đã giúp tôi có cái nhìn tổng quan về kinh doanh và khởi nghiệp. Rất cảm ơn đội ngũ giảng viên!
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                <div>
                  <p className="text-sm text-gray-600">Startup founder</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
