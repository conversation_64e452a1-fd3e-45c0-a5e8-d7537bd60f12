'use client';

import React from 'react';
import Head from 'next/head';
import { Award } from 'lucide-react';
import { useTranslation } from 'react-i18next';

// Import components
import Header from '../components/Header';
import Footer from '../components/Footer';
import GlobalStyles from '../components/GlobalStyles';
import IndustriesShowcase from '../components/IndustriesShowcase';
import CallToAction from '../components/CallToAction';
import QuickStats from '../components/QuickStats';

// Data imports
import { type TransformationStory } from '../data/abnasiadata';
import InlineChat from '../components/chat/InlineChat';

export default function SuccessStoriesPage() {
  const { t } = useTranslation();
  
  // Get data from translations and add missing fields
  const industryStoriesRaw = (t('data.industryStories', { returnObjects: true }) as { [key: string]: any[] }) || {};
  
  // Process the industry stories to add missing fields
  const industryStories: { [key: string]: TransformationStory[] } = {};
  Object.keys(industryStoriesRaw).forEach(industryKey => {
    industryStories[industryKey] = industryStoriesRaw[industryKey].map((story, index) => {
      // Assign icons based on industry and story content
      let icon = '🏢'; // Default
      
      if (industryKey === 'bankingFinance') {
        icon = story.title.includes('Digital Banking') ? '💳' : 
               story.title.includes('Risk Management') ? '🛡️' : '🏦';
      } else if (industryKey === 'government') {
        icon = story.title.includes('Smart City') ? '🏙️' : '🏛️';
      } else if (industryKey === 'education') {
        icon = story.title.includes('Learning') ? '📚' : '🎓';
      } else if (industryKey === 'agriculture') {
        icon = story.title.includes('Precision') ? '🌾' : '🚜';
      } else if (industryKey === 'development') {
        icon = story.title.includes('Sustainable') ? '🌱' : '🌍';
      } else if (industryKey === 'enterprise') {
        icon = story.title.includes('Workplace') ? '💼' : '🏢';
      }

      return {
        ...story,
        id: `${industryKey}-${index + 1}`,
        icon: icon
      };
    });
  });

  return (
    <div className="min-h-screen bg-white">
      <Head>
        <title>{t('successStories.title')}</title>
        <meta name="description" content={t('successStories.description')} />
      </Head>

      <GlobalStyles />
      <Header />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-20 lg:py-32 overflow-hidden">
        <div className="container mx-auto px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-8">
              <Award size={16} />
              <span>{t('successStories.hero.badge')}</span>
            </div>

            <h1 className="heading-font text-5xl lg:text-7xl font-bold text-slate-800 mb-8 leading-tight">
              {t('successStories.hero.title')}
              <span className="text-gradient block">{t('successStories.hero.titleHighlight')}</span>
            </h1>

            <p className="text-xl lg:text-2xl text-gray-600 mb-12 leading-relaxed max-w-3xl mx-auto">
              {t('successStories.hero.subtitle')}
            </p>
            {/* Quick stats */}
            <QuickStats />
          </div>
        </div>
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-1/3 h-full opacity-10">
          <div className="w-full h-full bg-gradient-to-l from-blue-200 to-transparent"></div>
        </div>
      </section>

      {/* Industries Showcase */}
      <IndustriesShowcase industryStories={industryStories} />
      {/* Call to Action Section */}
      {/* <CallToAction /> */}
      <Footer />
    </div>
  );
} 