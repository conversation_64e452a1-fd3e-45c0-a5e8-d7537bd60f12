'use client';

import React, { useEffect } from 'react';
import { useTheme, useThemeStyles } from '@/themes/ThemeProvider';

interface LayoutClientProps {
  children: React.ReactNode;
}

export default function LayoutClient({ children }: LayoutClientProps) {
  const { currentTheme, isLoading } = useTheme();
  const { isPaperTheme } = useThemeStyles();

  // Apply theme-specific body classes
  useEffect(() => {
    if (typeof window !== 'undefined' && !isLoading) {
      const body = document.body;
      
      // Remove existing theme classes
      body.classList.remove('theme-current', 'theme-paper-wireframe');
      
      // Add current theme class
      body.classList.add(`theme-${currentTheme}`);
      
      // Apply paper wireframe font to body if needed
      if (isPaperTheme) {
        body.style.fontFamily = "'Comic Sans MS', cursive, sans-serif";
      } else {
        body.style.fontFamily = '';
      }
    }
  }, [currentTheme, isPaperTheme, isLoading]);

  // Show loading state while theme is being initialized
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading theme...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`theme-wrapper ${isPaperTheme ? 'paper-wireframe' : 'current-theme'}`}>
      {children}
    </div>
  );
}
