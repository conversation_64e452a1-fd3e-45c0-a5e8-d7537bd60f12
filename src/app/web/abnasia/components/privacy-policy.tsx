import React from 'react';
import Head from 'next/head';
import { Shield, Lock, Eye, FileText, Mail, Globe } from 'lucide-react';
import InlineChat from "../components/chat/InlineChat";

// TypeScript interfaces
interface Product {
  sku: string;
  name: string;
  image: string | string[];
  categories: string[];
  description?: string;
  price?: number;
}

interface Category {
  name: string;
  image: string | string[];
  itemCount: number;
}

interface PrivacyPolicyProps {
  inventoryData?: Product[];
  categories?: Category[];
}

// Privacy policy data structure
interface PolicySection {
  title: string;
  content: string;
  icon?: React.ReactNode;
  highlight?: boolean;
}

const PrivacyPolicy: React.FC<PrivacyPolicyProps> = ({ 
  inventoryData = [], 
  categories: categoryData = [] 
}) => {
  const hotProductSKUs = [
    'abnasia.org.bot.document.1',
    'abnasia.org.ai.chatbot.1', 
    'abnasia.org.bot.rfp.1', 
    'abnasia.org.bot.bookwriter.1'
  ];
  
  const hotProducts = inventoryData.filter(product => 
    hotProductSKUs.includes(product.sku)
  );

  const getImageSrc = (product: Product): string => {
    if (Array.isArray(product.image) && product.image.length > 0) {
      return product.image[0];
    }
    if (typeof product.image === 'string') {
      return product.image;
    }
    return 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abn/abn.logo.griffin.withurl.png';
  };

  const policySections: PolicySection[] = [
    {
      title: "ABN Asia Ltd.",
      content: "ABN Asia Ltd. is a software company, dedicated to providing high-quality software services.",
      icon: <Globe className="w-5 h-5 text-blue-600" />
    },
    {
      title: "We care about your privacy",
      content: "We take the private nature of your personal information seriously, and are committed to protecting it. To do that, we've set up procedures to ensure that your information is handled responsibly and in accordance with applicable data protection and privacy laws. We're grateful for your trust, and we'll act that way.",
      icon: <Shield className="w-5 h-5 text-green-600" />,
      highlight: true
    },
    {
      title: "Info we collect",
      content: "This privacy policy describes what information we collect when you visit our website and our channels including chatbots, how we use that information, and what choices we offer you to access, update, and control it. This version of the policy is effective as of November 01, 2017.",
      icon: <Eye className="w-5 h-5 text-purple-600" />
    },
    {
      title: "Personal Information",
      content: "\"Personal information\" is any information that we could use to identify an individual. It does not include personal information that is encoded or anonymized, or publicly available information that has not been combined with non-public information.",
      icon: <Lock className="w-5 h-5 text-red-600" />
    },
    {
      title: "Sensitive Information",
      content: "\"Sensitive personal information\" is information that meets the \"personal information\" criteria and also reveals race, ethnic origin, political opinions, religious or philosophical beliefs, trade union membership, or concerns health or sex life, information about social security benefits, or information on criminal or administrative proceedings.",
      icon: <FileText className="w-5 h-5 text-orange-600" />
    }
  ];

  return (
    <>
      <Head>
        <title>Privacy Policy - ABN Asia | Your Data Protection Rights</title>
        <meta name="description" content="Learn how ABN Asia protects your privacy and handles your personal information. Comprehensive privacy policy and data protection guidelines." />
        <meta property="og:title" content="Privacy Policy - ABN Asia" key="title" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Hero Section */}
        <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-indigo-600">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-6">
                <Shield className="w-4 h-4 mr-2" />
                ABNASIA PRIVACY POLICY
              </div>
              <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight mb-6">
                Your Privacy Matters
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
                Transparent data protection and privacy practices you can trust
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Quick Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Shield className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="ml-3 text-lg font-semibold text-gray-900">Data Protection</h3>
              </div>
              <p className="text-gray-600">We implement robust security measures to protect your personal information.</p>
            </div>
            
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <Eye className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="ml-3 text-lg font-semibold text-gray-900">Transparency</h3>
              </div>
              <p className="text-gray-600">Clear information about what data we collect and how we use it.</p>
            </div>
            
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Lock className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="ml-3 text-lg font-semibold text-gray-900">Your Control</h3>
              </div>
              <p className="text-gray-600">You have full control over your data with options to access, update, or delete.</p>
            </div>
          </div>

          {/* Policy Sections */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-16">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6">
              <h2 className="text-2xl font-bold text-white">Privacy Policy Details</h2>
              <p className="text-blue-100 mt-2">Comprehensive information about our data practices</p>
            </div>
            
            <div className="divide-y divide-gray-200">
              {policySections.map((section, index) => (
                <div 
                  key={index} 
                  className={`p-8 hover:bg-gray-50 transition-colors duration-200 ${
                    section.highlight ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    {section.icon && (
                      <div className="flex-shrink-0 mt-1">
                        {section.icon}
                      </div>
                    )}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">
                        {section.title}
                      </h3>
                      <p className="text-gray-700 leading-relaxed">
                        {section.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Detailed Policy Table */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-16">
            <div className="bg-gradient-to-r from-gray-800 to-gray-900 px-8 py-6">
              <h2 className="text-2xl font-bold text-white">Detailed Privacy Information</h2>
              <p className="text-gray-300 mt-2">Complete breakdown of our privacy practices</p>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <tbody className="divide-y divide-gray-200">
                  <tr className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-8 py-6 align-top font-semibold text-gray-900 w-1/3 bg-gray-50">
                      Browser Information Collection
                  </td>
                    <td className="px-8 py-6 text-gray-700 leading-relaxed">
                      Your web browser tells us stuff like your approximate location and how you use the site. Information we get from your use of the network is primarily non-personally-identifying information of the sort that web browsers, servers, and services like Google Analytics typically make available, such as the browser type, language preference, referring site, and the time of each visit.
                  </td>
                </tr>
                  
                  <tr className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-8 py-6 align-top font-semibold text-gray-900 w-1/3 bg-gray-50">
                      IP Address Handling
                  </td>
                    <td className="px-8 py-6 text-gray-700 leading-relaxed">
                      Your browser also gives us your IP address, which could identify you, but we don't use it to do that. When you use the network, we also collect potentially personally identifying information in the form of Internet Protocol (IP) addresses. But we don't use that information to identify you.
                  </td>
                </tr>
                  
                  <tr className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-8 py-6 align-top font-semibold text-gray-900 w-1/3 bg-gray-50">
                      Account Registration
                  </td>
                    <td className="px-8 py-6 text-gray-700 leading-relaxed">
                      If you register an account, you may need to give us your email and other personal info. Certain visitors to the network choose to interact with it in ways that may require them to provide us with personally identifying information. We ask visitors who sign up for AbnAsia.org to provide a username and email address.
                  </td>
                </tr>
                  
                  <tr className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-8 py-6 align-top font-semibold text-gray-900 w-1/3 bg-gray-50">
                      Cookie Policy
                  </td>
                    <td className="px-8 py-6 text-gray-700 leading-relaxed">
                      If you don't like cookies or have other dietary restrictions, please tell your browser. A cookie is a string of information that a website stores on a visitor's computer. We use cookies to help us track visitors' use of the network and their preferences. You can set your browser to refuse cookies.
                  </td>
                </tr>
                  
                  <tr className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-8 py-6 align-top font-semibold text-gray-900 w-1/3 bg-gray-50">
                      Data Security
                  </td>
                    <td className="px-8 py-6 text-gray-700 leading-relaxed">
                      We promise to work hard to keep your info safe, and to restrict who can access it. All records containing personal or financial information are considered to be our property and are afforded confidential treatment at all times. We work hard to protect against unauthorized access, use, alteration or destruction of personal or financial information.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

          {/* Contact Section */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white mb-16">
            <div className="text-center">
              <Mail className="w-12 h-12 mx-auto mb-4 text-blue-200" />
              <h2 className="text-2xl font-bold mb-4">Questions About Privacy?</h2>
              <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                If you have any questions about this policy or our site in general, we're here to help.
              </p>
              <div className="space-y-2">
                <p className="text-blue-100">
                  <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-white hover:underline"><EMAIL></a>
                </p>
                <p className="text-blue-100">
                  <strong>Address:</strong> ABN Asia Ltd. Capital Tower, 21st Floor, 109 Tran Hung Dao Street, Hanoi, Vietnam
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Chat Section */}
        <div className="bg-white border-t border-gray-200">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <InlineChat
          title="ABNAsia.org Chat Assistant"
          initialMessage="We can help with any tech and business problem. Can we work with you?"
        />
        </div>
        </div>
      </div>
    </>
  );
};

export async function getStaticProps() {
  // Mock data for now - replace with actual data fetching
  const inventory: Product[] = [];
  
  const inventoryCategorized: Category[] = [];

  return {
    props: {
      inventoryData: inventory,
      categories: inventoryCategorized
    }
  };
}

export default PrivacyPolicy;
