class GoalsService {
  constructor() {
    this.baseUrl = '/api';
    this.dataPath = '/data/apps/web/abnasia/chats/admin';
    this.goalsFile = `${this.dataPath}/goals.json`;
    this.assignmentsFile = `${this.dataPath}/goal-assignments.json`;
    this.visitorsFile = `${this.dataPath}/visitors.json`;
  }

  // Goal Management
  async createGoal(goalData) {
    try {
      const response = await fetch(`${this.baseUrl}/goals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...goalData,
          id: this.generateId(),
          createdAt: new Date().toISOString(),
          isActive: true
        })
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to create goal:', error);
      // Fallback to localStorage for demo
      return this.createGoalLocal(goalData);
    }
  }

  async getGoals() {
    try {
      // Try to fetch from JSON file first
      const response = await fetch(this.goalsFile);
      if (response.ok) {
        const data = await response.json();
        return data.goals || [];
      }
    } catch (error) {
      console.warn('Failed to fetch goals from JSON file:', error);
    }
    
    // Fallback to localStorage
    return this.getGoalsLocal();
  }

  async updateGoal(goalId, updates) {
    try {
      const response = await fetch(`${this.baseUrl}/goals/${goalId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates)
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to update goal:', error);
      return this.updateGoalLocal(goalId, updates);
    }
  }

  async deleteGoal(goalId) {
    try {
      const response = await fetch(`${this.baseUrl}/goals/${goalId}`, {
        method: 'DELETE'
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to delete goal:', error);
      return this.deleteGoalLocal(goalId);
    }
  }

  // Goal Assignment Management
  async assignGoalToUser(userId, goalId, aggressivenessLevel, assignedBy = 'system') {
    const assignment = {
      id: this.generateId(),
      userId,
      goalId,
      aggressivenessLevel,
      assignedAt: new Date().toISOString(),
      assignedBy,
      status: 'active',
      progress: {
        interactions: 0,
        positiveResponses: 0,
        goalAchieved: false,
        notes: [],
        lastInteraction: null
      }
    };

    try {
      const response = await fetch(`${this.baseUrl}/goal-assignments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assignment)
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to assign goal:', error);
      return this.assignGoalLocal(assignment);
    }
  }

  async getUserGoalAssignments(userId) {
    try {
      const response = await fetch(`${this.baseUrl}/goal-assignments/user/${userId}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch user goal assignments:', error);
      return this.getUserGoalAssignmentsLocal(userId);
    }
  }

  async getAllGoalAssignments() {
    try {
      // Try to fetch from JSON file first
      const response = await fetch(this.assignmentsFile);
      if (response.ok) {
        const data = await response.json();
        return data.assignments || [];
      }
    } catch (error) {
      console.warn('Failed to fetch assignments from JSON file:', error);
    }
    
    // Fallback to localStorage
    return this.getAllGoalAssignmentsLocal();
  }

  async updateGoalAssignment(assignmentId, updates) {
    try {
      const response = await fetch(`${this.baseUrl}/goal-assignments/${assignmentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates)
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to update goal assignment:', error);
      return this.updateGoalAssignmentLocal(assignmentId, updates);
    }
  }

  async trackGoalProgress(userId, interactionData) {
    const assignments = await this.getUserGoalAssignments(userId);
    const activeAssignments = assignments.filter(a => a.status === 'active');

    for (const assignment of activeAssignments) {
      const updatedProgress = {
        ...assignment.progress,
        interactions: assignment.progress.interactions + 1,
        lastInteraction: new Date().toISOString()
      };

      // Analyze interaction for goal progress
      if (interactionData.sentiment === 'positive' || interactionData.goalIndicators?.length > 0) {
        updatedProgress.positiveResponses += 1;
        if (interactionData.notes) {
          updatedProgress.notes.push({
            timestamp: new Date().toISOString(),
            note: interactionData.notes,
            type: 'progress'
          });
        }
      }

      // Check if goal is achieved based on criteria
      if (this.checkGoalAchievement(assignment, updatedProgress, interactionData)) {
        updatedProgress.goalAchieved = true;
        await this.updateGoalAssignment(assignment.id, {
          status: 'completed',
          progress: updatedProgress
        });
      } else {
        await this.updateGoalAssignment(assignment.id, {
          progress: updatedProgress
        });
      }
    }
  }

  checkGoalAchievement(assignment, progress, interactionData) {
    // Simple achievement criteria - can be enhanced based on goal type
    const goal = this.getGoalById(assignment.goalId);
    if (!goal) return false;

    switch (goal.type) {
      case 'conversion':
        return interactionData.conversionSignals?.length > 0 || 
               interactionData.userMessage?.toLowerCase().includes('buy') ||
               interactionData.userMessage?.toLowerCase().includes('purchase');
      
      case 'support':
        return interactionData.sentiment === 'positive' && progress.positiveResponses >= 3;
      
      case 'retention':
        return progress.interactions >= 5 && progress.positiveResponses >= 3;
      
      case 'engagement':
        return progress.interactions >= 10;
      
      default:
        return progress.positiveResponses >= 2;
    }
  }

  // Generate goal-based system prompt modifications
  async generateGoalPromptModification(userId) {
    const assignments = await this.getUserGoalAssignments(userId);
    const activeAssignments = assignments.filter(a => a.status === 'active');
    
    if (activeAssignments.length === 0) {
      return '';
    }

    let promptModifications = '\n\nIMPORTANT CONVERSATION GOALS:\n';
    
    for (const assignment of activeAssignments) {
      const goal = await this.getGoalById(assignment.goalId);
      if (!goal) continue;

      const aggressivenessConfig = goal.aggressivenessLevels[assignment.aggressivenessLevel];
      
      promptModifications += `\n- GOAL: ${goal.name}\n`;
      promptModifications += `  Description: ${goal.description}\n`;
      promptModifications += `  Target Outcome: ${goal.targetOutcome}\n`;
      promptModifications += `  Approach Level: ${assignment.aggressivenessLevel.toUpperCase()}\n`;
      promptModifications += `  Strategy: ${aggressivenessConfig.promptModifier}\n`;
      
      // Add progress context
      if (assignment.progress.interactions > 0) {
        promptModifications += `  Progress: ${assignment.progress.interactions} interactions, ${assignment.progress.positiveResponses} positive responses\n`;
      }
    }

    promptModifications += `\nRemember: Pursue these goals naturally in conversation while maintaining a helpful and engaging tone. The user should not feel pressured or manipulated.`;
    
    return promptModifications;
  }

  // Default goals templates
  getDefaultGoals() {
    return [
      {
        id: 'goal-conversion-product',
        name: 'Product Conversion',
        description: 'Guide visitor towards purchasing our main product/service',
        type: 'conversion',
        targetOutcome: 'User expresses interest in purchasing or requests product information',
        aggressivenessLevels: {
          subtle: {
            description: 'Gentle mentions and suggestions',
            promptModifier: 'Naturally mention relevant products/services when appropriate. Focus on solving their problems first.'
          },
          moderate: {
            description: 'Clear recommendations with benefits',
            promptModifier: 'Actively recommend suitable products/services. Highlight benefits and value propositions clearly.'
          },
          aggressive: {
            description: 'Direct sales approach with urgency',
            promptModifier: 'Be direct about sales opportunities. Create urgency with limited-time offers or exclusive deals.'
          }
        },
        createdAt: new Date().toISOString(),
        isActive: true
      },
      {
        id: 'goal-customer-support',
        name: 'Customer Satisfaction',
        description: 'Ensure customer feels heard and supported',
        type: 'support',
        targetOutcome: 'Customer expresses satisfaction or positive sentiment',
        aggressivenessLevels: {
          subtle: {
            description: 'Empathetic listening and gentle guidance',
            promptModifier: 'Listen actively and provide gentle, supportive responses. Focus on understanding their concerns.'
          },
          moderate: {
            description: 'Proactive problem-solving approach',
            promptModifier: 'Take initiative in solving their problems. Offer specific solutions and follow-up questions.'
          },
          aggressive: {
            description: 'Comprehensive support with escalation',
            promptModifier: 'Be highly proactive. Offer comprehensive support and suggest escalation to specialists when needed.'
          }
        },
        createdAt: new Date().toISOString(),
        isActive: true
      },
      {
        id: 'goal-lead-generation',
        name: 'Lead Generation',
        description: 'Collect visitor contact information for follow-up',
        type: 'conversion',
        targetOutcome: 'User provides contact information or agrees to follow-up',
        aggressivenessLevels: {
          subtle: {
            description: 'Offer valuable content in exchange for info',
            promptModifier: 'Offer valuable resources, newsletters, or updates in exchange for contact information.'
          },
          moderate: {
            description: 'Direct requests with clear value proposition',
            promptModifier: 'Directly ask for contact information while clearly explaining the benefits they will receive.'
          },
          aggressive: {
            description: 'Persistent follow-up requests',
            promptModifier: 'Be persistent about collecting contact information. Emphasize missed opportunities if they don\'t provide details.'
          }
        },
        createdAt: new Date().toISOString(),
        isActive: true
      },
      {
        id: 'goal-engagement-retention',
        name: 'User Engagement',
        description: 'Keep visitor engaged and encourage return visits',
        type: 'engagement',
        targetOutcome: 'User engages in extended conversation or expresses intent to return',
        aggressivenessLevels: {
          subtle: {
            description: 'Interesting questions and topics',
            promptModifier: 'Ask engaging questions about their interests. Share interesting insights related to their needs.'
          },
          moderate: {
            description: 'Interactive content and recommendations',
            promptModifier: 'Actively suggest relevant content, tools, or resources. Encourage interaction with platform features.'
          },
          aggressive: {
            description: 'Gamification and exclusive content',
            promptModifier: 'Use gamification elements. Offer exclusive content or experiences to encourage return visits.'
          }
        },
        createdAt: new Date().toISOString(),
        isActive: true
      }
    ];
  }

  // Local storage fallback methods
  createGoalLocal(goalData) {
    const goals = this.getGoalsLocal();
    const newGoal = {
      ...goalData,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      isActive: true
    };
    goals.push(newGoal);
    localStorage.setItem('abnasia_goals', JSON.stringify(goals));
    return newGoal;
  }

  getGoalsLocal() {
    const stored = localStorage.getItem('abnasia_goals');
    if (stored) {
      return JSON.parse(stored);
    }
    // Initialize with default goals
    const defaultGoals = this.getDefaultGoals();
    localStorage.setItem('abnasia_goals', JSON.stringify(defaultGoals));
    return defaultGoals;
  }

  updateGoalLocal(goalId, updates) {
    const goals = this.getGoalsLocal();
    const index = goals.findIndex(g => g.id === goalId);
    if (index !== -1) {
      goals[index] = { ...goals[index], ...updates };
      localStorage.setItem('abnasia_goals', JSON.stringify(goals));
      return goals[index];
    }
    return null;
  }

  deleteGoalLocal(goalId) {
    const goals = this.getGoalsLocal();
    const filtered = goals.filter(g => g.id !== goalId);
    localStorage.setItem('abnasia_goals', JSON.stringify(filtered));
    return { success: true };
  }

  assignGoalLocal(assignment) {
    const assignments = this.getAllGoalAssignmentsLocal();
    assignments.push(assignment);
    localStorage.setItem('abnasia_goal_assignments', JSON.stringify(assignments));
    return assignment;
  }

  getUserGoalAssignmentsLocal(userId) {
    const assignments = this.getAllGoalAssignmentsLocal();
    return assignments.filter(a => a.userId === userId);
  }

  getAllGoalAssignmentsLocal() {
    const stored = localStorage.getItem('abnasia_goal_assignments');
    return stored ? JSON.parse(stored) : [];
  }

  updateGoalAssignmentLocal(assignmentId, updates) {
    const assignments = this.getAllGoalAssignmentsLocal();
    const index = assignments.findIndex(a => a.id === assignmentId);
    if (index !== -1) {
      assignments[index] = { ...assignments[index], ...updates };
      localStorage.setItem('abnasia_goal_assignments', JSON.stringify(assignments));
      return assignments[index];
    }
    return null;
  }

  async getGoalById(goalId) {
    const goals = await this.getGoals();
    return goals.find(g => g.id === goalId);
  }

  generateId() {
    return 'goal_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }
}

export default GoalsService; 