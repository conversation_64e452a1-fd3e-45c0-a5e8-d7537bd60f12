import React, { useState, useEffect } from 'react';
import { 
  Target, 
  Plus, 
  Edit3, 
  Trash2, 
  Users, 
  TrendingUp, 
  Settings,
  Save,
  X,
  Eye,
  UserPlus,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import JsonGoalsService from '../../../admin/chat/services/JsonGoalsService';

const GoalsManager = ({ currentUserId = null }) => {
  const [activeTab, setActiveTab] = useState('goals'); // 'goals', 'assignments', 'tracking'
  const [goals, setGoals] = useState([]);
  const [assignments, setAssignments] = useState([]);
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [showAssignmentForm, setShowAssignmentForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState(null);
  const [selectedGoalForAssignment, setSelectedGoalForAssignment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [goalsService] = useState(() => new JsonGoalsService());

  // Form states
  const [goalForm, setGoalForm] = useState({
    name: '',
    description: '',
    type: 'conversion',
    targetOutcome: '',
    aggressivenessLevels: {
      subtle: { description: '', promptModifier: '' },
      moderate: { description: '', promptModifier: '' },
      aggressive: { description: '', promptModifier: '' }
    }
  });

  const [assignmentForm, setAssignmentForm] = useState({
    userId: '',
    goalId: '',
    aggressivenessLevel: 'moderate'
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [goalsData, assignmentsData] = await Promise.all([
        goalsService.getGoals(),
        goalsService.getAssignments()
      ]);
      setGoals(goalsData);
      setAssignments(assignmentsData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateGoal = async () => {
    try {
      const newGoal = await goalsService.createGoal(goalForm);
      setGoals([...goals, newGoal]);
      resetGoalForm();
      setShowGoalForm(false);
    } catch (error) {
      console.error('Failed to create goal:', error);
    }
  };

  const handleUpdateGoal = async () => {
    try {
      const updatedGoal = await goalsService.updateGoal(editingGoal.id, goalForm);
      setGoals(goals.map(g => g.id === editingGoal.id ? updatedGoal : g));
      resetGoalForm();
      setEditingGoal(null);
      setShowGoalForm(false);
    } catch (error) {
      console.error('Failed to update goal:', error);
    }
  };

  const handleDeleteGoal = async (goalId) => {
    if (window.confirm('Are you sure you want to delete this goal?')) {
      try {
        await goalsService.deleteGoal(goalId);
        setGoals(goals.filter(g => g.id !== goalId));
        // Also remove related assignments
        setAssignments(assignments.filter(a => a.goalId !== goalId));
      } catch (error) {
        console.error('Failed to delete goal:', error);
      }
    }
  };

  const handleAssignGoal = async () => {
    try {
      const newAssignment = await goalsService.assignGoalToVisitor(
        assignmentForm.userId,
        assignmentForm.goalId,
        assignmentForm.aggressivenessLevel,
        'admin'
      );
      setAssignments([...assignments, newAssignment]);
      setAssignmentForm({ userId: '', goalId: '', aggressivenessLevel: 'moderate' });
      setShowAssignmentForm(false);
    } catch (error) {
      console.error('Failed to assign goal:', error);
    }
  };

  const handleUpdateAssignmentStatus = async (assignmentId, status) => {
    try {
      const updatedAssignment = await goalsService.updateAssignment(assignmentId, { status });
      setAssignments(assignments.map(a => a.id === assignmentId ? updatedAssignment : a));
    } catch (error) {
      console.error('Failed to update assignment status:', error);
    }
  };

  const resetGoalForm = () => {
    setGoalForm({
      name: '',
      description: '',
      type: 'conversion',
      targetOutcome: '',
      aggressivenessLevels: {
        subtle: { description: '', promptModifier: '' },
        moderate: { description: '', promptModifier: '' },
        aggressive: { description: '', promptModifier: '' }
      }
    });
  };

  const startEditGoal = (goal) => {
    setGoalForm(goal);
    setEditingGoal(goal);
    setShowGoalForm(true);
  };

  const getGoalTypeColor = (type) => {
    const colors = {
      conversion: 'bg-green-100 text-green-800',
      support: 'bg-blue-100 text-blue-800',
      retention: 'bg-purple-100 text-purple-800',
      engagement: 'bg-orange-100 text-orange-800',
      custom: 'bg-gray-100 text-gray-800'
    };
    return colors[type] || colors.custom;
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'text-green-600',
      completed: 'text-blue-600',
      paused: 'text-yellow-600'
    };
    return colors[status] || 'text-gray-600';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <Activity size={16} />;
      case 'completed': return <CheckCircle size={16} />;
      case 'paused': return <Clock size={16} />;
      default: return <AlertTriangle size={16} />;
    }
  };

  const renderGoalsTab = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Goal Templates</h3>
        <button
          onClick={() => setShowGoalForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <Plus size={16} />
          Create Goal
        </button>
      </div>

      <div className="grid gap-4">
        {goals.map((goal) => (
          <div key={goal.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold text-gray-900">{goal.name}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${getGoalTypeColor(goal.type)}`}>
                    {goal.type}
                  </span>
                </div>
                <p className="text-gray-600 text-sm mb-2">{goal.description}</p>
                <p className="text-gray-500 text-xs"><strong>Target:</strong> {goal.targetOutcome}</p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => startEditGoal(goal)}
                  className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded"
                >
                  <Edit3 size={16} />
                </button>
                <button
                  onClick={() => handleDeleteGoal(goal.id)}
                  className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            <div className="border-t pt-3">
              <div className="text-xs text-gray-500 mb-2">Aggressiveness Levels:</div>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="bg-green-50 p-2 rounded">
                  <div className="font-medium text-green-800">Subtle</div>
                  <div className="text-green-600">{goal.aggressivenessLevels.subtle.description}</div>
                </div>
                <div className="bg-yellow-50 p-2 rounded">
                  <div className="font-medium text-yellow-800">Moderate</div>
                  <div className="text-yellow-600">{goal.aggressivenessLevels.moderate.description}</div>
                </div>
                <div className="bg-red-50 p-2 rounded">
                  <div className="font-medium text-red-800">Aggressive</div>
                  <div className="text-red-600">{goal.aggressivenessLevels.aggressive.description}</div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAssignmentsTab = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Goal Assignments</h3>
        <button
          onClick={() => setShowAssignmentForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          <UserPlus size={16} />
          Assign Goal
        </button>
      </div>

      <div className="grid gap-4">
        {assignments.map((assignment) => {
          const goal = goals.find(g => g.id === assignment.goalId);
          if (!goal) return null;

          return (
            <div key={assignment.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-semibold text-gray-900">{goal.name}</h4>
                    <span className={`flex items-center gap-1 ${getStatusColor(assignment.status)}`}>
                      {getStatusIcon(assignment.status)}
                      <span className="text-sm capitalize">{assignment.status}</span>
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div><strong>User:</strong> {assignment.userId}</div>
                    <div><strong>Level:</strong> 
                      <span className={`ml-1 px-2 py-0.5 rounded text-xs ${
                        assignment.aggressivenessLevel === 'subtle' ? 'bg-green-100 text-green-700' :
                        assignment.aggressivenessLevel === 'moderate' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {assignment.aggressivenessLevel}
                      </span>
                    </div>
                    <div><strong>Assigned:</strong> {new Date(assignment.assignedAt).toLocaleDateString()}</div>
                  </div>
                </div>
                <div className="flex gap-2">
                  {assignment.status === 'active' && (
                    <button
                      onClick={() => handleUpdateAssignmentStatus(assignment.id, 'paused')}
                      className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                    >
                      Pause
                    </button>
                  )}
                  {assignment.status === 'paused' && (
                    <button
                      onClick={() => handleUpdateAssignmentStatus(assignment.id, 'active')}
                      className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                    >
                      Resume
                    </button>
                  )}
                  <button
                    onClick={() => handleUpdateAssignmentStatus(assignment.id, 'completed')}
                    className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                  >
                    Complete
                  </button>
                </div>
              </div>

              {/* Progress tracking */}
              <div className="border-t pt-3">
                <div className="text-xs text-gray-500 mb-2">Progress:</div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">{assignment.progress.interactions}</div>
                    <div className="text-gray-500 text-xs">Interactions</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-green-600">{assignment.progress.positiveResponses}</div>
                    <div className="text-gray-500 text-xs">Positive</div>
                  </div>
                  <div className="text-center">
                    <div className={`font-semibold ${assignment.progress.goalAchieved ? 'text-green-600' : 'text-gray-400'}`}>
                      {assignment.progress.goalAchieved ? 'Yes' : 'No'}
                    </div>
                    <div className="text-gray-500 text-xs">Achieved</div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderTrackingTab = () => {
    const completedGoals = assignments.filter(a => a.status === 'completed').length;
    const activeGoals = assignments.filter(a => a.status === 'active').length;
    const totalInteractions = assignments.reduce((sum, a) => sum + a.progress.interactions, 0);
    const totalPositiveResponses = assignments.reduce((sum, a) => sum + a.progress.positiveResponses, 0);

    return (
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">Goal Tracking Overview</h3>
        
        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{goals.length}</div>
            <div className="text-sm text-gray-600">Total Goals</div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{activeGoals}</div>
            <div className="text-sm text-gray-600">Active Assignments</div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{completedGoals}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {totalInteractions > 0 ? Math.round((totalPositiveResponses / totalInteractions) * 100) : 0}%
            </div>
            <div className="text-sm text-gray-600">Success Rate</div>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h4 className="font-semibold mb-4">Recent Goal Activities</h4>
          <div className="space-y-3">
            {assignments
              .sort((a, b) => new Date(b.assignedAt) - new Date(a.assignedAt))
              .slice(0, 5)
              .map((assignment) => {
                const goal = goals.find(g => g.id === assignment.goalId);
                return (
                  <div key={assignment.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        assignment.status === 'active' ? 'bg-green-400' :
                        assignment.status === 'completed' ? 'bg-blue-400' : 'bg-gray-400'
                      }`} />
                      <div>
                        <div className="text-sm font-medium">{goal?.name || 'Unknown Goal'}</div>
                        <div className="text-xs text-gray-500">
                          Assigned to {assignment.userId} • {assignment.aggressivenessLevel} level
                        </div>
                      </div>
                    </div>
                    <div className="text-right text-xs text-gray-500">
                      <div>{assignment.progress.interactions} interactions</div>
                      <div>{new Date(assignment.assignedAt).toLocaleDateString()}</div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Goals Management System</h2>
          <p className="text-gray-600">Create, assign, and track conversation goals for personalized user interactions.</p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-200 p-1 rounded-lg w-fit">
          <button
            onClick={() => setActiveTab('goals')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'goals'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Target size={16} className="inline mr-2" />
            Goals
          </button>
          <button
            onClick={() => setActiveTab('assignments')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'assignments'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Users size={16} className="inline mr-2" />
            Assignments
          </button>
          <button
            onClick={() => setActiveTab('tracking')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'tracking'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <TrendingUp size={16} className="inline mr-2" />
            Tracking
          </button>
        </div>

        {/* Tab Content */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {activeTab === 'goals' && renderGoalsTab()}
            {activeTab === 'assignments' && renderAssignmentsTab()}
            {activeTab === 'tracking' && renderTrackingTab()}
          </>
        )}

        {/* Goal Form Modal */}
        {showGoalForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-lg font-semibold">
                  {editingGoal ? 'Edit Goal' : 'Create New Goal'}
                </h3>
                <button
                  onClick={() => {
                    setShowGoalForm(false);
                    setEditingGoal(null);
                    resetGoalForm();
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Goal Name</label>
                  <input
                    type="text"
                    value={goalForm.name}
                    onChange={(e) => setGoalForm({ ...goalForm, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Product Conversion"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    value={goalForm.description}
                    onChange={(e) => setGoalForm({ ...goalForm, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                    placeholder="Describe the goal's purpose"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Goal Type</label>
                  <select
                    value={goalForm.type}
                    onChange={(e) => setGoalForm({ ...goalForm, type: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="conversion">Conversion</option>
                    <option value="support">Support</option>
                    <option value="retention">Retention</option>
                    <option value="engagement">Engagement</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Target Outcome</label>
                  <input
                    type="text"
                    value={goalForm.targetOutcome}
                    onChange={(e) => setGoalForm({ ...goalForm, targetOutcome: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="What should the user do or feel?"
                  />
                </div>

                {/* Aggressiveness Levels */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Aggressiveness Levels</h4>
                  
                  {['subtle', 'moderate', 'aggressive'].map((level) => (
                    <div key={level} className="border border-gray-200 rounded-lg p-4">
                      <h5 className="font-medium text-gray-800 capitalize mb-2">{level}</h5>
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={goalForm.aggressivenessLevels[level].description}
                          onChange={(e) => setGoalForm({
                            ...goalForm,
                            aggressivenessLevels: {
                              ...goalForm.aggressivenessLevels,
                              [level]: {
                                ...goalForm.aggressivenessLevels[level],
                                description: e.target.value
                              }
                            }
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder={`${level} approach description`}
                        />
                        <textarea
                          value={goalForm.aggressivenessLevels[level].promptModifier}
                          onChange={(e) => setGoalForm({
                            ...goalForm,
                            aggressivenessLevels: {
                              ...goalForm.aggressivenessLevels,
                              [level]: {
                                ...goalForm.aggressivenessLevels[level],
                                promptModifier: e.target.value
                              }
                            }
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          rows={2}
                          placeholder={`Prompt instructions for ${level} approach`}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end gap-3 p-6 border-t">
                <button
                  onClick={() => {
                    setShowGoalForm(false);
                    setEditingGoal(null);
                    resetGoalForm();
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={editingGoal ? handleUpdateGoal : handleCreateGoal}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
                >
                  <Save size={16} />
                  {editingGoal ? 'Update Goal' : 'Create Goal'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Assignment Form Modal */}
        {showAssignmentForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-lg font-semibold">Assign Goal to User</h3>
                <button
                  onClick={() => setShowAssignmentForm(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">User ID</label>
                  <input
                    type="text"
                    value={assignmentForm.userId}
                    onChange={(e) => setAssignmentForm({ ...assignmentForm, userId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter user ID or email"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Goal</label>
                  <select
                    value={assignmentForm.goalId}
                    onChange={(e) => setAssignmentForm({ ...assignmentForm, goalId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a goal</option>
                    {goals.filter(g => g.isActive).map((goal) => (
                      <option key={goal.id} value={goal.id}>{goal.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Aggressiveness Level</label>
                  <select
                    value={assignmentForm.aggressivenessLevel}
                    onChange={(e) => setAssignmentForm({ ...assignmentForm, aggressivenessLevel: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="subtle">Subtle</option>
                    <option value="moderate">Moderate</option>
                    <option value="aggressive">Aggressive</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end gap-3 p-6 border-t">
                <button
                  onClick={() => setShowAssignmentForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAssignGoal}
                  disabled={!assignmentForm.userId || !assignmentForm.goalId}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 flex items-center gap-2"
                >
                  <UserPlus size={16} />
                  Assign Goal
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GoalsManager; 