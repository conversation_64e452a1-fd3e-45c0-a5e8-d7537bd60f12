import React, { useState, useEffect } from 'react';
import { Target, TrendingUp, Activity } from 'lucide-react';
import JsonGoalsService from '../../../admin/chat/services/JsonGoalsService';

const GoalIndicator = ({ userId, className = '' }) => {
  const [activeGoals, setActiveGoals] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [goalsService] = useState(() => new JsonGoalsService());

  useEffect(() => {
    if (userId) {
      loadActiveGoals();
    } else {
      setActiveGoals([]);
    }
  }, [userId]);

  const loadActiveGoals = async () => {
    setIsLoading(true);
    try {
      // Add safety check and error handling for JSON parsing issues
      const assignments = await goalsService.getVisitorAssignments(userId);
      
      // Ensure assignments is an array
      if (!Array.isArray(assignments)) {
        console.warn('GoalIndicator: assignments is not an array, using empty array');
        setActiveGoals([]);
        return;
      }
      
      const activeAssignments = assignments.filter(a => a.status === 'active');
      
      const goalsWithDetails = await Promise.all(
        activeAssignments.map(async (assignment) => {
          try {
            const goal = await goalsService.getGoalById(assignment.goalId);
            return {
              ...assignment,
              goal
            };
          } catch (goalError) {
            console.warn('GoalIndicator: Failed to load goal details for assignment:', assignment.id, goalError);
            return null;
          }
        })
      );
      
      setActiveGoals(goalsWithDetails.filter(g => g && g.goal));
    } catch (error) {
      console.error('Failed to load active goals:', error);
      // Set empty goals on error to prevent component crash
      setActiveGoals([]);
    } finally {
      setIsLoading(false);
    }
  };

  if (!userId || activeGoals.length === 0) {
    return null;
  }

  const getAggressivenessColor = (level) => {
    switch (level) {
      case 'subtle': return 'text-green-600 bg-green-50';
      case 'moderate': return 'text-yellow-600 bg-yellow-50';
      case 'aggressive': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getGoalTypeIcon = (type) => {
    switch (type) {
      case 'conversion': return '💰';
      case 'support': return '🤝';
      case 'retention': return '🔄';
      case 'engagement': return '🎯';
      default: return '📋';
    }
  };

  return (
    <div className={`border-l-4 border-indigo-400 bg-indigo-50/50 p-2 ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <Target size={14} className="text-indigo-600" />
        <span className="text-sm font-medium text-indigo-800">
          Active Goals ({activeGoals.length})
        </span>
      </div>
      
      {isLoading ? (
        <div className="text-xs text-gray-500">Loading goals...</div>
      ) : (
        <div className="space-y-2">
          {activeGoals.map((goalAssignment) => (
            <div key={goalAssignment.id} className="bg-white/80 rounded p-2 text-xs">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-1">
                  <span>{getGoalTypeIcon(goalAssignment.goal.type)}</span>
                  <span className="font-medium text-gray-800">
                    {goalAssignment.goal.name}
                  </span>
                </div>
                <span className={`px-1.5 py-0.5 rounded text-xs ${getAggressivenessColor(goalAssignment.aggressivenessLevel)}`}>
                  {goalAssignment.aggressivenessLevel}
                </span>
              </div>
              
              <div className="text-gray-600 mb-1">
                {goalAssignment.goal.description}
              </div>
              
              {goalAssignment.progress.interactions > 0 && (
                <div className="flex items-center gap-3 text-gray-500">
                  <div className="flex items-center gap-1">
                    <Activity size={10} />
                    <span>{goalAssignment.progress.interactions} interactions</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TrendingUp size={10} />
                    <span>{goalAssignment.progress.positiveResponses} positive</span>
                  </div>
                  {goalAssignment.progress.goalAchieved && (
                    <span className="text-green-600 font-medium">✓ Achieved</span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default GoalIndicator; 