import React, { useState, useEffect } from 'react';
import { ChatCore } from '../ChatCore';
import JsonGoalsService from '../../../admin/chat/services/JsonGoalsService';
import { 
  Play, 
  Users, 
  Target, 
  TrendingUp, 
  Settings, 
  Info,
  User,
  Bot,
  Check,
  UserPlus
} from 'lucide-react';

const GoalsDemo = () => {
  const [demoStep, setDemoStep] = useState(0);
  const [selectedUser, setSelectedUser] = useState('demo-user-1');
  const [selectedGoal, setSelectedGoal] = useState('');
  const [aggressivenessLevel, setAggressivenessLevel] = useState('moderate');
  const [goals, setGoals] = useState([]);
  const [assignments, setAssignments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [goalsService] = useState(() => new JsonGoalsService());

  const demoUsers = [
    { id: 'demo-user-1', name: '<PERSON>', type: 'Potential Customer' },
    { id: 'demo-user-2', name: '<PERSON>', type: 'Support Seeker' },
    { id: 'demo-user-3', name: 'Carol Davis', type: 'Existing Customer' }
  ];

  const demoSteps = [
    {
      title: 'Welcome to Goals Demo',
      description: 'This demo shows how the Goals Management System works to guide conversations toward specific objectives.',
      action: 'Start Demo'
    },
    {
      title: 'Available Goals',
      description: 'First, let\'s look at the available goal templates that come with the system.',
      action: 'View Goals'
    },
    {
      title: 'Assign Goal to User',
      description: 'Now we\'ll assign a goal to a demo user and set the aggressiveness level.',
      action: 'Assign Goal'
    },
    {
      title: 'Experience Goal-Driven Chat',
      description: 'Chat with the AI as the demo user to see how goals influence the conversation.',
      action: 'Start Chat'
    },
    {
      title: 'Track Progress',
      description: 'Monitor how the system tracks progress toward the assigned goal.',
      action: 'View Progress'
    }
  ];

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      const goalsData = await goalsService.getGoals();
      setGoals(goalsData);
      if (goalsData.length > 0) {
        setSelectedGoal(goalsData[0].id);
      }
    } catch (error) {
      console.error('Failed to load goals:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssignGoal = async () => {
    if (!selectedGoal || !selectedUser) return;
    
    try {
      const assignment = await goalsService.assignGoalToVisitor(
        selectedUser,
        selectedGoal,
        aggressivenessLevel,
        'demo-admin'
      );
      setAssignments([...assignments, assignment]);
      setDemoStep(3);
    } catch (error) {
      console.error('Failed to assign goal:', error);
    }
  };

  const nextStep = () => {
    if (demoStep < demoSteps.length - 1) {
      setDemoStep(demoStep + 1);
    }
  };

  const prevStep = () => {
    if (demoStep > 0) {
      setDemoStep(demoStep - 1);
    }
  };

  const resetDemo = () => {
    setDemoStep(0);
    setAssignments([]);
  };

  const renderStepContent = () => {
    switch (demoStep) {
      case 0:
        return (
          <div className="text-center space-y-6">
            <div className="mx-auto w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center">
              <Target size={48} className="text-blue-600" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Goals Management System Demo</h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Experience how AI conversations can be guided toward specific objectives while maintaining 
                natural, helpful interactions. This system enables personalized chat experiences that 
                adapt based on user profiles and business goals.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto text-sm">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="font-medium text-blue-900 mb-2">🎯 Smart Targeting</div>
                <div className="text-blue-700">Assign specific goals to individual users</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="font-medium text-green-900 mb-2">📊 Progress Tracking</div>
                <div className="text-green-700">Monitor goal achievement and user satisfaction</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="font-medium text-purple-900 mb-2">🤖 Natural Integration</div>
                <div className="text-purple-700">AI adapts behavior without disrupting conversation flow</div>
              </div>
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Available Goal Templates</h3>
              <p className="text-gray-600">These pre-configured goals can be assigned to users with different aggressiveness levels.</p>
            </div>
            <div className="grid gap-4">
              {goals.map((goal) => (
                <div key={goal.id} className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-semibold text-gray-900">{goal.name}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          goal.type === 'conversion' ? 'bg-green-100 text-green-800' :
                          goal.type === 'support' ? 'bg-blue-100 text-blue-800' :
                          goal.type === 'retention' ? 'bg-purple-100 text-purple-800' :
                          goal.type === 'engagement' ? 'bg-orange-100 text-orange-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {goal.type}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm mb-2">{goal.description}</p>
                      <p className="text-gray-500 text-xs"><strong>Target:</strong> {goal.targetOutcome}</p>
                    </div>
                  </div>
                  <div className="border-t pt-3">
                    <div className="text-xs text-gray-500 mb-2">Aggressiveness Approaches:</div>
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="bg-green-50 p-2 rounded">
                        <div className="font-medium text-green-800">Subtle</div>
                        <div className="text-green-600">{goal.aggressivenessLevels.subtle.description}</div>
                      </div>
                      <div className="bg-yellow-50 p-2 rounded">
                        <div className="font-medium text-yellow-800">Moderate</div>
                        <div className="text-yellow-600">{goal.aggressivenessLevels.moderate.description}</div>
                      </div>
                      <div className="bg-red-50 p-2 rounded">
                        <div className="font-medium text-red-800">Aggressive</div>
                        <div className="text-red-600">{goal.aggressivenessLevels.aggressive.description}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Assign Goal to User</h3>
              <p className="text-gray-600">Select a demo user, goal, and aggressiveness level to see the system in action.</p>
            </div>
            
            <div className="max-w-md mx-auto space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Demo User</label>
                <select
                  value={selectedUser}
                  onChange={(e) => setSelectedUser(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {demoUsers.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.name} ({user.type})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Goal</label>
                <select
                  value={selectedGoal}
                  onChange={(e) => setSelectedGoal(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {goals.map((goal) => (
                    <option key={goal.id} value={goal.id}>
                      {goal.name} ({goal.type})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Aggressiveness Level</label>
                <select
                  value={aggressivenessLevel}
                  onChange={(e) => setAggressivenessLevel(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="subtle">Subtle - Natural and gentle approach</option>
                  <option value="moderate">Moderate - Clear and direct guidance</option>
                  <option value="aggressive">Aggressive - Proactive and persistent</option>
                </select>
              </div>

              {selectedGoal && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm">
                    <div className="font-medium text-blue-900 mb-2">Preview:</div>
                    <div className="text-blue-800">
                      The AI will pursue "{goals.find(g => g.id === selectedGoal)?.name}" using a{' '}
                      <span className="font-medium">{aggressivenessLevel}</span> approach for user{' '}
                      <span className="font-medium">{demoUsers.find(u => u.id === selectedUser)?.name}</span>.
                    </div>
                  </div>
                </div>
              )}

              <button
                onClick={handleAssignGoal}
                disabled={!selectedGoal || !selectedUser}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 flex items-center justify-center gap-2"
              >
                <UserPlus size={16} />
                Assign Goal
              </button>
            </div>
          </div>
        );

      case 3:
        const currentUser = demoUsers.find(u => u.id === selectedUser);
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Goal-Driven Chat Experience</h3>
              <p className="text-gray-600">
                Chat as <strong>{currentUser?.name}</strong> and notice how the AI adapts its responses 
                based on the assigned goal and aggressiveness level.
              </p>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Info size={20} className="text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <div className="font-medium text-yellow-800 mb-1">Active Goal Configuration:</div>
                  <div className="text-yellow-700">
                    <div>User: {currentUser?.name} ({currentUser?.type})</div>
                    <div>Goal: {goals.find(g => g.id === selectedGoal)?.name}</div>
                    <div>Level: {aggressivenessLevel}</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <ChatCore
                initialMessage={`Hello ${currentUser?.name}! I'm AbnAsia. I'm here to help you with any questions or needs you might have. How can I assist you today?`}
                containerClassName="bg-white"
                className="h-80"
                selectedDocuments={new Set()}
                availableDocuments={[]}
                onUserIdChange={() => {}}
                onAvatarChange={() => {}}
                initialAvatar={null}
              />
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-sm">
                <div className="font-medium text-blue-900 mb-2">Try these conversation starters:</div>
                <div className="space-y-1 text-blue-800">
                  {selectedGoal === 'goal-conversion-product' && (
                    <>
                      <div>• "I'm looking for a solution to help my business"</div>
                      <div>• "What services do you offer?"</div>
                      <div>• "How much does your product cost?"</div>
                    </>
                  )}
                  {selectedGoal === 'goal-customer-support' && (
                    <>
                      <div>• "I'm having trouble with something"</div>
                      <div>• "I need help understanding how this works"</div>
                      <div>• "I'm frustrated with my current situation"</div>
                    </>
                  )}
                  {selectedGoal === 'goal-lead-generation' && (
                    <>
                      <div>• "I'd like to learn more about your company"</div>
                      <div>• "Can someone contact me with more information?"</div>
                      <div>• "Do you have any resources I can download?"</div>
                    </>
                  )}
                  {selectedGoal === 'goal-engagement-retention' && (
                    <>
                      <div>• "What's new on your platform?"</div>
                      <div>• "I'm looking for interesting content"</div>
                      <div>• "What else can I explore here?"</div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-2">Progress Tracking</h3>
              <p className="text-gray-600">See how the system monitors goal achievement and conversation effectiveness.</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">5</div>
                <div className="text-sm text-gray-600">Total Interactions</div>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-600">3</div>
                <div className="text-sm text-gray-600">Positive Responses</div>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">60%</div>
                <div className="text-sm text-gray-600">Goal Progress</div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold mb-4">Detected Signals</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Check size={16} className="text-green-600" />
                  <div className="text-sm">
                    <div className="font-medium text-green-800">Pricing Inquiry Detected</div>
                    <div className="text-green-600">User asked about costs - strong conversion signal</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <TrendingUp size={16} className="text-blue-600" />
                  <div className="text-sm">
                    <div className="font-medium text-blue-800">Positive Sentiment</div>
                    <div className="text-blue-600">User expressed satisfaction with responses</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                  <Target size={16} className="text-orange-600" />
                  <div className="text-sm">
                    <div className="font-medium text-orange-800">Goal Achievement</div>
                    <div className="text-orange-600">User showed interest in contact/demo</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Check size={20} className="text-green-600" />
                <div className="font-medium text-green-800">Demo Completed Successfully!</div>
              </div>
              <p className="text-green-700 text-sm">
                You've experienced how the Goals Management System can guide conversations 
                toward specific objectives while maintaining natural interactions.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-900">Goals System Demo</h2>
            <div className="text-sm text-gray-500">
              Step {demoStep + 1} of {demoSteps.length}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {demoSteps.map((_, index) => (
              <div
                key={index}
                className={`h-2 flex-1 rounded ${
                  index <= demoStep ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              />
            ))}
          </div>
          
          <div className="mt-4 text-center">
            <h3 className="text-lg font-semibold text-gray-800">{demoSteps[demoStep].title}</h3>
            <p className="text-gray-600">{demoSteps[demoStep].description}</p>
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            renderStepContent()
          )}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={prevStep}
            disabled={demoStep === 0}
            className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <div className="flex gap-3">
            <button
              onClick={resetDemo}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Reset Demo
            </button>
            
            {demoStep < demoSteps.length - 1 ? (
              <button
                onClick={demoStep === 2 ? handleAssignGoal : nextStep}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
              >
                {demoSteps[demoStep].action}
                <Play size={16} />
              </button>
            ) : (
              <button
                onClick={resetDemo}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-2"
              >
                Start Over
                <Play size={16} />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GoalsDemo; 