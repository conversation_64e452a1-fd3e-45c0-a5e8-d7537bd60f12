import React, { useState, useEffect } from 'react';
import { Play, Clock, MessageCircle, Target, Users, Zap, CheckCircle, Timer } from 'lucide-react';
import JsonGoalsService from '../../admin/chat/services/JsonGoalsService';

const ProactiveMessagingDemo = () => {
  const [isActive, setIsActive] = useState(false);
  const [demoMessages, setDemoMessages] = useState([]);
  const [triggeredScenarios, setTriggeredScenarios] = useState([]);
  const [simulationTime, setSimulationTime] = useState(0);
  const [visitorProfile, setVisitorProfile] = useState({
    id: 'demo_visitor_001',
    signals: [],
    businessContext: {},
    interactions: 0
  });

  const goalsService = React.useMemo(() => new JsonGoalsService(), []);

  const demoScenarios = [
    {
      id: 'pricing_interest',
      name: 'Pricing Interest',
      trigger: 'User asks about pricing',
      delay: 300000, // 5 minutes
      expectedMessage: 'Pricing follow-up with ROI information',
      icon: Target,
      color: 'blue'
    },
    {
      id: 'feature_exploration',
      name: 'Feature Interest',
      trigger: 'User explores features but no pricing inquiry',
      delay: 300000, // 5 minutes
      expectedMessage: 'Follow-up with relevant examples',
      icon: MessageCircle,
      color: 'green'
    },
    {
      id: 'business_context',
      name: 'Business Context',
      trigger: 'User mentions company size/industry',
      delay: 450000, // 7.5 minutes
      expectedMessage: 'Industry-specific benefits',
      icon: Users,
      color: 'purple'
    },
    {
      id: 'demo_invitation',
      name: 'Demo Invitation',
      trigger: 'High engagement, multiple interactions',
      delay: 900000, // 15 minutes
      expectedMessage: 'Personalized demo invitation',
      icon: Play,
      color: 'orange'
    },
    {
      id: 're_engagement',
      name: 'Re-engagement',
      trigger: 'Always scheduled for later follow-up',
      delay: 3600000, // 1 hour
      expectedMessage: 'Gentle follow-up question',
      icon: Clock,
      color: 'gray'
    }
  ];

  const startDemo = async () => {
    setIsActive(true);
    setDemoMessages([]);
    setTriggeredScenarios([]);
    setSimulationTime(0);
    
    // Reset visitor profile
    const resetProfile = {
      id: 'demo_visitor_001',
      signals: [],
      businessContext: {},
      interactions: 0
    };
    setVisitorProfile(resetProfile);

    // Start simulation timer
    const timer = setInterval(() => {
      setSimulationTime(prev => prev + 1000);
    }, 100); // Speed up for demo (1 second = 10 seconds in simulation)

    // Simulate user interactions and triggers
    setTimeout(() => triggerScenario('pricing_interest'), 2000);
    setTimeout(() => triggerScenario('business_context'), 3000);
    setTimeout(() => triggerScenario('feature_exploration'), 4000);
    setTimeout(() => triggerScenario('demo_invitation'), 6000);
    setTimeout(() => triggerScenario('re_engagement'), 8000);

    // Stop after 10 seconds
    setTimeout(() => {
      setIsActive(false);
      clearInterval(timer);
    }, 10000);
  };

  const triggerScenario = async (scenarioId) => {
    const scenario = demoScenarios.find(s => s.id === scenarioId);
    if (!scenario) return;

    // Add to triggered scenarios
    setTriggeredScenarios(prev => [...prev, {
      ...scenario,
      triggeredAt: simulationTime,
      status: 'scheduled'
    }]);

    // Simulate conversion signals based on scenario
    let newSignals = [...visitorProfile.signals];
    let newContext = { ...visitorProfile.businessContext };

    switch (scenarioId) {
      case 'pricing_interest':
        newSignals.push('pricing_inquiry');
        break;
      case 'feature_exploration':
        newSignals.push('feature_interest');
        break;
      case 'business_context':
        newSignals.push('business_context');
        newContext.companySize = 'medium';
        newContext.industry = 'technology';
        break;
      case 'demo_invitation':
        setVisitorProfile(prev => ({ 
          ...prev, 
          interactions: prev.interactions + 3 
        }));
        break;
    }

    const updatedProfile = {
      ...visitorProfile,
      signals: newSignals,
      businessContext: newContext
    };
    setVisitorProfile(updatedProfile);

    // Generate proactive message
    try {
      const messageContent = await goalsService.generateProactiveMessage(
        scenarioId === 'pricing_interest' ? 'pricing_follow_up' :
        scenarioId === 'feature_exploration' ? 'follow_up_interest' :
        scenarioId === 'business_context' ? 'business_context_follow_up' :
        scenarioId === 'demo_invitation' ? 'demo_invitation' :
        're_engagement',
        newContext
      );

      if (messageContent) {
        // Add message after delay (scaled for demo)
        const demoDelay = scenario.delay / 1000; // Convert to demo scale
        setTimeout(() => {
          setDemoMessages(prev => [...prev, {
            id: Date.now(),
            scenario: scenarioId,
            content: messageContent.content,
            type: messageContent.messageType,
            sentAt: simulationTime + demoDelay
          }]);

          // Update scenario status
          setTriggeredScenarios(prev => prev.map(s => 
            s.id === scenarioId ? { ...s, status: 'sent' } : s
          ));
        }, Math.min(demoDelay / 60, 2000)); // Max 2 seconds for demo
      }
    } catch (error) {
      console.error('Demo: Error generating proactive message:', error);
    }
  };

  const formatTime = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'sent': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Proactive Messaging System Demo
        </h2>
        <p className="text-gray-600 mb-4">
          Watch how the system automatically triggers targeted messages based on user behavior and conversion signals.
        </p>
        
        <button
          onClick={startDemo}
          disabled={isActive}
          className={`px-6 py-3 rounded-lg font-medium ${
            isActive
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isActive ? (
            <>
              <Timer className="h-5 w-5 inline mr-2 animate-spin" />
              Demo Running... {formatTime(simulationTime)}
            </>
          ) : (
            <>
              <Play className="h-5 w-5 inline mr-2" />
              Start Demo
            </>
          )}
        </button>
      </div>

      {/* Visitor Profile */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Simulated Visitor Profile</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-sm font-medium text-gray-700">Conversion Signals</div>
            <div className="text-sm text-gray-600">
              {visitorProfile.signals.length > 0 
                ? visitorProfile.signals.join(', ') 
                : 'None detected yet'}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700">Business Context</div>
            <div className="text-sm text-gray-600">
              {Object.keys(visitorProfile.businessContext).length > 0
                ? Object.entries(visitorProfile.businessContext)
                    .map(([key, value]) => `${key}: ${value}`).join(', ')
                : 'Not identified yet'}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700">Interactions</div>
            <div className="text-sm text-gray-600">{visitorProfile.interactions}</div>
          </div>
        </div>
      </div>

      {/* Trigger Scenarios */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Trigger Scenarios</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {demoScenarios.map((scenario) => {
            const Icon = scenario.icon;
            const triggered = triggeredScenarios.find(t => t.id === scenario.id);
            
            return (
              <div
                key={scenario.id}
                className={`p-4 rounded-lg border-2 transition-all ${
                  triggered 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center mb-2">
                  <Icon className={`h-5 w-5 text-${scenario.color}-600 mr-2`} />
                  <span className="font-medium text-gray-900">{scenario.name}</span>
                  {triggered && (
                    <CheckCircle className="h-4 w-4 text-green-600 ml-auto" />
                  )}
                </div>
                <div className="text-xs text-gray-600 mb-2">{scenario.trigger}</div>
                <div className="text-xs text-gray-500">
                  Delay: {Math.floor(scenario.delay / 60000)} minutes
                </div>
                {triggered && (
                  <div className={`inline-block px-2 py-1 rounded-full text-xs mt-2 ${getStatusColor(triggered.status)}`}>
                    {triggered.status}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Generated Messages */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Generated Proactive Messages
          {demoMessages.length > 0 && (
            <span className="ml-2 text-sm font-normal text-gray-600">
              ({demoMessages.length} messages)
            </span>
          )}
        </h3>
        
        {demoMessages.length > 0 ? (
          <div className="space-y-4">
            {demoMessages.map((message) => {
              const scenario = demoScenarios.find(s => s.id === message.scenario);
              const Icon = scenario?.icon || MessageCircle;
              
              return (
                <div key={message.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Icon className={`h-4 w-4 text-${scenario?.color || 'blue'}-600 mr-2`} />
                    <span className="font-medium text-gray-900">{scenario?.name || 'Unknown'}</span>
                    <span className="ml-auto text-xs text-gray-500">
                      Sent at {formatTime(message.sentAt)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-700 bg-purple-50 p-3 rounded border border-purple-200">
                    "{message.content}"
                  </div>
                  <div className="text-xs text-purple-600 mt-1">📬 Proactive Message</div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No proactive messages generated yet</p>
            <p className="text-xs">Start the demo to see how messages are triggered</p>
          </div>
        )}
      </div>

      {/* How It Works */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">How Proactive Messaging Works</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">🎯 Smart Triggers</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Conversion signal detection (pricing, demo, contact)</li>
              <li>• Business context analysis (company size, industry)</li>
              <li>• Engagement level tracking (interactions, responses)</li>
              <li>• Timing-based follow-ups</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">⚡ Automatic Delivery</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Messages scheduled with optimal timing</li>
              <li>• Personalized content based on context</li>
              <li>• Prevents duplicate/spam messages</li>
              <li>• Tracks delivery and engagement</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProactiveMessagingDemo; 