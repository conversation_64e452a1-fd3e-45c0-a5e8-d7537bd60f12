/* const API_BASE_URL = 'https://fastapi.abnasia.org/abncommonapi';
const DOC_ID = '1AAFxFcDYFE_0FmZryZXFpUEc_m-enzHKfmAM-YqYKbk';
const WORKSHEET_NAME = 'abnasiaorg1';
 */


const API_BASE_URL = process.env.API_BASE_URL;
const DOC_ID = process.env.DOC_ID;
const WORKSHEET_NAME = process.env.WORKSHEET_NAME;



process.env.NEXT_PUBLIC_GROQ_API_KEY

process.env.NEXT_PUBLIC_GROQ_API_KEY
// Helper function to parse CSV to JSON
const parseCSVToJSON = (csv) => {
  const lines = csv.split('\n');
  const headers = lines[0].split(',');
  
  return lines.slice(1).map(line => {
    const values = line.split(',');
    return headers.reduce((obj, header, index) => {
      obj[header.trim()] = values[index]?.trim() || '';
      return obj;
    }, {});
  }).filter(obj => Object.values(obj).some(value => value));
};

export const sheetsApi = {
  // Base API calls
  async getChatHistory() {
    const response = await fetch(`${API_BASE_URL}/fetchgooglesheetascsv`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        doc_id: DOC_ID,
        worksheet_name: WORKSHEET_NAME
      })
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch chat history');
    }
    const data = await response.text();
    return parseCSVToJSON(data);
  },

  async saveChat(chatData) {
    const response = await fetch(`${API_BASE_URL}/json_to_google_sheet`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        doc_id: DOC_ID,
        worksheet_name: WORKSHEET_NAME,
        json_data: chatData
      }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to save chat');
    }
    return response.json();
  },

  // Higher-level functions for the FloatingChat component
  async fetchChatHistory(setIsLoadingHistory, setChatHistory) {
    setIsLoadingHistory(true);
    try {
      const data = await this.getChatHistory();
      const formattedHistory = data
        .filter(row => row.conversation_id)
        .map(row => ({
          id: row.conversation_id,
          timestamp: row.timestamp,
          conversation: JSON.parse(row.messages || '[]'),
        }))
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      
      setChatHistory(formattedHistory);
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      throw error;
    } finally {
      setIsLoadingHistory(false);
    }
  },

  async saveChatToHistory(messages, fetchChatHistory) {
    try {
      const chatData = [{
        conversation_id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        messages: JSON.stringify(messages),
        title: messages[0]?.content?.slice(0, 50) + '...'
      }];

      await this.saveChat(chatData);
      await fetchChatHistory();
    } catch (error) {
      console.error('Failed to save chat:', error);
      throw error;
    }
  }
};