import React, { useState, useEffect } from 'react';
import { Search, Calendar, Tag, BarChart3, Trash2, Download } from 'lucide-react';
import { jsonFilesApi } from './jsonFilesApi';

export const ChatHistory = ({ onChatSelect, onClose, userId = 'anonymous' }) => {
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState({ from: '', to: '' });
  const [selectedTags, setSelectedTags] = useState([]);
  const [sortBy, setSortBy] = useState('timestamp');
  const [sortOrder, setSortOrder] = useState('desc');
  const [stats, setStats] = useState(null);
  const [showStats, setShowStats] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const loadChats = async (reset = false) => {
    setLoading(true);
    try {
      const options = {
        limit: 20,
        offset: reset ? 0 : page * 20,
        sortBy,
        sortOrder,
        search: searchTerm,
        dateFrom: dateFilter.from || null,
        dateTo: dateFilter.to || null,
        userId: userId
      };

      const response = await jsonFilesApi.getChatHistory(options);
      
      if (reset) {
        setChats(response.chats);
        setPage(0);
      } else {
        setChats(prev => [...prev, ...response.chats]);
      }
      
      setHasMore(response.hasMore);
    } catch (error) {
      console.error('Failed to load chats:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await jsonFilesApi.getChatStats(userId);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  useEffect(() => {
    loadChats(true);
  }, [searchTerm, dateFilter, sortBy, sortOrder]);

  useEffect(() => {
    loadStats();
  }, []);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleDateFilter = (field, value) => {
    setDateFilter(prev => ({ ...prev, [field]: value }));
  };

  const handleDeleteChat = async (chatId, e) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this chat?')) {
      try {
        await jsonFilesApi.deleteChat(chatId, userId);
        loadChats(true);
        loadStats();
      } catch (error) {
        console.error('Failed to delete chat:', error);
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTagColor = (tag) => {
    const colors = {
      help: 'bg-blue-100 text-blue-800',
      support: 'bg-green-100 text-green-800',
      problem: 'bg-red-100 text-red-800',
      error: 'bg-red-100 text-red-800',
      question: 'bg-purple-100 text-purple-800',
      order: 'bg-yellow-100 text-yellow-800',
      payment: 'bg-orange-100 text-orange-800',
      shipping: 'bg-indigo-100 text-indigo-800'
    };
    return colors[tag] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="flex flex-col h-full bg-white border-l border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Chat History</h2>
          <div className="flex gap-2">
            <button
              onClick={() => setShowStats(!showStats)}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
              title="Statistics"
            >
              <BarChart3 size={18} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
            >
              ×
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Search chats..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filters */}
        <div className="flex gap-2 mb-3">
          <input
            type="date"
            value={dateFilter.from}
            onChange={(e) => handleDateFilter('from', e.target.value)}
            className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            placeholder="From"
          />
          <input
            type="date"
            value={dateFilter.to}
            onChange={(e) => handleDateFilter('to', e.target.value)}
            className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            placeholder="To"
          />
        </div>

        {/* Sort */}
        <div className="flex gap-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
          >
            <option value="timestamp">Date</option>
            <option value="title">Title</option>
            <option value="messageCount">Messages</option>
          </select>
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
          >
            <option value="desc">Newest</option>
            <option value="asc">Oldest</option>
          </select>
        </div>
      </div>

      {/* Statistics Panel */}
      {showStats && stats && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 gap-4 mb-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.totalChats}</div>
              <div className="text-xs text-gray-600">Total Chats</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.totalMessages}</div>
              <div className="text-xs text-gray-600">Total Messages</div>
            </div>
          </div>
          <div className="text-center mb-3">
            <div className="text-lg font-semibold text-purple-600">{stats.averageMessagesPerChat}</div>
            <div className="text-xs text-gray-600">Avg Messages/Chat</div>
          </div>
          {stats.mostCommonTags.length > 0 && (
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Popular Tags</div>
              <div className="flex flex-wrap gap-1">
                {stats.mostCommonTags.slice(0, 5).map(({ tag, count }) => (
                  <span
                    key={tag}
                    className={`px-2 py-1 text-xs rounded-full ${getTagColor(tag)}`}
                  >
                    {tag} ({count})
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {loading && chats.length === 0 ? (
          <div className="p-4 text-center text-gray-500">Loading...</div>
        ) : chats.length === 0 ? (
          <div className="p-4 text-center text-gray-500">No chats found</div>
        ) : (
          <div className="p-2">
            {chats.map((chat) => (
              <div
                key={chat.id}
                onClick={() => onChatSelect(chat)}
                className="p-3 mb-2 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm line-clamp-2">
                    {chat.title || 'Untitled Chat'}
                  </h3>
                  <button
                    onClick={(e) => handleDeleteChat(chat.id, e)}
                    className="p-1 text-gray-400 hover:text-red-500 ml-2"
                    title="Delete chat"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                  <span>{formatDate(chat.timestamp || chat.createdAt)}</span>
                  <span>{chat.messageCount || 0} messages</span>
                </div>

                {chat.tags && chat.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {chat.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className={`px-2 py-1 text-xs rounded-full ${getTagColor(tag)}`}
                      >
                        {tag}
                      </span>
                    ))}
                    {chat.tags.length > 3 && (
                      <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                        +{chat.tags.length - 3}
                      </span>
                    )}
                  </div>
                )}
              </div>
            ))}

            {hasMore && (
              <button
                onClick={() => {
                  setPage(prev => prev + 1);
                  loadChats(false);
                }}
                disabled={loading}
                className="w-full p-3 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
              >
                {loading ? 'Loading...' : 'Load More'}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}; 