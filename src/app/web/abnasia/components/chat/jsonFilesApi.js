// Client-side API for JSON file-based chat storage
const API_BASE_URL = '/api/chat/abnasia';

export const jsonFilesApi = {
  // Initialize the chat system
  async initialize() {
    // No client-side initialization needed - API handles this
    return true;
  },

  // Get all chat history with improved filtering and search
  async getChatHistory(options = {}) {
    const { 
      limit = 50, 
      offset = 0, 
      sortBy = 'timestamp', 
      sortOrder = 'desc',
      search = '',
      dateFrom = null,
      dateTo = null,
      userId = 'anonymous'
    } = options;

    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
      sortBy,
      sortOrder,
      userId,
    });

    if (search) params.append('search', search);
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const response = await fetch(`${API_BASE_URL}?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch chat history');
    }

    return response.json();
  },

  // Get a specific chat by ID
  async getChatById(chatId, userId = 'anonymous') {
    const response = await fetch(`${API_BASE_URL}?operation=chat&id=${chatId}&userId=${userId}`);
    
    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error('Failed to fetch chat');
    }

    return response.json();
  },

  // Save a new chat or update existing one
  async saveChat(chatData, options = {}) {
    const response = await fetch(API_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chatData,
        options
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to save chat');
    }

    return response.json();
  },

  // Delete a chat
  async deleteChat(chatId, userId = 'anonymous') {
    const response = await fetch(`${API_BASE_URL}?id=${chatId}&userId=${userId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete chat');
    }

    const result = await response.json();
    return result.success;
  },

  // Create backup of a chat
  async createBackup(chatId) {
    // This is handled automatically by the server when saving
    // For now, just return true - could implement a separate endpoint if needed
    return true;
  },

  // Get chat statistics
  async getChatStats(userId = 'anonymous') {
    const response = await fetch(`${API_BASE_URL}?operation=stats&userId=${userId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch chat stats');
    }

    return response.json();
  },

  // Higher-level functions for the FloatingChat component (compatible with original sheetsApi)
  async fetchChatHistory(setIsLoadingHistory, setChatHistory, options = {}) {
    setIsLoadingHistory(true);
    try {
      const { chats } = await this.getChatHistory(options);
      
      // Format for compatibility with existing ChatCore component
      const formattedHistory = chats.map(chat => ({
        id: chat.id,
        timestamp: chat.createdAt || chat.timestamp,
        conversation: chat.messages || [],
        title: chat.title,
        tags: chat.tags
      }));
      
      setChatHistory(formattedHistory);
      return formattedHistory;
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      throw error;
    } finally {
      setIsLoadingHistory(false);
    }
  },

  async saveChatToHistory(messages, fetchChatHistory, options = {}) {
    try {
      const chatData = {
        messages: messages,
        timestamp: new Date().toISOString(),
        createdAt: new Date().toISOString()
      };

      // Ensure options includes the userId for proper file organization
      const saveOptions = { ...options };
      
      await this.saveChat(chatData, saveOptions);
      
      // Refresh chat history if callback provided
      if (fetchChatHistory) {
        await fetchChatHistory();
      }
      
      return chatData;
    } catch (error) {
      console.error('Failed to save chat:', error);
      throw error;
    }
  }
}; 