// Chat Settings API Client
class ChatSettingsApi {
  constructor(baseUrl = '/api/chat/abnasia/settings') {
    this.baseUrl = baseUrl;
  }

  // Load user settings
  async loadSettings(userId) {
    try {
      const response = await fetch(`${this.baseUrl}?userId=${encodeURIComponent(userId)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error loading settings:', error);
      throw error;
    }
  }

  // Save user settings
  async saveSettings(userId, settings) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          settings
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  }

  // Update specific setting fields
  async updateSettings(userId, partialSettings) {
    try {
      // First load existing settings
      const currentSettings = await this.loadSettings(userId);
      
      // Merge with new settings
      const updatedSettings = {
        ...currentSettings.settings,
        ...partialSettings,
        lastUpdated: new Date().toISOString()
      };

      // Save merged settings
      return await this.saveSettings(userId, updatedSettings);
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  }

  // Save selected documents
  async saveSelectedDocuments(userId, selectedDocuments) {
    const documentsArray = Array.isArray(selectedDocuments) 
      ? selectedDocuments 
      : Array.from(selectedDocuments);
    
    return await this.updateSettings(userId, {
      selectedDocuments: documentsArray
    });
  }

  // Save selected avatar
  async saveSelectedAvatar(userId, avatar) {
    return await this.updateSettings(userId, {
      selectedAvatar: avatar
    });
  }

  // Save user preferences
  async savePreferences(userId, preferences) {
    return await this.updateSettings(userId, {
      preferences
    });
  }

  // Reset settings to defaults
  async resetSettings(userId) {
    try {
      const response = await fetch(`${this.baseUrl}?userId=${encodeURIComponent(userId)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw error;
    }
  }

  // Debounced save to avoid too many API calls
  debouncedSave = this.debounce((userId, settings) => {
    return this.saveSettings(userId, settings);
  }, 1000);

  // Debounce utility
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// Export singleton instance
export const settingsApi = new ChatSettingsApi();
export default settingsApi; 