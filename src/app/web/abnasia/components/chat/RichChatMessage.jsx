import React, { useCallback } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './MarkdownRenderer';
import { CONTENT_REGISTRY, SAMPLE_CONTENT } from './ContentRegistry';

const RichChatMessage = ({ message, isUser, className = "" }) => {
  // Validate and normalize message structure
  if (!message) {
    console.error('No message provided to RichChatMessage');
    return null;
  }

  // Debug logging to track problematic messages
  if (typeof message === 'object' && message.content && typeof message.content !== 'string') {
    console.warn('RichChatMessage received non-string content:', message.content, typeof message.content);
  }

  // Handle various message formats
  let normalizedMessage = message;
  
  // If message is a string, convert to object
  if (typeof message === 'string') {
    normalizedMessage = {
      content: message,
      sender: isUser ? 'user' : 'assistant',
      timestamp: new Date().toISOString()
    };
  }
  
  // If message is an array or other type, handle gracefully
  if (Array.isArray(message)) {
    console.warn('Array message detected, using first element:', message);
    normalizedMessage = message[0] || { content: '', sender: 'assistant', timestamp: new Date().toISOString() };
  }

  // Ensure we have a valid message object
  if (typeof normalizedMessage !== 'object') {
    normalizedMessage = {
      content: String(normalizedMessage || ''),
      sender: isUser ? 'user' : 'assistant',
      timestamp: new Date().toISOString()
    };
  }

  // Ensure content is always a string
  const messageContent = normalizedMessage.content ? String(normalizedMessage.content) : '';

  // Handle interactive content callbacks (safely without DOM references)
  const handleGameComplete = useCallback((gameData) => {
    try {
      console.log('Game completed:', {
        gameType: gameData?.gameType,
        result: gameData?.result,
        timestamp: new Date().toISOString()
      });
      // Could send analytics or update user engagement metrics
    } catch (error) {
      console.error('Error handling game completion:', error.message);
    }
  }, []);

  const handlePollVote = useCallback((optionId) => {
    try {
      console.log('Poll vote:', { optionId, timestamp: new Date().toISOString() });
      // Could send vote data to backend
    } catch (error) {
      console.error('Error handling poll vote:', error.message);
    }
  }, []);

  const handleCalculatorChange = useCallback((calculationData) => {
    try {
      console.log('Calculator data:', {
        result: calculationData?.result,
        inputs: calculationData?.inputs,
        timestamp: new Date().toISOString()
      });
      // Could store calculation results for lead qualification
    } catch (error) {
      console.error('Error handling calculator change:', error.message);
    }
  }, []);

  const handleProductClick = useCallback((product) => {
    try {
      console.log('Product clicked:', {
        id: product?.id,
        name: product?.name,
        timestamp: new Date().toISOString()
      });
      // Could open product details or start sales flow
      if (product?.id) {
        window.open(`/products/${product.id}`, '_blank');
      }
    } catch (error) {
      console.error('Error handling product click:', error.message);
    }
  }, []);

  const handleImageClick = useCallback((image) => {
    try {
      console.log('Image clicked:', {
        src: image?.src,
        alt: image?.alt,
        timestamp: new Date().toISOString()
      });
      // Could open image in lightbox or gallery view
      if (image?.src) {
        window.open(image.src, '_blank');
      }
    } catch (error) {
      console.error('Error handling image click:', error.message);
    }
  }, []);

  const handleCTAAction = useCallback((action) => {
    try {
      console.log('CTA action:', {
        type: action?.type,
        label: action?.label,
        timestamp: new Date().toISOString()
      });
      // Handle different CTA actions
      switch(action?.type) {
        case 'trial':
          window.open('/trial', '_blank');
          break;
        case 'demo':
          window.open('/demo', '_blank');
          break;
        default:
          console.log('Unknown CTA action type:', action?.type);
      }
    } catch (error) {
      console.error('Error handling CTA action:', error.message);
    }
  }, []);

  // Render different message types
  const renderMessageContent = () => {
    // Default to text message
    if (!normalizedMessage.richContent) {
      // Extra safety check for content
      const safeContent = messageContent || '';
      const stringContent = typeof safeContent === 'string' ? safeContent : String(safeContent);
      
      return (
        <div className="text-sm">
          {isUser ? (
            <div className="whitespace-pre-wrap">{stringContent}</div>
          ) : (
            <MarkdownRenderer 
              content={stringContent} 
              className={isUser ? 'text-blue-500' : 'text-gray-600'}
            />
          )}
        </div>
      );
    }

    // Render rich content
    const { type, contentId, data } = normalizedMessage.richContent;
    const contentDef = CONTENT_REGISTRY[contentId];
    
    if (!contentDef) {
      console.warn('Unknown content ID:', contentId);
      return (
        <div className="text-sm text-red-500">
          Unknown content type: {contentId}
        </div>
      );
    }

    const ContentComponent = contentDef.component;
    const props = { ...data };

    // Add appropriate callbacks based on content type
    switch(contentId) {
      case 'game:tictactoe':
        props.onComplete = handleGameComplete;
        break;
      
      case 'widget:poll':
        props.options = data?.options || SAMPLE_CONTENT.poll_interests.options;
        props.onVote = handlePollVote;
        break;
      
      case 'widget:calculator':
        props.onCalculate = handleCalculatorChange;
        break;
      
      case 'product:showcase':
        props.products = data?.products || SAMPLE_CONTENT.products_showcase;
        props.onProductClick = handleProductClick;
        break;
      
      case 'gallery:portfolio':
        props.images = data?.images || SAMPLE_CONTENT.portfolio_images;
        props.onImageClick = handleImageClick;
        break;
      
      case 'testimonial:customer':
        props.testimonial = data?.testimonial || SAMPLE_CONTENT.testimonial_sample;
        break;
      
      case 'cta:sales':
        props.cta = data?.cta || SAMPLE_CONTENT.cta_sales;
        props.onAction = handleCTAAction;
        break;
    }

    return (
      <div className="rich-content">
        {/* Optional text content above rich content */}
        {messageContent && (
          <div className="text-sm mb-3">
            <MarkdownRenderer 
              content={typeof messageContent === 'string' ? messageContent : String(messageContent)} 
              className="text-gray-600"
            />
          </div>
        )}
        
        {/* Rich interactive content */}
        <ContentComponent {...props} />
      </div>
    );
  };

  try {
    return (
      <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} animate-fade-in`}>
        <div className={`
          max-w-[85%] p-3 relative
          ${isUser ? 'text-blue-500 border-blue-500' : 'text-gray-600 border-gray-400'}
          border border-dashed rounded-2xl transition-all duration-300 hover:scale-[1.01]
          ${className}
        `}>
          {renderMessageContent()}
          
          {/* Message timestamp */}
          {normalizedMessage.timestamp && (
            <div className="text-xs opacity-70 mt-2 text-right">
              {new Date(normalizedMessage.timestamp).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </div>
          )}
          
          {/* Decorative corners */}
          <div className="absolute top-0 left-0 w-2 h-2 -translate-x-1 -translate-y-1 border-t border-l border-current" />
          <div className="absolute top-0 right-0 w-2 h-2 translate-x-1 -translate-y-1 border-t border-r border-current" />
          <div className="absolute bottom-0 left-0 w-2 h-2 -translate-x-1 translate-y-1 border-b border-l border-current" />
          <div className="absolute bottom-0 right-0 w-2 h-2 translate-x-1 translate-y-1 border-b border-r border-current" />
        </div>
      </div>
    );
  } catch (error) {
    // Safely log error without circular references
    console.error('RichChatMessage rendering error:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    console.error('Problematic message data:', {
      content: message?.content,
      sender: message?.sender,
      timestamp: message?.timestamp,
      hasRichContent: !!message?.richContent
    });
    return (
      <div className="flex justify-start">
        <div className="max-w-[85%] p-3 border border-red-300 bg-red-50 text-red-600 rounded-lg">
          <div className="text-sm">
            Error rendering message. Please try again.
          </div>
        </div>
      </div>
    );
  }
};

export default RichChatMessage; 