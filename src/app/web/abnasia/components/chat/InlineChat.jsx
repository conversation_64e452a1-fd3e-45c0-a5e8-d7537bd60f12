'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ChatCore } from './ChatCore';
import { ChatHeader } from './ChatHeader';
import { MessageSquare, X, Maximize2 } from 'lucide-react';
import { ChatButton } from './StyledChatComponents';

const InlineChat = ({ 
  className = "",
  title,
  initialMessage,
  miniMode = true
}) => {
  const [isMini, setIsMini] = useState(miniMode);
  const [hasStartedChat, setHasStartedChat] = useState(false);
  const [userId, setUserId] = useState('');
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [selectedDocuments] = useState(new Set());
  const router = useRouter();

  const handleStartChat = () => {
    setIsMini(false);
    setHasStartedChat(true);
  };

  const handleMinimize = () => {
    setIsMini(true);
  };

  const goToFullChat = () => {
    router.push('/web/abnasia/chat');
  };

  if (isMini) {
    return (
      <div 
        className={`
          cursor-pointer 
          transition-all 
          duration-300 
          hover:scale-105
          flex 
          items-center 
          gap-3 
          p-4 
          bg-white 
          border
          border-blue-500/30 
          rounded-lg 
          shadow-lg 
          ${className}
        `}
        onClick={handleStartChat}
      >
        <MessageSquare className="w-5 h-5 text-blue-500" />
        <div className="flex flex-col">
          <span className="font-medium text-gray-700">
            {hasStartedChat ? 'Continue Chat' : 'AI Chat'}
          </span>
          {userId && (
            <span className="text-xs text-purple-600">
              🧠 Proactive mode enabled
            </span>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`
      relative
      border 
      border-blue-500/30 
      rounded-lg 
      shadow-lg 
      ${className}
      transition-all 
      duration-300
    `}>
      <div className="absolute -top-3 -right-3">
        <button
          onClick={handleMinimize}
          className="
            p-1.5
            bg-white
            border
            border-gray-200
            rounded-full
            shadow-md
            hover:bg-gray-50
            transition-colors
          "
        >
          <X className="w-4 h-4 text-gray-500" />
        </button>
      </div>
      
      <ChatHeader title={title} />
      <div className="px-4 py-2 border-b border-blue-500/30">
        <ChatButton 
          onClick={goToFullChat}
          className="w-full flex items-center justify-center gap-2 text-sm"
        >
          <Maximize2 size={16} />
          Open Full Chat
        </ChatButton>
      </div>
      <ChatCore 
        initialMessage={initialMessage}
        containerClassName="border-t border-blue-500/30"
        selectedDocuments={selectedDocuments}
        availableDocuments={[]}
        onUserIdChange={setUserId}
        onAvatarChange={setSelectedAvatar}
        initialAvatar={selectedAvatar}
      />
    </div>
  );
};

export default InlineChat;