import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';

const MarkdownRenderer = ({ content, className = '' }) => {
  // Safe stringification that handles circular references
  const safeStringify = (obj) => {
    if (typeof obj === 'string') return obj;
    if (obj === null || obj === undefined) return '';
    
    // Handle primitive types
    if (typeof obj !== 'object') return String(obj);
    
    // For objects, try different safe approaches
    try {
      // If it has a meaningful toString method, use it
      if (obj.toString && typeof obj.toString === 'function' && obj.toString !== Object.prototype.toString) {
        return obj.toString();
      }
      
      // For plain objects, try to extract meaningful content
      if (obj.constructor === Object) {
        const keys = Object.keys(obj);
        if (keys.length === 0) return '[Empty Object]';
        
        // Try to extract text content if available
        if (obj.content && typeof obj.content === 'string') return obj.content;
        if (obj.text && typeof obj.text === 'string') return obj.text;
        if (obj.message && typeof obj.message === 'string') return obj.message;
        
        return `[Object with keys: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}]`;
      }
      
      return '[Complex Object]';
    } catch (error) {
      return '[Unserializable Object]';
    }
  };

  const safeContent = safeStringify(content);

  // Custom components for better styling
  const components = {
    // Headings
    h1: ({node, ...props}) => <h1 className="text-2xl font-bold mb-3 text-gray-900" {...props} />,
    h2: ({node, ...props}) => <h2 className="text-xl font-bold mb-2 text-gray-900" {...props} />,
    h3: ({node, ...props}) => <h3 className="text-lg font-bold mb-2 text-gray-900" {...props} />,
    h4: ({node, ...props}) => <h4 className="text-base font-bold mb-1 text-gray-900" {...props} />,
    h5: ({node, ...props}) => <h5 className="text-sm font-bold mb-1 text-gray-900" {...props} />,
    h6: ({node, ...props}) => <h6 className="text-xs font-bold mb-1 text-gray-900" {...props} />,
    
    // Paragraphs
    p: ({node, ...props}) => <p className="mb-2 leading-relaxed" {...props} />,
    
    // Lists
    ul: ({node, ...props}) => <ul className="list-disc list-inside mb-2 ml-4" {...props} />,
    ol: ({node, ...props}) => <ol className="list-decimal list-inside mb-2 ml-4" {...props} />,
    li: ({node, ...props}) => <li className="mb-1" {...props} />,
    
    // Emphasis
    strong: ({node, ...props}) => <strong className="font-bold text-gray-900" {...props} />,
    em: ({node, ...props}) => <em className="italic" {...props} />,
    
    // Links
    a: ({node, ...props}) => (
      <a 
        className="text-blue-600 hover:text-blue-800 underline transition-colors duration-200" 
        target="_blank" 
        rel="noopener noreferrer" 
        {...props} 
      />
    ),
    
    // Code
    code: ({node, inline, className: codeClassName, children, ...props}) => {
      if (inline) {
        return (
          <code 
            className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono" 
            {...props}
          >
            {children}
          </code>
        );
      }
      return (
        <code 
          className={`block bg-gray-100 text-gray-800 p-3 rounded-lg text-sm font-mono overflow-x-auto mb-2 ${codeClassName || ''}`} 
          {...props}
        >
          {children}
        </code>
      );
    },
    
    // Pre (code blocks)
    pre: ({node, ...props}) => (
      <pre className="bg-gray-800 text-gray-100 p-4 rounded-lg overflow-x-auto mb-2 text-sm font-mono border border-gray-600" {...props} />
    ),
    
    // Blockquotes
    blockquote: ({node, ...props}) => (
      <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 mb-2" {...props} />
    ),
    
    // Tables
    table: ({node, ...props}) => (
      <div className="overflow-x-auto mb-2">
        <table className="min-w-full border border-gray-300 bg-white" {...props} />
      </div>
    ),
    thead: ({node, ...props}) => <thead className="bg-gray-50" {...props} />,
    tbody: ({node, ...props}) => <tbody {...props} />,
    tr: ({node, ...props}) => <tr className="border-b border-gray-200" {...props} />,
    th: ({node, ...props}) => (
      <th className="px-4 py-2 text-left font-semibold text-gray-900 border-r border-gray-300" {...props} />
    ),
    td: ({node, ...props}) => (
      <td className="px-4 py-2 text-gray-700 border-r border-gray-300" {...props} />
    ),
    
    // Horizontal Rule
    hr: ({node, ...props}) => <hr className="my-4 border-gray-300" {...props} />,
    
    // Images
    img: ({node, ...props}) => (
      <img 
        className="max-w-full h-auto rounded-lg shadow-sm mb-2" 
        loading="lazy" 
        {...props} 
      />
    ),
  };

  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        components={components}
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeRaw]}
        skipHtml={false} // Allow HTML
      >
        {safeContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer; 