import React, { useState } from 'react';
import { Play, Clock, MessageCircle, Target, Users, CheckCircle, Timer } from 'lucide-react';

const ProactiveDemo = () => {
  const [isActive, setIsActive] = useState(false);
  const [demoMessages, setDemoMessages] = useState([]);
  const [triggeredScenarios, setTriggeredScenarios] = useState([]);
  const [simulationTime, setSimulationTime] = useState(0);

  const demoScenarios = [
    {
      id: 'pricing_interest',
      name: 'Pricing Interest',
      trigger: 'User asks about pricing',
      delay: '5 minutes',
      message: 'I understand pricing is an important consideration. Would it be helpful if I explained our different pricing tiers and what\'s included?',
      icon: Target,
      color: 'blue'
    },
    {
      id: 'feature_exploration',
      name: 'Feature Interest',
      trigger: 'User explores features',
      delay: '5 minutes',
      message: 'I noticed you were interested in our solutions. Would you like me to show you some specific examples that might be relevant to your business?',
      icon: MessageCircle,
      color: 'green'
    },
    {
      id: 'business_context',
      name: 'Business Context',
      trigger: 'User mentions company details',
      delay: '7.5 minutes',
      message: 'For medium-size technology companies like yours, we typically see the biggest impact in these key areas. Should I explain how that works?',
      icon: Users,
      color: 'purple'
    },
    {
      id: 'demo_invitation',
      name: 'Demo Invitation',
      trigger: 'High engagement detected',
      delay: '15 minutes',
      message: 'Would you be interested in a quick 15-minute demo? I can show you exactly how this would work for your use case.',
      icon: Play,
      color: 'orange'
    },
    {
      id: 're_engagement',
      name: 'Re-engagement',
      trigger: 'Follow-up after delay',
      delay: '1 hour',
      message: 'Hi! I wanted to follow up on our earlier conversation. Do you have any other questions I can help with?',
      icon: Clock,
      color: 'gray'
    }
  ];

  const startDemo = () => {
    setIsActive(true);
    setDemoMessages([]);
    setTriggeredScenarios([]);
    setSimulationTime(0);

    // Start simulation timer
    const timer = setInterval(() => {
      setSimulationTime(prev => prev + 1000);
    }, 100); // Speed up for demo

    // Simulate triggers
    const triggers = [
      { scenario: 'pricing_interest', delay: 2000 },
      { scenario: 'business_context', delay: 3000 },
      { scenario: 'feature_exploration', delay: 4000 },
      { scenario: 'demo_invitation', delay: 6000 },
      { scenario: 're_engagement', delay: 8000 }
    ];

    triggers.forEach(({ scenario, delay }) => {
      setTimeout(() => triggerScenario(scenario), delay);
    });

    // Stop after 10 seconds
    setTimeout(() => {
      setIsActive(false);
      clearInterval(timer);
    }, 10000);
  };

  const triggerScenario = (scenarioId) => {
    const scenario = demoScenarios.find(s => s.id === scenarioId);
    if (!scenario) return;

    // Add to triggered scenarios
    setTriggeredScenarios(prev => [...prev, {
      ...scenario,
      triggeredAt: simulationTime,
      status: 'scheduled'
    }]);

    // Add message after a short delay
    setTimeout(() => {
      setDemoMessages(prev => [...prev, {
        id: Date.now(),
        scenario: scenarioId,
        content: scenario.message,
        sentAt: simulationTime + 1000
      }]);

      // Update scenario status
      setTriggeredScenarios(prev => prev.map(s => 
        s.id === scenarioId ? { ...s, status: 'sent' } : s
      ));
    }, 1500);
  };

  const formatTime = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'sent': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          🤖 Proactive Messaging System Demo
        </h2>
        <p className="text-gray-600 mb-4">
          See how the chat can automatically send targeted messages based on user behavior and conversion signals
        </p>
        
        <button
          onClick={startDemo}
          disabled={isActive}
          className={`px-6 py-3 rounded-lg font-medium ${
            isActive
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isActive ? (
            <>
              <Timer className="h-5 w-5 inline mr-2 animate-spin" />
              Demo Running... {formatTime(simulationTime)}
            </>
          ) : (
            <>
              <Play className="h-5 w-5 inline mr-2" />
              Start Demo
            </>
          )}
        </button>
      </div>

      {/* Trigger Scenarios */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Trigger Scenarios</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {demoScenarios.map((scenario) => {
            const Icon = scenario.icon;
            const triggered = triggeredScenarios.find(t => t.id === scenario.id);
            
            return (
              <div
                key={scenario.id}
                className={`p-4 rounded-lg border-2 transition-all ${
                  triggered 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center mb-2">
                  <Icon className={`h-5 w-5 text-${scenario.color}-600 mr-2`} />
                  <span className="font-medium text-gray-900">{scenario.name}</span>
                  {triggered && (
                    <CheckCircle className="h-4 w-4 text-green-600 ml-auto" />
                  )}
                </div>
                <div className="text-xs text-gray-600 mb-2">{scenario.trigger}</div>
                <div className="text-xs text-gray-500">
                  Delay: {scenario.delay}
                </div>
                {triggered && (
                  <div className={`inline-block px-2 py-1 rounded-full text-xs mt-2 ${getStatusColor(triggered.status)}`}>
                    {triggered.status}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Generated Messages */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          📬 Generated Proactive Messages
          {demoMessages.length > 0 && (
            <span className="ml-2 text-sm font-normal text-gray-600">
              ({demoMessages.length} messages)
            </span>
          )}
        </h3>
        
        {demoMessages.length > 0 ? (
          <div className="space-y-4">
            {demoMessages.map((message) => {
              const scenario = demoScenarios.find(s => s.id === message.scenario);
              const Icon = scenario?.icon || MessageCircle;
              
              return (
                <div key={message.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Icon className={`h-4 w-4 text-${scenario?.color || 'blue'}-600 mr-2`} />
                    <span className="font-medium text-gray-900">{scenario?.name || 'Unknown'}</span>
                    <span className="ml-auto text-xs text-gray-500">
                      Sent at {formatTime(message.sentAt)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-700 bg-purple-50 p-3 rounded border border-purple-200">
                    "{message.content}"
                  </div>
                  <div className="text-xs text-purple-600 mt-1">📬 Proactive Message</div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No proactive messages generated yet</p>
            <p className="text-xs">Start the demo to see how messages are triggered</p>
          </div>
        )}
      </div>

      {/* How It Works */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">⚡ How Proactive Messaging Works</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">🎯 Smart Triggers</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Conversion signal detection (pricing, demo, contact)</li>
              <li>• Business context analysis (company size, industry)</li>
              <li>• Engagement level tracking (interactions, responses)</li>
              <li>• Timing-based follow-ups</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">🚀 Automatic Delivery</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Messages scheduled with optimal timing</li>
              <li>• Personalized content based on context</li>
              <li>• Prevents duplicate/spam messages</li>
              <li>• Tracks delivery and engagement</li>
            </ul>
          </div>
        </div>
        <div className="mt-4 p-4 bg-blue-100 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Key Benefit:</strong> The chat can now actively engage users instead of just responding, 
            leading to higher conversion rates and better user experience through timely, relevant follow-ups.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProactiveDemo; 