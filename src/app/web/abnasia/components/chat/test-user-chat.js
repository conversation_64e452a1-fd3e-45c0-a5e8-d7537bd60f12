// Test file for User-Specific Chat API
// Run this to test the new user-based chat storage system

import { jsonFilesApi } from './jsonFilesApi.js';

async function testUserSpecificChats() {
  console.log('Testing User-Specific Chat System...');
  
  try {
    const users = ['<EMAIL>', '<EMAIL>', 'anonymous'];
    
    // Test 1: Save chats for different users
    console.log('1. Saving chats for different users...');
    
    for (const userId of users) {
      const testChat = {
        messages: [
          {
            content: `Hello, I'm ${userId} and I need help`,
            sender: 'user',
            timestamp: new Date().toISOString()
          },
          {
            content: `Hi ${userId}! I'd be happy to help you.`,
            sender: 'AbnAsia.org',
            timestamp: new Date().toISOString()
          }
        ]
      };

      const savedChat = await jsonFilesApi.saveChat(testChat, { userId });
      console.log(`✓ Chat saved for user ${userId}:`, savedChat.id);
    }

    // Test 2: Verify each user sees only their own chats
    console.log('2. Verifying user-specific chat isolation...');
    
    for (const userId of users) {
      const userChats = await jsonFilesApi.getChatHistory({ userId, limit: 10 });
      console.log(`✓ User ${userId} has ${userChats.chats.length} chats`);
      
      // Verify the chats contain the correct user context
      userChats.chats.forEach(chat => {
        const hasUserMessage = chat.messages.some(msg => 
          msg.content.includes(userId) && msg.sender === 'user'
        );
        if (hasUserMessage) {
          console.log(`  ✓ Chat ${chat.id} belongs to ${userId}`);
        }
      });
    }

    // Test 3: Test user stats
    console.log('3. Testing user-specific statistics...');
    
    for (const userId of users) {
      const stats = await jsonFilesApi.getChatStats(userId);
      console.log(`✓ Stats for ${userId}:`, {
        totalChats: stats.totalChats,
        totalMessages: stats.totalMessages
      });
    }

    // Test 4: Test cross-user isolation (john shouldn't see jane's chats)
    console.log('4. Testing cross-user isolation...');
    
    const johnChats = await jsonFilesApi.getChatHistory({ userId: '<EMAIL>' });
    const janeChats = await jsonFilesApi.getChatHistory({ userId: '<EMAIL>' });
    
    const johnChatIds = johnChats.chats.map(chat => chat.id);
    const janeChatIds = janeChats.chats.map(chat => chat.id);
    const intersection = johnChatIds.filter(id => janeChatIds.includes(id));
    
    if (intersection.length === 0) {
      console.log('✓ Users cannot see each other\'s chats');
    } else {
      console.log('❌ User isolation failed - found shared chats:', intersection);
    }

    // Test 5: Test loading specific user's chat by ID
    console.log('5. Testing user-specific chat retrieval...');
    
    if (johnChats.chats.length > 0) {
      const chatId = johnChats.chats[0].id;
      
      // Should work for John
      const johnChat = await jsonFilesApi.getChatById(chatId, '<EMAIL>');
      console.log('✓ John can access his own chat');
      
      // Should fail for Jane (different user directory)
      try {
        const janeAttempt = await jsonFilesApi.getChatById(chatId, '<EMAIL>');
        if (!janeAttempt) {
          console.log('✓ Jane cannot access John\'s chat');
        } else {
          console.log('❌ Jane was able to access John\'s chat');
        }
      } catch (error) {
        console.log('✓ Jane cannot access John\'s chat (threw error)');
      }
    }

    console.log('\n🎉 All user-specific tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Test the complete user workflow
async function testUserWorkflow() {
  console.log('\nTesting Complete User Workflow...');
  
  try {
    const userId = '<EMAIL>';
    
    // 1. User starts fresh (no history)
    console.log('1. New user - checking empty history...');
    const initialHistory = await jsonFilesApi.getChatHistory({ userId });
    console.log(`✓ Initial history: ${initialHistory.chats.length} chats`);
    
    // 2. User has a conversation
    console.log('2. User has a conversation...');
    const conversationMessages = [
      {
        content: 'Hi, I need help with setting up my account',
        sender: 'user',
        timestamp: new Date().toISOString()
      },
      {
        content: 'I\'d be happy to help you set up your account. What specific area do you need assistance with?',
        sender: 'AbnAsia.org',
        timestamp: new Date().toISOString()
      },
      {
        content: 'I need to configure my profile settings',
        sender: 'user',
        timestamp: new Date().toISOString()
      },
      {
        content: 'Here are the steps to configure your profile settings...',
        sender: 'AbnAsia.org',
        timestamp: new Date().toISOString()
      }
    ];
    
    await jsonFilesApi.saveChatToHistory(conversationMessages, null, { userId });
    console.log('✓ Conversation saved');
    
    // 3. User returns later and history is loaded
    console.log('3. User returns - loading history...');
    const returnVisitHistory = await jsonFilesApi.getChatHistory({ userId, limit: 1 });
    
    if (returnVisitHistory.chats.length > 0) {
      const lastChat = returnVisitHistory.chats[0];
      console.log(`✓ Found previous conversation with ${lastChat.messageCount} messages`);
      console.log(`  Title: ${lastChat.title}`);
      console.log(`  Tags: ${lastChat.tags?.join(', ') || 'none'}`);
    }
    
    // 4. User stats
    console.log('4. Getting user statistics...');
    const userStats = await jsonFilesApi.getChatStats(userId);
    console.log(`✓ User has ${userStats.totalChats} chats with ${userStats.totalMessages} total messages`);
    
    console.log('\n🎉 User workflow test completed successfully!');
    
  } catch (error) {
    console.error('❌ Workflow test failed:', error);
  }
}

// Uncomment the lines below to run the tests
// testUserSpecificChats();
// testUserWorkflow();

export { testUserSpecificChats, testUserWorkflow }; 