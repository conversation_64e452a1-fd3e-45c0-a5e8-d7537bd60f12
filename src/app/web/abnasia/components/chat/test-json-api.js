// Test file for JSON Files API
// Run this to test the new chat storage system

import { json<PERSON>ilesApi } from './jsonFilesApi.js';

async function testJsonFilesApi() {
  console.log('Testing JSON Files API...');
  
  try {
    // Test 1: Initialize the system
    console.log('1. Initializing system...');
    await jsonFilesApi.initialize();
    console.log('✓ System initialized');

    // Test 2: Get empty chat history
    console.log('2. Getting initial chat history...');
    const initialHistory = await jsonFilesApi.getChatHistory();
    console.log('✓ Initial history:', initialHistory);

    // Test 3: Save a test chat
    console.log('3. Saving test chat...');
    const testChat = {
      messages: [
        {
          content: 'Hello, I need help with my order',
          sender: 'user',
          timestamp: new Date().toISOString()
        },
        {
          content: 'Hi! I\'d be happy to help you with your order. Can you please provide me with your order number?',
          sender: 'AbnAsia.org',
          timestamp: new Date().toISOString()
        }
      ]
    };

    const savedChat = await jsonFilesApi.saveChat(testChat);
    console.log('✓ Chat saved:', savedChat.id);

    // Test 4: Get updated chat history
    console.log('4. Getting updated chat history...');
    const updatedHistory = await jsonFilesApi.getChatHistory();
    console.log('✓ Updated history:', updatedHistory);

    // Test 5: Get specific chat by ID
    console.log('5. Getting chat by ID...');
    const specificChat = await jsonFilesApi.getChatById(savedChat.id);
    console.log('✓ Specific chat:', specificChat?.title);

    // Test 6: Get chat statistics
    console.log('6. Getting chat statistics...');
    const stats = await jsonFilesApi.getChatStats();
    console.log('✓ Chat stats:', stats);

    // Test 7: Test search functionality
    console.log('7. Testing search...');
    const searchResults = await jsonFilesApi.getChatHistory({ search: 'order' });
    console.log('✓ Search results:', searchResults);

    console.log('\n🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Uncomment the line below to run the test
// testJsonFilesApi();

export { testJsonFilesApi }; 