import React, { useState, useEffect } from 'react';
import { 
  Calculator, Gamepad2, ShoppingCart, Image, Trophy, Target, 
  Play, Heart, Star, Check, X, TrendingUp, DollarSign 
} from 'lucide-react';

// Content Registry - defines all available interactive content types
export const CONTENT_TYPES = {
  TEXT: 'text',
  GAME: 'game',
  WIDGET: 'widget',
  PRODUCT: 'product',
  GALLERY: 'gallery',
  POLL: 'poll',
  CALCULATOR: 'calculator',
  ATTACHMENT: 'attachment',
  TESTIMONIAL: 'testimonial',
  CTA: 'cta'
};

// Interactive Components
const TicTacToeGame = ({ onComplete }) => {
  const [board, setBoard] = useState(Array(9).fill(null));
  const [isXNext, setIsXNext] = useState(true);
  const [winner, setWinner] = useState(null);

  const checkWinner = (squares) => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8],
      [0, 3, 6], [1, 4, 7], [2, 5, 8],
      [0, 4, 8], [2, 4, 6]
    ];
    
    for (let i = 0; i < lines.length; i++) {
      const [a, b, c] = lines[i];
      if (squares[a] && squares[a] === squares[b] && squares[a] === squares[c]) {
        return squares[a];
      }
    }
    return null;
  };

  const handleClick = (index) => {
    if (board[index] || winner) return;
    
    const newBoard = [...board];
    newBoard[index] = isXNext ? 'X' : 'O';
    setBoard(newBoard);
    setIsXNext(!isXNext);
    
    const gameWinner = checkWinner(newBoard);
    if (gameWinner) {
      setWinner(gameWinner);
      onComplete?.({ winner: gameWinner, moves: newBoard.filter(Boolean).length });
    }
  };

  const resetGame = () => {
    setBoard(Array(9).fill(null));
    setIsXNext(true);
    setWinner(null);
  };

  return (
    <div className="p-4 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border border-blue-200">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">🎮 Tic-Tac-Toe</h3>
        {winner ? (
          <div className="p-2 bg-green-100 rounded text-green-800 font-semibold">
            🎉 Player {winner} Wins! 🎉
          </div>
        ) : (
          <div className="text-gray-600">
            Next player: <span className="font-semibold text-blue-600">{isXNext ? 'X' : 'O'}</span>
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-3 gap-2 mb-4 max-w-48 mx-auto">
        {board.map((cell, index) => (
          <button
            key={index}
            onClick={() => handleClick(index)}
            className="w-12 h-12 border-2 border-gray-300 rounded text-xl font-bold hover:bg-gray-50 transition-colors"
          >
            {cell}
          </button>
        ))}
      </div>
      
      <div className="text-center">
        <button
          onClick={resetGame}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
        >
          New Game
        </button>
      </div>
    </div>
  );
};

const QuickPoll = ({ options, onVote }) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [showResults, setShowResults] = useState(false);

  const handleVote = (optionId) => {
    setSelectedOption(optionId);
    setShowResults(true);
    onVote?.(optionId);
  };

  const totalVotes = options.reduce((sum, option) => sum + option.votes, 0);

  return (
    <div className="p-4 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg border border-green-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-3">🗳️ Quick Poll</h3>
      
      {!showResults ? (
        <div className="space-y-2">
          {options.map(option => (
            <button
              key={option.id}
              onClick={() => handleVote(option.id)}
              className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-white hover:shadow-sm transition-all"
            >
              {option.text}
            </button>
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {options.map(option => {
            const percentage = totalVotes > 0 ? Math.round((option.votes / totalVotes) * 100) : 0;
            return (
              <div key={option.id} className="relative">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">{option.text}</span>
                  <span className="text-sm text-gray-600">{percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${option.color || 'bg-blue-500'} transition-all duration-1000`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                {selectedOption === option.id && (
                  <div className="absolute -right-2 -top-2">
                    <Check size={16} className="text-green-600 bg-white rounded-full" />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

const ROICalculator = ({ onCalculate }) => {
  const [monthlyRevenue, setMonthlyRevenue] = useState(10000);
  const [teamSize, setTeamSize] = useState(5);
  const [savingsPercent] = useState(25);

  const calculatedSavings = (monthlyRevenue * (savingsPercent / 100));
  const yearlyProfit = calculatedSavings * 12;

  useEffect(() => {
    onCalculate?.({ monthlyRevenue, teamSize, monthlyProfit: calculatedSavings, yearlyProfit });
  }, [monthlyRevenue, teamSize, calculatedSavings, yearlyProfit, onCalculate]);

  return (
    <div className="p-4 bg-gradient-to-br from-orange-50 to-yellow-50 rounded-lg border border-orange-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <Calculator size={20} className="text-orange-600" />
        ROI Calculator
      </h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Monthly Revenue: ${monthlyRevenue.toLocaleString()}
          </label>
          <input
            type="range"
            min="1000"
            max="100000"
            step="1000"
            value={monthlyRevenue}
            onChange={(e) => setMonthlyRevenue(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg cursor-pointer"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Team Size: {teamSize} people
          </label>
          <input
            type="range"
            min="1"
            max="50"
            value={teamSize}
            onChange={(e) => setTeamSize(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg cursor-pointer"
          />
        </div>

        <div className="bg-white p-4 rounded-lg border border-orange-200">
          <h4 className="font-semibold text-gray-800 mb-3">💰 Your Potential Savings</h4>
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-xl font-bold text-green-600">${calculatedSavings.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Monthly</div>
            </div>
            <div>
              <div className="text-xl font-bold text-blue-600">${yearlyProfit.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Yearly</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ProductShowcase = ({ products, onProductClick }) => {
  return (
    <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg border border-purple-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <ShoppingCart size={20} className="text-purple-600" />
        Our Products
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {products.map(product => (
          <div key={product.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
            <div className="p-4 text-center">
              <div className="text-2xl mb-2">{product.icon}</div>
              <h4 className="font-semibold text-gray-800 mb-2">{product.name}</h4>
              <div className="text-lg font-bold text-blue-600 mb-2">{product.price}</div>
              <ul className="text-xs text-gray-600 space-y-1 mb-3">
                {product.features.map((feature, idx) => (
                  <li key={idx} className="flex items-center gap-1 justify-center">
                    <Check size={10} className="text-green-500" />
                    {feature}
                  </li>
                ))}
              </ul>
              <button 
                onClick={() => onProductClick?.(product)}
                className="w-full py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
              >
                Learn More
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ImageGallery = ({ images, onImageClick }) => {
  return (
    <div className="p-4 bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg border border-indigo-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <Image size={20} className="text-indigo-600" />
        Gallery
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {images.map(image => (
          <div key={image.id} className="group cursor-pointer" onClick={() => onImageClick?.(image)}>
            <div className="relative overflow-hidden rounded-lg">
              <img 
                src={image.src} 
                alt={image.title}
                className="w-full h-24 object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity duration-300 flex items-center justify-center">
                <span className="text-white text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {image.title}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const TestimonialCard = ({ testimonial }) => {
  return (
    <div className="p-4 bg-gradient-to-br from-rose-50 to-orange-50 rounded-lg border border-rose-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <Heart size={20} className="text-rose-600" />
        Customer Story
      </h3>
      
      <div className="bg-white p-4 rounded-lg">
        <div className="text-center mb-3">
          <div className="text-3xl mb-2">{testimonial.avatar}</div>
          <blockquote className="text-gray-700 italic text-sm mb-2">
            "{testimonial.content}"
          </blockquote>
          <div className="flex justify-center mb-2">
            {[...Array(testimonial.rating)].map((_, i) => (
              <Star key={i} size={14} className="text-yellow-400 fill-current" />
            ))}
          </div>
          <cite className="text-xs text-gray-600">
            <strong>{testimonial.name}</strong><br/>
            {testimonial.role}
          </cite>
        </div>
      </div>
    </div>
  );
};

const CTAWidget = ({ cta, onAction }) => {
  return (
    <div className="p-4 bg-gradient-to-br from-emerald-50 to-cyan-50 rounded-lg border border-emerald-200">
      <div className="text-center">
        <h3 className="text-lg font-bold text-gray-800 mb-2">{cta.title}</h3>
        <p className="text-gray-600 mb-4 text-sm">{cta.description}</p>
        <div className="flex flex-wrap justify-center gap-2">
          {cta.actions.map((action, idx) => (
            <button
              key={idx}
              onClick={() => onAction?.(action)}
              className={`px-4 py-2 rounded-lg text-sm font-semibold transition-colors ${
                action.primary 
                  ? 'bg-blue-600 text-white hover:bg-blue-700' 
                  : 'border border-blue-600 text-blue-600 hover:bg-blue-50'
              }`}
            >
              {action.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

// Content Registry with all available content
export const CONTENT_REGISTRY = {
  // Games
  'game:tictactoe': {
    type: CONTENT_TYPES.GAME,
    component: TicTacToeGame,
    title: 'Tic-Tac-Toe Game',
    description: 'Classic game while we chat'
  },

  // Widgets
  'widget:poll': {
    type: CONTENT_TYPES.POLL,
    component: QuickPoll,
    title: 'Quick Poll',
    description: 'Interactive poll widget'
  },

  'widget:calculator': {
    type: CONTENT_TYPES.CALCULATOR,
    component: ROICalculator,
    title: 'ROI Calculator',
    description: 'Calculate potential savings'
  },

  // Product showcases
  'product:showcase': {
    type: CONTENT_TYPES.PRODUCT,
    component: ProductShowcase,
    title: 'Product Showcase',
    description: 'Display our products'
  },

  // Media
  'gallery:portfolio': {
    type: CONTENT_TYPES.GALLERY,
    component: ImageGallery,
    title: 'Image Gallery',
    description: 'Visual portfolio'
  },

  // Social proof
  'testimonial:customer': {
    type: CONTENT_TYPES.TESTIMONIAL,
    component: TestimonialCard,
    title: 'Customer Testimonial',
    description: 'Customer success story'
  },

  // Call to actions
  'cta:sales': {
    type: CONTENT_TYPES.CTA,
    component: CTAWidget,
    title: 'Call to Action',
    description: 'Sales conversion widget'
  }
};

// Helper function to get content component
export const getContentComponent = (contentId) => {
  return CONTENT_REGISTRY[contentId];
};

// Helper function to get available content by type
export const getContentByType = (type) => {
  return Object.entries(CONTENT_REGISTRY)
    .filter(([_, content]) => content.type === type)
    .map(([id, content]) => ({ id, ...content }));
};

// Pre-defined content data
export const SAMPLE_CONTENT = {
  poll_interests: {
    options: [
      { id: 1, text: "AI & Automation", votes: 45, color: "bg-blue-500" },
      { id: 2, text: "Green Technology", votes: 32, color: "bg-green-500" },
      { id: 3, text: "Data Analytics", votes: 38, color: "bg-purple-500" },
      { id: 4, text: "Cloud Solutions", votes: 28, color: "bg-orange-500" }
    ]
  },

  products_showcase: [
    { 
      id: 1, 
      name: "ABN Smart Analytics", 
      price: "$299/mo", 
      icon: "🤖", 
      features: ["AI-Powered", "Real-time", "Custom Reports"] 
    },
    { 
      id: 2, 
      name: "Green Energy Solutions", 
      price: "$199/mo", 
      icon: "🌱", 
      features: ["Eco-Friendly", "Cost Saving", "24/7 Support"] 
    },
    { 
      id: 3, 
      name: "Automation Suite", 
      price: "$499/mo", 
      icon: "⚡", 
      features: ["Full Automation", "Integration Ready", "Scalable"] 
    }
  ],

  portfolio_images: [
    { id: 1, src: "https://picsum.photos/300/200?random=1", title: "Smart Office Solutions" },
    { id: 2, src: "https://picsum.photos/300/200?random=2", title: "Green Technology" },
    { id: 3, src: "https://picsum.photos/300/200?random=3", title: "Team Collaboration" },
    { id: 4, src: "https://picsum.photos/300/200?random=4", title: "Innovation Hub" },
    { id: 5, src: "https://picsum.photos/300/200?random=5", title: "Customer Success" },
    { id: 6, src: "https://picsum.photos/300/200?random=6", title: "Future Vision" }
  ],

  testimonial_sample: {
    name: "Sarah Johnson",
    role: "CEO, TechStart Inc",
    content: "ABN Asia transformed our business operations. 40% cost reduction in just 6 months!",
    rating: 5,
    avatar: "👩‍💼"
  },

  cta_sales: {
    title: "Ready to Transform Your Business?",
    description: "Join thousands of companies already saving time and money with our solutions",
    actions: [
      { label: "Start Free Trial", primary: true, type: "trial" },
      { label: "Schedule Demo", primary: false, type: "demo" }
    ]
  }
};

export default CONTENT_REGISTRY; 