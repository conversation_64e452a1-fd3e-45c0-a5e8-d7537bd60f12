// components/chat/FloatingChat.jsx
'use client';
import React, { useState } from 'react';
import { MessageCircle, Maximize2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ChatCore } from './ChatCore';
import { ChatHeader } from './ChatHeader';
import { StyledChatBox, FloatingButton, ChatButton } from './StyledChatComponents';

const FloatingChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const router = useRouter();

  const toggleChat = () => {
    setIsOpen(!isOpen);
    setIsMinimized(false);
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const goToFullChat = () => {
    router.push('/web/abnasia/chat');
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isOpen && (
        <StyledChatBox isMinimized={isMinimized}>
          <ChatHeader 
            showHistory={showHistory}
            onToggleHistory={() => setShowHistory(!showHistory)}
            isMinimized={isMinimized}
            onToggleMinimize={toggleMinimize}
            onClose={toggleChat}
          />
          
          {!isMinimized && (
            <>
              <div className="px-4 py-2 border-b border-blue-500/30">
                <ChatButton 
                  onClick={goToFullChat}
                  className="w-full flex items-center justify-center gap-2 text-sm"
                >
                  <Maximize2 size={16} />
                  Open Full Chat
                </ChatButton>
              </div>
              <ChatCore />
            </>
          )}
        </StyledChatBox>
      )}

      {!isOpen && (
        <FloatingButton onClick={toggleChat}>
          <MessageCircle size={24} />
        </FloatingButton>
      )}
    </div>
  );
};

export default FloatingChat;