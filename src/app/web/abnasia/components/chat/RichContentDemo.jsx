import React from 'react';
import { Gamepad2, Calculator, ShoppingCart, Image, Heart, Target, Zap } from 'lucide-react';
import { contentInjectionService } from './ContentInjectionService';

const RichContentDemo = ({ onSendRichContent }) => {
  const demoActions = [
    {
      id: 'game',
      title: 'Send Game',
      icon: Gamepad2,
      color: 'bg-blue-500',
      action: () => onSendRichContent(contentInjectionService.createGameMessage())
    },
    {
      id: 'calculator',
      title: 'Send Calculator',
      icon: Calculator,
      color: 'bg-orange-500',
      action: () => onSendRichContent(contentInjectionService.createCalculatorMessage())
    },
    {
      id: 'products',
      title: 'Send Products',
      icon: ShoppingCart,
      color: 'bg-purple-500',
      action: () => onSendRichContent(contentInjectionService.createProductMessage())
    },
    {
      id: 'gallery',
      title: 'Send Gallery',
      icon: Image,
      color: 'bg-indigo-500',
      action: () => onSendRichContent(contentInjectionService.createGalleryMessage())
    },
    {
      id: 'testimonial',
      title: 'Send Testimonial',
      icon: Heart,
      color: 'bg-rose-500',
      action: () => onSendRichContent(contentInjectionService.createTestimonialMessage())
    },
    {
      id: 'poll',
      title: 'Send Poll',
      icon: Target,
      color: 'bg-green-500',
      action: () => onSendRichContent(contentInjectionService.createPollMessage())
    },
    {
      id: 'cta',
      title: 'Send CTA',
      icon: Zap,
      color: 'bg-emerald-500',
      action: () => onSendRichContent(contentInjectionService.createCTAMessage())
    }
  ];

  return (
    <div className="p-4 bg-gray-100 border-t border-gray-200">
      {/* <h3 className="text-sm font-semibold text-gray-700 mb-3">🎛️ Rich Content Demo</h3> */}
      <div className="grid grid-cols-4 gap-2">
        {demoActions.map(action => (
          <button
            key={action.id}
            onClick={action.action}
            className={`p-2 ${action.color} text-white rounded-lg hover:opacity-90 transition-opacity flex flex-col items-center gap-1`}
          >
            <action.icon size={16} />
            <span className="text-xs">{action.title}</span>
          </button>
        ))}
      </div>
      {/* <p className="text-xs text-gray-500 mt-2">
        Click buttons to manually send rich content, or use keywords like "game", "calculator", "products" in your messages
      </p> */}
    </div>
  );
};

export default RichContentDemo; 