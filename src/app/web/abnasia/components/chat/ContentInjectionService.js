import { CONTENT_REGISTRY, SAMPLE_CONTENT } from './ContentRegistry';

/**
 * Content Injection Service
 * Handles the creation and injection of rich content into chat messages
 * This service can be used by the AI to send interactive content based on user requests
 */

class ContentInjectionService {
  
  /**
   * Create a rich message with interactive content
   * @param {string} textContent - Optional text content
   * @param {string} contentId - Content ID from registry
   * @param {Object} data - Content-specific data
   * @returns {Object} Rich message object
   */
  createRichMessage(textContent = '', contentId, data = {}) {
    return {
      content: textContent,
      sender: 'assistant',
      timestamp: new Date().toISOString(),
      richContent: {
        type: CONTENT_REGISTRY[contentId]?.type,
        contentId,
        data
      }
    };
  }

  /**
   * Create a game message
   */
  createGameMessage(gameType = 'tictactoe', textContent = '') {
    const contentId = `game:${gameType}`;
    return this.createRichMessage(
      textContent || "Let's play a quick game! 🎮",
      contentId
    );
  }

  /**
   * Create a poll message
   */
  createPollMessage(options = null, textContent = '') {
    return this.createRichMessage(
      textContent || "I'd love to know your interests! Please vote:",
      'widget:poll',
      { options: options || SAMPLE_CONTENT.poll_interests.options }
    );
  }

  /**
   * Create a calculator message
   */
  createCalculatorMessage(textContent = '') {
    return this.createRichMessage(
      textContent || "Let's calculate your potential ROI with our solutions:",
      'widget:calculator'
    );
  }

  /**
   * Create a product showcase message
   */
  createProductMessage(products = null, textContent = '') {
    return this.createRichMessage(
      textContent || "Here are our main products and solutions:",
      'product:showcase',
      { products: products || SAMPLE_CONTENT.products_showcase }
    );
  }

  /**
   * Create an image gallery message
   */
  createGalleryMessage(images = null, textContent = '') {
    return this.createRichMessage(
      textContent || "Here's a visual showcase of our work:",
      'gallery:portfolio',
      { images: images || SAMPLE_CONTENT.portfolio_images }
    );
  }

  /**
   * Create a testimonial message
   */
  createTestimonialMessage(testimonial = null, textContent = '') {
    return this.createRichMessage(
      textContent || "Here's what one of our customers says:",
      'testimonial:customer',
      { testimonial: testimonial || SAMPLE_CONTENT.testimonial_sample }
    );
  }

  /**
   * Create a call-to-action message
   */
  createCTAMessage(cta = null, textContent = '') {
    return this.createRichMessage(
      textContent || "Ready to get started?",
      'cta:sales',
      { cta: cta || SAMPLE_CONTENT.cta_sales }
    );
  }

  /**
   * Analyze user input and suggest appropriate content
   * This can be used by the AI to automatically determine what content to send
   */
  analyzeUserIntent(userMessage) {
    const message = userMessage.toLowerCase();
    const suggestions = [];

    // Game keywords
    if (message.includes('game') || message.includes('play') || message.includes('fun') || 
        message.includes('entertainment') || message.includes('tic tac toe')) {
      suggestions.push({
        type: 'game',
        contentId: 'game:tictactoe',
        reason: 'User mentioned games or entertainment',
        priority: 'high'
      });
    }

    // Calculator keywords
    if (message.includes('calculate') || message.includes('roi') || message.includes('savings') ||
        message.includes('cost') || message.includes('price') || message.includes('money')) {
      suggestions.push({
        type: 'calculator',
        contentId: 'widget:calculator',
        reason: 'User mentioned calculations or financial terms',
        priority: 'high'
      });
    }

    // Product keywords
    if (message.includes('product') || message.includes('service') || message.includes('solution') ||
        message.includes('what do you') || message.includes('what can') || message.includes('offer')) {
      suggestions.push({
        type: 'product',
        contentId: 'product:showcase',
        reason: 'User asked about products or services',
        priority: 'high'
      });
    }

    // Visual/gallery keywords
    if (message.includes('show') || message.includes('see') || message.includes('picture') ||
        message.includes('image') || message.includes('visual') || message.includes('gallery') ||
        message.includes('portfolio')) {
      suggestions.push({
        type: 'gallery',
        contentId: 'gallery:portfolio',
        reason: 'User requested visual content',
        priority: 'medium'
      });
    }

    // Poll/survey keywords
    if (message.includes('poll') || message.includes('survey') || message.includes('vote') ||
        message.includes('opinion') || message.includes('prefer')) {
      suggestions.push({
        type: 'poll',
        contentId: 'widget:poll',
        reason: 'User mentioned polls or preferences',
        priority: 'medium'
      });
    }

    // Testimonial keywords
    if (message.includes('customer') || message.includes('review') || message.includes('testimonial') ||
        message.includes('feedback') || message.includes('experience') || message.includes('success')) {
      suggestions.push({
        type: 'testimonial',
        contentId: 'testimonial:customer',
        reason: 'User asked about customer experiences',
        priority: 'medium'
      });
    }

    // CTA keywords
    if (message.includes('start') || message.includes('begin') || message.includes('trial') ||
        message.includes('demo') || message.includes('contact') || message.includes('buy') ||
        message.includes('purchase') || message.includes('ready')) {
      suggestions.push({
        type: 'cta',
        contentId: 'cta:sales',
        reason: 'User expressed interest in getting started',
        priority: 'high'
      });
    }

    return suggestions.sort((a, b) => {
      const priority = { 'high': 3, 'medium': 2, 'low': 1 };
      return priority[b.priority] - priority[a.priority];
    });
  }

  /**
   * Create content based on user intent analysis
   */
  createContentFromIntent(userMessage, suggestions = null) {
    const intents = suggestions || this.analyzeUserIntent(userMessage);
    
    if (intents.length === 0) {
      return null; // No rich content suggestions, use regular text response
    }

    const topIntent = intents[0];
    
    switch(topIntent.contentId) {
      case 'game:tictactoe':
        return this.createGameMessage();
      
      case 'widget:calculator':
        return this.createCalculatorMessage();
      
      case 'product:showcase':
        return this.createProductMessage();
      
      case 'gallery:portfolio':
        return this.createGalleryMessage();
      
      case 'widget:poll':
        return this.createPollMessage();
      
      case 'testimonial:customer':
        return this.createTestimonialMessage();
      
      case 'cta:sales':
        return this.createCTAMessage();
      
      default:
        return null;
    }
  }

  /**
   * Create multiple content messages for complex responses
   */
  createMultiContentResponse(userMessage) {
    const intents = this.analyzeUserIntent(userMessage);
    const messages = [];

    // If user asks general questions, show multiple types
    if (userMessage.toLowerCase().includes('everything') || 
        userMessage.toLowerCase().includes('all') ||
        userMessage.toLowerCase().includes('overview')) {
      
      messages.push(this.createProductMessage());
      messages.push(this.createCalculatorMessage("Want to see potential savings?"));
      messages.push(this.createTestimonialMessage("Here's what our customers say:"));
      messages.push(this.createCTAMessage());
      
      return messages;
    }

    // Return single content based on highest priority intent
    const content = this.createContentFromIntent(userMessage, intents);
    return content ? [content] : [];
  }

  /**
   * Get available content types
   */
  getAvailableContentTypes() {
    return Object.keys(CONTENT_REGISTRY).map(contentId => ({
      contentId,
      ...CONTENT_REGISTRY[contentId]
    }));
  }

  /**
   * Create custom content with specific parameters
   */
  createCustomContent(contentId, textContent, customData) {
    if (!CONTENT_REGISTRY[contentId]) {
      throw new Error(`Unknown content ID: ${contentId}`);
    }

    return this.createRichMessage(textContent, contentId, customData);
  }
}

// Export singleton instance
export const contentInjectionService = new ContentInjectionService();

// Export class for testing or custom instances
export default ContentInjectionService; 