import React, { useState, useRef, useEffect } from 'react';
import { Send, User, LogIn, Database, FileText, Check, X, ChevronDown, ChevronUp, Bot, Search, Target } from 'lucide-react';
import { chatApi } from './chatApi';
import { jsonFilesApi } from './jsonFilesApi';
import RagService from './rag/RagService';
import RagManager from './rag/RagManager';
import JsonGoalsService from '../../admin/chat/services/JsonGoalsService';
import GoalsManager from './goals/GoalsManager';
import GoalIndicator from './goals/GoalIndicator';
import avatarsData from '../../../../aikols/avatars.json';
import { 
  ChatMessage,
  ChatInput,
  ChatButton
} from './StyledChatComponents';
import MarkdownRenderer from './MarkdownRenderer';
import RichChatMessage from './RichChatMessage';
import { contentInjectionService } from './ContentInjectionService';
import RichContentDemo from './RichContentDemo';

export const ChatCore = ({ 
  initialMessage = "Hello! I'm AbnAsia. How can I help you today?",
  className = "",
  containerClassName = "",
  selectedDocuments = new Set(),
  availableDocuments = [],
  onUserIdChange = (userId) => {},
  onAvatarChange = (avatar) => {},
  initialAvatar = null,
  enableRAG = true
}) => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState('');
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [hasUserSentMessage, setHasUserSentMessage] = useState(false);
  const [userId, setUserId] = useState('');
  const [userIdInput, setUserIdInput] = useState('');
  const [showUserIdInput, setShowUserIdInput] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [showRagManager, setShowRagManager] = useState(false);
  const [showGoalsManager, setShowGoalsManager] = useState(false);
  const [ragService] = useState(() => new RagService());
  const [goalsService] = useState(() => new JsonGoalsService());
  const [ragEnabled, setRagEnabled] = useState(enableRAG);
  
  // Proactive messaging state
  const [conversationContext, setConversationContext] = useState({
    conversionSignals: [],
    businessContext: {},
    sentiment: 'neutral',
    conversationFlow: 'discovery',
    engagementLevel: 0,
    lastInteraction: null,
    proactiveOpportunities: []
  });
  const [proactiveTimer, setProactiveTimer] = useState(null);
  const [isProactiveModeActive, setIsProactiveModeActive] = useState(false);
  
  // Document selection state is now managed by parent
  
  // Avatar/Character selection state
  const [selectedAvatar, setSelectedAvatar] = useState(initialAvatar);
  const [showAvatarSelector, setShowAvatarSelector] = useState(false);
  const [avatarSearchQuery, setAvatarSearchQuery] = useState('');
  const [avatarCategoryFilter, setAvatarCategoryFilter] = useState('');
  
  const messagesContainerRef = useRef(null);
  const messagesEndRef = useRef(null);

  // Get available avatars and categories
  const availableAvatars = avatarsData.avatars || [];
  const availableCategories = [...new Set(availableAvatars.map(avatar => avatar.category))].sort();

  // Proactive messaging functions
  const shouldBeProactive = async (messages, userContext) => {
    if (!userId || messages.length === 0) return { beProactive: false };

    const conversationAnalysis = {
      messageCount: messages.length,
      lastUserMessage: messages.filter(m => m.sender === 'user').slice(-1)[0]?.content || '',
      recentMessages: messages.slice(-5).map(m => `${m.sender}: ${m.content}`).join('\n'),
      conversionSignals: userContext.conversionSignals || [],
      businessContext: userContext.businessContext || {},
      sentiment: userContext.sentiment || 'neutral',
      timeSinceLastMessage: userContext.lastInteraction ? 
        Math.floor((Date.now() - new Date(userContext.lastInteraction).getTime()) / 1000) : 0,
      engagementLevel: userContext.engagementLevel || 0
    };

    const proactivePrompt = `
As an intelligent sales assistant, analyze this conversation context and decide if you should proactively send a follow-up message:

CONVERSATION ANALYSIS:
- Messages exchanged: ${conversationAnalysis.messageCount}
- Last user message: "${conversationAnalysis.lastUserMessage}"
- Recent conversation:
${conversationAnalysis.recentMessages}
- Conversion signals detected: ${conversationAnalysis.conversionSignals.join(', ') || 'none'}
- Business context: ${JSON.stringify(conversationAnalysis.businessContext)}
- User sentiment: ${conversationAnalysis.sentiment}
- Time since last message: ${conversationAnalysis.timeSinceLastMessage} seconds
- Engagement level: ${conversationAnalysis.engagementLevel}/10

DECISION CRITERIA:
- Be proactive when you detect buying signals, hesitation, or opportunities to add value
- Consider optimal timing (not too pushy, not too passive)
- Focus on sales conversion, lead qualification, and customer success
- Don't be proactive for simple informational queries that seem complete

Respond with JSON only:
{
  "beProactive": boolean,
  "reasoning": "brief explanation",
  "timing": "immediate|short_delay|scheduled",
  "messageType": "follow_up|clarification|value_add|conversion_nudge|support_check"
}`;

    try {
      const response = await chatApi.streamCompletion({
        messages: [{ role: 'user', content: proactivePrompt }],
        onToken: () => {},
        onError: () => ({ beProactive: false }),
        onFinish: (result) => {
          try {
            return JSON.parse(result.replace(/```json\n?|\n?```/g, ''));
          } catch {
            return { beProactive: false };
          }
        }
      });

      return new Promise((resolve) => {
        chatApi.streamCompletion({
          messages: [{ role: 'user', content: proactivePrompt }],
          onToken: () => {},
          onError: () => resolve({ beProactive: false }),
          onFinish: (result) => {
            try {
              const decision = JSON.parse(result.replace(/```json\n?|\n?```/g, ''));
              resolve(decision);
            } catch {
              resolve({ beProactive: false });
            }
          }
        });
      });
    } catch (error) {
      console.error('Error in proactive decision making:', error);
      return { beProactive: false };
    }
  };

  const generateProactiveMessage = async (decision, conversationContext) => {
    const messagePrompt = `
Generate a natural, contextual follow-up message for this sales conversation:

CONTEXT:
- Decision type: ${decision.messageType}
- Reasoning: ${decision.reasoning}
- User ID: ${userId}
- Conversation signals: ${conversationContext.conversionSignals.join(', ') || 'none'}
- Business context: ${JSON.stringify(conversationContext.businessContext)}
- Sentiment: ${conversationContext.sentiment}

GUIDELINES:
- Be natural and conversational, not pushy
- Add genuine value or move the sales process forward
- Keep it concise (1-2 sentences max)
- Match the tone of previous conversation
- Focus on helping the customer make progress

Generate only the message text (no JSON, no quotes, no formatting):`;

    try {
      return new Promise((resolve) => {
        chatApi.streamCompletion({
          messages: [{ role: 'user', content: messagePrompt }],
          onToken: () => {},
          onError: () => resolve("Is there anything else I can help clarify for you?"),
          onFinish: (result) => {
            const cleanMessage = result.replace(/^["']|["']$/g, '').trim();
            resolve(cleanMessage || "Is there anything else I can help clarify for you?");
          }
        });
      });
    } catch (error) {
      console.error('Error generating proactive message:', error);
      return "Is there anything else I can help clarify for you?";
    }
  };

  const scheduleProactiveMessage = async (message, timing) => {
    const delays = {
      immediate: 1000,
      short_delay: 5000,
      scheduled: 15000
    };

    const delay = delays[timing] || delays.short_delay;

    if (proactiveTimer) {
      clearTimeout(proactiveTimer);
    }

    const timer = setTimeout(async () => {
      const proactiveMessage = {
        content: `🧠 ${message}`,
        sender: selectedAvatar ? selectedAvatar.name.en : 'AbnAsia.org',
        timestamp: new Date().toISOString(),
        isProactive: true
      };

      setMessages(prev => [...prev, proactiveMessage]);
      
      // Track proactive message analytics
      if (userId) {
        try {
          await goalsService.trackProactiveAnalytics(userId, {
            message: message,
            timing: timing,
            context: conversationContext,
            deliveredAt: new Date().toISOString()
          });
        } catch (error) {
          console.error('Failed to track proactive analytics:', error);
        }
      }
    }, delay);

    setProactiveTimer(timer);
  };

  const handleProactiveDecision = async () => {
    if (!userId || messages.length === 0) {
      setIsProactiveModeActive(false);
      return;
    }

    try {
      setIsProactiveModeActive(true);
      const decision = await shouldBeProactive(messages, conversationContext);
      
      if (decision.beProactive) {
        const proactiveMessage = await generateProactiveMessage(decision, conversationContext);
        await scheduleProactiveMessage(proactiveMessage, decision.timing);
      }
    } catch (error) {
      console.error('Error in proactive decision process:', error);
      setIsProactiveModeActive(false);
    }
  };
  
  // Filter avatars based on search and category
  const filteredAvatars = availableAvatars.filter(avatar => {
    const matchesSearch = !avatarSearchQuery || 
      avatar.name.en.toLowerCase().includes(avatarSearchQuery.toLowerCase()) ||
      avatar.oneliner.toLowerCase().includes(avatarSearchQuery.toLowerCase()) ||
      avatar.category.toLowerCase().includes(avatarSearchQuery.toLowerCase());
    
    const matchesCategory = !avatarCategoryFilter || avatar.category === avatarCategoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  useEffect(() => {
    // Load initial message or user's chat history
    if (userId) {
      loadUserChatHistory();
      setIsProactiveModeActive(true); // Enable proactive mode when user is identified
    } else {
      setMessages([
        {
          content: getInitialMessage(),
          sender: selectedAvatar ? selectedAvatar.name.en : 'AbnAsia.org',
          timestamp: new Date().toISOString()
        }
      ]);
      setIsProactiveModeActive(false); // Disable proactive mode when no user
    }
  }, [initialMessage, userId, selectedAvatar]);

  const getInitialMessage = () => {
    if (selectedAvatar) {
      return `Hello! I'm ${selectedAvatar.name.en}. ${selectedAvatar.oneliner}. How can I help you today?`;
    }
    return initialMessage;
  };

  const getSystemPrompt = async () => {
    // Chat mode settings
    const chatMode = localStorage.getItem('abnChatMode') || 'conversational'; // 'conversational' or 'detailed'
    
    let conversationalModifier = '';
    if (chatMode === 'conversational') {
      conversationalModifier = `
CONVERSATION STYLE: Keep responses SHORT and CONVERSATIONAL like texting with a friend. Use:
- 1-2 sentences maximum for most responses
- Casual, friendly tone
- Ask follow-up questions to keep conversation flowing
- Use emojis occasionally
- Avoid long explanations unless specifically requested
- Think "quick chat" not "detailed report"`;
    }

    let basePrompt = `You are ABNCopilot, a smart, inquisitive and helpful personal assistant. You were created by AbnAsia.org. More information about you, the company or ABN apps can be found at https://abnasia.org. About ABN Asia: Ai BASE NETWORK [ABN], ABN ASIA was founded by people with deep roots in academia, with work experience in the US, Holland, Hungary, Japan, South Korea, Singapore, and Vietnam. ABN Asia is where academia and technology meet opportunity. With our cutting-edge solutions and competent software development services, we are re helping businesses level up and take on the global scene. Our commitment: Faster. Better. More reliable. In most cases: Cheaper as well.${conversationalModifier}

INTERACTIVE FEATURES: You can enhance conversations by mentioning that you can send interactive content:
- Games (like Tic-Tac-Toe) when users want entertainment or to pass time
- ROI Calculators when discussing costs, savings, or pricing
- Product showcases when users ask about services or solutions
- Image galleries to show visual examples of work
- Customer testimonials for social proof
- Interactive polls to understand preferences

When appropriate, mention these capabilities to create a more engaging experience. The system will automatically detect keywords and send relevant interactive content alongside your responses.

Feel free to reach out to us whenever you require IT services, digital consulting, off-the-shelf software solutions, or if you would like to send us requests for proposals (RFPs). You can contact us at +84945924877 (Asia# Mobile, WhatsApp, Telegram, Viber, Zalo); +*********** (US# Mobile, WhatsApp, Telegram) <EMAIL> . We are ready to assist you with all your technology needs.`;

    if (selectedAvatar) {
      basePrompt = `You are now roleplaying as ${selectedAvatar.name.en}. ${selectedAvatar.oneliner}. You should respond in character, drawing from this person's known expertise, personality, and communication style. Maintain their perspective and approach to problem-solving while being helpful and engaging. You are part of the AbnAsia.org platform.

INTERACTIVE FEATURES: You can enhance conversations by mentioning that you can send interactive content like games, calculators, product showcases, galleries, and more to create engaging experiences.`;
    }

    if (userId) {
      basePrompt += ` The user you are talking to has identified themselves as: ${userId}.`;
      
      // Add goals-based modifications to the system prompt
      try {
        const goalPromptModification = await goalsService.generateGoalPromptModification(userId);
        if (goalPromptModification) {
          basePrompt += goalPromptModification;
        }
      } catch (error) {
        console.error('Failed to get goal prompt modification:', error);
      }
    }

    return basePrompt;
  };

  // Document selection is now handled by parent component

  // Handle avatar selection
  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar);
    setShowAvatarSelector(false);
    onAvatarChange(avatar); // Notify parent
    
    // Update initial message if no conversation has started
    if (!hasUserSentMessage) {
      setMessages([
        {
          content: `Hello! I'm ${avatar.name.en}. ${avatar.oneliner}. How can I help you today?`,
          sender: avatar.name.en,
          timestamp: new Date().toISOString()
        }
      ]);
    }
  };

  const resetToDefault = () => {
    setSelectedAvatar(null);
    setShowAvatarSelector(false);
    onAvatarChange(null); // Notify parent
    
    // Update initial message if no conversation has started
    if (!hasUserSentMessage) {
      setMessages([
        {
          content: initialMessage,
          sender: 'AbnAsia.org',
          timestamp: new Date().toISOString()
        }
      ]);
    }
  };

  const loadUserChatHistory = async () => {
    if (!userId) return;
    
    setIsLoadingHistory(true);
    try {
      const response = await jsonFilesApi.getChatHistory({ 
        userId: userId,
        limit: 1,
        sortOrder: 'desc'
      });
      
      if (response.chats && response.chats.length > 0) {
        // Load the most recent chat for this user
        const lastChat = response.chats[0];
        setMessages(lastChat.messages || []);
        setHasUserSentMessage(lastChat.messages.some(msg => msg.sender === 'user'));
      } else {
        // No previous chats, show welcome message with user's name
        const welcomeMessage = selectedAvatar 
          ? `Hello ${userId}! I'm ${selectedAvatar.name.en}. ${selectedAvatar.oneliner}. How can I help you today?`
          : `Hello ${userId}! I'm AbnAsia. How can I help you today?`;
        
        setMessages([
          {
            content: welcomeMessage,
            sender: selectedAvatar ? selectedAvatar.name.en : 'AbnAsia.org',
            timestamp: new Date().toISOString()
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load user chat history:', error);
      const welcomeMessage = selectedAvatar 
        ? `Hello ${userId}! I'm ${selectedAvatar.name.en}. ${selectedAvatar.oneliner}. How can I help you today?`
        : `Hello ${userId}! I'm AbnAsia. How can I help you today?`;
      
      setMessages([
        {
          content: welcomeMessage,
          sender: selectedAvatar ? selectedAvatar.name.en : 'AbnAsia.org',
          timestamp: new Date().toISOString()
        }
      ]);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const handleUserIdSubmit = () => {
    if (userIdInput.trim()) {
      setUserId(userIdInput.trim());
      onUserIdChange(userIdInput.trim()); // Notify parent
      setShowUserIdInput(false);
      setUserIdInput('');
    }
  };

  const handleUserIdChange = () => {
    setShowUserIdInput(true);
    setUserIdInput(userId);
  };

  const isNearBottom = () => {
    const container = messagesContainerRef.current;
    if (!container) return true;
    
    const threshold = 100; // pixels from bottom
    const bottomPosition = container.scrollHeight - container.scrollTop - container.clientHeight;
    return bottomPosition <= threshold;
  };

  const handleScroll = () => {
    setShouldAutoScroll(isNearBottom());
  };

  const scrollToBottom = () => {
    if (shouldAutoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  };

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  useEffect(() => {
    // Only auto-scroll if user has sent at least one message
    const lastMessage = messages[messages.length - 1];
    if (hasUserSentMessage && (lastMessage?.sender === 'user' || shouldAutoScroll)) {
      scrollToBottom();
    }
  }, [messages, streamingMessage, hasUserSentMessage]);

  
  const saveChatToHistory = async (messages) => {
    try {
      await jsonFilesApi.saveChatToHistory(messages, null, { userId: userId || 'anonymous' });
    } catch (error) {
      console.error('Failed to save chat:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;
    
    // Set the flag when user sends their first message
    setHasUserSentMessage(true);
    
    const userMessage = {
      content: inputMessage,
      sender: 'user',
      timestamp: new Date().toISOString()
    };
    
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setInputMessage('');
    setIsLoading(true);
    setStreamingMessage('');

    // Get RAG context for the user's message with selected documents
    let ragContext = '';
    if (ragEnabled && selectedDocuments.size > 0) {
      try {
        const contextResult = await ragService.generateChatContext(inputMessage, userId, {
          selectedDocuments: Array.from(selectedDocuments),
          limit: 3,
          minSimilarity: 0.1,
          includePublic: true,
          includeChatHistory: true
        });
        if (contextResult.hasContext) {
          ragContext = `\n\nRelevant Context:\n${contextResult.context}`;
        }
      } catch (error) {
        console.error('Error getting RAG context:', error);
      }
    }

    const systemPrompt = await getSystemPrompt() + (ragContext ? `\n\nAdditional context from knowledge base and previous conversations: ${ragContext}` : '');

    const apiMessages = [
      {
        role: 'system',
        content: systemPrompt
      },
      ...updatedMessages.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: typeof msg.content === 'string' ? msg.content : String(msg.content || '')
      }))
    ];

    try {
      await chatApi.streamCompletion({
        messages: apiMessages,
        onToken: (accumulatedMessage) => {
          setStreamingMessage(accumulatedMessage);
        },
        onError: (error) => {
          const errorMessage = {
            content: "I apologize, but I'm having trouble connecting right now. Please try again later.",
            sender: selectedAvatar ? selectedAvatar.name.en : 'AbnAsia.org',
            timestamp: new Date().toISOString()
          };
          setMessages(prev => [...prev, errorMessage]);
          setIsLoading(false);
          saveChatToHistory([...updatedMessages, errorMessage]);
        },
        onFinish: async (finalMessage) => {
          const botMessage = {
            content: finalMessage,
            sender: selectedAvatar ? selectedAvatar.name.en : 'AbnAsia.org',
            timestamp: new Date().toISOString()
          };
          
          // Check if we should send rich content instead of or in addition to text
          const richContentSuggestions = contentInjectionService.analyzeUserIntent(inputMessage);
          let finalMessages = [...updatedMessages, botMessage];
          
          // If there are high-priority rich content suggestions, add them
          if (richContentSuggestions.length > 0 && richContentSuggestions[0].priority === 'high') {
            const richContent = contentInjectionService.createContentFromIntent(inputMessage, richContentSuggestions);
            if (richContent && typeof richContent === 'object' && richContent.content !== undefined) {
              // Ensure content is properly formatted before adding
              const validatedRichContent = {
                ...richContent,
                content: typeof richContent.content === 'string' ? richContent.content : String(richContent.content || ''),
                sender: richContent.sender || 'assistant',
                timestamp: richContent.timestamp || new Date().toISOString()
              };
              
              // Add a small delay before sending rich content
              setTimeout(() => {
                setMessages(prev => [...prev, validatedRichContent]);
              }, 1500);
            }
          }
          
          setMessages(finalMessages);
          setStreamingMessage('');
          setIsLoading(false);
          await saveChatToHistory(finalMessages);
          
          // Track goal progress and update conversation context
          if (userId) {
            const conversionSignals = extractConversionSignals(inputMessage);
            const businessContext = extractBusinessContext(inputMessage);
            const sentiment = analyzeSentiment(finalMessage);
            const goalIndicators = extractGoalIndicators(finalMessage);
            
            try {
              await goalsService.trackGoalProgress(userId, {
                userMessage: inputMessage,
                botMessage: finalMessage,
                sentiment: sentiment,
                goalIndicators: goalIndicators,
                conversionSignals: conversionSignals,
                businessContext: businessContext,
                conversationFlow: {
                  messageCount: finalMessages.length,
                  userInitiated: hasUserSentMessage,
                  timestamp: new Date().toISOString()
                }
              });

              // Update conversation context for proactive decisions
              setConversationContext(prev => ({
                ...prev,
                conversionSignals: [...new Set([...prev.conversionSignals, ...conversionSignals])],
                businessContext: { ...prev.businessContext, ...businessContext },
                sentiment: sentiment,
                conversationFlow: determineConversationFlow(finalMessages),
                engagementLevel: calculateEngagementLevel(finalMessages),
                lastInteraction: new Date().toISOString(),
                proactiveOpportunities: identifyProactiveOpportunities(inputMessage, finalMessage)
              }));

              // Make proactive decision after updating context
              setTimeout(() => handleProactiveDecision(), 2000);
            } catch (error) {
              console.error('Failed to track goal progress:', error);
            }
          }
        }
      });
    } catch (error) {
      console.error('Failed to process message:', error);
      const errorMessage = {
        content: "An error occurred while processing your message. Please try again.",
        sender: selectedAvatar ? selectedAvatar.name.en : 'AbnAsia.org',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
      setIsLoading(false);
    }
  };

  // Helper functions for goal tracking
  const analyzeSentiment = (message) => {
    const positiveWords = ['good', 'great', 'excellent', 'perfect', 'love', 'like', 'awesome', 'wonderful', 'fantastic', 'amazing', 'helpful', 'thanks', 'thank you', 'satisfied', 'pleased', 'happy', 'appreciate', 'impressed'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'horrible', 'angry', 'frustrated', 'annoyed', 'disappointed', 'useless', 'waste', 'confusing', 'difficult'];
    
    const lowerMessage = message.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerMessage.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerMessage.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  };

  const extractGoalIndicators = (message) => {
    const indicators = [];
    const lowerMessage = message.toLowerCase();
    
    // Sales conversion indicators  
    if (lowerMessage.includes('buy') || lowerMessage.includes('purchase') || lowerMessage.includes('order') || 
        lowerMessage.includes('get started') || lowerMessage.includes('sign up')) {
      indicators.push('conversion_intent');
    }
    
    // Pricing/budget indicators
    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('budget') ||
        lowerMessage.includes('how much') || lowerMessage.includes('expensive') || lowerMessage.includes('affordable')) {
      indicators.push('pricing_interest');
    }
    
    // Product engagement indicators
    if (lowerMessage.includes('tell me more') || lowerMessage.includes('interested') || 
        lowerMessage.includes('learn more') || lowerMessage.includes('details') || lowerMessage.includes('features')) {
      indicators.push('product_engagement');
    }
    
    // Support satisfaction indicators
    if (lowerMessage.includes('helpful') || lowerMessage.includes('solved') || lowerMessage.includes('fixed') ||
        lowerMessage.includes('working') || lowerMessage.includes('resolved')) {
      indicators.push('support_success');
    }

    // Contact/demo intent
    if (lowerMessage.includes('demo') || lowerMessage.includes('call') || lowerMessage.includes('meeting') ||
        lowerMessage.includes('contact') || lowerMessage.includes('speak to') || lowerMessage.includes('sales')) {
      indicators.push('contact_intent');
    }

    // Decision-making indicators
    if (lowerMessage.includes('decide') || lowerMessage.includes('consider') || lowerMessage.includes('think about') ||
        lowerMessage.includes('compare') || lowerMessage.includes('evaluate')) {
      indicators.push('decision_phase');
    }
    
    return indicators;
  };

  const extractConversionSignals = (userMessage) => {
    const signals = [];
    const lowerMessage = userMessage.toLowerCase();
    
    // Strong conversion signals (high intent)
    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('how much') ||
        lowerMessage.includes('pricing') || lowerMessage.includes('quote')) {
      signals.push('pricing_inquiry');
    }
    
    if (lowerMessage.includes('demo') || lowerMessage.includes('trial') || lowerMessage.includes('test') ||
        lowerMessage.includes('try it') || lowerMessage.includes('preview')) {
      signals.push('trial_interest');
    }
    
    if (lowerMessage.includes('contact') || lowerMessage.includes('speak to') || lowerMessage.includes('call me') ||
        lowerMessage.includes('sales team') || lowerMessage.includes('representative')) {
      signals.push('contact_request');
    }

    if (lowerMessage.includes('buy') || lowerMessage.includes('purchase') || lowerMessage.includes('order') ||
        lowerMessage.includes('get started') || lowerMessage.includes('sign up') || lowerMessage.includes('subscribe')) {
      signals.push('purchase_intent');
    }

    // Medium conversion signals (building interest)
    if (lowerMessage.includes('features') || lowerMessage.includes('capabilities') || lowerMessage.includes('benefits') ||
        lowerMessage.includes('advantages') || lowerMessage.includes('what can')) {
      signals.push('feature_interest');
    }

    if (lowerMessage.includes('business') || lowerMessage.includes('company') || lowerMessage.includes('organization') ||
        lowerMessage.includes('enterprise') || lowerMessage.includes('team')) {
      signals.push('business_context');
    }

    // Comparison signals (competitive evaluation)
    if (lowerMessage.includes('compare') || lowerMessage.includes('versus') || lowerMessage.includes('vs') ||
        lowerMessage.includes('alternative') || lowerMessage.includes('competitor') || lowerMessage.includes('better than')) {
      signals.push('comparison_inquiry');
    }

    // Timeline/urgency signals
    if (lowerMessage.includes('when') || lowerMessage.includes('soon') || lowerMessage.includes('immediately') ||
        lowerMessage.includes('urgent') || lowerMessage.includes('deadline') || lowerMessage.includes('asap')) {
      signals.push('timeline_inquiry');
    }

    // Budget/decision authority signals
    if (lowerMessage.includes('budget') || lowerMessage.includes('approve') || lowerMessage.includes('decision') ||
        lowerMessage.includes('manager') || lowerMessage.includes('team lead') || lowerMessage.includes('authorize')) {
      signals.push('budget_authority');
    }
    
    return signals;
  };

  const extractBusinessContext = (userMessage) => {
    const context = {};
    const lowerMessage = userMessage.toLowerCase();
    
    // Company size indicators
    if (lowerMessage.includes('startup') || lowerMessage.includes('small business') || lowerMessage.includes('freelanc')) {
      context.companySize = 'small';
    } else if (lowerMessage.includes('enterprise') || lowerMessage.includes('corporation') || lowerMessage.includes('large company')) {
      context.companySize = 'enterprise';
    } else if (lowerMessage.includes('medium') || lowerMessage.includes('mid-size') || lowerMessage.includes('growing')) {
      context.companySize = 'medium';
    }
    
    // Industry indicators
    const industries = ['tech', 'healthcare', 'finance', 'education', 'retail', 'manufacturing', 'legal', 'consulting'];
    industries.forEach(industry => {
      if (lowerMessage.includes(industry)) {
        context.industry = industry;
      }
    });
    
    // Use case indicators
    if (lowerMessage.includes('automat') || lowerMessage.includes('efficien') || lowerMessage.includes('streamlin')) {
      context.useCase = 'automation';
    } else if (lowerMessage.includes('analytic') || lowerMessage.includes('report') || lowerMessage.includes('insight')) {
      context.useCase = 'analytics';
    } else if (lowerMessage.includes('integrat') || lowerMessage.includes('connect') || lowerMessage.includes('api')) {
      context.useCase = 'integration';
    }
    
    return context;
  };

  // Additional helper functions for conversation analysis
  const determineConversationFlow = (messages) => {
    const lastFewMessages = messages.slice(-3);
    const hasQuestions = lastFewMessages.some(m => m.content.includes('?'));
    const hasBusinessTerms = lastFewMessages.some(m => 
      /business|company|enterprise|solution|service|product|pricing|cost/.test(m.content.toLowerCase())
    );
    
    if (hasBusinessTerms && hasQuestions) return 'qualification';
    if (hasBusinessTerms) return 'interest';
    if (messages.length < 3) return 'discovery';
    return 'engagement';
  };

  const calculateEngagementLevel = (messages) => {
    const userMessages = messages.filter(m => m.sender === 'user');
    if (userMessages.length === 0) return 0;
    
    const avgLength = userMessages.reduce((sum, m) => sum + m.content.length, 0) / userMessages.length;
    const hasQuestions = userMessages.some(m => m.content.includes('?'));
    const hasBusinessContext = userMessages.some(m => 
      /business|company|need|looking for|interested|solution/.test(m.content.toLowerCase())
    );
    
    let score = Math.min(10, Math.floor(avgLength / 20)); // Base engagement from message length
    if (hasQuestions) score += 2;
    if (hasBusinessContext) score += 3;
    if (userMessages.length > 3) score += 1;
    
    return Math.min(10, score);
  };

  const identifyProactiveOpportunities = (userMessage, botMessage) => {
    const opportunities = [];
    const userLower = userMessage.toLowerCase();
    const botLower = botMessage.toLowerCase();
    
    // Identify moments where user might need follow-up
    if (userLower.includes('think about') || userLower.includes('consider')) {
      opportunities.push('decision_support');
    }
    
    if (userLower.includes('price') || userLower.includes('cost')) {
      opportunities.push('pricing_follow_up');
    }
    
    if (botLower.includes('let me know') || botLower.includes('feel free')) {
      opportunities.push('gentle_follow_up');
    }
    
    if (userLower.includes('interested') || userLower.includes('sounds good')) {
      opportunities.push('conversion_opportunity');
    }
    
    return opportunities;
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (proactiveTimer) {
        clearTimeout(proactiveTimer);
      }
    };
  }, [proactiveTimer]);

  // Handler for manual rich content injection (demo purposes)
  const handleSendRichContent = (richMessage) => {
    setMessages(prev => [...prev, richMessage]);
  };
  
  return (
    <div className={containerClassName}>
      {/* User Identification Section */}
      <div className="p-3 border-b border-dashed border-blue-500/30 bg-blue-50/50">
        {!userId ? (
          <div className="flex items-center gap-2">
            <User size={16} className="text-blue-600" />
            <span className="text-sm text-blue-700">Identify yourself for personalized experience:</span>
            <button
              onClick={() => setShowUserIdInput(true)}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <LogIn size={14} className="inline mr-1" />
              Sign In
            </button>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User size={16} className="text-green-600" />
                <span className="text-sm text-green-700">Signed in as:</span>
                <span className="font-medium text-green-800">{userId}</span>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setShowRagManager(true)}
                  className="px-2 py-1 text-xs bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                  title="Manage Knowledge Base"
                >
                  <Database size={14} className="inline mr-1" />
                  Knowledge Base
                </button>
                <button
                  onClick={() => setShowGoalsManager(true)}
                  className="px-2 py-1 text-xs bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                  title="Manage Goals"
                >
                  <Target size={14} className="inline mr-1" />
                  Goals
                </button>
                <button
                  onClick={handleUserIdChange}
                  className="px-2 py-1 text-xs text-blue-600 hover:text-blue-800 underline"
                >
                  Change
                </button>
              </div>
            </div>

            {/* Avatar/Character Selector */}
            <div className="border-t border-gray-200 pt-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bot size={16} className="text-purple-600" />
                  <span className="text-sm text-purple-700">Chat Character:</span>
                  <div className="flex items-center gap-2">
                    {selectedAvatar ? (
                      <div className="flex items-center gap-2">
                        <img 
                          src={selectedAvatar.image} 
                          alt={selectedAvatar.name.en}
                          className="w-6 h-6 rounded-full object-cover"
                          onError={(e) => {
                            e.target.src = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/abnasia.org-griffin-logo.transparent-mini.png'; // fallback image
                          }}
                        />
                        <span className="text-sm font-medium text-purple-800">
                          {selectedAvatar.name.en}
                        </span>
                      </div>
                    ) : (
                      <span className="text-sm font-medium text-purple-800">Default (AbnAsia)</span>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => setShowAvatarSelector(!showAvatarSelector)}
                  className="flex items-center gap-1 px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors"
                >
                  <span>Choose Character</span>
                  {showAvatarSelector ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                </button>
              </div>

              {/* Avatar Selection Panel */}
              {showAvatarSelector && (
                <div className="mt-2 p-3 bg-white border border-gray-200 rounded-lg max-h-80 overflow-hidden">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700">Choose your AI assistant:</span>
                    <button
                      onClick={resetToDefault}
                      className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                    >
                      Reset to Default
                    </button>
                  </div>

                  {/* Search and Filter */}
                  <div className="flex gap-2 mb-3">
                    <div className="flex-1 relative">
                      <Search size={14} className="absolute left-2 top-2 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search characters..."
                        value={avatarSearchQuery}
                        onChange={(e) => setAvatarSearchQuery(e.target.value)}
                        className="w-full pl-8 pr-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    </div>
                    <select
                      value={avatarCategoryFilter}
                      onChange={(e) => setAvatarCategoryFilter(e.target.value)}
                      className="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="">All Categories</option>
                      {availableCategories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                  {/* Avatar Grid */}
                  <div className="max-h-60 overflow-y-auto">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {filteredAvatars.map((avatar) => (
                        <div
                          key={avatar.id}
                          className={`flex items-center gap-3 p-2 rounded-md cursor-pointer transition-colors ${
                            selectedAvatar?.id === avatar.id
                              ? 'bg-purple-50 border border-purple-200'
                              : 'bg-gray-50 hover:bg-gray-100'
                          }`}
                          onClick={() => handleAvatarSelect(avatar)}
                        >
                          <img 
                            src={avatar.image} 
                            alt={avatar.name.en}
                            className="w-8 h-8 rounded-full object-cover flex-shrink-0"
                            onError={(e) => {
                              e.target.src = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/abnasia.org-griffin-logo.transparent-mini.png'; // fallback image
                            }}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {avatar.name.en}
                            </div>
                            <div className="text-xs text-gray-600 truncate">
                              {avatar.oneliner}
                            </div>
                            <div className="text-xs text-purple-600 mt-1">
                              {avatar.category}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    {filteredAvatars.length === 0 && (
                      <div className="text-center py-4 text-gray-500">
                        <Bot size={24} className="mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No characters found matching your criteria.</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>


          </div>
        )}

        {/* User ID Input Modal */}
        {showUserIdInput && (
          <div className="mt-2 p-3 bg-white border border-blue-200 rounded-lg">
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Enter your ID (email, username, etc.)"
                value={userIdInput}
                onChange={(e) => setUserIdInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleUserIdSubmit()}
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autoFocus
              />
              <button
                onClick={handleUserIdSubmit}
                disabled={!userIdInput.trim()}
                className="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 transition-colors"
              >
                Confirm
              </button>
              <button
                onClick={() => {
                  setShowUserIdInput(false);
                  setUserIdInput('');
                }}
                className="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
            <p className="mt-2 text-xs text-gray-600">
              Your ID will be used to save and restore your chat history. This will become your login ID in the future.
            </p>
          </div>
        )}
      </div>

      {/* Goal Indicator */}
      {userId && (
        <GoalIndicator userId={userId} className="mx-3" />
      )}

      <div 
        ref={messagesContainerRef}
        className={`h-96 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-blue-200/50 scrollbar-track-transparent ${className}`}
      >
        {isLoadingHistory ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex gap-2 items-center text-blue-600">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm">Loading your chat history...</span>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => (
              <div key={index} className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                {message.sender !== 'user' && selectedAvatar && (
                  <img 
                    src={selectedAvatar.image} 
                    alt={selectedAvatar.name.en}
                    className="w-8 h-8 rounded-full object-cover flex-shrink-0 mt-1"
                    onError={(e) => {
                      e.target.src = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/abnasia.org-griffin-logo.transparent-mini.png'; // fallback image
                    }}
                  />
                )}
                <div className="flex flex-col">
                  {/* Use RichChatMessage for enhanced content rendering */}
                  <RichChatMessage 
                    message={message}
                    isUser={message.sender === 'user'}
                    className={message.isProactive ? 'bg-gradient-to-r from-purple-50 to-blue-50 border-l-4 border-purple-400' : ''}
                  />
                  {message.isProactive && (
                    <div className="text-xs text-purple-600 mt-1 ml-2 flex items-center gap-1">
                      <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></span>
                      AI-driven proactive message
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {streamingMessage && (
              <div className="flex gap-3 justify-start">
                {selectedAvatar && (
                  <img 
                    src={selectedAvatar.image} 
                    alt={selectedAvatar.name.en}
                    className="w-8 h-8 rounded-full object-cover flex-shrink-0 mt-1"
                    onError={(e) => {
                      e.target.src = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/abnasia.org-griffin-logo.transparent-mini.png'; // fallback image
                    }}
                  />
                )}
                <div className="flex flex-col">
                  <RichChatMessage 
                    message={{ content: streamingMessage, sender: 'assistant', timestamp: new Date().toISOString() }}
                    isUser={false}
                  />
                </div>
              </div>
            )}

            {isLoading && !streamingMessage && (
              <div className="flex gap-3 justify-start">
                {selectedAvatar && (
                  <img 
                    src={selectedAvatar.image} 
                    alt={selectedAvatar.name.en}
                    className="w-8 h-8 rounded-full object-cover flex-shrink-0 mt-1"
                    onError={(e) => {
                      e.target.src = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/abnasia.org-griffin-logo.transparent-mini.png'; // fallback image
                    }}
                  />
                )}
                <ChatMessage isUser={false}>
                  <div className="flex gap-1">
                    <div className="w-2 h-2 rounded-full border border-blue-400 animate-bounce" />
                    <div className="w-2 h-2 rounded-full border border-blue-400 animate-bounce delay-100" />
                    <div className="w-2 h-2 rounded-full border border-blue-400 animate-bounce delay-200" />
                  </div>
                </ChatMessage>
              </div>
            )}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      <div className="p-4 border-t border-dashed border-blue-500/30">
        <div className="flex gap-2">
          <ChatInput
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && !isLoading && handleSendMessage()}
            disabled={isLoading}
            placeholder={userId ? `Message as ${userId}...` : "Type your message..."}
          />
          <ChatButton
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
          >
            <Send size={18} />
          </ChatButton>
        </div>
        
        {/* Active Status Info */}
        <div className="mt-2 flex items-center justify-between text-xs text-gray-600">
          <div className="flex items-center gap-4">
            {selectedAvatar && (
              <span>🤖 Chatting with {selectedAvatar.name.en}</span>
            )}
            {userId && selectedDocuments.size > 0 && ragEnabled && (
              <span>💡 Using {selectedDocuments.size} selected document{selectedDocuments.size !== 1 ? 's' : ''}</span>
            )}
            {userId && (
              <span>🎯 Goal-driven conversation active</span>
            )}
            {userId && (
              <span className={`flex items-center gap-1 ${isProactiveModeActive ? 'text-purple-600' : 'text-gray-500'}`}>
                <span className={`w-2 h-2 rounded-full ${isProactiveModeActive ? 'bg-purple-400 animate-pulse' : 'bg-gray-400'}`}></span>
                🧠 Proactive mode {isProactiveModeActive ? 'active' : 'standby'}
                {conversationContext.engagementLevel > 0 && (
                  <span className="text-gray-500">({conversationContext.engagementLevel}/10)</span>
                )}
              </span>
            )}
          </div>
          
          {/* Admin Controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                const newMode = localStorage.getItem('abnChatMode') === 'conversational' ? 'detailed' : 'conversational';
                localStorage.setItem('abnChatMode', newMode);
                setMessages(prev => [...prev, {
                  content: `Chat mode switched to ${newMode} 💬`,
                  sender: 'system',
                  timestamp: new Date().toISOString()
                }]);
              }}
              className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
              title="Toggle between conversational and detailed responses"
            >
              {(typeof window !== 'undefined' && localStorage.getItem('abnChatMode') === 'detailed') ? '📝 Detailed' : '💬 Chat'}
            </button>
            
            <button
              onClick={() => {
                const newState = !ragEnabled;
                setRagEnabled(newState);
                setMessages(prev => [...prev, {
                  content: `Knowledge base search ${newState ? 'enabled' : 'disabled'} 🔍`,
                  sender: 'system',
                  timestamp: new Date().toISOString()
                }]);
              }}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                ragEnabled ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              title="Toggle knowledge base search (saves API costs when disabled)"
            >
              {ragEnabled ? '🔍 KB On' : '💰 KB Off'}
            </button>
          </div>
        </div>
        
        {/* Rich Content Demo Panel */}
        <RichContentDemo onSendRichContent={handleSendRichContent} />
      </div>

      {/* RAG Manager Modal */}
      {showRagManager && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Knowledge Base Manager</h2>
              <button
                onClick={() => {
                  setShowRagManager(false);
                  // Refresh document list when closing RAG manager
                  if (typeof loadAvailableDocuments === 'function') {
                    loadAvailableDocuments();
                  }
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <RagManager userId={userId} />
            </div>
          </div>
        </div>
      )}

      {/* Goals Manager Modal */}
      {showGoalsManager && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[95vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Goals Management System</h2>
              <button
                onClick={() => setShowGoalsManager(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(95vh-80px)]">
              <GoalsManager currentUserId={userId} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};