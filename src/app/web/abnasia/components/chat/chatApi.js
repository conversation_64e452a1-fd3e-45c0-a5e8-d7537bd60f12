// API Configuration
const API_CONFIGS = {
  groq: {
    url: 'https://api.groq.com/openai/v1/chat/completions',
    defaultModel: 'llama-3.3-70b-versatile',
    apiKey: '********************************************************',
    headers: () => ({
      'Authorization': `Bearer ********************************************************`,
      'Content-Type': 'application/json',
    })
  },
  openai: {
    url: 'https://api.openai.com/v1/chat/completions',
    defaultModel: 'gpt-3.5-turbo',
    apiKey: '*****************************************************************************************************************************************************', // Replace with your actual OpenAI key
    headers: () => ({
      'Authorization': `Bearer *****************************************************************************************************************************************************`, // Replace with your actual OpenAI key
      'Content-Type': 'application/json',
    })
  },
  // Add more providers here
};

// Default configuration
const DEFAULT_PROVIDER = 'groq';
//const DEFAULT_PROVIDER = 'openai';

class ChatApiService {
  constructor(provider = DEFAULT_PROVIDER, customConfig = {}) {
    this.setProvider(provider, customConfig);
  }

  setProvider(provider, customConfig = {}) {
    const config = API_CONFIGS[provider];
    if (!config) {
      throw new Error(`Unsupported provider: ${provider}`);
    }
    
    this.config = {
      ...config,
      ...customConfig,
    };
    this.provider = provider;
  }

  async streamCompletion({ messages, onToken, onError, onFinish }) {
    try {
      const response = await fetch(this.config.url, {
        method: 'POST',
        headers: this.config.headers(),
        body: JSON.stringify({
          model: this.config.defaultModel,
          messages: messages,
          stream: true,
          temperature: 0.7,
          max_tokens: 1024,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No reader available');

      let accumulatedMessage = '';
      let buffer = ''; // Buffer to handle incomplete JSON

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Convert the chunk to text and add to buffer
        const chunk = new TextDecoder().decode(value);
        buffer += chunk;

        // Split by newlines and process complete lines
        const lines = buffer.split('\n');
        
        // Keep the last potentially incomplete line in buffer
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (!trimmedLine) continue;
          if (trimmedLine.includes('[DONE]')) continue;
          if (!trimmedLine.startsWith('data:')) continue;

          try {
            const jsonStr = trimmedLine.slice(5).trim();
            
            // Skip empty data lines
            if (!jsonStr) continue;
            
            const data = JSON.parse(jsonStr);
            if (data.choices && data.choices[0]?.delta?.content) {
              accumulatedMessage += data.choices[0].delta.content;
              onToken(accumulatedMessage);
            }
          } catch (e) {
            // Only log if it's not an empty or malformed line
            if (trimmedLine.slice(5).trim()) {
              console.warn('Skipping malformed JSON chunk:', trimmedLine.slice(5).trim().substring(0, 100) + '...');
            }
          }
        }
      }

      onFinish(accumulatedMessage);
    } catch (error) {
      console.error('API Error:', error);
      onError(error);
    }
  }

  // Add other API methods as needed
  async getModels() {
    // Implementation for getting available models
  }
}

export const chatApi = new ChatApiService();
export default ChatApiService;
