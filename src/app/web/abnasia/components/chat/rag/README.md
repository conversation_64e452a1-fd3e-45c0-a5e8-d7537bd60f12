# RAG (Retrieval Augmented Generation) System

## Overview

The RAG system enhances the abnasia chat system by providing contextual information retrieval from various data sources including uploaded documents, user-specific files, and chat history. This enables the <PERSON> assistant to provide more accurate and personalized responses based on relevant context.

## Architecture

### Core Components

1. **RagService.js** - Client-side service for RAG operations
2. **RagServiceServer.js** - Server-side service handling document processing and search
3. **RagManager.jsx** - React component for document management UI
4. **API Routes** - RESTful endpoints for RAG operations

### Data Storage Structure

```
/data/apps/web/abnasia/rag/
├── public/
│   ├── documents/          # Public document metadata
│   ├── vectors/           # Public document embeddings
│   └── index.json         # Public documents index
└── users/
    └── [userId]/
        ├── documents/     # User-specific document metadata
        ├── vectors/       # User-specific document embeddings
        └── index.json     # User documents index
```

## Features

### Document Processing
- **Supported Formats**: `.txt`, `.md`, `.json`
- **Text Chunking**: Automatic chunking with configurable overlap
- **Embeddings**: OpenAI embeddings with fallback to hash-based embeddings
- **Metadata Storage**: Rich metadata including title, type, size, and creation date

### Search Capabilities
- **Semantic Search**: Vector similarity search using cosine similarity
- **Multi-source Search**: Search across user documents, public documents, and chat history
- **Configurable Parameters**: Similarity threshold, result limits, source filtering
- **Context Generation**: Automatic context compilation for chat enhancement

### Privacy & Security
- **User Isolation**: Complete data separation between users
- **Public/Private Documents**: Support for both public and user-specific documents
- **Sanitized Storage**: Safe file system storage with sanitized user IDs

## API Reference

### Main RAG Endpoint

**POST** `/api/chat/abnasia/rag`

#### Actions

##### Search Documents
```json
{
  "action": "search",
  "query": "search query",
  "userId": "<EMAIL>",
  "options": {
    "limit": 5,
    "minSimilarity": 0.1,
    "includePublic": true,
    "includeChatHistory": true
  }
}
```

##### Generate Chat Context
```json
{
  "action": "generateContext",
  "query": "user message",
  "userId": "<EMAIL>",
  "options": {
    "limit": 3,
    "minSimilarity": 0.1,
    "includePublic": true,
    "includeChatHistory": true
  }
}
```

##### Process Document
```json
{
  "action": "processDocument",
  "content": "document content",
  "metadata": {
    "title": "Document Title",
    "type": "text",
    "filename": "document.txt"
  },
  "userId": "<EMAIL>"
}
```

##### Delete Document
```json
{
  "action": "deleteDocument",
  "docId": "document-uuid",
  "userId": "<EMAIL>"
}
```

### File Upload Endpoint

**POST** `/api/chat/abnasia/rag/upload`

Form data:
- `file`: Document file
- `userId`: User identifier (optional for public documents)
- `title`: Document title
- `isPublic`: "true" for public documents, "false" for private

### Document Listing Endpoint

**GET** `/api/chat/abnasia/rag?userId={userId}&includePublic={true|false}`

## Usage Examples

### Basic Document Upload

```javascript
const formData = new FormData();
formData.append('file', file);
formData.append('userId', '<EMAIL>');
formData.append('title', 'My Document');
formData.append('isPublic', 'false');

const response = await fetch('/api/chat/abnasia/rag/upload', {
  method: 'POST',
  body: formData,
});
```

### Search Documents

```javascript
const response = await fetch('/api/chat/abnasia/rag', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'search',
    query: 'What is ABN Asia?',
    userId: '<EMAIL>',
    options: {
      limit: 5,
      minSimilarity: 0.1,
      includePublic: true,
      includeChatHistory: false
    }
  }),
});

const results = await response.json();
```

### Generate Context for Chat

```javascript
import RagService from './rag/RagService';

const ragService = new RagService();
const contextResult = await ragService.generateChatContext(
  "Tell me about the company",
  "<EMAIL>"
);

if (contextResult.hasContext) {
  console.log('Context:', contextResult.context);
  console.log('Sources:', contextResult.sources);
}
```

## Integration with Chat System

The RAG system is automatically integrated into the chat flow:

1. **User sends message** → RAG searches for relevant context
2. **Context found** → Added to system prompt for AI
3. **AI generates response** → Enhanced with contextual information
4. **Chat saved** → Available for future context searches

### Chat Enhancement

```javascript
// In ChatCore.jsx
const handleSendMessage = async () => {
  // ... existing code ...
  
  // Get RAG context
  const contextResult = await ragService.generateChatContext(inputMessage, userId);
  
  // Enhanced system prompt
  const systemPrompt = `${basePrompt}${contextResult.hasContext ? 
    `\n\nAdditional context: ${contextResult.context}` : ''}`;
  
  // ... continue with chat completion ...
};
```

## Configuration

### Environment Variables

```bash
# OpenAI API Key for embeddings (optional - falls back to hash-based embeddings)
OPENAI_API_KEY=your_openai_api_key

# Or use Groq API
NEXT_PUBLIC_GROQ_API_KEY=your_groq_api_key
```

### Chunking Parameters

```javascript
// In RagServiceServer.js
this.maxChunkSize = 1000; // tokens
this.overlapSize = 100;   // tokens
```

### Search Parameters

```javascript
// Default search options
const defaultOptions = {
  limit: 5,              // Maximum results
  minSimilarity: 0.1,    // Minimum similarity threshold
  includePublic: true,   // Include public documents
  includeChatHistory: true // Include chat history
};
```

## Testing

### Automated Test Suite

Run the comprehensive test suite:

```javascript
// In browser console
window.ragTests.runCompleteTest('<EMAIL>');
```

### Individual Tests

```javascript
// Test document upload
await window.ragTests.testUpload('<EMAIL>');

// Test search functionality
await window.ragTests.testSearch('<EMAIL>');

// Test context generation
await window.ragTests.testContext('<EMAIL>');

// Test document listing
await window.ragTests.testListing('<EMAIL>');
```

## Performance Considerations

### Embedding Generation
- **OpenAI Embeddings**: High quality but requires API calls
- **Hash-based Fallback**: Fast local computation, lower quality
- **Caching**: Embeddings are cached in vector store

### Search Optimization
- **Similarity Threshold**: Adjust `minSimilarity` to balance relevance vs. recall
- **Result Limits**: Use appropriate `limit` values to control response time
- **Source Filtering**: Disable unnecessary sources for faster searches

### Storage Management
- **Chunking**: Optimal chunk size balances context and search granularity
- **Cleanup**: Implement periodic cleanup of unused documents
- **Indexing**: Maintain up-to-date indexes for fast document listing

## Troubleshooting

### Common Issues

1. **Upload Fails**
   - Check file format (only .txt, .md, .json supported)
   - Verify file size limits
   - Ensure proper form data format

2. **Search Returns No Results**
   - Lower `minSimilarity` threshold
   - Check if documents are properly indexed
   - Verify user permissions for private documents

3. **Context Not Generated**
   - Ensure documents exist and are searchable
   - Check search parameters
   - Verify user ID matches document ownership

### Debug Mode

Enable debug logging:

```javascript
// In RagServiceServer.js
console.log('Search results:', results);
console.log('Generated context:', contextResult);
```

## Future Enhancements

### Planned Features
- **PDF Support**: Add PDF document processing
- **Image Analysis**: OCR and image content extraction
- **Advanced Search**: Filters by date, type, tags
- **Bulk Operations**: Batch upload and processing
- **Analytics**: Usage statistics and search analytics

### Performance Improvements
- **Vector Database**: Migrate to dedicated vector database
- **Caching Layer**: Redis-based caching for frequent searches
- **Background Processing**: Async document processing
- **CDN Integration**: Faster document delivery

## Contributing

### Adding New File Types

1. Extend `processFile` method in `RagServiceServer.js`
2. Add file type validation in upload endpoint
3. Update UI file type restrictions
4. Add tests for new file type

### Improving Search Quality

1. Experiment with different embedding models
2. Adjust chunking parameters
3. Implement query preprocessing
4. Add relevance scoring improvements

## License

This RAG system is part of the abnasia chat platform and follows the same licensing terms. 