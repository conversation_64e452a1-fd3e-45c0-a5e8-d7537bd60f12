/**
 * RAG System Test Suite
 * 
 * This file contains comprehensive tests for the RAG (Retrieval Augmented Generation) system
 * including document processing, search functionality, and chat integration.
 */

// Test data for document upload
const testDocuments = {
  // Company information document
  companyInfo: {
    filename: 'abn-asia-company-info.txt',
    content: `ABN Asia Company Information

About ABN Asia:
ABN ASIA was founded by people with deep roots in academia, with work experience in the US, Holland, Hungary, Japan, South Korea, Singapore, and Vietnam. ABN Asia is where academia and technology meet opportunity.

Services:
- IT services and digital consulting
- Off-the-shelf software solutions
- Custom software development
- Request for Proposals (RFPs) handling

Contact Information:
- Asia Mobile: +*********** (WhatsApp, Telegram, Viber, Zalo)
- US Mobile: +*********** (WhatsApp, Telegram)
- Email: <EMAIL>
- Website: https://abnasia.org

Our Commitment: Faster. Better. More reliable. In most cases: Cheaper as well.

We are ready to assist you with all your technology needs.`,
    metadata: {
      title: 'ABN Asia Company Information',
      type: 'company_info',
      category: 'public'
    }
  },

  // Technical documentation
  techDoc: {
    filename: 'api-documentation.md',
    content: `# API Documentation

## Chat API Endpoints

### POST /api/chat/abnasia
Main chat endpoint for processing user messages.

**Request Body:**
\`\`\`json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello"
    }
  ],
  "userId": "<EMAIL>"
}
\`\`\`

**Response:**
Streaming response with chat completion.

### GET /api/chat/abnasia
Retrieve chat history for a user.

**Query Parameters:**
- userId: User identifier
- limit: Number of chats to retrieve
- sortOrder: 'asc' or 'desc'

## RAG API Endpoints

### POST /api/chat/abnasia/rag
Main RAG operations endpoint.

**Actions:**
- search: Search documents and chat history
- generateContext: Generate context for chat
- processDocument: Process and store a document
- deleteDocument: Delete a document

### POST /api/chat/abnasia/rag/upload
Upload and process documents.

**Form Data:**
- file: Document file (.txt, .md, .json)
- userId: User identifier
- title: Document title
- isPublic: Whether document is public

### GET /api/chat/abnasia/rag
List documents.

**Query Parameters:**
- userId: User identifier
- includePublic: Include public documents`,
    metadata: {
      title: 'API Documentation',
      type: 'technical_doc',
      category: 'public'
    }
  },

  // User-specific document
  userDoc: {
    filename: 'my-project-notes.txt',
    content: `My Project Notes

Project: E-commerce Platform Development
Client: TechCorp Inc.
Start Date: 2024-01-15
Expected Completion: 2024-06-30

Requirements:
- User authentication and authorization
- Product catalog management
- Shopping cart functionality
- Payment integration (Stripe, PayPal)
- Order management system
- Admin dashboard
- Mobile responsive design

Technology Stack:
- Frontend: React.js with Next.js
- Backend: Node.js with Express
- Database: PostgreSQL
- Payment: Stripe API
- Hosting: AWS

Progress:
- Week 1-2: Project setup and authentication ✓
- Week 3-4: Product catalog development ✓
- Week 5-6: Shopping cart implementation (In Progress)
- Week 7-8: Payment integration (Pending)
- Week 9-10: Order management (Pending)
- Week 11-12: Testing and deployment (Pending)

Notes:
- Client requested additional feature: Wishlist functionality
- Need to discuss mobile app development for Phase 2
- Consider implementing real-time notifications`,
    metadata: {
      title: 'E-commerce Project Notes',
      type: 'project_notes',
      category: 'private'
    }
  }
};

// Test queries for search functionality
const testQueries = [
  {
    query: "What is ABN Asia's contact information?",
    expectedSources: ['company_info'],
    description: "Should find company contact details"
  },
  {
    query: "How do I upload documents to the RAG system?",
    expectedSources: ['technical_doc'],
    description: "Should find API documentation about file upload"
  },
  {
    query: "What is the status of my e-commerce project?",
    expectedSources: ['project_notes'],
    description: "Should find user's project notes (user-specific)"
  },
  {
    query: "What payment methods are supported?",
    expectedSources: ['project_notes'],
    description: "Should find payment integration details"
  },
  {
    query: "What are the API endpoints for chat?",
    expectedSources: ['technical_doc'],
    description: "Should find chat API documentation"
  }
];

/**
 * Test RAG Document Upload
 */
async function testDocumentUpload(userId = '<EMAIL>') {
  console.log('🧪 Testing RAG Document Upload...');
  
  const results = [];
  
  for (const [key, doc] of Object.entries(testDocuments)) {
    try {
      console.log(`📄 Uploading ${doc.filename}...`);
      
      // Create a blob from the content
      const blob = new Blob([doc.content], { type: 'text/plain' });
      const file = new File([blob], doc.filename, { type: 'text/plain' });
      
      // Prepare form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', doc.metadata.category === 'public' ? '' : userId);
      formData.append('title', doc.metadata.title);
      formData.append('isPublic', doc.metadata.category === 'public' ? 'true' : 'false');
      
      // Upload document
      const response = await fetch('/api/chat/abnasia/rag/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Successfully uploaded ${doc.filename}`);
        console.log(`   Document ID: ${result.document.id}`);
        console.log(`   Chunks: ${result.document.chunkCount}`);
        results.push({ key, success: true, result });
      } else {
        const error = await response.json();
        console.log(`❌ Failed to upload ${doc.filename}: ${error.error}`);
        results.push({ key, success: false, error });
      }
    } catch (error) {
      console.log(`❌ Error uploading ${doc.filename}:`, error);
      results.push({ key, success: false, error: error.message });
    }
  }
  
  return results;
}

/**
 * Test RAG Search Functionality
 */
async function testRagSearch(userId = '<EMAIL>') {
  console.log('🔍 Testing RAG Search Functionality...');
  
  const results = [];
  
  for (const testQuery of testQueries) {
    try {
      console.log(`🔎 Testing query: "${testQuery.query}"`);
      
      const response = await fetch('/api/chat/abnasia/rag', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'search',
          query: testQuery.query,
          userId: userId,
          options: {
            limit: 5,
            minSimilarity: 0.1,
            includePublic: true,
            includeChatHistory: false
          }
        }),
      });
      
      if (response.ok) {
        const searchResults = await response.json();
        console.log(`✅ Found ${searchResults.length} results`);
        
        searchResults.forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.metadata?.filename || result.id} (${Math.round(result.similarity * 100)}% match)`);
          console.log(`      Source: ${result.source}`);
          console.log(`      Preview: ${result.text.substring(0, 100)}...`);
        });
        
        results.push({ 
          query: testQuery.query, 
          success: true, 
          resultCount: searchResults.length,
          results: searchResults 
        });
      } else {
        const error = await response.json();
        console.log(`❌ Search failed: ${error.error}`);
        results.push({ query: testQuery.query, success: false, error });
      }
    } catch (error) {
      console.log(`❌ Error searching for "${testQuery.query}":`, error);
      results.push({ query: testQuery.query, success: false, error: error.message });
    }
    
    console.log(''); // Empty line for readability
  }
  
  return results;
}

/**
 * Test RAG Context Generation
 */
async function testContextGeneration(userId = '<EMAIL>') {
  console.log('🧠 Testing RAG Context Generation...');
  
  const testMessages = [
    "Tell me about ABN Asia's services",
    "How can I contact the company?",
    "What's the progress on my e-commerce project?",
    "What API endpoints are available for chat?"
  ];
  
  const results = [];
  
  for (const message of testMessages) {
    try {
      console.log(`💭 Generating context for: "${message}"`);
      
      const response = await fetch('/api/chat/abnasia/rag', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generateContext',
          query: message,
          userId: userId,
          options: {
            limit: 3,
            minSimilarity: 0.1,
            includePublic: true,
            includeChatHistory: true
          }
        }),
      });
      
      if (response.ok) {
        const contextResult = await response.json();
        console.log(`✅ Context generated (${contextResult.sources.length} sources)`);
        
        if (contextResult.hasContext) {
          console.log(`   Context preview: ${contextResult.context.substring(0, 200)}...`);
          contextResult.sources.forEach((source, index) => {
            console.log(`   Source ${index + 1}: ${source.type} (${Math.round(source.similarity * 100)}% match)`);
          });
        } else {
          console.log('   No relevant context found');
        }
        
        results.push({ 
          message, 
          success: true, 
          hasContext: contextResult.hasContext,
          sourceCount: contextResult.sources.length 
        });
      } else {
        const error = await response.json();
        console.log(`❌ Context generation failed: ${error.error}`);
        results.push({ message, success: false, error });
      }
    } catch (error) {
      console.log(`❌ Error generating context for "${message}":`, error);
      results.push({ message, success: false, error: error.message });
    }
    
    console.log(''); // Empty line for readability
  }
  
  return results;
}

/**
 * Test Document Listing
 */
async function testDocumentListing(userId = '<EMAIL>') {
  console.log('📋 Testing Document Listing...');
  
  try {
    const response = await fetch(`/api/chat/abnasia/rag?userId=${userId}&includePublic=true`);
    
    if (response.ok) {
      const documents = await response.json();
      console.log(`✅ Found ${documents.length} documents`);
      
      const publicDocs = documents.filter(doc => doc.source === 'public');
      const userDocs = documents.filter(doc => doc.source === 'user');
      
      console.log(`   Public documents: ${publicDocs.length}`);
      console.log(`   User documents: ${userDocs.length}`);
      
      documents.forEach((doc, index) => {
        console.log(`   ${index + 1}. ${doc.title} (${doc.source})`);
        console.log(`      File: ${doc.filename}`);
        console.log(`      Size: ${doc.size} bytes, ${doc.chunkCount} chunks`);
        console.log(`      Created: ${new Date(doc.createdAt).toLocaleDateString()}`);
      });
      
      return { success: true, documents };
    } else {
      const error = await response.json();
      console.log(`❌ Failed to list documents: ${error.error}`);
      return { success: false, error };
    }
  } catch (error) {
    console.log(`❌ Error listing documents:`, error);
    return { success: false, error: error.message };
  }
}

/**
 * Run Complete RAG System Test
 */
async function runCompleteRagTest(userId = '<EMAIL>') {
  console.log('🚀 Starting Complete RAG System Test');
  console.log('=====================================');
  
  const testResults = {
    upload: null,
    search: null,
    context: null,
    listing: null,
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test 1: Document Upload
    testResults.upload = await testDocumentUpload(userId);
    console.log('');
    
    // Wait a bit for documents to be processed
    console.log('⏳ Waiting for documents to be processed...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('');
    
    // Test 2: Document Listing
    testResults.listing = await testDocumentListing(userId);
    console.log('');
    
    // Test 3: Search Functionality
    testResults.search = await testRagSearch(userId);
    console.log('');
    
    // Test 4: Context Generation
    testResults.context = await testContextGeneration(userId);
    console.log('');
    
    // Summary
    console.log('📊 Test Summary');
    console.log('===============');
    console.log(`Upload Success Rate: ${testResults.upload.filter(r => r.success).length}/${testResults.upload.length}`);
    console.log(`Search Success Rate: ${testResults.search.filter(r => r.success).length}/${testResults.search.length}`);
    console.log(`Context Success Rate: ${testResults.context.filter(r => r.success).length}/${testResults.context.length}`);
    console.log(`Document Listing: ${testResults.listing.success ? 'Success' : 'Failed'}`);
    
  } catch (error) {
    console.log('❌ Test suite failed:', error);
  }
  
  return testResults;
}

// Export test functions for use in browser console or other scripts
if (typeof window !== 'undefined') {
  window.ragTests = {
    runCompleteTest: runCompleteRagTest,
    testUpload: testDocumentUpload,
    testSearch: testRagSearch,
    testContext: testContextGeneration,
    testListing: testDocumentListing,
    testData: testDocuments,
    testQueries: testQueries
  };
  
  console.log('🧪 RAG Test Suite loaded! Use window.ragTests to run tests.');
  console.log('Example: window.ragTests.runCompleteTest("your-user-id")');
}

export {
  runCompleteRagTest,
  testDocumentUpload,
  testRagSearch,
  testContextGeneration,
  testDocumentListing,
  testDocuments,
  testQueries
}; 