/**
 * Client-side RAG Service
 * 
 * This service handles all RAG operations from the client-side by making
 * API calls to the server. It does not perform any file system operations
 * or embedding generation directly.
 */

export class RagService {
  constructor() {
    // Pure client-side service - all operations via API calls
    this.baseUrl = '/api/chat/abnasia/rag';
  }

  /**
   * Search for documents and generate context for chat
   */
  async generateChatContext(query, userId = null, options = {}) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generateContext',
          query,
          userId,
          options: {
            limit: 3,
            minSimilarity: 0.1,
            includePublic: true,
            includeChatHistory: true,
            ...options
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate context');
      }

      return await response.json();
    } catch (error) {
      console.error('Error generating chat context:', error);
      return {
        context: '',
        sources: [],
        hasContext: false
      };
    }
  }

  /**
   * Search documents
   */
  async search(query, userId = null, options = {}) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'search',
          query,
          userId,
          options: {
            limit: 5,
            minSimilarity: 0.1,
            includePublic: true,
            includeChatHistory: false,
            ...options
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to search documents');
      }

      return await response.json();
    } catch (error) {
      console.error('Error searching documents:', error);
      return [];
    }
  }

  /**
   * Upload and process a document
   */
  async uploadDocument(file, userId = null, metadata = {}) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', userId || '');
      formData.append('title', metadata.title || file.name);
      formData.append('isPublic', metadata.isPublic ? 'true' : 'false');

      const response = await fetch(`${this.baseUrl}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Upload failed');
      }

      return await response.json();
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    }
  }

  /**
   * Process text content as a document
   */
  async processDocument(content, metadata = {}, userId = null) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'processDocument',
          content,
          metadata,
          userId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to process document');
      }

      return await response.json();
    } catch (error) {
      console.error('Error processing document:', error);
      throw error;
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(docId, userId = null) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'deleteDocument',
          docId,
          userId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete document');
      }

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error deleting document:', error);
      return false;
    }
  }

  /**
   * List documents
   */
  async listDocuments(userId = null, includePublic = true) {
    try {
      const params = new URLSearchParams();
      if (userId) params.append('userId', userId);
      params.append('includePublic', includePublic.toString());

      const response = await fetch(`${this.baseUrl}?${params}`);

      if (!response.ok) {
        throw new Error('Failed to list documents');
      }

      return await response.json();
    } catch (error) {
      console.error('Error listing documents:', error);
      return [];
    }
  }

  /**
   * Get document by ID
   */
  async getDocument(docId, userId = null) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'getDocument',
          docId,
          userId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get document');
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting document:', error);
      return null;
    }
  }
}

export default RagService; 