import React, { useState, useEffect } from 'react';
import { Upload, Search, FileText, Trash2, Globe, User, Filter } from 'lucide-react';

const RagManager = ({ userId }) => {
  const [documents, setDocuments] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    loadDocuments();
  }, [userId, filter]);

  const loadDocuments = async () => {
    setLoading(true);
    try {
      const includePublic = filter === 'all' || filter === 'public';
      const response = await fetch(`/api/chat/abnasia/rag?userId=${userId || ''}&includePublic=${includePublic}`);
      const docs = await response.json();
      
      let filteredDocs = docs;
      if (filter === 'user') {
        filteredDocs = docs.filter(doc => doc.source === 'user');
      } else if (filter === 'public') {
        filteredDocs = docs.filter(doc => doc.source === 'public');
      }
      
      setDocuments(filteredDocs);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    setUploading(true);
    
    for (const file of files) {
      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('userId', userId || '');
        formData.append('title', file.name);
        formData.append('isPublic', filter === 'public' ? 'true' : 'false');

        const response = await fetch('/api/chat/abnasia/rag/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Upload failed');
        }

        const result = await response.json();
        console.log('Document uploaded:', result);
      } catch (error) {
        console.error('Error uploading file:', error);
        alert(`Error uploading ${file.name}: ${error.message}`);
      }
    }

    setUploading(false);
    loadDocuments();
    event.target.value = '';
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/chat/abnasia/rag', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'search',
          query: searchQuery,
          userId,
          options: {
            limit: 10,
            minSimilarity: 0.1,
            includePublic: filter === 'all' || filter === 'public',
            includeChatHistory: false
          }
        }),
      });

      const results = await response.json();
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDocument = async (docId, source) => {
    if (!confirm('Are you sure you want to delete this document?')) return;

    try {
      const response = await fetch('/api/chat/abnasia/rag', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'deleteDocument',
          docId,
          userId: source === 'public' ? null : userId
        }),
      });

      const result = await response.json();
      if (result.success) {
        loadDocuments();
        setSearchResults([]);
      } else {
        alert('Failed to delete document');
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      alert('Error deleting document');
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
    return Math.round(bytes / (1024 * 1024)) + ' MB';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Knowledge Base Manager</h2>
        
        <div className="flex flex-wrap gap-4 mb-4">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border rounded px-3 py-1 text-sm"
            >
              <option value="all">All Documents</option>
              <option value="user">My Documents</option>
              <option value="public">Public Documents</option>
            </select>
          </div>
          
          <div className="flex items-center gap-2">
            <Upload className="w-4 h-4 text-blue-500" />
            <label className="cursor-pointer bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors">
              Upload Files
              <input
                type="file"
                multiple
                accept=".txt,.md,.json"
                onChange={handleFileUpload}
                className="hidden"
                disabled={uploading}
              />
            </label>
            {uploading && <span className="text-sm text-gray-500">Uploading...</span>}
          </div>
        </div>

        <div className="flex gap-2 mb-4">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
          >
            Search
          </button>
        </div>
      </div>

      {searchResults.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">Search Results</h3>
          <div className="space-y-3">
            {searchResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-4 bg-yellow-50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">
                      {result.metadata?.filename || result.id}
                    </span>
                    <span className="text-sm text-gray-500">
                      ({Math.round(result.similarity * 100)}% match)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {result.source === 'public' ? (
                      <Globe className="w-4 h-4 text-green-500" title="Public" />
                    ) : result.source === 'user' ? (
                      <User className="w-4 h-4 text-blue-500" title="User" />
                    ) : (
                      <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                        Chat History
                      </span>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-600 line-clamp-3">
                  {result.text.substring(0, 200)}...
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      <div>
        <h3 className="text-lg font-medium mb-3">
          Documents ({documents.length})
        </h3>
        
        {loading ? (
          <div className="text-center py-8 text-gray-500">Loading...</div>
        ) : documents.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No documents found. Upload some files to get started.
          </div>
        ) : (
          <div className="space-y-3">
            {documents.map((doc) => (
              <div key={doc.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-gray-500" />
                    <div>
                      <h4 className="font-medium">{doc.title}</h4>
                      <div className="text-sm text-gray-500 flex items-center gap-4">
                        <span>{doc.filename}</span>
                        <span>{formatFileSize(doc.size)}</span>
                        <span>{doc.chunkCount} chunks</span>
                        <span>{formatDate(doc.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {doc.source === 'public' ? (
                      <Globe className="w-4 h-4 text-green-500" title="Public Document" />
                    ) : (
                      <User className="w-4 h-4 text-blue-500" title="Your Document" />
                    )}
                    
                    <button
                      onClick={() => handleDeleteDocument(doc.id, doc.source)}
                      className="p-1 text-red-500 hover:bg-red-50 rounded transition-colors"
                      title="Delete document"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Upload Instructions</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Supported formats: .txt, .md, .json</li>
          <li>• Public documents are accessible to all users</li>
          <li>• Private documents are only accessible to you</li>
          <li>• Documents are automatically chunked and indexed for search</li>
          <li>• The AI assistant can reference these documents during conversations</li>
        </ul>
      </div>
    </div>
  );
};

export default RagManager; 