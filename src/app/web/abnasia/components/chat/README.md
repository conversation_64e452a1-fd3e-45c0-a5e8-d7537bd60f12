# JSON Files-Based Chat System

This document explains the new JSON files-based chat storage system that replaces the Google Sheets API integration.

## Overview

The chat system has been migrated from Google Sheets storage to a local JSON files-based approach, offering improved performance, better search capabilities, and enhanced metadata features.

## Architecture

### File Structure
```
/data/apps/web/abnasia/chats/
└── users/
    ├── anonymous/             # Anonymous user chats
    │   ├── index.json         # User's chat index
    │   ├── chat_[timestamp]_[id].json
    │   └── backups/
    │       └── chat_[id]_backup_[timestamp].json
    ├── <EMAIL>/      # User-specific directory
    │   ├── index.json
    │   ├── chat_[timestamp]_[id].json
    │   └── backups/
    └── <EMAIL>/      # Another user's directory
        ├── index.json
        ├── chat_[timestamp]_[id].json
        └── backups/
```

### Components

1. **jsonFilesApi.js** - Client-side API interface
2. **src/app/api/chat/abnasia/route.ts** - Server-side API endpoints
3. **ChatCore.jsx** - Main chat component with user identification
4. **ChatHistory.jsx** - Enhanced chat history browser
5. **test-json-api.js** - Basic API test utilities
6. **test-user-chat.js** - User-specific functionality tests

## API Reference

### jsonFilesApi

#### Methods

##### `getChatHistory(options)`
Retrieve chat history with advanced filtering and pagination.

```javascript
const options = {
  limit: 50,           // Number of chats to retrieve
  offset: 0,           // Pagination offset
  sortBy: 'timestamp', // Sort field: 'timestamp', 'title', 'messageCount'
  sortOrder: 'desc',   // Sort order: 'asc' or 'desc'
  search: '',          // Search in titles and tags
  dateFrom: null,      // Filter from date (ISO string)
  dateTo: null,        // Filter to date (ISO string)
  userId: 'anonymous'  // User ID for filtering user-specific chats
}

const result = await jsonFilesApi.getChatHistory(options);
// Returns: { chats: [...], total: number, hasMore: boolean }
```

##### `saveChat(chatData, options)`
Save a new chat or update an existing one.

```javascript
const chatData = {
  messages: [...],     // Array of message objects
  title: "...",        // Optional: auto-generated if not provided
  tags: [...]          // Optional: auto-extracted if not provided
};

const options = {
  generateTitle: true, // Auto-generate title from first user message
  extractTags: true,   // Auto-extract tags from message content
  autoBackup: true     // Create automatic backup
};

const savedChat = await jsonFilesApi.saveChat(chatData, options);
```

##### `getChatById(chatId)`
Retrieve a specific chat by its ID.

```javascript
const chat = await jsonFilesApi.getChatById(chatId);
```

##### `deleteChat(chatId)`
Delete a chat and remove it from the index.

```javascript
const success = await jsonFilesApi.deleteChat(chatId);
```

##### `getChatStats()`
Get comprehensive statistics about the chat system.

```javascript
const stats = await jsonFilesApi.getChatStats();
// Returns:
// {
//   totalChats: number,
//   totalMessages: number,
//   averageMessagesPerChat: number,
//   mostCommonTags: [{ tag: string, count: number }],
//   lastUpdated: string
// }
```

## Data Structure

### Chat Object
```javascript
{
  id: "chat_1703123456789_abc123def",
  title: "Hello, I need help with my order...",
  messages: [
    {
      content: "Hello, I need help with my order",
      sender: "user",
      timestamp: "2023-12-21T10:30:00.000Z"
    },
    {
      content: "Hi! I'd be happy to help...",
      sender: "AbnAsia.org",
      timestamp: "2023-12-21T10:30:15.000Z"
    }
  ],
  tags: ["help", "order"],
  createdAt: "2023-12-21T10:30:00.000Z",
  lastUpdated: "2023-12-21T10:30:15.000Z",
  messageCount: 2,
  userMessageCount: 1,
  assistantMessageCount: 1
}
```

### Index Object
```javascript
{
  chats: [
    {
      id: "chat_1703123456789_abc123def",
      title: "Hello, I need help with my order...",
      timestamp: "2023-12-21T10:30:00.000Z",
      lastUpdated: "2023-12-21T10:30:15.000Z",
      messageCount: 2,
      tags: ["help", "order"],
      fileSize: 1234
    }
  ],
  lastUpdated: "2023-12-21T10:30:15.000Z",
  version: "1.0"
}
```

## Migration from sheetsApi

### What Changed
1. **Storage**: Google Sheets → Local JSON files
2. **Performance**: Much faster read/write operations
3. **Search**: Added full-text search capabilities
4. **Filtering**: Date range and tag-based filtering
5. **Metadata**: Automatic tag extraction and statistics
6. **Backups**: Automatic backup creation

### Compatibility
The new API maintains compatibility with existing ChatCore components through the `fetchChatHistory` and `saveChatToHistory` wrapper methods.

### Migration Steps
1. Replace `import { sheetsApi }` with `import { jsonFilesApi }`
2. Update method calls:
   - `sheetsApi.saveChat(messages)` → `jsonFilesApi.saveChatToHistory(messages)`
   - `sheetsApi.getChatHistory()` → `jsonFilesApi.getChatHistory()`

## New Features

### 1. User Identification & Isolation
- **User ID Input**: Visitors can identify themselves with any ID (email, username, etc.)
- **Isolated Storage**: Each user's chats are stored in separate directories
- **Future Login Integration**: User IDs will become login credentials in the future
- **Cross-User Privacy**: Users cannot access each other's chat history

### 2. Advanced Search
- Search across chat titles and tags
- Real-time search with debouncing
- Case-insensitive matching
- User-specific search results

### 3. Smart Tagging
Automatic extraction of tags from message content based on keywords:
- help, support, problem, error, question, order, payment, shipping

### 4. Enhanced Metadata
- Message counts (total, user, assistant)
- File size tracking
- Creation and update timestamps
- Automatic title generation

### 5. Statistics Dashboard
- Total chats and messages per user
- Average messages per chat
- Most common tags
- User-specific usage analytics

### 6. Improved Performance
- Pagination support
- Lazy loading
- Efficient filtering and sorting
- Index-based metadata lookup

### 7. Backup System
- Automatic backup creation per user
- Backup history preservation
- Data recovery capabilities

## API Endpoints

### Server Routes (`/api/chat/abnasia`)

#### GET
- `?operation=stats` - Get chat statistics
- `?operation=chat&id=<chatId>` - Get specific chat
- Default: Get chat history with filtering/pagination

#### POST
Save new chat with automatic processing

#### DELETE
- `?id=<chatId>` - Delete specific chat

## Error Handling

The API includes comprehensive error handling:
- Network failures
- File system errors
- Invalid data validation
- Missing resources (404)
- Server errors (500)

## Performance Considerations

1. **Pagination**: Large chat histories are paginated to prevent memory issues
2. **Index Usage**: Metadata is stored in a separate index for fast querying
3. **Lazy Loading**: Full chat content is only loaded when needed
4. **Caching**: Consider adding Redis/memory caching for high-traffic scenarios

## User Identification System

### How It Works

1. **Initial State**: When a user first opens the chat, they see a "Sign In" button
2. **User Input**: Clicking the button opens an input field for their ID (email, username, etc.)
3. **Storage**: Once identified, all chats are saved to the user's specific directory
4. **History Loading**: When the user returns, their previous chat history is automatically loaded
5. **Context Awareness**: The AI assistant knows the user's identity and can provide personalized responses

### User Experience

- **Anonymous Users**: Can chat without identification (stored under 'anonymous')
- **Identified Users**: Get personalized greetings and chat history persistence
- **Easy Switching**: Users can change their ID at any time
- **Future Integration**: User IDs will become login credentials when authentication is added

### Privacy & Security

- **Data Isolation**: Each user's data is stored in separate directories
- **No Cross-Access**: Users cannot view or access other users' chat histories
- **Sanitized IDs**: User IDs are sanitized for safe file system storage
- **Future-Proof**: Ready for integration with authentication systems

## Testing

Use the provided test files to verify functionality:

```javascript
// Basic API functionality
import { testJsonFilesApi } from './test-json-api.js';
await testJsonFilesApi();

// User-specific functionality
import { testUserSpecificChats, testUserWorkflow } from './test-user-chat.js';
await testUserSpecificChats();
await testUserWorkflow();
```

## Future Enhancements

1. **Search Improvements**: Full-text search with Elasticsearch/Algolia
2. **Export Features**: Export chats to various formats (PDF, CSV, etc.)
3. **Analytics**: Advanced analytics and reporting
4. **Collaboration**: Multi-user chat sharing
5. **AI Integration**: Smart categorization and insights

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure the data directory has write permissions
2. **Missing Directory**: The system automatically creates directories as needed
3. **Corrupted Index**: The index will be rebuilt if corrupted
4. **Large File Sizes**: Consider implementing compression for large chats

### Debug Mode
Enable debug logging by setting:
```javascript
// Add to environment variables
DEBUG_CHAT_API=true
``` 