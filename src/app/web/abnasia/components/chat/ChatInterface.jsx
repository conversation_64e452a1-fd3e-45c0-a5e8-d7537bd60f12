import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { Send, Loader2 } from 'lucide-react';
import JsonGoalsService from '../../admin/chat/services/JsonGoalsService';
import MarkdownRenderer from './MarkdownRenderer';

const ChatInterface = ({ 
  onMessageSent = () => {},
  className = "",
  style = {}
}) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hi! I'm here to help you discover how AbnAsia.org can transform your business operations. What brings you here today?",
      sender: 'AbnAsia.org',
      timestamp: new Date().toISOString()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(() => Math.random().toString(36).substring(7));
  const [visitorId] = useState(() => `visitor_${Date.now()}_${Math.random().toString(36).substring(7)}`);
  const [conversationContext, setConversationContext] = useState({
    conversionSignals: [],
    businessContext: {},
    sentiment: 'neutral',
    conversationFlow: 'discovery',
    engagementLevel: 0,
    lastInteraction: null,
    proactiveOpportunities: []
  });
  const [proactiveMode, setProactiveMode] = useState(false);
  const [proactiveTimer, setProactiveTimer] = useState(null);
  
  const messagesEndRef = useRef(null);
  const goalsService = useMemo(() => new JsonGoalsService(), []);

  // Enhanced conversation analysis with LLM-driven insights
  const analyzeConversation = useCallback(async (message, currentContext) => {
    const lowerMessage = message.toLowerCase();
    
    // Detect conversion signals
    let newSignals = [...currentContext.conversionSignals];
    
    // Strong conversion signals
    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('pricing')) {
      if (!newSignals.includes('pricing_inquiry')) newSignals.push('pricing_inquiry');
    }
    if (lowerMessage.includes('buy') || lowerMessage.includes('purchase') || lowerMessage.includes('get started')) {
      if (!newSignals.includes('purchase_intent')) newSignals.push('purchase_intent');
    }
    if (lowerMessage.includes('contact') || lowerMessage.includes('demo') || lowerMessage.includes('call')) {
      if (!newSignals.includes('contact_request')) newSignals.push('contact_request');
    }
    if (lowerMessage.includes('trial') || lowerMessage.includes('free') || lowerMessage.includes('test')) {
      if (!newSignals.includes('trial_interest')) newSignals.push('trial_interest');
    }
    
    // Medium signals
    if (lowerMessage.includes('feature') || lowerMessage.includes('functionality') || lowerMessage.includes('capabilities')) {
      if (!newSignals.includes('feature_interest')) newSignals.push('feature_interest');
    }
    if (lowerMessage.includes('company') || lowerMessage.includes('business') || lowerMessage.includes('team')) {
      if (!newSignals.includes('business_context')) newSignals.push('business_context');
    }
    
    // Extract business context
    let businessContext = { ...currentContext.businessContext };
    
    // Company size detection
    if (lowerMessage.includes('startup') || lowerMessage.includes('small')) {
      businessContext.companySize = 'small';
    } else if (lowerMessage.includes('enterprise') || lowerMessage.includes('large')) {
      businessContext.companySize = 'enterprise';
    } else if (lowerMessage.includes('medium') || lowerMessage.includes('mid-size')) {
      businessContext.companySize = 'medium';
    }
    
    // Industry detection
    const industries = ['technology', 'finance', 'healthcare', 'retail', 'manufacturing', 'education'];
    for (const industry of industries) {
      if (lowerMessage.includes(industry)) {
        businessContext.industry = industry;
        break;
      }
    }
    
    // Sentiment analysis
    const positiveWords = ['great', 'excellent', 'perfect', 'amazing', 'love', 'interested', 'excited'];
    const negativeWords = ['concern', 'worry', 'problem', 'issue', 'difficult', 'expensive'];
    
    let sentiment = currentContext.sentiment;
    const hasPositive = positiveWords.some(word => lowerMessage.includes(word));
    const hasNegative = negativeWords.some(word => lowerMessage.includes(word));
    
    if (hasPositive && !hasNegative) sentiment = 'positive';
    else if (hasNegative && !hasPositive) sentiment = 'negative';
    else if (hasPositive && hasNegative) sentiment = 'mixed';
    
    // Conversation flow detection
    let conversationFlow = currentContext.conversationFlow;
    if (newSignals.includes('purchase_intent') || newSignals.includes('contact_request')) {
      conversationFlow = 'conversion';
    } else if (newSignals.includes('pricing_inquiry') || newSignals.includes('trial_interest')) {
      conversationFlow = 'consideration';
    } else if (newSignals.includes('feature_interest') || newSignals.includes('business_context')) {
      conversationFlow = 'evaluation';
    }
    
    // Increase engagement level
    const engagementLevel = currentContext.engagementLevel + 1;
    
    return {
      conversionSignals: newSignals,
      businessContext,
      sentiment,
      conversationFlow,
      engagementLevel,
      lastInteraction: new Date().toISOString(),
      proactiveOpportunities: currentContext.proactiveOpportunities
    };
  }, []);

  // LLM-driven proactive decision making
  const shouldBeProactive = useCallback(async (context, messageHistory) => {
    // Create a prompt for the LLM to decide if it should be proactive
    const proactivePrompt = `
    You are an AI sales assistant analyzing a conversation to determine if you should proactively send a follow-up message.
    
    Current conversation context:
    - Conversion signals: ${context.conversionSignals.join(', ') || 'none'}
    - Business context: ${JSON.stringify(context.businessContext)}
    - Sentiment: ${context.sentiment}
    - Conversation flow: ${context.conversationFlow}
    - Engagement level: ${context.engagementLevel}
    - Time since last interaction: ${context.lastInteraction ? Math.round((Date.now() - new Date(context.lastInteraction).getTime()) / 1000) : 0} seconds
    
    Recent messages (last 3):
    ${messageHistory.slice(-3).map(m => `${m.sender}: ${m.text}`).join('\n')}
    
    Should you proactively send a follow-up message? Consider:
    1. Has the user shown interest but the conversation stalled?
    2. Are there conversion opportunities to pursue?
    3. Would a follow-up be helpful and not annoying?
    4. Is enough time passed for a natural follow-up?
    
    Respond with JSON only:
    {
      "shouldBeProactive": boolean,
      "reason": "explanation",
      "timing": "immediate|delayed|scheduled",
      "delaySeconds": number (if delayed/scheduled),
      "messageType": "follow_up|clarification|value_proposition|demo_offer|pricing_info|next_steps"
    }
    `;

    try {
      // This would call your actual LLM API
      const response = await callOpenAIForProactiveDecision(proactivePrompt);
      return JSON.parse(response);
    } catch (error) {
      console.error('Error in proactive decision making:', error);
      return { shouldBeProactive: false, reason: 'error' };
    }
  }, []);

  // Generate proactive message using LLM
  const generateProactiveMessage = useCallback(async (context, messageHistory, messageType) => {
    const conversationSummary = messageHistory.slice(-5).map(m => `${m.sender}: ${m.text}`).join('\n');
    
    const proactiveMessagePrompt = `
    You are an AI sales assistant for AbnAsia.org. Based on the conversation context, generate a natural, helpful proactive message.
    
    Conversation context:
    - Conversion signals: ${context.conversionSignals.join(', ') || 'none'}
    - Business context: ${JSON.stringify(context.businessContext)}
    - Sentiment: ${context.sentiment}
    - Flow: ${context.conversationFlow}
    - Message type needed: ${messageType}
    
    Recent conversation:
    ${conversationSummary}
    
    Generate a proactive message that:
    1. Feels natural and conversational
    2. Adds value to the conversation
    3. Gently guides toward conversion goals
    4. Matches the conversation tone and context
    5. Is personalized based on detected business context
    
    The message should be direct text only, no JSON wrapper.
    Keep it concise (1-2 sentences) and engaging.
    `;

    try {
      return await callOpenAI(proactiveMessagePrompt, messageHistory);
    } catch (error) {
      console.error('Error generating proactive message:', error);
      return null;
    }
  }, []);

  // Enhanced OpenAI API call with proactive capabilities
  const callOpenAI = async (prompt, messageHistory) => {
    // For demo purposes, simulating different responses based on prompt type
    return new Promise((resolve) => {
      setTimeout(() => {
        if (prompt.includes('proactive decision')) {
          // Simulate LLM deciding when to be proactive
          const shouldProact = Math.random() > 0.7; // 30% chance to be proactive
          const decision = {
            shouldBeProactive: shouldProact,
            reason: shouldProact ? "User showed interest but conversation may benefit from follow-up" : "Continue with reactive responses",
            timing: "delayed",
            delaySeconds: Math.floor(Math.random() * 30) + 10, // 10-40 seconds for demo
            messageType: ["follow_up", "value_proposition", "demo_offer"][Math.floor(Math.random() * 3)]
          };
          resolve(JSON.stringify(decision));
        } else if (prompt.includes('Generate a proactive message')) {
          // Generate contextual proactive messages
          const proactiveMessages = [
            "I noticed you're exploring our automation features. Would it help if I showed you how companies in your industry typically implement this?",
            "Based on what you've shared, I think there's a specific solution that could address your challenges. Should I walk you through it?",
            "I'd love to understand your current workflow better. What's the biggest operational challenge you're facing right now?",
            "Since you mentioned scalability, would you be interested in seeing our enterprise-grade features in action?",
            "I can set up a quick personalized demo that focuses on your specific use case. Would that be valuable?"
          ];
          resolve(proactiveMessages[Math.floor(Math.random() * proactiveMessages.length)]);
        } else {
          // Regular chat responses
          const responses = [
            "That's a great question! Based on what you've shared, I think our platform could really help streamline your operations. Let me explain how...",
            "I understand your needs. Many businesses in your situation have found success with our automation features. Would you like to see how that works?",
            "Absolutely! Our solution is designed specifically for challenges like yours. Here's what makes us different...",
            "I'd be happy to walk you through that. Based on your requirements, I think you'd be particularly interested in our integration capabilities..."
          ];
          resolve(responses[Math.floor(Math.random() * responses.length)]);
        }
      }, 1000 + Math.random() * 2000);
    });
  };

  const callOpenAIForProactiveDecision = async (prompt) => {
    return await callOpenAI(prompt, []);
  };

  // Enhanced chat prompt with proactive instructions
  const chatPrompt = `You are an intelligent AI assistant for AbnAsia.org, a business automation platform. Your role is to:

1. UNDERSTAND business needs and challenges
2. GUIDE conversations toward demonstrating value
3. IDENTIFY conversion opportunities naturally
4. BE PROACTIVE when it adds value to the conversation
5. PERSONALIZE responses based on company context

You have access to conversation context including:
- Detected conversion signals
- Business context (company size, industry)
- Conversation sentiment and flow
- User engagement patterns

Be conversational, helpful, and strategically guide toward conversion goals while remaining genuine and valuable.`;

  // Proactive message scheduling and delivery
  const scheduleProactiveMessage = useCallback(async (delaySeconds, messageType, context) => {
    if (proactiveTimer) {
      clearTimeout(proactiveTimer);
    }

    const timer = setTimeout(async () => {
      try {
        const proactiveMessage = await generateProactiveMessage(context, messages, messageType);
        
        if (proactiveMessage) {
          const newMessage = {
            id: Date.now() + Math.random(),
            text: proactiveMessage,
            sender: 'AbnAsia.org',
            timestamp: new Date().toISOString(),
            isProactive: true
          };
          
          setMessages(prev => [...prev, newMessage]);
          
          // Track proactive message
          await goalsService.trackGoalProgress(visitorId, {
            type: 'proactive_message',
            messageType,
            content: proactiveMessage,
            context
          });
        }
      } catch (error) {
        console.error('Error sending proactive message:', error);
      }
    }, delaySeconds * 1000);

    setProactiveTimer(timer);
  }, [messages, goalsService, visitorId, proactiveTimer]);

  // Enhanced message sending with LLM-driven proactive capabilities
  const sendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      text: inputText.trim(),
      sender: 'user',
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      // Analyze conversation for signals and context
      const updatedContext = await analyzeConversation(inputText, conversationContext);
      setConversationContext(updatedContext);

      // Track goal progress
      await goalsService.trackGoalProgress(visitorId, {
        type: 'user_message',
        content: inputText,
        conversionSignals: updatedContext.conversionSignals,
        businessContext: updatedContext.businessContext,
        sentiment: updatedContext.sentiment
      });

      // Get AI response with enhanced context
      const goalModification = await goalsService.generateGoalPromptModification(visitorId);
      const contextualPrompt = `${chatPrompt}

Current conversation context:
- Signals: ${updatedContext.conversionSignals.join(', ')}
- Business: ${JSON.stringify(updatedContext.businessContext)}
- Sentiment: ${updatedContext.sentiment}
- Flow: ${updatedContext.conversationFlow}

${goalModification}

User message: ${inputText}`;
      
      const aiResponse = await callOpenAI(contextualPrompt, [...messages, userMessage]);
      
      const botMessage = {
        id: Date.now() + 1,
        text: aiResponse,
        sender: 'AbnAsia.org',
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, botMessage]);
      
      // After responding, check if LLM should be proactive
      const proactiveDecision = await shouldBeProactive(updatedContext, [...messages, userMessage, botMessage]);
      
      if (proactiveDecision.shouldBeProactive) {
        console.log('LLM decided to be proactive:', proactiveDecision.reason);
        
        if (proactiveDecision.timing === 'immediate') {
          // Send proactive message immediately
          scheduleProactiveMessage(1, proactiveDecision.messageType, updatedContext);
        } else if (proactiveDecision.timing === 'delayed' || proactiveDecision.timing === 'scheduled') {
          // Schedule proactive message for later
          scheduleProactiveMessage(
            proactiveDecision.delaySeconds || 30, 
            proactiveDecision.messageType, 
            updatedContext
          );
        }
      }

      // Track bot response
      await goalsService.trackGoalProgress(visitorId, {
        type: 'bot_response',
        content: aiResponse,
        userMessage: inputText,
        proactiveDecision
      });

      onMessageSent(userMessage.text, aiResponse);

    } catch (error) {
      console.error('ChatInterface: Error sending message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I apologize, but I'm having trouble connecting right now. Please try again in a moment.",
        sender: 'AbnAsia.org',
        timestamp: new Date().toISOString(),
        isError: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      if (proactiveTimer) {
        clearTimeout(proactiveTimer);
      }
    };
  }, [proactiveTimer]);

  return (
    <div className={`flex flex-col h-full bg-white rounded-lg border border-gray-200 shadow-sm ${className}`} style={style}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <span className="text-sm font-bold">A</span>
          </div>
          <div>
            <h3 className="font-semibold">AbnAsia.org Assistant</h3>
            <p className="text-xs opacity-90">Intelligent & Proactive AI Helper</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          <span className="text-xs">Online</span>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                message.sender === 'user'
                  ? 'bg-blue-600 text-white'
                  : message.isProactive
                  ? 'bg-gradient-to-r from-purple-50 to-blue-50 text-gray-900 border border-purple-200'
                  : 'bg-gray-100 text-gray-900'
              }`}
            >
              {message.sender === 'user' ? (
                <p className="text-sm whitespace-pre-wrap">{message.text}</p>
              ) : (
                <div className="text-sm">
                  <MarkdownRenderer 
                    content={message.text} 
                    className={message.sender === 'user' ? 'text-white' : 'text-gray-900'}
                  />
                </div>
              )}
              {message.isProactive && (
                <p className="text-xs text-purple-600 mt-1 flex items-center">
                  🧠 AI-driven proactive message
                </p>
              )}
              <p className="text-xs opacity-75 mt-1">
                {new Date(message.timestamp).toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </p>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-2xl max-w-xs">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            placeholder="Type your message..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
          <button
            onClick={sendMessage}
            disabled={isLoading || !inputText.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
        
        {conversationContext.conversionSignals.length > 0 && (
          <div className="mt-2 text-xs text-gray-500">
            <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
              🎯 {conversationContext.conversionSignals.length} signals detected
            </span>
            {conversationContext.engagementLevel > 2 && (
              <span className="bg-purple-50 text-purple-700 px-2 py-1 rounded-full ml-2">
                🧠 High engagement - AI may be proactive
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInterface; 