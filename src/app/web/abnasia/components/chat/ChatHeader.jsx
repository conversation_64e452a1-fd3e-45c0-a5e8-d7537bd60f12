// components/chat/ChatHeader.jsx
import React from 'react';
import { History, Minimize2, Maximize2, X } from 'lucide-react';
import { ChatButton } from './StyledChatComponents';

export const ChatHeader = ({ 
  showHistory,
  onToggleHistory,
  isMinimized,
  onToggleMinimize,
  onClose,
  title = "ABN Copilot"
}) => (
  <div className="flex justify-between items-center p-4 border-b border-blue-500/30">
    <div className="flex items-center gap-3">
      <div className="w-2 h-2 rounded-full border border-green-400 animate-pulse" />
      <span className="font-medium text-blue-500">{title}</span>
    </div>
    <div className="flex gap-2">
      {onToggleHistory && (
        <ChatButton 
          onClick={onToggleHistory} 
          className="!p-1"
          title="View Chat History"
        >
          <History size={18} />
        </ChatButton>
      )}
      {onToggleMinimize && (
        <ChatButton 
          onClick={onToggleMinimize}
          className="!p-1"
        >
          {isMinimized ? <Maximize2 size={18} /> : <Minimize2 size={18} />}
        </ChatButton>
      )}
      {onClose && (
        <ChatButton 
          onClick={onClose}
          className="!p-1"
        >
          <X size={18} />
        </ChatButton>
      )}
    </div>
  </div>
);
