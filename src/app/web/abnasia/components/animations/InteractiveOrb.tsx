'use client';

import React, { useState, useEffect, useRef } from 'react';

interface InteractiveOrbProps {
  className?: string;
}

const InteractiveOrb: React.FC<InteractiveOrbProps> = ({ className = '' }) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [animationMode, setAnimationMode] = useState(0);
  const [currentPhase, setCurrentPhase] = useState(0);
  const [isClient, setIsClient] = useState(false);
  const orbRef = useRef<HTMLDivElement>(null);

  // Animation modes for the orb
  const animationModes = [
    'mouse-follow',
    'orbital',
    'pulse-wave',
    'morph-shape',
    'color-cycle',
    'spiral',
    'bounce',
    'breathing'
  ];

  useEffect(() => {
    setIsClient(true);
    // Change animation mode every 10 seconds (fixed interval)
    const interval = setInterval(() => {
      setAnimationMode(prev => (prev + 1) % animationModes.length);
    }, 10000); // Fixed 10 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Phase animation for various effects
  useEffect(() => {
    const phaseInterval = setInterval(() => {
      setCurrentPhase(prev => (prev + 1) % 360);
    }, 50);
    
    return () => clearInterval(phaseInterval);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (orbRef.current && animationModes[animationMode] === 'mouse-follow') {
        const rect = orbRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const deltaX = (e.clientX - centerX) * 0.1;
        const deltaY = (e.clientY - centerY) * 0.1;
        
        setPosition({ x: deltaX, y: deltaY });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [animationMode]);

  // Calculate dynamic styles based on animation mode
  const getDynamicStyles = () => {
    const mode = animationModes[animationMode];
    const phase = currentPhase;
    
    switch (mode) {
      case 'orbital':
        return {
          transform: `translate(${Math.cos(phase * 0.05) * 30}px, ${Math.sin(phase * 0.05) * 30}px) rotate(${phase}deg)`,
        };
      
      case 'pulse-wave':
        const scale = 1 + Math.sin(phase * 0.1) * 0.3;
        return {
          transform: `scale(${scale}) rotate(${phase * 0.5}deg)`,
        };
      
      case 'morph-shape':
        const borderRadius = 50 + Math.sin(phase * 0.08) * 30;
        return {
          borderRadius: `${borderRadius}%`,
          transform: `rotate(${phase * 0.3}deg)`,
        };
      
      case 'color-cycle':
        const hue = phase;
        return {
          filter: `hue-rotate(${hue}deg) saturate(1.5)`,
          transform: `rotate(${phase * 0.2}deg)`,
        };
      
      case 'spiral':
        const spiralX = Math.cos(phase * 0.1) * (phase * 0.1);
        const spiralY = Math.sin(phase * 0.1) * (phase * 0.1);
        return {
          transform: `translate(${spiralX}px, ${spiralY}px) rotate(${phase * 2}deg)`,
        };
      
      case 'bounce':
        const bounceY = Math.abs(Math.sin(phase * 0.15)) * 50;
        return {
          transform: `translateY(-${bounceY}px) rotate(${phase * 0.5}deg)`,
        };
      
      case 'breathing':
        const breathScale = 1 + Math.sin(phase * 0.05) * 0.5;
        const breathOpacity = 0.6 + Math.sin(phase * 0.05) * 0.4;
        return {
          transform: `scale(${breathScale})`,
          opacity: breathOpacity,
        };
      
      default: // mouse-follow
        return {
          transform: `translate(${position.x}px, ${position.y}px)`,
        };
    }
  };

  if (!isClient) return null;

  return (
    <div
      ref={orbRef}
      className={`relative transition-all duration-300 ease-out ${className}`}
      style={getDynamicStyles()}
    >
      {/* Main orb layers with different behaviors per mode */}
      <div className="relative w-32 h-32">
        {/* Background layer */}
        <div 
          className="absolute inset-0 bg-gradient-to-r from-blue-800 to-blue-900 rounded-full opacity-20"
          style={{
            animationDuration: animationModes[animationMode] === 'pulse-wave' ? '1s' : '3s',
          }}
        />
        
        {/* Middle layer */}
        <div 
          className="absolute inset-4 bg-gradient-to-r from-blue-900 to-slate-800 rounded-full opacity-30"
          style={{
            animation: animationModes[animationMode] === 'color-cycle' ? 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite' : 'ping 4s cubic-bezier(0, 0, 0.2, 1) infinite',
          }}
        />
        
        {/* Inner core */}
        <div 
          className="absolute inset-8 bg-gradient-to-r from-blue-800 to-blue-700 rounded-full opacity-40"
          style={{
            transform: animationModes[animationMode] === 'morph-shape' ? `rotate(${currentPhase * -0.5}deg)` : '',
          }}
        />
        
        {/* Dynamic particles around orb */}
        {animationModes[animationMode] === 'spiral' && (
          <div className="absolute inset-0">
            {Array.from({ length: 8 }).map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-blue-400 rounded-full"
                style={{
                  left: '50%',
                  top: '50%',
                  transform: `
                    translate(-50%, -50%) 
                    rotate(${currentPhase * 2 + i * 45}deg) 
                    translateX(${40 + Math.sin(currentPhase * 0.1) * 20}px)
                  `,
                  opacity: 0.7,
                }}
              />
            ))}
          </div>
        )}
        
        {/* Orbital rings for orbital mode */}
        {animationModes[animationMode] === 'orbital' && (
          <>
            <div 
              className="absolute inset-[-20px] border border-blue-300 rounded-full opacity-20"
              style={{ transform: `rotate(${currentPhase * 0.5}deg)` }}
            />
            <div 
              className="absolute inset-[-40px] border border-blue-400 rounded-full opacity-10"
              style={{ transform: `rotate(${currentPhase * -0.3}deg)` }}
            />
          </>
        )}
        
        {/* Breathing glow effect */}
        {animationModes[animationMode] === 'breathing' && (
          <div 
            className="absolute inset-[-10px] bg-gradient-radial from-blue-400/30 to-transparent rounded-full"
            style={{
              transform: `scale(${1 + Math.sin(currentPhase * 0.05) * 0.8})`,
              opacity: 0.3 + Math.sin(currentPhase * 0.05) * 0.3,
            }}
          />
        )}
        
        {/* Bounce trail effect */}
        {animationModes[animationMode] === 'bounce' && (
          <div className="absolute inset-0">
            {Array.from({ length: 3 }).map((_, i) => (
              <div
                key={i}
                className="absolute inset-2 bg-gradient-to-r from-blue-600/20 to-blue-800/20 rounded-full"
                style={{
                  transform: `translateY(${Math.abs(Math.sin((currentPhase - i * 30) * 0.15)) * 25}px)`,
                  opacity: 0.3 - i * 0.1,
                }}
              />
            ))}
          </div>
        )}
      </div>
      
      {/* Mode indicator dot */}
      <div className="absolute -bottom-2 -right-2 w-3 h-3 bg-white rounded-full shadow-lg flex items-center justify-center">
        <div 
          className="w-1.5 h-1.5 rounded-full"
          style={{
            backgroundColor: `hsl(${animationMode * 40}, 70%, 50%)`,
          }}
        />
      </div>
    </div>
  );
};

export default InteractiveOrb; 