'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Brain, Cpu, Database, Code, Zap, Target, Star, Heart } from 'lucide-react';

// Interfaces
interface FloatingParticle {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  opacity: number;
  direction: number;
}

interface ClickableElement {
  id: number;
  x: number;
  y: number;
  size: number;
  type: 'circle' | 'star' | 'heart' | 'brain';
  points: number;
  opacity: number;
}

interface AIIcon {
  id: number;
  x: number;
  y: number;
  size: number;
  rotation: number;
  speed: number;
  icon: React.ComponentType<any>;
  color: string;
}

// Animation Mode 1: Floating Particles
export const FloatingParticles: React.FC = () => {
  const [particles, setParticles] = useState<FloatingParticle[]>([]);
  const [isClient, setIsClient] = useState(false);
  const animationRef = useRef<number | null>(null);

  useEffect(() => {
    setIsClient(true);
    if (typeof window === 'undefined') return;
    
    const initialParticles: FloatingParticle[] = Array.from({ length: 50 }, (_, i) => ({
      id: i,
      x: (i * 37) % window.innerWidth,
      y: (i * 23) % window.innerHeight,
      size: (i % 4) + 1,
      speed: ((i % 5) + 1) * 0.1,
      opacity: ((i % 5) + 1) * 0.1,
      direction: (i * 0.5) % (Math.PI * 2),
    }));
    
    setParticles(initialParticles);

    const animate = () => {
      setParticles(prev => prev.map(particle => {
        let { x, y, direction } = particle;
        
        x += Math.cos(direction) * particle.speed;
        y += Math.sin(direction) * particle.speed;
        
        if (x < 0) x = window.innerWidth;
        if (x > window.innerWidth) x = 0;
        if (y < 0) y = window.innerHeight;
        if (y > window.innerHeight) y = 0;
        
        return { ...particle, x, y };
      }));
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  if (!isClient) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map(particle => (
        <div
          key={particle.id}
          className="absolute w-1 h-1 bg-blue-800 rounded-full"
          style={{
            left: particle.x,
            top: particle.y,
            width: particle.size,
            height: particle.size,
            opacity: particle.opacity,
          }}
        />
      ))}
    </div>
  );
};

// Animation Mode 2: Click Game
export const ClickGame: React.FC = () => {
  const [elements, setElements] = useState<ClickableElement[]>([]);
  const [score, setScore] = useState(0);
  const [isClient, setIsClient] = useState(false);
  const elementCounter = useRef(0);

  const spawnElement = React.useCallback(() => {
    if (typeof window === 'undefined') return;
    
    const types: Array<ClickableElement['type']> = ['circle', 'star', 'heart', 'brain'];
    const typeIndex = elementCounter.current % types.length;
    const type = types[typeIndex];
    
    const newElement: ClickableElement = {
      id: Date.now() + elementCounter.current,
      x: ((elementCounter.current * 73) % (window.innerWidth - 50)),
      y: ((elementCounter.current * 47) % (window.innerHeight - 50)),
      size: ((elementCounter.current % 20) + 15),
      type,
      points: type === 'brain' ? 10 : type === 'star' ? 5 : 1,
      opacity: 1,
    };
    
    elementCounter.current++;
    setElements(prev => [...prev, newElement]);
    
    setTimeout(() => {
      setElements(prev => prev.filter(el => el.id !== newElement.id));
    }, 3000);
  }, []);

  useEffect(() => {
    setIsClient(true);
    const interval = setInterval(spawnElement, 800);
    return () => clearInterval(interval);
  }, [spawnElement]);

  const handleClick = (element: ClickableElement) => {
    setScore(prev => prev + element.points);
    setElements(prev => prev.filter(el => el.id !== element.id));
  };

  const ElementIcon = ({ type }: { type: ClickableElement['type'] }) => {
    switch (type) {
      case 'star': return <Star className="w-full h-full" />;
      case 'heart': return <Heart className="w-full h-full" />;
      case 'brain': return <Brain className="w-full h-full" />;
      default: return <div className="w-full h-full rounded-full bg-current" />;
    }
  };

  if (!isClient) return null;

  return (
    <div className="absolute inset-0 overflow-hidden">
      {elements.map(element => (
        <div
          key={element.id}
          className="absolute cursor-pointer hover:scale-110 transition-transform duration-200"
          style={{
            left: element.x,
            top: element.y,
            width: element.size,
            height: element.size,
            opacity: element.opacity,
            color: element.type === 'brain' ? '#7c3aed' : element.type === 'star' ? '#f59e0b' : element.type === 'heart' ? '#ef4444' : '#3b82f6'
          }}
          onClick={() => handleClick(element)}
        >
          <ElementIcon type={element.type} />
        </div>
      ))}
    </div>
  );
};

// Animation Mode 3: Floating AI Icons
export const FloatingAIIcons: React.FC = () => {
  const [icons, setIcons] = useState<AIIcon[]>([]);
  const [isClient, setIsClient] = useState(false);
  const animationRef = useRef<number | null>(null);

  const aiIcons = [Brain, Cpu, Database, Code, Zap, Target];
  const colors = ['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'];

  useEffect(() => {
    setIsClient(true);
    if (typeof window === 'undefined') return;
    
    const initialIcons: AIIcon[] = Array.from({ length: 12 }, (_, i) => ({
      id: i,
      x: (i * 83) % window.innerWidth,
      y: (i * 67) % window.innerHeight,
      size: ((i % 20) + 20),
      rotation: 0,
      speed: ((i % 3) + 1) * 0.1,
      icon: aiIcons[i % aiIcons.length],
      color: colors[i % colors.length],
    }));
    
    setIcons(initialIcons);

    const animate = () => {
      setIcons(prev => prev.map(icon => {
        let { x, y, rotation } = icon;
        
        y -= icon.speed;
        rotation += 1;
        
        if (y < -50) y = window.innerHeight + 50;
        
        return { ...icon, x, y, rotation };
      }));
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  if (!isClient) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {icons.map(icon => {
        const IconComponent = icon.icon;
        return (
          <div
            key={icon.id}
            className="absolute opacity-20"
            style={{
              left: icon.x,
              top: icon.y,
              transform: `rotate(${icon.rotation}deg)`,
              color: icon.color,
            }}
          >
            <IconComponent size={icon.size} />
          </div>
        );
      })}
    </div>
  );
};

// Animation Mode 4: Digital Rain
export const DigitalRain: React.FC = () => {
  const [drops, setDrops] = useState<Array<{id: number, x: number, y: number, speed: number, char: string}>>([]);
  const [isClient, setIsClient] = useState(false);
  const animationRef = useRef<number | null>(null);

  const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

  useEffect(() => {
    setIsClient(true);
    if (typeof window === 'undefined') return;
    
    const initialDrops = Array.from({ length: 30 }, (_, i) => ({
      id: i,
      x: (i * 43) % window.innerWidth,
      y: (i * 29) % window.innerHeight,
      speed: ((i % 2) + 1),
      char: characters[i % characters.length],
    }));
    
    setDrops(initialDrops);

    const animate = () => {
      setDrops(prev => prev.map(drop => {
        let { y, char } = drop;
        y += drop.speed;
        
        if (y > window.innerHeight) {
          y = -20;
          char = characters[(drop.id * 7) % characters.length];
        }
        
        return { ...drop, y, char };
      }));
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  if (!isClient) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {drops.map(drop => (
        <div
          key={drop.id}
          className="absolute text-green-500 font-mono text-sm opacity-30"
          style={{
            left: drop.x,
            top: drop.y,
          }}
        >
          {drop.char}
        </div>
      ))}
    </div>
  );
};

// Animation Mode 5: Neural Network
export const NeuralNetwork: React.FC = () => {
  const [nodes, setNodes] = useState<Array<{id: number, x: number, y: number, connections: number[]}>>([]);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    if (typeof window === 'undefined') return;
    
    const nodeCount = 15;
    const initialNodes = Array.from({ length: nodeCount }, (_, i) => ({
      id: i,
      x: (i * 97) % window.innerWidth,
      y: (i * 71) % window.innerHeight,
      connections: Array.from({ length: (i % 3) + 1 }, (_, j) => 
        (i + j + 1) % nodeCount
      ).filter(id => id !== i),
    }));
    
    setNodes(initialNodes);
  }, []);

  if (!isClient) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <svg className="w-full h-full">
        {nodes.map(node => 
          node.connections.map(connId => {
            const connNode = nodes[connId];
            if (!connNode) return null;
            return (
              <line
                key={`${node.id}-${connId}`}
                x1={node.x}
                y1={node.y}
                x2={connNode.x}
                y2={connNode.y}
                stroke="#3b82f6"
                strokeWidth="1"
                opacity="0.2"
                className="animate-pulse"
              />
            );
          })
        )}
        {nodes.map(node => (
          <circle
            key={node.id}
            cx={node.x}
            cy={node.y}
            r="4"
            fill="#3b82f6"
            opacity="0.6"
            className="animate-pulse"
          />
        ))}
      </svg>
    </div>
  );
};

// Main Background Animations Component
interface BackgroundAnimationsProps {
  currentMode: number;
}

const BackgroundAnimations: React.FC<BackgroundAnimationsProps> = ({ currentMode }) => {
  const animationComponents = [
    FloatingParticles,
    ClickGame,
    FloatingAIIcons,
    DigitalRain,
    NeuralNetwork,
  ];

  const CurrentComponent = animationComponents[currentMode];

  return <CurrentComponent />;
};

export default BackgroundAnimations; 