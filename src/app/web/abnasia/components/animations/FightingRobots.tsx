'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Zap, Shield, Swords, Cpu } from 'lucide-react';

interface Robot {
  id: string;
  x: number;
  y: number;
  health: number;
  energy: number;
  isAttacking: boolean;
  isDefending: boolean;
  animationPhase: number;
  color: string;
  name: string;
}

interface FightingRobotsProps {
  className?: string;
}

const FightingRobots: React.FC<FightingRobotsProps> = ({ className = '' }) => {
  const [robots, setRobots] = useState<Robot[]>([]);
  const [battlePhase, setBattlePhase] = useState(0);
  const [currentAttacker, setCurrentAttacker] = useState<string | null>(null);
  const [effects, setEffects] = useState<Array<{id: string, x: number, y: number, type: string}>>([]);
  const [isClient, setIsClient] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const effectIdCounter = useRef(0);

  // Initialize robots
  useEffect(() => {
    setIsClient(true);
    if (typeof window === 'undefined') return;
    
    const initialRobots: Robot[] = [
      {
        id: 'robot1',
        x: window.innerWidth * 0.2,
        y: window.innerHeight * 0.6,
        health: 100,
        energy: 100,
        isAttacking: false,
        isDefending: false,
        animationPhase: 0,
        color: '#3b82f6', // Blue
        name: 'ABN-Alpha'
      },
      {
        id: 'robot2',
        x: window.innerWidth * 0.8,
        y: window.innerHeight * 0.6,
        health: 100,
        energy: 100,
        isAttacking: false,
        isDefending: false,
        animationPhase: 0,
        color: '#ef4444', // Red
        name: 'Tech-Beta'
      }
    ];
    
    setRobots(initialRobots);
  }, []);

  // Battle sequence controller
  useEffect(() => {
    const battleInterval = setInterval(() => {
      setBattlePhase(prev => {
        const newPhase = prev + 1;
        
        // Deterministically choose an attacker based on battle phase
        const attackerIndex = newPhase % 2;
        const attackerId = attackerIndex === 0 ? 'robot1' : 'robot2';
        setCurrentAttacker(attackerId);
        
        // Execute attack sequence
        setRobots(prevRobots => prevRobots.map(robot => {
          if (robot.id === attackerId) {
            return { ...robot, isAttacking: true, energy: Math.max(0, robot.energy - 20) };
          } else {
            // Defender - deterministic defense based on phase
            const willDefend = (newPhase % 3) !== 0; // Defend 2/3 of the time
            const damage = willDefend ? 5 : 15;
            return { 
              ...robot, 
              isDefending: willDefend,
              health: Math.max(0, robot.health - damage)
            };
          }
        }));
        
        // Add battle effect
        const effectId = `effect-${++effectIdCounter.current}`;
        if (attackerIndex === 0) {
          setEffects(prev => [...prev, {
            id: `${effectId}-laser`,
            x: window.innerWidth * 0.5,
            y: window.innerHeight * 0.6,
            type: 'laser'
          }]);
        } else {
          setEffects(prev => [...prev, {
            id: `${effectId}-explosion`,
            x: window.innerWidth * 0.5,
            y: window.innerHeight * 0.6,
            type: 'explosion'
          }]);
        }
        
        return newPhase;
      });
      
      // Reset attack states after animation
      setTimeout(() => {
        setRobots(prev => prev.map(robot => ({
          ...robot,
          isAttacking: false,
          isDefending: false
        })));
        setCurrentAttacker(null);
      }, 1000);
      
      // Remove effects after they finish
      setTimeout(() => {
        const effectId = `effect-${effectIdCounter.current}`;
        setEffects(prev => prev.filter(effect => 
          !effect.id.startsWith(`${effectId}-`)
        ));
      }, 1500);
      
    }, 3000); // Battle every 3 seconds
    
    return () => clearInterval(battleInterval);
  }, []);

  // Animation phase controller
  useEffect(() => {
    const phaseInterval = setInterval(() => {
      setRobots(prev => prev.map(robot => ({
        ...robot,
        animationPhase: (robot.animationPhase + 1) % 360
      })));
    }, 50);
    
    return () => clearInterval(phaseInterval);
  }, []);

  // Robot component
  const RobotDisplay = ({ robot }: { robot: Robot }) => {
    const isMoving = robot.isAttacking || robot.isDefending;
    const moveOffset = isMoving ? Math.sin(robot.animationPhase * 0.3) * 10 : 0;
    const scaleEffect = robot.isAttacking ? 1.2 : robot.isDefending ? 0.9 : 1;
    
    return (
      <div
        className="absolute transition-all duration-300 pointer-events-none"
        style={{
          left: robot.x - 30,
          top: robot.y - 60 + moveOffset,
          transform: `scale(${scaleEffect})`,
          zIndex: robot.isAttacking ? 20 : 10,
        }}
      >
        {/* Robot Body */}
        <div className="relative w-16 h-20">
          {/* Head */}
          <div 
            className="absolute top-0 left-2 w-12 h-8 rounded-t-lg border-2"
            style={{ 
              backgroundColor: robot.color,
              borderColor: robot.isAttacking ? '#fbbf24' : robot.isDefending ? '#10b981' : robot.color,
              boxShadow: robot.isAttacking ? '0 0 15px #fbbf24' : robot.isDefending ? '0 0 15px #10b981' : 'none'
            }}
          >
            {/* Eyes */}
            <div className="flex justify-between px-2 py-1">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            </div>
          </div>
          
          {/* Body */}
          <div 
            className="absolute top-6 left-1 w-14 h-10 rounded border-2"
            style={{ 
              backgroundColor: robot.color,
              borderColor: robot.isAttacking ? '#fbbf24' : robot.isDefending ? '#10b981' : robot.color,
            }}
          >
            {/* Chest Panel */}
            <div className="absolute top-1 left-1 right-1 h-6 bg-black/20 rounded flex items-center justify-center">
              {robot.isAttacking ? <Zap size={12} className="text-yellow-400" /> :
               robot.isDefending ? <Shield size={12} className="text-green-400" /> :
               <Cpu size={12} className="text-white" />}
            </div>
          </div>
          
          {/* Arms */}
          <div 
            className="absolute top-8 -left-2 w-4 h-6 rounded border"
            style={{ 
              backgroundColor: robot.color,
              transform: robot.isAttacking ? 'rotate(-30deg)' : robot.isDefending ? 'rotate(45deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s'
            }}
          />
          <div 
            className="absolute top-8 -right-2 w-4 h-6 rounded border"
            style={{ 
              backgroundColor: robot.color,
              transform: robot.isAttacking ? 'rotate(30deg)' : robot.isDefending ? 'rotate(-45deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s'
            }}
          />
          
          {/* Legs */}
          <div 
            className="absolute top-14 left-3 w-3 h-8 rounded-b border"
            style={{ backgroundColor: robot.color }}
          />
          <div 
            className="absolute top-14 left-10 w-3 h-8 rounded-b border"
            style={{ backgroundColor: robot.color }}
          />
        </div>
        
        {/* Robot Name */}
        <div className="absolute -bottom-8 left-0 right-0 text-center">
          <div className="text-xs font-mono text-slate-600">{robot.name}</div>
        </div>
        
        {/* Health Bar */}
        <div className="absolute -top-8 left-0 right-0">
          <div className="w-full h-1 bg-gray-300 rounded overflow-hidden">
            <div 
              className="h-full transition-all duration-500"
              style={{ 
                width: `${robot.health}%`,
                backgroundColor: robot.health > 50 ? '#10b981' : robot.health > 20 ? '#f59e0b' : '#ef4444'
              }}
            />
          </div>
        </div>
        
        {/* Energy Bar */}
        <div className="absolute -top-6 left-0 right-0">
          <div className="w-full h-0.5 bg-gray-200 rounded overflow-hidden">
            <div 
              className="h-full bg-blue-400 transition-all duration-500"
              style={{ width: `${robot.energy}%` }}
            />
          </div>
        </div>
        
        {/* Attack Effect */}
        {robot.isAttacking && (
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute inset-0 bg-yellow-400/30 rounded-full animate-ping" />
            {Array.from({ length: 6 }).map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-6 bg-yellow-400"
                style={{
                  left: '50%',
                  top: '50%',
                  transform: `translate(-50%, -50%) rotate(${i * 60}deg) translateY(-20px)`,
                  opacity: 0.8,
                }}
              />
            ))}
          </div>
        )}
        
        {/* Defense Effect */}
        {robot.isDefending && (
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute inset-[-10px] border-2 border-green-400 rounded-full animate-pulse" />
            <div className="absolute inset-[-5px] bg-green-400/20 rounded-full" />
          </div>
        )}
      </div>
    );
  };

  if (!isClient) return null;

  return (
    <div ref={containerRef} className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {/* Battle Arena Background */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-slate-200/20 to-transparent" />
      
      {/* Robots */}
      {robots.map(robot => (
        <RobotDisplay key={robot.id} robot={robot} />
      ))}
      
      {/* Battle Effects */}
      {effects.map(effect => (
        <div
          key={effect.id}
          className="absolute pointer-events-none"
          style={{
            left: effect.x - 20,
            top: effect.y - 20,
          }}
        >
          {effect.type === 'laser' ? (
            <div className="relative">
              <div className="w-8 h-1 bg-red-500 animate-pulse" />
              <div className="w-16 h-0.5 bg-red-300 animate-ping" />
            </div>
          ) : (
            <div className="relative">
              <div className="w-10 h-10 bg-orange-500 rounded-full animate-ping opacity-70" />
              <div className="absolute inset-0 w-6 h-6 bg-yellow-400 rounded-full animate-bounce" />
            </div>
          )}
        </div>
      ))}
      
      {/* Battle Status */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2">
        <div className="text-xs text-white font-mono">
          Battle Phase: {battlePhase} | Current Action: {currentAttacker ? `${robots.find(r => r.id === currentAttacker)?.name} attacking!` : 'Standby'}
        </div>
      </div>
    </div>
  );
};

export default FightingRobots; 