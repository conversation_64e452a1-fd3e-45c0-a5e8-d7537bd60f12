import React from 'react';
import { useTranslation } from 'react-i18next';
import customersData from './customersdata';
import {
  Business as BusinessIcon,
  Factory as FactoryIcon,
  Store as StoreIcon,
  School as SchoolIcon,
  LocalHospital as HospitalIcon,
  AccountBalance as BankIcon,
  Restaurant as RestaurantIcon,
  LocalShipping as LogisticsIcon,
  Apartment as RealEstateIcon,
  Computer as TechIcon,
  Phone as TelecomIcon,
  LocalGroceryStore as RetailIcon,
  LocalLibrary as EducationIcon,
  LocalPharmacy as PharmacyIcon,
  LocalGasStation as EnergyIcon,
  LocalAtm as FinanceIcon,
  LocalCafe as FoodIcon,
  LocalHotel as HospitalityIcon,
  LocalCarWash as AutomotiveIcon,
  LocalFlorist as AgricultureIcon,
  LocalMall as ShoppingIcon,
  LocalPostOffice as PostalIcon,
  LocalPrintshop as PrintingIcon,
  LocalSee as TourismIcon,
  LocalTaxi as TransportationIcon,
  Security as SecurityIcon,
  Lock as LockIcon,
  VpnKey as KeyIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Password as PasswordIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Help as HelpIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountCircleIcon,
  Group as GroupIcon,
  BusinessCenter as BusinessCenterIcon,
  Work as WorkIcon,
  Assignment as AssignmentIcon,
  Dashboard as DashboardIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
  ShowChart as ShowChartIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  Euro as EuroIcon,
  CurrencyExchange as CurrencyIcon,
  CreditCard as CreditCardIcon,
  AccountBalanceWallet as WalletIcon,
  Receipt as ReceiptIcon,
  Description as DocumentIcon,
  Folder as FolderIcon,
  Cloud as CloudIcon,
  Storage as StorageIcon,
  Devices as DevicesIcon,
  Smartphone as SmartphoneIcon,
  Tablet as TabletIcon,
  Laptop as LaptopIcon,
  DesktopWindows as DesktopIcon,
  Print as PrintIcon,
  Scanner as ScannerIcon,
  Router as RouterIcon,
  Wifi as WifiIcon,
  Bluetooth as BluetoothIcon,
  NetworkCheck as NetworkIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  SdStorage as SdIcon,
  Usb as UsbIcon,
  Power as PowerIcon,
  BatteryChargingFull as BatteryIcon,
  PowerSettingsNew as PowerSettingsIcon,
  SettingsBackupRestore as BackupIcon,
  VerifiedUser as VerifiedIcon,
  Gavel as GavelIcon,
  Balance as BalanceIcon,
  Scale as ScaleIcon,
  Policy as PolicyIcon,
  Verified as VerifiedIcon2,
  Check as CheckIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  ContentCopy as CopyIcon,
  ContentPaste as PasteIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  Search as SearchIcon,
  FindInPage as FindIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Fullscreen as FullscreenIcon,
  MoreVert as MoreVertIcon,
  Menu as MenuIcon,
  Apps as AppsIcon,
  ViewList as ListIcon,
  ViewModule as ModuleIcon,
  ViewQuilt as QuiltIcon,
  ViewWeek as WeekIcon,
  ViewDay as DayIcon,
  ViewAgenda as AgendaIcon,
  ViewCarousel as CarouselIcon,
  ViewComfy as ComfyIcon,
  ViewCompact as CompactIcon,
  ViewHeadline as HeadlineIcon,
  ViewStream as StreamIcon,
  ViewColumn as ColumnIcon,
  ViewArray as ArrayIcon,
  ViewComfyAlt as ComfyAltIcon,
  ViewCompactAlt as CompactAltIcon,
  ViewSidebar as SidebarIcon,
  ViewInAr as InArIcon,
  ViewKanban as KanbanIcon,
  ViewTimeline as TimelineIcon2,
} from '@mui/icons-material';
// TypeScript interfaces - aligned with customersdata.ts
interface Customer {
  categories: string[];
  name: string;
  image: string[];
  description: string;
  industry: string;
  sku: string;
  website: string;
  id?: string;
}

interface Category {
  name: string;
  image?: string[];
  itemCount: number;
}

interface CustomersProps {
  inventoryData?: Customer[];
  categories?: Category[];
}

// Simple customer card component to replace the missing ListItem
const CustomerCard: React.FC<{
  title: string;
  imageSrc: string;
  link?: string;
  openInNewTab?: boolean;
}> = ({ title, imageSrc, link, openInNewTab = false }) => {
  const { t } = useTranslation();
  
  const content = (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
      <div className="aspect-square mb-3 overflow-hidden rounded-md bg-gray-100">
        <img 
          src={imageSrc} 
          alt={`${title} - ${t('customers.imageAlt')}`}
          className="w-full h-full object-contain"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abn/abn.logo.griffin.withurl.png';
            target.alt = t('customers.fallbackImageAlt');
          }}
        />
      </div>
      <h3 className="font-medium text-gray-900 text-sm line-clamp-2">{title}</h3>
    </div>
  );

  if (link) {
    return (
      <a 
        href={link} 
        target={openInNewTab ? "_blank" : "_self"}
        rel={openInNewTab ? "noopener noreferrer" : undefined}
        className="block hover:transform hover:scale-105 transition-transform duration-200"
      >
        {content}
      </a>
    );
  }

  return content;
};

const Customers: React.FC<CustomersProps> = () => {
  const { t } = useTranslation();
  // Use local customers data
  const inventoryData = customersData as Customer[];
  
  const hotCustomerIDs = ['gl61ofjg', 'g913fstj', 'glf7g3xr', 'ggcpe6jf', 'g2td7qn0'];
  const hotCustomers = inventoryData.filter(product => 
    hotCustomerIDs.includes(product.sku)
  );
  
  // Generate categories from customers data
  const categoryData = inventoryData.reduce((acc: Category[], customer) => {
    customer.categories.forEach(categoryName => {
      const existingCategory = acc.find(cat => cat.name === categoryName);
      if (existingCategory) {
        existingCategory.itemCount += 1;
      } else {
        acc.push({
          name: categoryName,
          image: customer.image,
          itemCount: 1
        });
      }
    });
    return acc;
  }, []);
  
  const getImageSrc = (customer: Customer): string => {
    if (Array.isArray(customer.image) && customer.image.length > 0) {
      return customer.image[0];
    }
    return 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abn/abn.logo.griffin.withurl.png';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="pt-10 pb-6 flex flex-col items-center">
        <h2 className="text-4xl font-bold mb-3 text-gray-900">{t('customers.title')}</h2>
        <p className="text-gray-600 text-sm">{t('customers.subtitle')}</p>
      </div>
      {/* All Customers Grid */}
      <div className="my-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {inventoryData.map((item, index) => {
            const imageSrc = getImageSrc(item);
            return (
              <CustomerCard
                key={item.id || index}
                link={item.website}
                title={item.name}
                imageSrc={imageSrc}
                openInNewTab={true}
              />
            );
          })}
        </div>
      </div>
      <div className="mt-4 grid grid-cols-6 gap-2">
              <BusinessIcon className="text-gray-400" />
              <FactoryIcon className="text-gray-400" />
              <StoreIcon className="text-gray-400" />
              <SchoolIcon className="text-gray-400" />
              <HospitalIcon className="text-gray-400" />
              <BankIcon className="text-gray-400" />
              <RestaurantIcon className="text-gray-400" />
              <LogisticsIcon className="text-gray-400" />
              <RealEstateIcon className="text-gray-400" />
              <TechIcon className="text-gray-400" />
              <TelecomIcon className="text-gray-400" />
              <RetailIcon className="text-gray-400" />
              <EducationIcon className="text-gray-400" />
              <PharmacyIcon className="text-gray-400" />
              <EnergyIcon className="text-gray-400" />
              <FinanceIcon className="text-gray-400" />
              <FoodIcon className="text-gray-400" />
              <HospitalityIcon className="text-gray-400" />
              <AutomotiveIcon className="text-gray-400" />
              <AgricultureIcon className="text-gray-400" />
              <ShoppingIcon className="text-gray-400" />
              <PostalIcon className="text-gray-400" />
              <PrintingIcon className="text-gray-400" />
              <TourismIcon className="text-gray-400" />
            </div>
    </div>
  )
}

export default Customers;