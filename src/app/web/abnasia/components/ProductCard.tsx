import React, { useState, useMemo } from 'react';
import { Star, ShoppingBag, ChevronRight, Award, Info } from 'lucide-react';
import MakeItYoursButton from './MakeItYoursButton';

type Product = {
  id: string;
  name: string;
  price: number;
  currency: string;
  category: string;
  rating: number;
  reviewCount: number;
  description: string;
  relatedStoryId: number;
};

type Story = {
  id: number;
  customer: string;
  industry: string;
};

// Color schemes for the front face
const colorSchemes = [
  {
    gradient: 'from-green-50 to-emerald-100',
    iconBg: 'bg-white',
    iconColor: 'text-green-600',
    categoryColor: 'text-green-600',
    chevronColor: 'text-green-600'
  },
  {
    gradient: 'from-blue-50 to-cyan-100',
    iconBg: 'bg-white',
    iconColor: 'text-blue-600',
    categoryColor: 'text-blue-600',
    chevronColor: 'text-blue-600'
  },
  {
    gradient: 'from-purple-50 to-violet-100',
    iconBg: 'bg-white',
    iconColor: 'text-purple-600',
    categoryColor: 'text-purple-600',
    chevronColor: 'text-purple-600'
  },
  {
    gradient: 'from-orange-50 to-amber-100',
    iconBg: 'bg-white',
    iconColor: 'text-orange-600',
    categoryColor: 'text-orange-600',
    chevronColor: 'text-orange-600'
  },
  {
    gradient: 'from-rose-50 to-pink-100',
    iconBg: 'bg-white',
    iconColor: 'text-rose-600',
    categoryColor: 'text-rose-600',
    chevronColor: 'text-rose-600'
  },
  {
    gradient: 'from-indigo-50 to-blue-100',
    iconBg: 'bg-white',
    iconColor: 'text-indigo-600',
    categoryColor: 'text-indigo-600',
    chevronColor: 'text-indigo-600'
  },
  {
    gradient: 'from-teal-50 to-cyan-100',
    iconBg: 'bg-white',
    iconColor: 'text-teal-600',
    categoryColor: 'text-teal-600',
    chevronColor: 'text-teal-600'
  },
  {
    gradient: 'from-slate-50 to-gray-100',
    iconBg: 'bg-white',
    iconColor: 'text-slate-600',
    categoryColor: 'text-slate-600',
    chevronColor: 'text-slate-600'
  }
];

const ProductCard: React.FC<{ item: Product; relatedStory?: Story }> = ({ item, relatedStory }) => {
  const [isFlipped, setIsFlipped] = useState(false);
  
  // Generate consistent color scheme based on product ID
  const colorScheme = useMemo(() => {
    const hash = item.id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colorSchemes[hash % colorSchemes.length];
  }, [item.id]);
  
  return (
    <div 
      className="relative bg-white rounded-2xl corporate-shadow-lg w-full h-[28rem] border border-slate-200 cursor-pointer corporate-hover-shadow group overflow-hidden perspective-1000"
      onClick={() => setIsFlipped(!isFlipped)}
    >
      <div className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${isFlipped ? 'rotate-y-180' : ''}`}>
        {/* Front Face - Colorful Design */}
        <div className={`absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br ${colorScheme.gradient} rounded-2xl flex flex-col items-center justify-center p-8`}>
          {/* Product Icon */}
          <div className={`w-20 h-20 ${colorScheme.iconBg} rounded-2xl shadow-lg flex items-center justify-center mb-6 group-hover:scale-105 transition-transform`}>
            <ShoppingBag size={32} className={colorScheme.iconColor} />
          </div>
          
          {/* Product Info */}
          <div className="text-center">
            <div className={`text-xs font-semibold ${colorScheme.categoryColor} uppercase tracking-wider mb-2`}>
              {item.category}
            </div>
            <h3 className="text-lg font-bold text-slate-800 leading-tight mb-3 text-center max-w-48">
              {item.name}
            </h3>
            <div className="text-xs text-slate-500 bg-white bg-opacity-70 px-3 py-1 rounded-full">
              {item.currency}{item.price.toLocaleString()}
            </div>
          </div>
          
          {/* Subtle hint indicator */}
          <div className="absolute bottom-4 right-4 w-8 h-8 bg-white bg-opacity-50 rounded-full flex items-center justify-center">
            <ChevronRight size={16} className={`${colorScheme.chevronColor} group-hover:translate-x-0.5 transition-transform`} />
          </div>
        </div>
        
        {/* Back Face - Detailed Content */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-white rounded-2xl p-8 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <ShoppingBag size={16} className="text-green-600" />
              </div>
              <span className="text-xs font-semibold text-green-600 uppercase tracking-wide">{item.category}</span>
            </div>
            <div className="text-lg font-bold text-green-600">
              {item.currency}{item.price.toLocaleString()}
            </div>
          </div>
          
          {/* Content */}
          <div className="flex-1">
            <h3 className="text-lg font-bold text-slate-800 leading-tight mb-3">
              {item.name}
            </h3>
            <p className="text-sm text-slate-600 leading-relaxed mb-4">
              {item.description}
            </p>
            
            {/* Rating */}
            <div className="flex items-center space-x-2 mb-4">
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    size={14} 
                    className={i < Math.floor(item.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'} 
                  />
                ))}
              </div>
              <span className="text-sm text-slate-600">{item.rating}</span>
              <span className="text-xs text-slate-400">({item.reviewCount} reviews)</span>
            </div>
            
            {/* Related Story */}
            {relatedStory && (
              <div className="bg-blue-50 p-3 rounded-lg mb-4">
                <div className="flex items-center space-x-2 mb-1">
                  <Award size={14} className="text-blue-600" />
                  <span className="text-xs font-semibold text-blue-600">Success Story</span>
                </div>
                <div className="text-xs text-blue-700">
                  Used by {relatedStory.customer} in {relatedStory.industry}
                </div>
              </div>
            )}
          </div>
          
          {/* Action buttons */}
          <div className="grid grid-cols-2 gap-2">
            <MakeItYoursButton
              context={{
                type: 'product',
                title: item.name,
                id: item.id,
                category: item.category,
                price: item.price,
                currency: item.currency
              }}
              buttonStyle="primary"
              size="sm"
              className="flex-1 justify-center"
            />
            <button className="flex items-center justify-center gap-2 px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
              <Info size={16} />
              Details
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard; 