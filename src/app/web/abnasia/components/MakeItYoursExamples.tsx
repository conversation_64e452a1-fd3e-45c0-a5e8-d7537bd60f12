import React from 'react';
import MakeItYoursButton from './MakeItYoursButton';

const MakeItYoursExamples: React.FC = () => {
  return (
    <div className="p-8 space-y-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">MakeItYours Button Examples</h1>
        
        {/* Product Context Example */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold mb-4">Product Context</h2>
          <div className="flex flex-wrap gap-4">
            <MakeItYoursButton
              context={{
                type: 'product',
                title: 'AI Chatbot Pro',
                id: 'product-1',
                category: 'AI & Machine Learning',
                price: 299,
                currency: '$'
              }}
              buttonStyle="primary"
              size="sm"
            />
            
            <MakeItYoursButton
              context={{
                type: 'product',
                title: 'Enterprise Security Suite',
                id: 'product-2',
                category: 'Security',
                price: 1299,
                currency: '$'
              }}
              buttonStyle="secondary"
              size="md"
              buttonText="Get This Product"
            />
          </div>
        </div>

        {/* Success Story Context Example */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold mb-4">Success Story Context</h2>
          <div className="flex flex-wrap gap-4">
            <MakeItYoursButton
              context={{
                type: 'story',
                title: 'How TechCorp Increased Efficiency by 300%',
                customer: 'TechCorp Inc.',
                industry: 'Technology',
                location: 'San Francisco, CA'
              }}
              buttonText="Achieve Similar Results"
              buttonStyle="primary"
              size="md"
            />
            
            <MakeItYoursButton
              context={{
                type: 'story',
                title: 'Manufacturing Giant Saves $2M Annually',
                customer: 'Global Manufacturing Ltd.',
                industry: 'Manufacturing',
                location: 'Detroit, MI'
              }}
              buttonText="Get Similar Savings"
              buttonStyle="minimal"
              size="sm"
            />
          </div>
        </div>

        {/* Service Context Example */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold mb-4">Service Context</h2>
          <div className="flex flex-wrap gap-4">
            <MakeItYoursButton
              context={{
                type: 'service',
                title: 'Custom Software Development',
                category: 'Development Services'
              }}
              buttonText="Start Your Project"
              buttonStyle="primary"
              size="lg"
            />
            
            <MakeItYoursButton
              context={{
                type: 'service',
                title: 'Digital Marketing Consultation',
                category: 'Marketing Services'
              }}
              buttonText="Book Consultation"
              buttonStyle="secondary"
              size="md"
            />
          </div>
        </div>

        {/* General Context Example */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold mb-4">General Context</h2>
          <div className="flex flex-wrap gap-4">
            <MakeItYoursButton
              context={{
                type: 'general',
                title: 'ABN Green Solutions'
              }}
              buttonText="Contact Us"
              buttonStyle="minimal"
              size="sm"
            />
            
            <MakeItYoursButton
              context={{
                type: 'general',
                title: 'Partnership Opportunities'
              }}
              buttonText="Become a Partner"
              buttonStyle="primary"
              size="md"
            />
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4 text-blue-900">How to Use</h2>
          <div className="text-sm text-blue-800 space-y-2">
            <p><strong>Context Types:</strong> 'product', 'story', 'service', 'general'</p>
            <p><strong>Button Styles:</strong> 'primary', 'secondary', 'minimal'</p>
            <p><strong>Sizes:</strong> 'sm', 'md', 'lg'</p>
            <p><strong>Required:</strong> context.type and context.title</p>
            <p><strong>Optional:</strong> All other context fields, buttonText, buttonStyle, size, className</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MakeItYoursExamples; 