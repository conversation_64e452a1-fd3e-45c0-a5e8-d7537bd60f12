import React from 'react';
import { TrendingUp, Award, Users, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface StatItem {
  icon: React.ReactNode;
  value: string;
  labelKey: string;
  bgColor: string;
  iconColor: string;
}

interface QuickStatsProps {
  stats?: StatItem[];
}

export default function QuickStats({ stats }: QuickStatsProps) {
  const { t } = useTranslation();
  
  const defaultStats: StatItem[] = [
    {
      icon: <TrendingUp className="w-8 h-8" />,
      value: "+",
      labelKey: "stats.successfulProjects",
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600"
    },
    {
      icon: <Users className="w-8 h-8" />,
      value: "+",
      labelKey: "stats.enterpriseClients",
      bgColor: "bg-green-100",
      iconColor: "text-green-600"
    },
    {
      icon: <Target className="w-8 h-8" />,
      value: "100%",
      labelKey: "stats.successRate",
      bgColor: "bg-purple-100",
      iconColor: "text-purple-600"
    },
    {
      icon: <Award className="w-8 h-8" />,
      value: "15+",
      labelKey: "stats.yearsExperience",
      bgColor: "bg-orange-100",
      iconColor: "text-orange-600"
    }
  ];

  const statsToUse = stats || defaultStats;

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto">
      {statsToUse.map((stat, index) => (
        <div key={index} className="text-center">
          <div className={`flex items-center justify-center w-16 h-16 ${stat.bgColor} rounded-full mx-auto mb-4`}>
            <div className={stat.iconColor}>
              {stat.icon}
            </div>
          </div>
          <div className="text-3xl font-bold text-gradient">{stat.value}</div>
          <div className="text-sm text-gray-600">{t(stat.labelKey)}</div>
        </div>
      ))}
    </div>
  );
} 