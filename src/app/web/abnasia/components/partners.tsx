import React from 'react';
import { useTranslation } from 'react-i18next';
import partners from './partnersdata';

interface Partner {
  categories: string[];
  name: string;
  image: string[];
  description: string;
  industry: string;
  sku: string;
  website: string;
  id?: string;
}

interface Category {
  name: string;
  image?: string[];
  itemCount: number;
}

interface PartnersProps {
  inventoryData?: Partner[];
  categories?: Category[];
}

const PartnerCard: React.FC<{
  title: string;
  imageSrc: string;
  link?: string;
  openInNewTab?: boolean;
}> = ({ title, imageSrc, link, openInNewTab = false }) => {
  const { t } = useTranslation();
  
  const content = (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
      <div className="aspect-square mb-3 overflow-hidden rounded-md bg-gray-100">
        <img 
          src={imageSrc} 
          alt={`${title} - ${t('partners.imageAlt')}`}
          className="w-full h-full object-contain"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abn/abn.logo.griffin.withurl.png';
            target.alt = t('partners.fallbackImageAlt');
          }}
        />
      </div>
      <h3 className="font-medium text-gray-900 text-sm line-clamp-2">{title}</h3>
    </div>
  );

  if (link) {
    return (
      <a 
        href={link} 
        target={openInNewTab ? "_blank" : "_self"}
        rel={openInNewTab ? "noopener noreferrer" : undefined}
        className="block hover:transform hover:scale-105 transition-transform duration-200"
      >
        {content}
      </a>
    );
  }

  return content;
};

const Partners: React.FC<PartnersProps> = () => {
  const { t } = useTranslation();
  // Use local partners data
  const inventoryData = partners;
  
  const hotPartnerIDs = ['g7eleven1', 'gfamily1', 'gsinopac1', 'gl61ofjg', 'ggcpe6jf'];
  const hotPartners = inventoryData.filter(partner => 
    hotPartnerIDs.includes(partner.sku)
  );
  
  // Generate categories from partners data
  const categoryData = inventoryData.reduce((acc: Category[], partner) => {
    partner.categories.forEach(categoryName => {
      const existingCategory = acc.find(cat => cat.name === categoryName);
      if (existingCategory) {
        existingCategory.itemCount += 1;
      } else {
        acc.push({
          name: categoryName,
          image: partner.image,
          itemCount: 1
        });
      }
    });
    return acc;
  }, []);
  
  const getImageSrc = (partner: Partner): string => {
    if (Array.isArray(partner.image) && partner.image.length > 0) {
      return partner.image[0];
    }
    return 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abn/abn.logo.griffin.withurl.png';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="pt-10 pb-6 flex flex-col items-center">
        <h2 className="text-4xl font-bold mb-3 text-gray-900">{t('partners.title')}</h2>
        <p className="text-gray-600 text-sm">{t('partners.subtitle')}</p>
      </div>
      
      {/* All Partners Grid */}
      <div className="my-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {inventoryData.map((item, index) => {
            const imageSrc = getImageSrc(item);
            return (
              <PartnerCard
                key={item.id || index}
                link={item.website}
                title={item.name}
                imageSrc={imageSrc}
                openInNewTab={true}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Partners;