import React, { useState } from 'react';
import { Building2, ChevronR<PERSON>, Star, Quote, FileText, Mail } from 'lucide-react';
import { useThemeStyles } from '@/themes/ThemeProvider';

type Testimonial = {
  id: string;
  clientName: string;
  clientTitle: string;
  company: string;
  industry: string;
  testimonial: string;
  rating: number;
  projectType: string;
  results: string[];
};

const TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({ testimonial }) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const { getCardClass, isPaperTheme } = useThemeStyles();
  
  return (
    <div
      className={`relative w-full h-[32rem] cursor-pointer group overflow-hidden perspective-1000 ${getCardClass()}`}
      onClick={() => setIsFlipped(!isFlipped)}
    >
      <div className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${isFlipped ? 'rotate-y-180' : ''}`}>
        {/* Front Face - Minimal Design */}
        <div className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl flex flex-col items-center justify-center p-8">
          {/* Client Logo/Icon */}
          <div className="w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-6 group-hover:scale-105 transition-transform">
            <Building2 size={32} className="text-blue-600" />
          </div>
          
          {/* Client Name */}
          <div className="text-center">
            <div className="text-xs font-semibold text-blue-600 uppercase tracking-wider mb-2">
              {testimonial.industry}
            </div>
            <h3 className="text-lg font-bold text-slate-800 leading-tight mb-1 text-center max-w-48">
              {testimonial.company}
            </h3>
            <div className="text-sm text-slate-600 mb-3">
              {testimonial.clientName}
            </div>
            <div className="text-xs text-slate-500 bg-white bg-opacity-70 px-3 py-1 rounded-full">
              Client Testimonial
            </div>
          </div>
          
          {/* Subtle hint indicator */}
          <div className="absolute bottom-4 right-4 w-8 h-8 bg-white bg-opacity-50 rounded-full flex items-center justify-center">
            <ChevronRight size={16} className="text-blue-600 group-hover:translate-x-0.5 transition-transform" />
          </div>
        </div>
        
        {/* Back Face - Detailed Content */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-white rounded-2xl p-8 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Building2 size={16} className="text-blue-600" />
              </div>
              <span className="text-xs font-semibold text-blue-600 uppercase tracking-wide">{testimonial.industry}</span>
            </div>
            <div className="flex items-center space-x-1">
              {[...Array(5)].map((_, i) => (
                <Star 
                  key={i} 
                  size={12} 
                  className={i < Math.floor(testimonial.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'} 
                />
              ))}
            </div>
          </div>
          
          {/* Content */}
          <div className="flex-1">
            <div className="flex items-start space-x-2 mb-4">
              <Quote size={16} className="text-blue-600 mt-1 flex-shrink-0" />
              <p className="text-sm text-slate-600 leading-relaxed italic">
                "{testimonial.testimonial}"
              </p>
            </div>
            
            {/* Client info */}
            <div className="mb-4">
              <div className="text-sm font-semibold text-slate-800">{testimonial.clientName}</div>
              <div className="text-xs text-slate-600">{testimonial.clientTitle}</div>
              <div className="text-xs text-blue-600 font-medium">{testimonial.company}</div>
            </div>
            
            {/* Project results */}
            <div className="space-y-1 mb-4">
              <div className="text-xs font-semibold text-green-600 mb-1">Key Results:</div>
              {testimonial.results.slice(0, 2).map((result: string, index: number) => (
                <div key={index} className="flex items-center text-xs text-slate-600">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></div>
                  {result}
                </div>
              ))}
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="grid grid-cols-2 gap-2">
            <button>
              <FileText size={16} className="mr-2" />
            </button>
            <button>
              <Mail size={16} className="mr-2" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialCard; 