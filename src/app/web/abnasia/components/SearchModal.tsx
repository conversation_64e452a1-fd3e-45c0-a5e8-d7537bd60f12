'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Search, X, FileText, Star, Briefcase, Users, Tag, ArrowRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: 'story' | 'service' | 'testimonial' | 'product' | 'industry' | 'translation';
  content?: string;
  path?: string;
  icon?: string;
  sectionId?: string;
}

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SearchModal: React.FC<SearchModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Search index data
  const [searchIndex, setSearchIndex] = useState<SearchResult[]>([]);

  // Build search index on component mount
  useEffect(() => {
    const buildSearchIndex = () => {
      const index: SearchResult[] = [];

      // Get all translation data
      const featuredStories = (t('data.featuredStories', { returnObjects: true }) as any[]) || [];
      const categories = (t('data.categories', { returnObjects: true }) as any[]) || [];
      const aiProducts = (t('data.aiProducts', { returnObjects: true }) as any[]) || [];
      const industryStories = (t('data.industryStories', { returnObjects: true }) as any) || {};

      // Get current path for conditional logic
      const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
      const isOnMainPage = currentPath === '/web/abnasia';

      // Index featured stories (available on main page)
      featuredStories.forEach((story, idx) => {
        index.push({
          id: `story-${idx}`,
          title: story.title || '',
          description: story.excerpt || story.description || '',
          content: `${story.title} ${story.excerpt} ${story.description} ${story.industry}`,
          category: 'story',
          icon: '📰',
          path: '/web/abnasia/success-stories', // Navigate to success stories page
        });
      });

      // Index services (available on main page)
      const services = [
        {
          title: t('services.strategy.title') || 'AI Strategy Consulting',
          description: t('services.strategy.description') || '',
          features: (t('services.strategy.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.development.title') || 'Custom AI Development',
          description: t('services.development.description') || '',
          features: (t('services.development.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.analytics.title') || 'Data Analytics Platform',
          description: t('services.analytics.description') || '',
          features: (t('services.analytics.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.automation.title') || 'Intelligent Process Automation',
          description: t('services.automation.description') || '',
          features: (t('services.automation.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.cloudMigration.title') || 'Cloud Migration & Modernization',
          description: t('services.cloudMigration.description') || '',
          features: (t('services.cloudMigration.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.cybersecurity.title') || 'Enterprise Cybersecurity Suite',
          description: t('services.cybersecurity.description') || '',
          features: (t('services.cybersecurity.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.digitalTransformation.title') || 'Complete Digital Transformation',
          description: t('services.digitalTransformation.description') || '',
          features: (t('services.digitalTransformation.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.performanceOptimization.title') || 'Business Performance Optimization',
          description: t('services.performanceOptimization.description') || '',
          features: (t('services.performanceOptimization.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.softwareDevelopment.title') || 'Custom Software Development',
          description: t('services.softwareDevelopment.description') || '',
          features: (t('services.softwareDevelopment.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.aiIntegration.title') || 'AI Integration & Implementation',
          description: t('services.aiIntegration.description') || '',
          features: (t('services.aiIntegration.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.dataModernization.title') || 'Data Modernization & Analytics',
          description: t('services.dataModernization.description') || '',
          features: (t('services.dataModernization.features', { returnObjects: true }) as string[]) || [],
        },
        {
          title: t('services.enterpriseIntegration.title') || 'Enterprise System Integration',
          description: t('services.enterpriseIntegration.description') || '',
          features: (t('services.enterpriseIntegration.features', { returnObjects: true }) as string[]) || [],
        }
      ];

      services.forEach((service, idx) => {
        index.push({
          id: `service-${idx}`,
          title: service.title,
          description: service.description,
          content: `${service.title} ${service.description} ${service.features.join(' ')}`,
          category: 'service',
          icon: '⚙️',
          // Only use sectionId if we're on the main page, otherwise navigate
          sectionId: isOnMainPage ? 'services' : undefined,
          path: !isOnMainPage ? '/web/abnasia#services' : undefined
        });
      });

      // Index AI products (primary location is products page)
      aiProducts.forEach((product, idx) => {
        index.push({
          id: `product-${idx}`,
          title: product.name || product.title || '',
          description: product.description || '',
          content: `${product.name} ${product.description} ${product.category}`,
          category: 'product',
          icon: '🤖',
          path: '/web/abnasia/products' // Navigate to products page
        });
      });

      // Index industry stories (primary location is success stories page)
      Object.keys(industryStories).forEach(industryKey => {
        const stories = industryStories[industryKey] || [];
        stories.forEach((story: any, idx: number) => {
          index.push({
            id: `industry-${industryKey}-${idx}`,
            title: story.title || '',
            description: story.description || story.excerpt || '',
            content: `${story.title} ${story.description} ${story.industry} ${industryKey}`,
            category: 'industry',
            icon: '🏢',
            path: `/web/abnasia/success-stories?industry=${industryKey}`
          });
        });
      });

      // Index testimonials (primary location is customers page)
      const testimonials = [
        {
          company: 'A Retail Bank in Vietnam',
          testimonial: 'ABN Asia delivered excellent consulting for our lending operations.',
          projectType: 'Consulting'
        },
        {
          company: 'A Bank in Vietnam',
          testimonial: 'Outstanding digital transformation results. Highly professional.',
          projectType: 'Digital Transformation'
        },
        {
          company: 'WISE',
          testimonial: 'Great partnership in optimizing our fintech operations.',
          projectType: 'Fintech Optimization'
        }
      ];

      testimonials.forEach((testimonial, idx) => {
        index.push({
          id: `testimonial-${idx}`,
          title: testimonial.company,
          description: testimonial.testimonial,
          content: `${testimonial.company} ${testimonial.testimonial} ${testimonial.projectType}`,
          category: 'testimonial',
          icon: '⭐',
          path: '/web/abnasia/customers' // Navigate to customers page
        });
      });

      // Index page sections and key content
      const pageContent = [
        {
          title: t('nav.about') || 'Our Story',
          description: 'Learn about ABN Asia\'s journey, mission, and values',
          content: 'about company story mission values team',
          category: 'translation' as const,
          icon: '🏢',
          path: '/web/abnasia/our-story'
        },
        {
          title: t('nav.contact') || 'Contact Us',
          description: 'Get in touch with our team for consultations and partnerships',
          content: 'contact get in touch consultation partnership',
          category: 'translation' as const,
          icon: '📞',
          path: '/web/abnasia/contact'
        },
        {
          title: t('nav.news') || 'News & Updates',
          description: 'Latest news, insights, and updates from ABN Asia',
          content: 'news updates insights blog articles',
          category: 'translation' as const,
          icon: '📰',
          path: 'https://news.abnasia.org'
        },
        {
          title: 'Services',
          description: 'AI Strategy, Development, and Analytics services',
          content: 'services consulting development analytics AI strategy',
          category: 'translation' as const,
          icon: '⚙️',
          sectionId: isOnMainPage ? 'services' : undefined,
          path: !isOnMainPage ? '/web/abnasia#services' : undefined
        },
        {
          title: 'Success Stories',
          description: 'Customer success stories and case studies across industries',
          content: 'success stories case studies customers results',
          category: 'translation' as const,
          icon: '📈',
          sectionId: isOnMainPage ? 'featured-stories' : undefined,
          path: !isOnMainPage ? '/web/abnasia/success-stories' : undefined
        },
        {
          title: 'AI Products',
          description: 'Browse our comprehensive AI product catalog',
          content: 'AI products catalog solutions tools',
          category: 'translation' as const,
          icon: '🤖',
          path: '/web/abnasia/products'
        }
      ];

      pageContent.forEach((content, idx) => {
        index.push({
          id: `page-${idx}`,
          title: content.title,
          description: content.description,
          content: content.content,
          category: content.category,
          icon: content.icon,
          path: content.path,
          sectionId: content.sectionId
        });
      });

      setSearchIndex(index);
    };

    buildSearchIndex();
  }, [t]);

  // Perform search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setSelectedIndex(-1);
      return;
    }

    setIsLoading(true);
    
    // Simple fuzzy search implementation
    const query = searchQuery.toLowerCase();
    const results = searchIndex.filter(item => {
      const searchText = `${item.title} ${item.description} ${item.content}`.toLowerCase();
      
      // Exact match gets highest priority
      if (searchText.includes(query)) return true;
      
      // Word-based matching
      const queryWords = query.split(' ');
      return queryWords.some(word => searchText.includes(word));
    });

    // Sort by relevance (exact title match first, then description match)
    results.sort((a, b) => {
      const aTitle = a.title.toLowerCase();
      const bTitle = b.title.toLowerCase();
      
      if (aTitle.includes(query) && !bTitle.includes(query)) return -1;
      if (!aTitle.includes(query) && bTitle.includes(query)) return 1;
      
      return 0;
    });

    setSearchResults(results.slice(0, 10)); // Limit to 10 results
    setSelectedIndex(-1);
    setIsLoading(false);
  }, [searchQuery, searchIndex]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < searchResults.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : prev);
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && searchResults[selectedIndex]) {
            handleResultClick(searchResults[selectedIndex]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, searchResults, selectedIndex, onClose]);

  // Focus search input when modal opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    onClose();
    
    if (result.path) {
      // Check if it's an external URL
      if (result.path.startsWith('http')) {
        window.open(result.path, '_blank', 'noopener,noreferrer');
        return;
      }
      
      // Check if the path includes a hash for section navigation
      if (result.path.includes('#')) {
        const [path, hash] = result.path.split('#');
        
        // If we're already on the target page, just scroll to section
        if (window.location.pathname === path) {
          const element = document.getElementById(hash);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        } else {
          // Navigate to page with hash - the section will be scrolled to on load
          window.location.href = result.path;
        }
      } else {
        // Regular page navigation
        window.location.href = result.path;
      }
    } else if (result.sectionId) {
      // Scroll to section on current page with retry mechanism
      const scrollToSection = (attempts = 0) => {
        const element = document.getElementById(result.sectionId!);
        if (element) {
          // Wait a bit to ensure any dynamic content is loaded
          setTimeout(() => {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }, 100);
        } else if (attempts < 5) {
          // Retry after a short delay in case content is still loading
          setTimeout(() => scrollToSection(attempts + 1), 200);
        } else {
          console.warn(`Section ${result.sectionId} not found after multiple attempts`);
        }
      };
      
      scrollToSection();
    }
  };

  // Get category icon and label
  const getCategoryInfo = (category: SearchResult['category']) => {
    switch (category) {
      case 'story':
        return { icon: <FileText size={16} />, label: 'Story' };
      case 'service':
        return { icon: <Briefcase size={16} />, label: 'Service' };
      case 'testimonial':
        return { icon: <Star size={16} />, label: 'Testimonial' };
      case 'product':
        return { icon: <Tag size={16} />, label: 'Product' };
      case 'industry':
        return { icon: <Users size={16} />, label: 'Industry' };
      case 'translation':
        return { icon: <ArrowRight size={16} />, label: 'Section' };
      default:
        return { icon: <FileText size={16} />, label: 'Content' };
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-20">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4 max-h-[600px] overflow-hidden">
        {/* Header */}
        <div className="flex items-center p-4 border-b border-gray-200">
          <Search size={20} className="text-gray-400 mr-3" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search stories, services, products..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1 text-lg outline-none"
          />
          <button
            onClick={onClose}
            className="ml-3 p-1 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-400" />
          </button>
        </div>

        {/* Results */}
        <div 
          ref={resultsRef}
          className="max-h-[500px] overflow-y-auto"
        >
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-2">Searching...</p>
            </div>
          ) : searchQuery && searchResults.length === 0 ? (
            <div className="p-8 text-center">
              <Search size={48} className="text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No results found</h3>
              <p className="text-gray-500">Try different keywords or check spelling</p>
            </div>
          ) : searchResults.length > 0 ? (
            <div className="py-2">
              {searchResults.map((result, index) => {
                const categoryInfo = getCategoryInfo(result.category);
                const isSelected = index === selectedIndex;
                
                return (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className={`w-full text-left p-4 hover:bg-gray-50 transition-colors border-l-4 ${
                      isSelected 
                        ? 'bg-blue-50 border-blue-500' 
                        : 'border-transparent'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 text-2xl">
                        {result.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          {categoryInfo.icon}
                          <span className="text-xs text-gray-500 uppercase tracking-wide">
                            {categoryInfo.label}
                          </span>
                        </div>
                        <h3 className="text-sm font-semibold text-gray-900 mb-1 truncate">
                          {result.title}
                        </h3>
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {result.description}
                        </p>
                      </div>
                      <ArrowRight size={16} className="text-gray-400 flex-shrink-0 mt-1" />
                    </div>
                  </button>
                );
              })}
            </div>
          ) : (
            <div className="p-8 text-center">
              <Search size={48} className="text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">Start searching</h3>
              <p className="text-gray-500">Find stories, services, products and more</p>
            </div>
          )}
        </div>

        {/* Footer */}
        {searchResults.length > 0 && (
          <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{searchResults.length} results found</span>
              <div className="flex items-center space-x-4">
                <span>↑↓ Navigate</span>
                <span>Enter Select</span>
                <span>Esc Close</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchModal; 