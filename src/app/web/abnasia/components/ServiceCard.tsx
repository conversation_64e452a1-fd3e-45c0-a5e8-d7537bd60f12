import React, { useState } from 'react';
import { Code, ChevronRight, MessageSquareText, Eye } from 'lucide-react';
import { useThemeStyles } from '@/themes/ThemeProvider';

type Service = {
  id: string;
  name: string;
  category: string;
  description: string;
  features: string[];
  timeline: string;
  startingPrice: number;
  currency: string;
  icon: string;
};

const ServiceCard: React.FC<{ service: Service }> = ({ service }) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const { getCardClass, isPaperTheme } = useThemeStyles();
  
  return (
    <div
      className={`relative w-full h-[28rem] cursor-pointer group overflow-hidden perspective-1000 ${getCardClass()}`}
      onClick={() => setIsFlipped(!isFlipped)}
    >
      <div className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${isFlipped ? 'rotate-y-180' : ''}`}>
        {/* Front Face - Minimal Design */}
        <div className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-purple-50 to-violet-100 rounded-2xl flex flex-col items-center justify-center p-8">
          {/* Service Icon */}
          <div className="w-20 h-20 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-6 group-hover:scale-105 transition-transform">
            <span className="text-3xl">{service.icon}</span>
          </div>
          
          {/* Service Info */}
          <div className="text-center">
            <div className="text-xs font-semibold text-purple-600 uppercase tracking-wider mb-2">
              {service.category}
            </div>
            <h3 className={`text-lg font-bold leading-tight mb-3 text-center max-w-48 ${isPaperTheme ? 'text-[var(--paper-black)]' : 'text-slate-800'}`}>
              {service.name}
            </h3>
            <div className="text-xs text-slate-500 bg-white bg-opacity-70 px-3 py-1 rounded-full">
              From {service.currency}{service.startingPrice.toLocaleString()}
            </div>
          </div>
          
          {/* Subtle hint indicator */}
          <div className="absolute bottom-4 right-4 w-8 h-8 bg-white bg-opacity-50 rounded-full flex items-center justify-center">
            <ChevronRight size={16} className="text-purple-600 group-hover:translate-x-0.5 transition-transform" />
          </div>
        </div>
        
        {/* Back Face - Detailed Content */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-white rounded-2xl p-8 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-lg">{service.icon}</span>
              </div>
              <span className="text-xs font-semibold text-purple-600 uppercase tracking-wide">{service.category}</span>
            </div>
          </div>
          
          {/* Content */}
          <div className="flex-1">
            <h3 className="text-lg font-bold text-slate-800 leading-tight mb-3">
              {service.name}
            </h3>
            <p className="text-sm text-slate-600 leading-relaxed mb-4">
              {service.description}
            </p>
            
            {/* Features */}
            <div className="space-y-1 mb-4">
              {(Array.isArray(service.features) ? service.features : []).slice(0, 3).map((feature: string, index: number) => (
                <div key={index} className="flex items-center text-xs text-slate-600">
                  <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-2"></div>
                  {feature}
                </div>
              ))}
            </div>
            
            {/* Service details */}
            <div className="bg-purple-50 p-3 rounded-lg text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-gray-600">Timeline: {service.timeline}</span>
                <span className="font-bold text-gradient">{service.currency}{service.startingPrice.toLocaleString()}+</span>
              </div>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="grid grid-cols-2 gap-2">
            <button>
              <MessageSquareText size={16} className="mr-2" />
            </button>
            <button>
              <Eye size={16} className="mr-2" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceCard; 