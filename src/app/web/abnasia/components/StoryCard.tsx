import React, { useState } from 'react';
import { Award, ChevronRight, Calendar, MapPin } from 'lucide-react';
import MakeItYoursButton from './MakeItYoursButton';
import ShareModule from '@/components/common/ShareModule';
import { useThemeStyles } from '@/themes/ThemeProvider';

type Story = {
  id?: number | string;
  title: string;
  readTime?: number;
  publishDate?: string;
  image?: string;
  snippet: string;
  fullStory?: string;
  customer: string;
  industry: string;
  location: string;
  tags?: string[];
};

const StoryCard: React.FC<{ item: Story }> = ({ item }) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const { getCardClass, isPaperTheme } = useThemeStyles();

  // Construct the current URL for sharing
  const getCurrentUrl = () => {
    if (typeof window !== 'undefined') {
      return window.location.href;
    }
    return `${process.env.NEXT_PUBLIC_BASE_URL || 'https://abn.green'}/stories/${item.id || 'story'}`;
  };

  return (
    <div
      className={`relative w-full h-[32rem] cursor-pointer group overflow-hidden perspective-1000 ${getCardClass()}`}
      onClick={() => setIsFlipped(!isFlipped)}
    >
      <div className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${isFlipped ? 'rotate-y-180' : ''}`}>
        {/* Front Face - Minimal Design */}
        <div className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center p-8">
          {/* Minimal Logo/Icon */}
          <div className="w-20 h-20 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-6 group-hover:scale-105 transition-transform">
            <Award size={32} className="text-blue-600" />
          </div>
          
          {/* Minimal Text */}
          <div className="text-center">
            <div className="text-xs font-semibold text-blue-600 uppercase tracking-wider mb-2">
              {item.industry}
            </div>
            <h3 className="text-lg font-bold text-slate-800 leading-tight mb-3 text-center max-w-48">
              {item.customer}
            </h3>
            <div className="text-xs text-slate-500 bg-white bg-opacity-70 px-3 py-1 rounded-full">
              Success Story
            </div>
          </div>
          
          {/* Subtle hint indicator */}
          <div className="absolute bottom-4 right-4 w-8 h-8 bg-white bg-opacity-50 rounded-full flex items-center justify-center">
            <ChevronRight size={16} className="text-blue-600 group-hover:translate-x-0.5 transition-transform" />
          </div>
        </div>
        
        {/* Back Face - Detailed Content */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-white rounded-2xl p-8 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Award size={16} className="text-blue-600" />
              </div>
              <span className="text-xs font-semibold text-blue-600 uppercase tracking-wide">{item.industry}</span>
            </div>
            <div className="text-xs text-slate-400">{item.location}</div>
          </div>
          
          {/* Content */}
          <div className="flex-1">
            <h3 className="font-bold text-slate-800 leading-tight mb-3 text-lg">
              {item.title}
            </h3>
            <p className="text-sm text-slate-600 leading-relaxed mb-4">
              {item.snippet}
            </p>
            
            {/* Meta info */}
            <div className="flex items-center space-x-4 text-xs text-slate-500 mb-4">
              <div className="flex items-center space-x-1">
                <Calendar size={12} />
                <span>{item.publishDate || 'Recent'}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MapPin size={12} />
                <span>{item.location}</span>
              </div>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="grid grid-cols-2 gap-2">
            <MakeItYoursButton
              context={{
                type: 'story',
                title: item.title,
                id: item.id?.toString(),
                customer: item.customer,
                industry: item.industry,
                location: item.location
              }}
              buttonText="Get Similar Results"
              buttonStyle="primary"
              size="sm"
              className="flex-1 justify-center"
            />
            <div onClick={(e) => e.stopPropagation()}>
              <ShareModule
                title={item.title}
                description={item.snippet}
                url={getCurrentUrl()}
                buttonText="Share"
                buttonClassName="flex items-center justify-center gap-2 px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors border-0"
                buttonSize="sm"
                iconSize={32}
                showLabel={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoryCard; 