import { v4 as uuid } from 'uuid'

// TypeScript interface for partner
interface Partner {
  categories: string[];
  name: string;
  image: string[];
  description: string;
  industry: string;
  sku: string;
  website: string;
  id?: string;
}

let partners: Partner[] = [
  // Retail partners
  {
    "categories": ["Retail"],
    "name": "7-Eleven",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo.7-eleven.png"],
    "description": "Convenience store chain with locations worldwide",
    "industry": "Retail",
    "sku": "g7eleven1",
    "website": "https://www.7-eleven.com/"
  },
  {
    "categories": ["Retail"],
    "name": "FamilyMart",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/familymart-logo.webp"],
    "description": "International convenience store franchise",
    "industry": "Retail",
    "sku": "gfamily1",
    "website": "https://www.familymart.com.tw/"
  },
  {
    "categories": ["Banking"],
    "name": "Sinopac Bank",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo.bank.sinopac.webp"],
    "description": "Taiwanese commercial bank",
    "industry": "Banking",
    "sku": "gsinopac1",
    "website": "https://www.bank.sinopac.com/"
  },
  {
    "categories": ["Technology"],
    "name": "Amazon Web Services (AWS)",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo.aws.png"],
    "description": "Comprehensive cloud computing platform",
    "industry": "Cloud Computing",
    "sku": "gaws1",
    "website": "https://aws.amazon.com/"
  },
  {
    "categories": ["Technology"],
    "name": "Google Cloud",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo.google.cloud.webp"],
    "description": "Enterprise cloud computing services",
    "industry": "Cloud Computing",
    "sku": "ggoogle1",
    "website": "https://cloud.google.com/"
  },
  {
    "categories": ["Technology"],
    "name": "Oracle Cloud",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo.oracle.cloud.png"],
    "description": "Enterprise cloud infrastructure and applications",
    "industry": "Cloud Computing",
    "sku": "goracle1",
    "website": "https://www.oracle.com/cloud/"
  },
  {
    "categories": ["Technology"],
    "name": "Vercel",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo.vercel.jpg"],
    "description": "Cloud platform for static sites and serverless functions",
    "industry": "Cloud Computing",
    "sku": "gvercel1",
    "website": "https://vercel.com/"
  },
  {
    "categories": ["Technology"],
    "name": "Cloudflare",
    "image": ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo.cloudflare.png"],
    "description": "Web infrastructure and website security services",
    "industry": "Cloud Computing",
    "sku": "gcloudflare1",
    "website": "https://www.cloudflare.com/"
  }
]

partners.forEach(i => {
  i.id = uuid()
})

export default partners