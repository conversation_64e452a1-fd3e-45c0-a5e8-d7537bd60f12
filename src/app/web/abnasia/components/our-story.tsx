import React from 'react';
import Head from 'next/head';
import Image from 'next/image';
import { useTranslation } from 'react-i18next';
import InlineChat from "./chat/InlineChat";
import LocationsComponent from './LocationsComponent';

// TypeScript interfaces
interface Product {
  sku: string;
  name: string;
  image: string | string[];
  categories: string[];
  description?: string;
  price?: number;
}

interface Category {
  name: string;
  image: string | string[];
  itemCount: number;
}

interface OurStoryProps {
  inventoryData?: Product[];
  categories?: Category[];
}

const OurStory: React.FC<OurStoryProps> = () => {
  const { t } = useTranslation();

  return (
    <>
      <div className="min-h-screen">
        <Head>
          <title>{t('ourStory.title')}</title>
          <meta name="description" content={t('ourStory.description')} />
          <meta property="og:title" content={t('ourStory.title')} key="title" />
        </Head>

        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div className="text-center">
              <h1 className="mt-8 text-5xl md:text-6xl font-bold text-gray-900 leading-tight">
                {t('ourStory.hero.title')}
              </h1>
              <p className="mt-6 text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {t('ourStory.hero.subtitle')}
              </p>
              <div className="mt-8 flex justify-center">
                <div className="h-1 w-24 bg-gray-300 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Company Introduction */}
          <div className="rounded-2xl shadow-xl p-8 md:p-12 mb-16 border border-gray-100">
            <div className="prose prose-xl max-w-none">
              <p className="text-lg leading-relaxed text-gray-700 first-letter:text-5xl first-letter:font-bold first-letter:text-gray-900 first-letter:float-left first-letter:mr-3 first-letter:mt-1">
                {t('ourStory.introduction')}
              </p>
            </div>
          </div>

          {/* Location Image */}
          <div className="mb-16">
            <div className="relative group">
              <Image
                src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/abnasia-locations-1024x448_output.webp"
                alt={t('ourStory.locationImageAlt')}
                width={1024}
                height={448}
                className="w-full rounded-2xl shadow-2xl transition-transform duration-500 group-hover:scale-[1.02]"
                priority
              />
            </div>
          </div>

          {/* About Section */}
          <div className="rounded-2xl shadow-xl p-8 md:p-12 mb-16 border border-gray-100">
            <div className="space-y-8 text-gray-700">
              <p className="text-lg leading-relaxed">
                {t('ourStory.about.founding')}
              </p>

              <p className="text-lg leading-relaxed">
                {t('ourStory.about.contact')} <a href={`mailto:${t('ourStory.about.contactEmail')}`} className="text-gray-600 hover:underline">{t('ourStory.about.contactEmail')}</a>{t('ourStory.about.contactContinue')}
              </p>

              <div className="border-l-4 border-gray-400 p-6 rounded-r-xl">
                <p className="text-lg italic font-medium text-gray-700">
                  &ldquo;{t('ourStory.about.quote')}&rdquo;
                </p>
              </div>

              <p className="text-lg leading-relaxed">
                {t('ourStory.about.customers')}
              </p>
              <div className="flex justify-center mt-12">
                <div className="w-full lg:w-1/3">
                  <InlineChat
                    title={t('chat.title')}
                    initialMessage={t('chat.initialMessage')}
                  />
                </div>
              </div>
              <div className="text-center py-8">
                <p className="text-2xl font-bold text-gray-900">
                  {t('ourStory.about.motto')}
                </p>
                <p className="text-lg text-gray-600 mt-2">
                  {t('ourStory.about.efficiency')}
                </p>
              </div>

              {/* ABN Asia Dossier Link */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-100 p-8 rounded-2xl border border-blue-200 mt-8">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    ABN Asia Dossier
                  </h3>
                  <p className="text-lg text-gray-700 mb-6">
                    Comprehensive company profile for bidding, tendering, and partnership opportunities.
                    Detailed information about our capabilities, solutions, and success stories.
                  </p>
                  <p className="text-lg text-gray-700 mb-6">
                    Hồ sơ công ty toàn diện cho đấu thầu, chào hàng và cơ hội hợp tác.
                    Thông tin chi tiết về khả năng, giải pháp và câu chuyện thành công của chúng tôi.
                  </p>
                  <a
                    href="/web/abnasia/dossier"
                    className="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-colors duration-200 shadow-lg hover:shadow-xl"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    View ABN Asia Dossier
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Chat Section */}
        <div className="border-t border-gray-100">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <LocationsComponent />
          </div>
        </div>
      </div>
    </>
  );
};

export default OurStory;