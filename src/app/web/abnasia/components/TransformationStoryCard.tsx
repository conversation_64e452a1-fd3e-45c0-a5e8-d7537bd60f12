import React, { useState } from 'react';
import { X, ChevronRight, FileText } from 'lucide-react';

type Story = {
  id: string;
  title: string;
  challenge: string;
  solution: string;
  outcome: string;
  transformation: string;
  icon: string;
  metrics: string[];
};

const TransformationStoryCard: React.FC<{ story: Story }> = ({ story }) => {
  const [isFlipped, setIsFlipped] = useState(false);
  
  return (
    <div 
      className="relative bg-white rounded-2xl corporate-shadow-lg w-full h-[34rem] border border-slate-200 cursor-pointer corporate-hover-shadow group overflow-hidden perspective-1000"
      onClick={() => setIsFlipped(!isFlipped)}
    >
      <div className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${isFlipped ? 'rotate-y-180' : ''}`}>
        {/* Front Face - Minimal Design */}
        <div className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center p-8">
          {/* Icon */}
          <div className="w-20 h-20 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-6 group-hover:scale-105 transition-transform">
            <span className="text-3xl">{story.icon}</span>
          </div>
          
          {/* Title */}
          <div className="text-center">
            <h3 className="text-lg font-bold text-slate-800 leading-tight mb-3 max-w-56">
              {story.title}
            </h3>
            <div className="text-xs text-blue-600 font-medium mb-4">
              {story.transformation.split(' to ')[1] || 'Transformation Story'}
            </div>
            <div className="text-xs text-slate-500 bg-white bg-opacity-70 px-3 py-1 rounded-full">
              See transformation
            </div>
          </div>
          
          {/* Subtle hint indicator */}
          <div className="absolute bottom-4 right-4 w-8 h-8 bg-white bg-opacity-50 rounded-full flex items-center justify-center">
            <ChevronRight size={16} className="text-blue-600 group-hover:translate-x-0.5 transition-transform" />
          </div>
        </div>
        
        {/* Back Face - Detailed Content */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-white rounded-2xl p-8 flex flex-col">
          {/* Header with close button */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-lg">{story.icon}</span>
              </div>
              <span className="text-xs font-semibold text-blue-600 uppercase tracking-wide">Transformation</span>
            </div>
            <button 
              className="text-slate-400 hover:text-slate-600 transition-colors p-1"
              onClick={(e) => {
                e.stopPropagation();
                setIsFlipped(false);
              }}
            >
              <X size={16} />
            </button>
          </div>
          
          {/* Content */}
          <div className="flex-1">
            <h3 className="text-lg font-bold text-slate-800 leading-tight mb-3">
              {story.title}
            </h3>
            
            {/* Challenge & Solution */}
            <div className="mb-4">
              <div className="text-xs font-semibold text-red-600 mb-1">Challenge:</div>
              <div className="text-xs text-slate-600 mb-3">{story.challenge}</div>
              
              <div className="text-xs font-semibold text-green-600 mb-1">Solution:</div>
              <div className="text-xs text-slate-600 mb-3">{story.solution}</div>
            </div>
            
            {/* Key metrics */}
            <div className="space-y-1 mb-4">
              {story.metrics.slice(0, 3).map((metric: string, index: number) => (
                <div key={index} className="flex items-center text-xs text-slate-600">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></div>
                  {metric}
                </div>
              ))}
            </div>
            
            {/* Outcome */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 p-3 rounded-lg text-xs">
              <div className="font-semibold text-blue-800 mb-1">Outcome:</div>
              <div className="text-blue-700">{story.outcome}</div>
            </div>
          </div>
          
          {/* Action button */}
          <button>
            <FileText size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default TransformationStoryCard; 