// TypeScript interfaces for type safety - Updated to match existing components
export interface Story {
  id: number;
  title: string;
  readTime: number;
  publishDate: string;
  image: string;
  snippet: string;
  fullStory: string;
  customer: string;
  industry: string;
  location: string;
  tags?: string[];
}

export interface Category {
  id: string;
  name: string;
  description: string;
  count: number;
  icon: string;
}

export interface Product {
  id: string;
  name: string;
  price: number;
  currency: string;
  category: string;
  rating: number;
  reviewCount: number;
  description: string;
  relatedStoryId: number;
}

export interface TransformationStory {
  id: string;
  title: string;
  challenge: string;
  solution: string;
  outcome: string;
  transformation: string;
  icon: string;
  metrics: string[];
}

// Data is now available in translation files:
// - src/app/web/abnasia/locales/en.json (English)
// - src/app/web/abnasia/locales/vi.json (Vietnamese)
// Access via: t('data.featuredStories'), t('data.categories'), t('data.aiProducts'), t('data.industryStories') 