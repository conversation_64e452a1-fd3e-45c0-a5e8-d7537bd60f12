import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Translation resources - ABN Asia specific
import enTranslations from '../locales/en.json';
import viTranslations from '../locales/vi.json';

const resources = {
  en: {
    translation: enTranslations
  },
  vi: {
    translation: viTranslations
  }
};

/**
 * i18next Configuration with English Fallback
 * 
 * This configuration ensures that when a translation key is missing in Vietnamese,
 * the system will automatically fall back to the English translation.
 * 
 * Key fallback settings:
 * - fallbackLng: 'en' - Always fallback to English for missing translations
 * - returnNull: false - Don't return null for missing keys
 * - returnEmptyString: false - Don't return empty string for missing keys
 * - saveMissing: false - Don't save missing keys to avoid pollution
 * - missingKeyHandler: false - Use default fallback behavior
 */

// Initialize i18n synchronously for SSR compatibility
if (!i18n.isInitialized) {
  i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: 'en', // Set default language for SSR
      fallbackLng: 'en', // Always fallback to English when translation is missing
      debug: false,
      
      interpolation: {
        escapeValue: false, // React already does escaping
      },
      
      // Ensure fallback behavior works properly
      returnNull: false, // Don't return null for missing keys
      returnEmptyString: false, // Don't return empty string for missing keys
      
      // Additional fallback options
      saveMissing: false, // Don't save missing keys
      missingKeyHandler: false, // Don't handle missing keys specially
      
      // Simplified config for SSR compatibility
      react: {
        useSuspense: false
      }
    });
}

// Client-side language detection setup
if (typeof window !== 'undefined') {
  // Only use language detector on client side
  import('i18next-browser-languagedetector').then((LanguageDetector) => {
    // Add language detection and reinitialize with client-side detection
    i18n.use(LanguageDetector.default);
    
    // Update detection settings for client while preserving fallback behavior
    i18n.init({
      resources,
      fallbackLng: 'en', // Ensure fallback is preserved
      returnNull: false,
      returnEmptyString: false,
      saveMissing: false,
      missingKeyHandler: false,
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
      }
    });
  });
}

export default i18n; 