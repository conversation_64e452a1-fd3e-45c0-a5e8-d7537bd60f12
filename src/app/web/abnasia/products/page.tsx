'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '../components/Header';
import Footer from '../components/Footer';
import GlobalStyles from '../components/GlobalStyles';
import AIProductsSection from '../components/AIProductsSection';
import { type Story, type Product } from '../data/abnasiadata';

export default function ProductsPage() {
  const { t } = useTranslation();
  
  // Get data from translations and add missing fields
  const aiProductsRaw = (t('data.aiProducts', { returnObjects: true }) as any[]) || [];
  const aiProducts = aiProductsRaw.map((product, index) => ({
    ...product,
    id: `prod-${index + 1}`,
    price: product.price || 1,
    currency: product.currency || '$',
    rating: product.rating || 5,
    reviewCount: product.reviewCount || "50",
    relatedStoryId: (index % 6) + 1 // Assuming 6 featured stories
  })) as Product[];
  
  const featuredStoriesRaw = (t('data.featuredStories', { returnObjects: true }) as any[]) || [];
  const featuredStories = featuredStoriesRaw.map((story, index) => ({
    ...story,
    id: index + 1,
    readTime: story.readTime || Math.floor(Math.random() * 8) + 3,
    publishDate: story.publishDate || "",
    image: story.image || '/placeholder.jpg'
  })) as Story[];

  return (
    <div className="min-h-screen bg-gray-50">
      <GlobalStyles />
      <Header />
      <AIProductsSection products={aiProducts} stories={featuredStories} />
      <Footer />
    </div>
  );
} 