// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Travel Platform Constants
export const TRAVEL_CONSTANTS = {
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Search
  MIN_SEARCH_LENGTH: 2,
  SEARCH_DEBOUNCE_MS: 300,
  
  // Booking
  BOOKING_EXPIRY_MINUTES: 15,
  MAX_GUESTS_PER_BOOKING: 10,
  
  // Pricing
  DEFAULT_CURRENCY: 'VND',
  SUPPORTED_CURRENCIES: ['VND', 'USD', 'EUR'] as const,
  
  // Images
  MAX_IMAGE_SIZE_MB: 5,
  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'webp'] as const,
  
  // Validation
  MIN_PASSWORD_LENGTH: 8,
  MAX_DESCRIPTION_LENGTH: 1000,
  MAX_REVIEW_LENGTH: 500,
} as const;

// Service Categories
export const SERVICE_CATEGORIES = {
  HOTELS: {
    id: 'hotels',
    name: 'Khách sạn',
    icon: 'Building2',
    color: 'blue',
    subcategories: [
      { id: 'luxury', name: 'Khách sạn cao cấp' },
      { id: 'boutique', name: 'Khách sạn boutique' },
      { id: 'business', name: 'Khách sạn doanh nhân' },
      { id: 'resort', name: 'Resort' },
      { id: 'hostel', name: 'Hostel' },
    ]
  },
  FLIGHTS: {
    id: 'flights',
    name: 'Vé máy bay',
    icon: 'Plane',
    color: 'green',
    subcategories: [
      { id: 'domestic', name: 'Nội địa' },
      { id: 'international', name: 'Quốc tế' },
      { id: 'roundtrip', name: 'Khứ hồi' },
      { id: 'oneway', name: 'Một chiều' },
    ]
  },
  TOURS: {
    id: 'tours',
    name: 'Tour du lịch',
    icon: 'Camera',
    color: 'purple',
    subcategories: [
      { id: 'cultural', name: 'Tour văn hóa' },
      { id: 'adventure', name: 'Tour phiêu lưu' },
      { id: 'beach', name: 'Tour biển đảo' },
      { id: 'city', name: 'Tour thành phố' },
      { id: 'nature', name: 'Tour thiên nhiên' },
    ]
  },
  CARS: {
    id: 'cars',
    name: 'Thuê xe',
    icon: 'Car',
    color: 'orange',
    subcategories: [
      { id: 'economy', name: 'Xe tiết kiệm' },
      { id: 'compact', name: 'Xe nhỏ gọn' },
      { id: 'midsize', name: 'Xe cỡ trung' },
      { id: 'luxury', name: 'Xe sang' },
      { id: 'suv', name: 'SUV' },
    ]
  },
  SERVICES: {
    id: 'services',
    name: 'Dịch vụ',
    icon: 'Headphones',
    color: 'pink',
    subcategories: [
      { id: 'guide', name: 'Hướng dẫn viên' },
      { id: 'transport', name: 'Vận chuyển' },
      { id: 'activity', name: 'Hoạt động' },
      { id: 'insurance', name: 'Bảo hiểm' },
      { id: 'visa', name: 'Visa' },
    ]
  }
} as const;

// Popular Destinations
export const POPULAR_DESTINATIONS = [
  {
    id: 'hanoi',
    name: 'Hà Nội',
    country: 'Việt Nam',
    region: 'Miền Bắc',
    coordinates: { lat: 21.0285, lng: 105.8542 },
    image: '/images/destinations/hanoi.jpg',
    description: 'Thủ đô ngàn năm văn hiến'
  },
  {
    id: 'hochiminh',
    name: 'TP. Hồ Chí Minh',
    country: 'Việt Nam',
    region: 'Miền Nam',
    coordinates: { lat: 10.8231, lng: 106.6297 },
    image: '/images/destinations/hochiminh.jpg',
    description: 'Thành phố năng động nhất Việt Nam'
  },
  {
    id: 'danang',
    name: 'Đà Nẵng',
    country: 'Việt Nam',
    region: 'Miền Trung',
    coordinates: { lat: 16.0471, lng: 108.2068 },
    image: '/images/destinations/danang.jpg',
    description: 'Thành phố đáng sống bậc nhất'
  },
  {
    id: 'phuquoc',
    name: 'Phú Quốc',
    country: 'Việt Nam',
    region: 'Miền Nam',
    coordinates: { lat: 10.2899, lng: 103.9840 },
    image: '/images/destinations/phuquoc.jpg',
    description: 'Đảo ngọc thiên đường'
  },
  {
    id: 'sapa',
    name: 'Sa Pa',
    country: 'Việt Nam',
    region: 'Miền Bắc',
    coordinates: { lat: 22.3380, lng: 103.8442 },
    image: '/images/destinations/sapa.jpg',
    description: 'Thị trấn trong mây'
  },
  {
    id: 'nhatrang',
    name: 'Nha Trang',
    country: 'Việt Nam',
    region: 'Miền Trung',
    coordinates: { lat: 12.2388, lng: 109.1967 },
    image: '/images/destinations/nhatrang.jpg',
    description: 'Vịnh biển đẹp nhất Việt Nam'
  }
] as const;

// Rating Configurations
export const RATING_CONFIG = {
  MIN_RATING: 1,
  MAX_RATING: 5,
  DEFAULT_RATING: 0,
  RATING_LABELS: {
    1: 'Rất tệ',
    2: 'Tệ',
    3: 'Bình thường',
    4: 'Tốt',
    5: 'Xuất sắc'
  }
} as const;

// Booking Status
export const BOOKING_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  REFUNDED: 'refunded'
} as const;

export const BOOKING_STATUS_LABELS = {
  [BOOKING_STATUS.PENDING]: 'Đang xử lý',
  [BOOKING_STATUS.CONFIRMED]: 'Đã xác nhận',
  [BOOKING_STATUS.CANCELLED]: 'Đã hủy',
  [BOOKING_STATUS.COMPLETED]: 'Hoàn thành',
  [BOOKING_STATUS.REFUNDED]: 'Đã hoàn tiền'
} as const;

// Payment Methods
export const PAYMENT_METHODS = [
  {
    id: 'credit_card',
    name: 'Thẻ tín dụng',
    icon: 'CreditCard',
    description: 'Visa, Mastercard, JCB'
  },
  {
    id: 'bank_transfer',
    name: 'Chuyển khoản ngân hàng',
    icon: 'Building',
    description: 'Chuyển khoản trực tiếp'
  },
  {
    id: 'e_wallet',
    name: 'Ví điện tử',
    icon: 'Smartphone',
    description: 'MoMo, ZaloPay, VNPay'
  },
  {
    id: 'cash',
    name: 'Tiền mặt',
    icon: 'Banknote',
    description: 'Thanh toán tại văn phòng'
  }
] as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Lỗi kết nối mạng. Vui lòng thử lại.',
  INVALID_INPUT: 'Thông tin nhập vào không hợp lệ.',
  UNAUTHORIZED: 'Bạn cần đăng nhập để thực hiện thao tác này.',
  FORBIDDEN: 'Bạn không có quyền thực hiện thao tác này.',
  NOT_FOUND: 'Không tìm thấy thông tin yêu cầu.',
  SERVER_ERROR: 'Lỗi máy chủ. Vui lòng thử lại sau.',
  BOOKING_EXPIRED: 'Phiên đặt chỗ đã hết hạn. Vui lòng thực hiện lại.',
  INSUFFICIENT_AVAILABILITY: 'Không đủ chỗ trống cho yêu cầu của bạn.',
  PAYMENT_FAILED: 'Thanh toán thất bại. Vui lòng kiểm tra thông tin và thử lại.'
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  BOOKING_CREATED: 'Đặt chỗ thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.',
  BOOKING_CANCELLED: 'Hủy đặt chỗ thành công.',
  PAYMENT_SUCCESS: 'Thanh toán thành công!',
  PROFILE_UPDATED: 'Cập nhật thông tin thành công.',
  REVIEW_SUBMITTED: 'Gửi đánh giá thành công. Cảm ơn bạn!',
  FAVORITE_ADDED: 'Đã thêm vào danh sách yêu thích.',
  FAVORITE_REMOVED: 'Đã xóa khỏi danh sách yêu thích.'
} as const;