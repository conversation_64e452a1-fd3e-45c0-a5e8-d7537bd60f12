'use client';

import React, { useState, useEffect } from 'react';
import { PageLayout, SidebarLayout } from '../../components/layout';
import { ServiceCard } from '../../components/cards';
import { SearchBar, LocationSearch, FilterPanel } from '../../components/search';
import { LoadingSpinner, EmptyState } from '../../components/ui';
import { useFavorites } from '../../hooks';
import { api } from '../../services/api';
import type { TravelService, SearchFilters } from '../../types';
import { SERVICE_CATEGORIES } from '../../constants';
import { MapPin } from 'lucide-react';

export default function ServicesPage() {
  const [services, setServices] = useState<TravelService[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [location, setLocation] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [serviceType, setServiceType] = useState('');
  const [priceRange, setPriceRange] = useState('');

  const { isFavorite, toggleFavorite } = useFavorites();

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      // Fallback data similar to mytour.vn travel services
      const fallbackServices: TravelService[] = [
        {
          id: '1',
          name: 'Saigon Tourist',
          type: 'agency',
          description: 'Công ty du lịch hàng đầu Việt Nam với hơn 30 năm kinh nghiệm',
          rating: 4.8,
          reviewCount: 1256,
          image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d',
          location: 'TP.HCM',
          contact: {
            phone: '028-3829-8914',
            email: '<EMAIL>',
            website: 'https://saigontourist.net'
          },
          services: ['Tour trong nước', 'Tour nước ngoài', 'Đặt khách sạn', 'Vé máy bay'],
          specialties: ['Tour cao cấp', 'MICE Tourism', 'Honeymoon packages'],
          priceRange: 'premium',
          verified: true,
          yearsOfExperience: 32,
          languages: ['Tiếng Việt', 'English', '中文', '日本語'],
          availability: true
        },
        {
          id: '2',
          name: 'Vietravel',
          type: 'agency',
          description: 'Tập đoàn du lịch lớn nhất Việt Nam với mạng lưới toàn quốc',
          rating: 4.7,
          reviewCount: 2103,
          image: 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0',
          location: 'Hà Nội',
          contact: {
            phone: '024-3933-1978',
            email: '<EMAIL>',
            website: 'https://vietravel.com'
          },
          services: ['Tour trọn gói', 'Du lịch doanh nghiệp', 'Visa', 'Bảo hiểm du lịch'],
          specialties: ['Tour châu Âu', 'Tour Mỹ', 'Tour châu Á'],
          priceRange: 'mid',
          verified: true,
          yearsOfExperience: 28,
          languages: ['Tiếng Việt', 'English', 'Français'],
          availability: true
        },
        {
          id: '3',
          name: 'Hướng dẫn viên Minh Tuấn',
          type: 'guide',
          description: 'Hướng dẫn viên chuyên nghiệp với kinh nghiệm 15 năm tại miền Bắc',
          rating: 4.9,
          reviewCount: 89,
          image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
          location: 'Hà Nội',
          contact: {
            phone: '0912-345-678',
            email: '<EMAIL>'
          },
          services: ['Hướng dẫn tour', 'Dịch thuật', 'Tư vấn lịch trình'],
          specialties: ['Sapa', 'Hạ Long', 'Ninh Bình', 'Văn hóa dân tộc'],
          priceRange: 'budget',
          verified: true,
          yearsOfExperience: 15,
          languages: ['Tiếng Việt', 'English', '中文'],
          availability: true
        },
        {
          id: '4',
          name: 'Mai Linh Express',
          type: 'transport',
          description: 'Dịch vụ vận chuyển uy tín với đội xe hiện đại',
          rating: 4.6,
          reviewCount: 567,
          image: 'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957',
          location: 'Toàn quốc',
          contact: {
            phone: '1900-6067',
            email: '<EMAIL>',
            website: 'https://mailinh.vn'
          },
          services: ['Xe limousine', 'Xe giường nằm', 'Thuê xe theo tháng', 'Shuttle bus'],
          specialties: ['Tuyến Sài Gòn - Đà Lạt', 'Tuyến Hà Nội - Sapa'],
          priceRange: 'mid',
          verified: true,
          yearsOfExperience: 25,
          languages: ['Tiếng Việt', 'English'],
          availability: true
        },
        {
          id: '5',
          name: 'VNI Insurance',
          type: 'insurance',
          description: 'Bảo hiểm du lịch toàn diện với mức phí hợp lý',
          rating: 4.5,
          reviewCount: 234,
          image: 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85',
          location: 'TP.HCM',
          contact: {
            phone: '1900-545-411',
            email: '<EMAIL>',
            website: 'https://vni.com.vn'
          },
          services: ['Bảo hiểm trong nước', 'Bảo hiểm quốc tế', 'Bảo hiểm nhóm'],
          specialties: ['Bảo hiểm y tế', 'Bảo hiểm hành lý', 'Bảo hiểm hủy chuyến'],
          priceRange: 'budget',
          verified: true,
          yearsOfExperience: 20,
          languages: ['Tiếng Việt', 'English'],
          availability: true
        },
        {
          id: '6',
          name: 'Visa Express Service',
          type: 'visa',
          description: 'Dịch vụ làm visa nhanh chóng và uy tín',
          rating: 4.8,
          reviewCount: 445,
          image: 'https://images.unsplash.com/photo-1554224155-8d04cb21cd6c',
          location: 'Hà Nội',
          contact: {
            phone: '024-3936-3636',
            email: '<EMAIL>',
            website: 'https://visaexpress.vn'
          },
          services: ['Visa Schengen', 'Visa Mỹ', 'Visa Nhật', 'Visa Hàn Quốc'],
          specialties: ['Visa du lịch', 'Visa công tác', 'Visa định cư'],
          priceRange: 'mid',
          verified: true,
          yearsOfExperience: 12,
          languages: ['Tiếng Việt', 'English', '한국어'],
          availability: true
        }
      ];

      try {
        const response = await fetch('/api/platforms/travel/start/travel-services');
        if (response.ok) {
          const data = await response.json();
          setServices(Array.isArray(data) ? data : fallbackServices);
        } else {
          setServices(fallbackServices);
        }
      } catch (error) {
        console.warn('API fetch failed, using fallback data:', error);
        setServices(fallbackServices);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    console.log('Searching services:', { searchQuery, location, serviceType, priceRange });
  };

  const handleContact = (serviceId: string) => {
    console.log('Contacting service:', serviceId);
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = !searchQuery || 
      service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesLocation = !location || 
      service.location.toLowerCase().includes(location.toLowerCase());
    
    const matchesType = serviceType === 'all' || service.type === serviceType;
    
    const matchesPrice = priceRange === 'all' || service.priceRange === priceRange;

    return matchesSearch && matchesLocation && matchesType && matchesPrice && service.availability;
  });

  const getServiceTypeName = (type: string) => {
    const names = {
      agency: 'Công ty du lịch',
      guide: 'Hướng dẫn viên',
      transport: 'Vận chuyển',
      insurance: 'Bảo hiểm',
      visa: 'Dịch vụ visa'
    };
    return names[type as keyof typeof names] || type;
  };

  const getPriceRangeName = (range: string) => {
    const names = {
      budget: 'Tiết kiệm',
      mid: 'Trung bình',
      premium: 'Cao cấp'
    };
    return names[range as keyof typeof names] || range;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dịch vụ du lịch...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Dịch Vụ Du Lịch</h1>
          
          {/* Search Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm dịch vụ..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Khu vực"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={serviceType}
              onChange={(e) => setServiceType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="all">Tất cả dịch vụ</option>
              <option value="agency">Công ty du lịch</option>
              <option value="guide">Hướng dẫn viên</option>
              <option value="transport">Vận chuyển</option>
              <option value="insurance">Bảo hiểm</option>
              <option value="visa">Dịch vụ visa</option>
            </select>
            
            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="all">Tất cả mức giá</option>
              <option value="budget">Tiết kiệm</option>
              <option value="mid">Trung bình</option>
              <option value="premium">Cao cấp</option>
            </select>
            
            <Button onClick={handleSearch} className="bg-pink-600 hover:bg-pink-700">
              Tìm kiếm
            </Button>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredServices.map((service) => (
            <Card key={service.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={service.image}
                  alt={service.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 left-2">
                  <Badge variant="default">
                    {getServiceTypeName(service.type)}
                  </Badge>
                </div>
                {service.verified && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Đã xác minh
                    </Badge>
                  </div>
                )}
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2">{service.name}</h3>
                
                <div className="flex items-center mb-2">
                  <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                  <span className="text-sm text-gray-600">{service.location}</span>
                  <Badge variant="outline" className="ml-2 text-xs">
                    {getPriceRangeName(service.priceRange)}
                  </Badge>
                </div>
                
                <div className="flex items-center mb-3">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium ml-1">{service.rating}</span>
                  <span className="text-sm text-gray-500 ml-1">({service.reviewCount} đánh giá)</span>
                  <Award className="h-4 w-4 text-gray-400 ml-4 mr-1" />
                  <span className="text-sm text-gray-600">{service.yearsOfExperience} năm</span>
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{service.description}</p>
                
                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">Dịch vụ:</p>
                  <div className="flex flex-wrap gap-1">
                    {service.services.slice(0, 2).map((srv, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {srv}
                      </Badge>
                    ))}
                    {service.services.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{service.services.length - 2} khác
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">Chuyên môn:</p>
                  <div className="flex flex-wrap gap-1">
                    {service.specialties.slice(0, 2).map((specialty, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="mb-4">
                  <p className="text-xs text-gray-500 mb-1">Ngôn ngữ:</p>
                  <p className="text-sm text-gray-600">{service.languages.join(', ')}</p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <Phone className="h-4 w-4 text-gray-400 mr-2" />
                    <span>{service.contact.phone}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Mail className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="truncate">{service.contact.email}</span>
                  </div>
                  {service.contact.website && (
                    <div className="flex items-center text-sm">
                      <Globe className="h-4 w-4 text-gray-400 mr-2" />
                      <a 
                        href={service.contact.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-pink-600 hover:underline truncate"
                      >
                        Website
                      </a>
                    </div>
                  )}
                </div>
                
                <Button 
                  onClick={() => handleContact(service.id)}
                  className="w-full mt-4 bg-pink-600 hover:bg-pink-700"
                  size="sm"
                >
                  Liên hệ ngay
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {filteredServices.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Không tìm thấy dịch vụ phù hợp với tiêu chí tìm kiếm.</p>
          </div>
        )}
      </div>
    </div>
  );
}