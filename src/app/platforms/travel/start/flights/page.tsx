'use client';

import React, { useState, useEffect } from 'react';
import { PageLayout, SidebarLayout } from '../../components/layout';
import { FlightCard } from '../../components/cards';
import { SearchBar, LocationSearch, DatePicker, FilterPanel } from '../../components/search';
import { LoadingSpinner, EmptyState } from '../../components/ui';
import { useSearch, useFavorites } from '../../hooks';
import { api } from '../../services/api';
import type { Flight, SearchFilters } from '../../types';
import { Plane } from 'lucide-react';

export default function FlightsPage() {
  const [flights, setFlights] = useState<Flight[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [departure, setDeparture] = useState('');
  const [destination, setDestination] = useState('');
  const [departureDate, setDepartureDate] = useState('');
  const [returnDate, setReturnDate] = useState('');
  const [passengers, setPassengers] = useState(1);
  const [tripType, setTripType] = useState<'roundtrip' | 'oneway'>('roundtrip');

  const [searchQuery, setSearchQuery] = useState('');
  const { isFavorite, toggleFavorite } = useFavorites();

  // Mock data - replace with actual API call
  const mockFlights: Flight[] = [
    {
      id: '1',
      airline: 'Vietnam Airlines',
      flightNumber: 'VN210',
      departure: {
        airport: 'Nội Bài (HAN)',
        city: 'Hà Nội',
        time: '08:00',
        date: '2024-01-15'
      },
      arrival: {
        airport: 'Tân Sơn Nhất (SGN)',
        city: 'TP.HCM',
        time: '10:15',
        date: '2024-01-15'
      },
      duration: '2h 15m',
      price: 2500000,
      originalPrice: 3000000,
      class: 'Economy',
      aircraft: 'Airbus A321',
      amenities: ['wifi', 'meal', 'entertainment'],
      baggage: {
        cabin: '7kg',
        checked: '23kg'
      },
      stops: 0,
      discount: 17
    },
    {
      id: '2',
      airline: 'Jetstar Pacific',
      flightNumber: 'BL550',
      departure: {
        airport: 'Nội Bài (HAN)',
        city: 'Hà Nội',
        time: '14:30',
        date: '2024-01-15'
      },
      arrival: {
        airport: 'Tân Sơn Nhất (SGN)',
        city: 'TP.HCM',
        time: '16:45',
        date: '2024-01-15'
      },
      duration: '2h 15m',
      price: 1800000,
      class: 'Economy',
      aircraft: 'Airbus A320',
      amenities: ['wifi'],
      baggage: {
        cabin: '7kg',
        checked: '20kg'
      },
      stops: 0
    },
    {
      id: '3',
      airline: 'VietJet Air',
      flightNumber: 'VJ130',
      departure: {
        airport: 'Nội Bài (HAN)',
        city: 'Hà Nội',
        time: '19:20',
        date: '2024-01-15'
      },
      arrival: {
        airport: 'Tân Sơn Nhất (SGN)',
        city: 'TP.HCM',
        time: '21:35',
        date: '2024-01-15'
      },
      duration: '2h 15m',
      price: 1600000,
      originalPrice: 2000000,
      class: 'Economy',
      aircraft: 'Airbus A321',
      amenities: ['wifi', 'meal'],
      baggage: {
        cabin: '7kg',
        checked: '20kg'
      },
      stops: 0,
      discount: 20
    }
  ];

  useEffect(() => {
    loadFlights();
  }, [filters, searchQuery]);

  const loadFlights = async () => {
    setLoading(true);
    try {
      // Replace with actual API call
      // const result = await api.flights.search({ ...filters, departure, destination, departureDate, returnDate, passengers });
      
      // Simulate API call
      setTimeout(() => {
        let filtered = mockFlights;
        
        if (searchQuery) {
          filtered = filtered.filter(flight => 
            flight.airline.toLowerCase().includes(searchQuery.toLowerCase()) ||
            flight.flightNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
            flight.departure.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
            flight.arrival.city.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }
        
        if (departure) {
          filtered = filtered.filter(flight => 
            flight.departure.city.toLowerCase().includes(departure.toLowerCase())
          );
        }
        
        if (destination) {
          filtered = filtered.filter(flight => 
            flight.arrival.city.toLowerCase().includes(destination.toLowerCase())
          );
        }
        
        if (filters.priceRange) {
          filtered = filtered.filter(flight => 
            flight.price >= filters.priceRange!.min && 
            flight.price <= filters.priceRange!.max
          );
        }
        
        setFlights(filtered);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading flights:', error);
      setLoading(false);
    }
  };

  const handleSearch = () => {
    const searchFilters: SearchFilters = {
      departure,
      destination,
      departureDate,
      returnDate,
      passengers,
      ...filters
    };
    setFilters(searchFilters);
  };

  const handleFavorite = (flightId: string) => {
    toggleFavorite(flightId);
  };

  const breadcrumbs = [
    { label: 'Trang chủ', href: '/platforms/travel/start' },
    { label: 'Vé máy bay' }
  ];

  const searchSection = (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <div className="mb-4">
        <div className="flex gap-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="tripType"
              value="roundtrip"
              checked={tripType === 'roundtrip'}
              onChange={(e) => setTripType(e.target.value as 'roundtrip')}
              className="mr-2"
            />
            Khứ hồi
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="tripType"
              value="oneway"
              checked={tripType === 'oneway'}
              onChange={(e) => setTripType(e.target.value as 'oneway')}
              className="mr-2"
            />
            Một chiều
          </label>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <LocationSearch
          value={departure}
          onChange={setDeparture}
          placeholder="Điểm khởi hành"
        />
        
        <LocationSearch
          value={destination}
          onChange={setDestination}
          placeholder="Điểm đến"
        />
        
        <DatePicker
          value={departureDate}
          onChange={setDepartureDate}
          placeholder="Ngày đi"
        />
        
        {tripType === 'roundtrip' && (
          <DatePicker
            value={returnDate}
            onChange={setReturnDate}
            placeholder="Ngày về"
            minDate={departureDate}
          />
        )}
        
        <select
          value={passengers}
          onChange={(e) => setPassengers(Number(e.target.value))}
          className="px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {Array.from({ length: 9 }, (_, i) => (
            <option key={i + 1} value={i + 1}>
              {i + 1} hành khách
            </option>
          ))}
        </select>
        
        <button
          onClick={handleSearch}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          Tìm chuyến bay
        </button>
      </div>
    </div>
  );

  const sidebar = (
    <div className="space-y-6">
      <SearchBar
        placeholder="Tìm kiếm chuyến bay..."
        onSearch={setSearchQuery}
      />
      
      <FilterPanel
        filters={filters}
        onFiltersChange={setFilters}
        showAirlineFilter={true}
        showDepartureTimeFilter={true}
      />
    </div>
  );

  const content = (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          {loading ? 'Đang tìm kiếm...' : `Tìm thấy ${flights.length} chuyến bay`}
        </h2>
      </div>

      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-32"></div>
                    <div className="h-3 bg-gray-200 rounded w-24"></div>
                  </div>
                </div>
                <div className="text-right space-y-2">
                  <div className="h-6 bg-gray-200 rounded w-24"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : flights.length > 0 ? (
        <div className="space-y-4">
          {flights.map((flight) => (
            <FlightCard
              key={flight.id}
              flight={flight}
              onFavorite={handleFavorite}
            />
          ))}
        </div>
      ) : (
        <EmptyState
          title="Không tìm thấy chuyến bay"
          description="Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm"
          icon={<Plane className="w-16 h-16" />}
        />
      )}
    </div>
  );

  return (
    <PageLayout
      title="Vé máy bay"
      breadcrumbs={breadcrumbs}
    >
      {searchSection}
      
      <SidebarLayout sidebar={sidebar}>
        {content}
      </SidebarLayout>
    </PageLayout>
  );
}