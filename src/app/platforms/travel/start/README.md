# Safe Travel Platform

A comprehensive travel booking platform inspired by SeaGo.vn, built with Next.js and integrated with the OneID authentication system.

## 🌟 Features

### Core Services
- **Hotel Booking**: Browse and book accommodations with detailed information and amenities
- **Flight Booking**: Search and book domestic and international flights
- **Tour Packages**: Discover and book guided tours and experiences
- **Car Rental**: Rent vehicles for your travel needs

### Security & Authentication
- **OneID Integration**: Secure authentication using the ABN.Green OneID system
- **Protected Bookings**: All booking operations require user authentication
- **Session Management**: Secure session handling with JWT tokens

### User Experience
- **Responsive Design**: Mobile-first design that works on all devices
- **Modern UI**: Clean, intuitive interface with Tailwind CSS
- **Icon-based Design**: Uses Lucide React icons instead of images for better performance
- **Real-time Search**: Dynamic filtering and search capabilities

## 🏗️ Architecture

### Frontend Structure
```
src/app/platforms/travel/start/
├── page.tsx              # Main landing page
├── layout.tsx            # Layout with OneID provider
├── hotels/
│   └── page.tsx         # Hotel booking page
├── flights/
│   └── page.tsx         # Flight booking page
├── tours/
│   └── page.tsx         # Tour packages page
└── cars/
    └── page.tsx         # Car rental page
```

### API Routes
```
src/app/api/platforms/travel/start/
├── services/route.ts     # Travel services API
├── destinations/route.ts # Destinations API
├── bookings/route.ts     # Booking management API
└── search/route.ts       # Search functionality API
```

### Data Structure
```
data/apps/platforms/travel/start/
├── services.json         # Travel services data
├── destinations.json     # Popular destinations
├── bookings.json         # User bookings
└── config.json          # Platform configuration
```

## 🔧 Technology Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui components
- **Icons**: Lucide React
- **Authentication**: OneID system integration
- **Data Storage**: JSON files (can be migrated to database)
- **TypeScript**: Full type safety

## 🚀 Getting Started

### Prerequisites
- Node.js 22.17.0 (use `nvm use 22.17.0`)
- Yarn package manager
- OneID system properly configured

### Installation

1. **Navigate to the project directory**:
   ```bash
   cd /Users/<USER>/Workspace/abn.green
   ```

2. **Install dependencies** (if not already installed):
   ```bash
   yarn install
   ```

3. **Start the development server**:
   ```bash
   yarn dev
   ```

4. **Access the platform**:
   Open [http://localhost:3000/platforms/travel/start](http://localhost:3000/platforms/travel/start)

## 🔐 OneID Integration

The platform is fully integrated with the OneID authentication system:

### Authentication Flow
1. Users click "Login with OneID" button
2. Redirected to OneID login page
3. After successful authentication, returned to the platform
4. Session token stored and managed automatically

### Protected Features
- All booking operations require authentication
- User-specific booking history
- Secure API endpoints with session validation

### Usage Example
```typescript
import { useOneID } from '@/app/backbone/oneid/hooks/useOneID';

function BookingComponent() {
  const { user, isAuthenticated, login } = useOneID();
  
  const handleBooking = () => {
    if (!isAuthenticated) {
      login(); // Redirects to OneID login
      return;
    }
    // Proceed with booking
  };
}
```

## 📊 Data Management

### Services Data
The platform uses JSON files for data storage:

- **services.json**: Contains hotels, flights, tours, and car rental data
- **destinations.json**: Popular travel destinations
- **bookings.json**: User booking records
- **config.json**: Platform configuration

### API Endpoints

#### Services API
- `GET /api/platforms/travel/start/services` - Get all services
- `GET /api/platforms/travel/start/services?type=hotel` - Filter by type
- `POST /api/platforms/travel/start/services` - Create new service

#### Bookings API
- `GET /api/platforms/travel/start/bookings` - Get user bookings
- `POST /api/platforms/travel/start/bookings` - Create new booking
- `PUT /api/platforms/travel/start/bookings` - Update booking status

#### Search API
- `GET /api/platforms/travel/start/search?q=query` - Search services and destinations

## 🎨 Design System

### Color Palette
- **Primary**: Blue (#2563eb)
- **Secondary**: Gray (#64748b)
- **Accent**: Green (#10b981)
- **Warning**: Yellow (#eab308)

### Components
- Cards for service listings
- Badges for ratings and features
- Buttons with consistent styling
- Form inputs with proper validation
- Loading states and animations

## 🔄 Future Enhancements

### Planned Features
1. **Payment Integration**: Connect with ABN Payment Hub
2. **Real-time Availability**: Live inventory management
3. **Reviews & Ratings**: User feedback system
4. **Notifications**: Email and push notifications
5. **Mobile App**: React Native implementation

### Database Migration
The current JSON-based storage can be migrated to:
- PostgreSQL for relational data
- MongoDB for flexible document storage
- Redis for caching and sessions

## 🧪 Testing

### Manual Testing
1. Navigate to the platform
2. Test search functionality
3. Verify OneID authentication flow
4. Test booking process (requires authentication)
5. Check responsive design on different devices

### API Testing
Use tools like Postman or curl to test API endpoints:

```bash
# Get all hotels
curl http://localhost:3000/api/platforms/travel/start/services?type=hotel

# Search for services
curl "http://localhost:3000/api/platforms/travel/start/search?q=hanoi"
```

## 📝 Configuration

### Platform Settings
Edit `data/apps/platforms/travel/start/config.json` to modify:
- Platform name and description
- Feature toggles
- API endpoints
- UI theme settings
- Integration configurations

### OneID Settings
The platform automatically uses the OneID system configured in:
- `/src/app/backbone/oneid/`

## 🤝 Contributing

1. Follow the existing code structure
2. Use TypeScript for type safety
3. Implement proper error handling
4. Add loading states for async operations
5. Ensure responsive design
6. Test OneID integration thoroughly

## 📞 Support

For technical support or questions:
- Check the OneID documentation in `/src/app/backbone/oneid/`
- Review the API documentation
- Contact the development team

## 📄 License

This project is part of the ABN.Green ecosystem and follows the same licensing terms.

---

**Safe Travel** - Your trusted travel companion for safe and memorable journeys worldwide. Powered by ABN.Green OneID System.