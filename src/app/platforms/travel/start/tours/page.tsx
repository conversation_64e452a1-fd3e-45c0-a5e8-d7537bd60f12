'use client';

import React, { useState, useEffect } from 'react';
import { PageLayout, SidebarLayout } from '../../components/layout';
import { TourCard } from '../../components/cards';
import { SearchBar, LocationSearch, DatePicker, FilterPanel } from '../../components/search';
import { LoadingSpinner, EmptyState } from '../../components/ui';
import { useFavorites } from '../../hooks';
import { api } from '../../services/api';
import type { Tour, SearchFilters } from '../../types';
import { MapPin } from 'lucide-react';

export default function ToursPage() {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [destination, setDestination] = useState('');
  const [tourType, setTourType] = useState<'all' | 'domestic' | 'international'>('all');
  const [priceRange, setPriceRange] = useState<'all' | 'budget' | 'mid' | 'luxury'>('all');
  const { isFavorite, toggleFavorite } = useFavorites();

  useEffect(() => {
    fetchTours();
  }, []);

  const fetchTours = async () => {
    try {
      // Fallback data similar to mytour.vn tour packages
      const fallbackTours: Tour[] = [
        {
          id: '1',
          name: 'Tour Hà Nội - Hạ Long - Sapa 4N3Đ',
          destination: 'Hà Nội - Hạ Long - Sapa',
          duration: '4 ngày 3 đêm',
          price: 4500000,
          originalPrice: 5200000,
          rating: 4.8,
          reviewCount: 156,
          image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19',
          description: 'Khám phá vẻ đẹp của Thủ đô Hà Nội, vịnh Hạ Long kỳ vĩ và Sapa mờ sương',
          highlights: ['Vịnh Hạ Long UNESCO', 'Ruộng bậc thang Sapa', 'Phố cổ Hà Nội'],
          includes: ['Xe du lịch', 'Khách sạn 3*', 'Ăn theo chương trình', 'Hướng dẫn viên'],
          tourType: 'domestic',
          groupSize: '15-20 người',
          difficulty: 'easy',
          departureDate: '2024-02-15',
          availability: 8
        },
        {
          id: '2',
          name: 'Tour Phú Quốc 3N2Đ - Khám phá đảo ngọc',
          destination: 'Phú Quốc',
          duration: '3 ngày 2 đêm',
          price: 3200000,
          originalPrice: 3800000,
          rating: 4.9,
          reviewCount: 203,
          image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
          description: 'Tận hưởng thiên đường biển đảo với những bãi cát trắng mịn và nước biển trong xanh',
          highlights: ['Cáp treo Hòn Thơm', 'Safari Phú Quốc', 'Chợ đêm Dinh Cậu'],
          includes: ['Vé máy bay', 'Resort 4*', 'Ăn sáng', 'Tour tham quan'],
          tourType: 'domestic',
          groupSize: '10-15 người',
          difficulty: 'easy',
          departureDate: '2024-02-20',
          availability: 12
        },
        {
          id: '3',
          name: 'Tour Thái Lan Bangkok - Pattaya 5N4Đ',
          destination: 'Bangkok - Pattaya',
          duration: '5 ngày 4 đêm',
          price: 8900000,
          originalPrice: 10500000,
          rating: 4.7,
          reviewCount: 89,
          image: 'https://images.unsplash.com/photo-1528181304800-259b08848526',
          description: 'Khám phá xứ sở chùa vàng với những trải nghiệm văn hóa độc đáo',
          highlights: ['Chùa Wat Pho', 'Floating Market', 'Pattaya Beach'],
          includes: ['Vé máy bay', 'Khách sạn 4*', 'Visa Thái Lan', 'Hướng dẫn viên'],
          tourType: 'international',
          groupSize: '20-25 người',
          difficulty: 'easy',
          departureDate: '2024-03-01',
          availability: 5
        },
        {
          id: '4',
          name: 'Tour Nhật Bản Tokyo - Osaka 6N5Đ',
          destination: 'Tokyo - Osaka',
          duration: '6 ngày 5 đêm',
          price: 25900000,
          originalPrice: 28500000,
          rating: 4.9,
          reviewCount: 67,
          image: 'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e',
          description: 'Trải nghiệm văn hóa Nhật Bản từ Tokyo hiện đại đến Osaka truyền thống',
          highlights: ['Núi Phú Sĩ', 'Chùa Senso-ji', 'Lâu đài Osaka'],
          includes: ['Vé máy bay', 'Khách sạn 4*', 'JR Pass', 'Visa Nhật Bản'],
          tourType: 'international',
          groupSize: '15-20 người',
          difficulty: 'moderate',
          departureDate: '2024-03-15',
          availability: 3
        },
        {
          id: '5',
          name: 'Tour Đà Lạt 2N1Đ - Thành phố ngàn hoa',
          destination: 'Đà Lạt',
          duration: '2 ngày 1 đêm',
          price: 1800000,
          rating: 4.6,
          reviewCount: 124,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96',
          description: 'Khám phá thành phố mộng mơ với khí hậu mát mẻ quanh năm',
          highlights: ['Hồ Xuân Hương', 'Thác Elephant', 'Làng hoa Vạn Thành'],
          includes: ['Xe du lịch', 'Khách sạn 3*', 'Ăn sáng', 'Vé tham quan'],
          tourType: 'domestic',
          groupSize: '12-18 người',
          difficulty: 'easy',
          departureDate: '2024-02-25',
          availability: 15
        },
        {
          id: '6',
          name: 'Tour Singapore - Malaysia 4N3Đ',
          destination: 'Singapore - Kuala Lumpur',
          duration: '4 ngày 3 đêm',
          price: 12500000,
          originalPrice: 14200000,
          rating: 4.8,
          reviewCount: 91,
          image: 'https://images.unsplash.com/photo-1525625293386-3f8f99389edd',
          description: 'Khám phá hai quốc gia Đông Nam Á với những trải nghiệm đa dạng',
          highlights: ['Marina Bay Sands', 'Petronas Towers', 'Universal Studios'],
          includes: ['Vé máy bay', 'Khách sạn 4*', 'Visa', 'Tour tham quan'],
          tourType: 'international',
          groupSize: '18-22 người',
          difficulty: 'easy',
          departureDate: '2024-03-10',
          availability: 7
        }
      ];

      try {
        const response = await fetch('/api/platforms/travel/start/tours');
        if (response.ok) {
          const data = await response.json();
          setTours(Array.isArray(data) ? data : fallbackTours);
        } else {
          setTours(fallbackTours);
        }
      } catch (error) {
        console.warn('API fetch failed, using fallback data:', error);
        setTours(fallbackTours);
      }
    } catch (error) {
      console.error('Error fetching tours:', error);
      setTours([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    // Implement search logic
    console.log('Searching tours:', { searchQuery, destination, tourType, priceRange });
  };

  const handleBooking = (tourId: string) => {
    if (!isAuthenticated) {
      login();
      return;
    }
    console.log('Booking tour:', tourId);
  };

  const filteredTours = tours.filter(tour => {
    const matchesSearch = !searchQuery || 
      tour.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tour.destination.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesDestination = !destination || 
      tour.destination.toLowerCase().includes(destination.toLowerCase());
    
    const matchesType = tourType === 'all' || tour.tourType === tourType;
    
    const matchesPrice = priceRange === 'all' || 
      (priceRange === 'budget' && tour.price < 5000000) ||
      (priceRange === 'mid' && tour.price >= 5000000 && tour.price < 15000000) ||
      (priceRange === 'luxury' && tour.price >= 15000000);

    return matchesSearch && matchesDestination && matchesType && matchesPrice;
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải tour du lịch...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Tour Du Lịch</h1>
          
          {/* Search Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm tour..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Điểm đến"
                value={destination}
                onChange={(e) => setDestination(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={tourType}
              onChange={(e) => setTourType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="all">Tất cả tour</option>
              <option value="domestic">Tour trong nước</option>
              <option value="international">Tour nước ngoài</option>
            </select>
            
            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="all">Tất cả giá</option>
              <option value="budget">Dưới 5 triệu</option>
              <option value="mid">5-15 triệu</option>
              <option value="luxury">Trên 15 triệu</option>
            </select>
            
            <Button onClick={handleSearch} className="bg-pink-600 hover:bg-pink-700">
              Tìm kiếm
            </Button>
          </div>
        </div>
      </div>

      {/* Tours Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTours.map((tour) => (
            <Card key={tour.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={tour.image}
                  alt={tour.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 left-2">
                  <Badge variant={tour.tourType === 'domestic' ? 'default' : 'secondary'}>
                    {tour.tourType === 'domestic' ? 'Trong nước' : 'Nước ngoài'}
                  </Badge>
                </div>
                {tour.originalPrice && (
                  <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
                    Giảm {Math.round((1 - tour.price / tour.originalPrice) * 100)}%
                  </div>
                )}
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2 line-clamp-2">{tour.name}</h3>
                
                <div className="flex items-center mb-2">
                  <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                  <span className="text-sm text-gray-600">{tour.destination}</span>
                </div>
                
                <div className="flex items-center mb-2">
                  <Clock className="h-4 w-4 text-gray-400 mr-1" />
                  <span className="text-sm text-gray-600">{tour.duration}</span>
                  <Users className="h-4 w-4 text-gray-400 ml-4 mr-1" />
                  <span className="text-sm text-gray-600">{tour.groupSize}</span>
                </div>
                
                <div className="flex items-center mb-3">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium ml-1">{tour.rating}</span>
                  <span className="text-sm text-gray-500 ml-1">({tour.reviewCount} đánh giá)</span>
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{tour.description}</p>
                
                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">Điểm nổi bật:</p>
                  <div className="flex flex-wrap gap-1">
                    {tour.highlights.slice(0, 2).map((highlight, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {highlight}
                      </Badge>
                    ))}
                    {tour.highlights.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{tour.highlights.length - 2} khác
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    {tour.originalPrice && (
                      <span className="text-sm text-gray-400 line-through">
                        {tour.originalPrice.toLocaleString('vi-VN')}đ
                      </span>
                    )}
                    <div className="text-lg font-bold text-pink-600">
                      {tour.price.toLocaleString('vi-VN')}đ
                    </div>
                    <div className="text-xs text-gray-500">
                      Còn {tour.availability} chỗ
                    </div>
                  </div>
                  
                  <Button 
                    onClick={() => handleBooking(tour.id)}
                    className="bg-pink-600 hover:bg-pink-700"
                    size="sm"
                  >
                    Đặt ngay
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {filteredTours.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Không tìm thấy tour phù hợp với tiêu chí tìm kiếm.</p>
          </div>
        )}
      </div>
    </div>
  );
}