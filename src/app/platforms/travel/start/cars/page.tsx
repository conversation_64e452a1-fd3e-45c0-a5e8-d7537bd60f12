'use client';

import React, { useState, useEffect } from 'react';
import { PageLayout, SidebarLayout } from '../../components/layout';
import { CarCard } from '../../components/cards';
import { SearchBar, LocationSearch, DatePicker, FilterPanel } from '../../components/search';
import { LoadingSpinner, EmptyState } from '../../components/ui';
import { useFavorites } from '../../hooks';
import { api } from '../../services/api';
import type { CarRental, SearchFilters } from '../../types';
import { 
  Search, 
  Star, 
  MapPin, 
  Calendar,
  Users,
  Clock,
  Car,
  Fuel,
  Settings,
  Shield
} from 'lucide-react';

export default function CarRentalPage() {
  const [cars, setCars] = useState<CarRental[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [pickupLocation, setPickupLocation] = useState('');
  const [pickupDate, setPickupDate] = useState('');
  const [returnDate, setReturnDate] = useState('');
  const [category, setCategory] = useState<'all' | CarRental['category']>('all');
  const { isFavorite, toggleFavorite } = useFavorites();

  useEffect(() => {
    fetchCars();
  }, []);

  const fetchCars = async () => {
    try {
      // Fallback data similar to mytour.vn car rental services
      const fallbackCars: CarRental[] = [
        {
          id: '1',
          name: 'Toyota Vios 2023',
          brand: 'Toyota',
          model: 'Vios',
          year: 2023,
          seats: 5,
          transmission: 'automatic',
          fuelType: 'petrol',
          pricePerDay: 800000,
          originalPrice: 950000,
          rating: 4.8,
          reviewCount: 156,
          image: 'https://images.unsplash.com/photo-1549924231-f129b911e442',
          description: 'Xe sedan tiết kiệm nhiên liệu, phù hợp cho gia đình nhỏ',
          features: ['Điều hòa', 'GPS', 'Camera lùi', 'Bluetooth'],
          location: 'Hà Nội',
          availability: true,
          pickupLocations: ['Sân bay Nội Bài', 'Trung tâm Hà Nội', 'Ga Hà Nội'],
          insurance: true,
          mileageLimit: '300km/ngày',
          category: 'economy'
        },
        {
          id: '2',
          name: 'Honda City 2023',
          brand: 'Honda',
          model: 'City',
          year: 2023,
          seats: 5,
          transmission: 'automatic',
          fuelType: 'petrol',
          pricePerDay: 850000,
          rating: 4.7,
          reviewCount: 203,
          image: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d',
          description: 'Sedan hiện đại với thiết kế trẻ trung, động cơ mạnh mẽ',
          features: ['Điều hòa', 'GPS', 'Camera 360', 'Cảm biến lùi'],
          location: 'TP.HCM',
          availability: true,
          pickupLocations: ['Sân bay Tân Sơn Nhất', 'Quận 1', 'Quận 3'],
          insurance: true,
          mileageLimit: '300km/ngày',
          category: 'compact'
        },
        {
          id: '3',
          name: 'Toyota Fortuner 2023',
          brand: 'Toyota',
          model: 'Fortuner',
          year: 2023,
          seats: 7,
          transmission: 'automatic',
          fuelType: 'diesel',
          pricePerDay: 1500000,
          originalPrice: 1800000,
          rating: 4.9,
          reviewCount: 89,
          image: 'https://images.unsplash.com/photo-1519641471654-76ce0107ad1b',
          description: 'SUV 7 chỗ mạnh mẽ, phù hợp cho du lịch gia đình và địa hình khó',
          features: ['4WD', 'Điều hòa 2 chiều', 'GPS', 'Camera 360', 'Cảm biến va chạm'],
          location: 'Đà Nẵng',
          availability: true,
          pickupLocations: ['Sân bay Đà Nẵng', 'Trung tâm Đà Nẵng', 'Hội An'],
          insurance: true,
          mileageLimit: '400km/ngày',
          category: 'suv'
        },
        {
          id: '4',
          name: 'Mercedes C-Class 2023',
          brand: 'Mercedes',
          model: 'C-Class',
          year: 2023,
          seats: 5,
          transmission: 'automatic',
          fuelType: 'petrol',
          pricePerDay: 2500000,
          rating: 4.9,
          reviewCount: 67,
          image: 'https://images.unsplash.com/photo-1563720223185-11003d516935',
          description: 'Sedan hạng sang với nội thất cao cấp và công nghệ hiện đại',
          features: ['Ghế da', 'Âm thanh Burmester', 'Điều hòa tự động', 'Cửa sổ trời'],
          location: 'TP.HCM',
          availability: true,
          pickupLocations: ['Sân bay Tân Sơn Nhất', 'Quận 1', 'Quận 7'],
          insurance: true,
          mileageLimit: '250km/ngày',
          category: 'luxury'
        },
        {
          id: '5',
          name: 'Ford Transit 2023',
          brand: 'Ford',
          model: 'Transit',
          year: 2023,
          seats: 16,
          transmission: 'manual',
          fuelType: 'diesel',
          pricePerDay: 1200000,
          rating: 4.6,
          reviewCount: 124,
          image: 'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957',
          description: 'Xe 16 chỗ phù hợp cho nhóm du lịch lớn',
          features: ['Điều hòa', 'Âm thanh', 'Ghế ngả', 'Cửa trượt'],
          location: 'Hà Nội',
          availability: true,
          pickupLocations: ['Sân bay Nội Bài', 'Trung tâm Hà Nội'],
          insurance: true,
          mileageLimit: '500km/ngày',
          category: 'van'
        },
        {
          id: '6',
          name: 'Hyundai Accent 2023',
          brand: 'Hyundai',
          model: 'Accent',
          year: 2023,
          seats: 5,
          transmission: 'automatic',
          fuelType: 'petrol',
          pricePerDay: 750000,
          rating: 4.5,
          reviewCount: 91,
          image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70',
          description: 'Sedan kinh tế với thiết kế hiện đại và tiết kiệm nhiên liệu',
          features: ['Điều hòa', 'GPS', 'USB', 'Bluetooth'],
          location: 'Nha Trang',
          availability: true,
          pickupLocations: ['Sân bay Cam Ranh', 'Trung tâm Nha Trang'],
          insurance: true,
          mileageLimit: '300km/ngày',
          category: 'economy'
        }
      ];

      try {
        const response = await fetch('/api/platforms/travel/start/cars');
        if (response.ok) {
          const data = await response.json();
          setCars(Array.isArray(data) ? data : fallbackCars);
        } else {
          setCars(fallbackCars);
        }
      } catch (error) {
        console.warn('API fetch failed, using fallback data:', error);
        setCars(fallbackCars);
      }
    } catch (error) {
      console.error('Error fetching cars:', error);
      setCars([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    console.log('Searching cars:', { searchQuery, pickupLocation, pickupDate, returnDate, category });
  };

  const handleBooking = (carId: string) => {
    if (!isAuthenticated) {
      login();
      return;
    }
    console.log('Booking car:', carId);
  };

  const filteredCars = cars.filter(car => {
    const matchesSearch = !searchQuery || 
      car.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      car.brand.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesLocation = !pickupLocation || 
      car.location.toLowerCase().includes(pickupLocation.toLowerCase()) ||
      car.pickupLocations.some(loc => loc.toLowerCase().includes(pickupLocation.toLowerCase()));
    
    const matchesCategory = category === 'all' || car.category === category;

    return matchesSearch && matchesLocation && matchesCategory && car.availability;
  });

  const getCategoryName = (cat: string) => {
    const names = {
      economy: 'Kinh tế',
      compact: 'Nhỏ gọn',
      standard: 'Tiêu chuẩn',
      luxury: 'Sang trọng',
      suv: 'SUV',
      van: 'Xe tải/Van'
    };
    return names[cat as keyof typeof names] || cat;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải xe cho thuê...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Thuê Xe Du Lịch</h1>
          
          {/* Search Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm xe..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Điểm nhận xe"
                value={pickupLocation}
                onChange={(e) => setPickupLocation(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="relative">
              <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                type="date"
                placeholder="Ngày nhận"
                value={pickupDate}
                onChange={(e) => setPickupDate(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="relative">
              <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                type="date"
                placeholder="Ngày trả"
                value={returnDate}
                onChange={(e) => setReturnDate(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="all">Tất cả loại xe</option>
              <option value="economy">Kinh tế</option>
              <option value="compact">Nhỏ gọn</option>
              <option value="standard">Tiêu chuẩn</option>
              <option value="luxury">Sang trọng</option>
              <option value="suv">SUV</option>
              <option value="van">Xe tải/Van</option>
            </select>
            
            <Button onClick={handleSearch} className="bg-pink-600 hover:bg-pink-700">
              Tìm kiếm
            </Button>
          </div>
        </div>
      </div>

      {/* Cars Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCars.map((car) => (
            <Card key={car.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={car.image}
                  alt={car.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 left-2">
                  <Badge variant="default">
                    {getCategoryName(car.category)}
                  </Badge>
                </div>
                {car.originalPrice && (
                  <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
                    Giảm {Math.round((1 - car.pricePerDay / car.originalPrice) * 100)}%
                  </div>
                )}
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2">{car.name}</h3>
                
                <div className="flex items-center mb-2">
                  <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                  <span className="text-sm text-gray-600">{car.location}</span>
                </div>
                
                <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-gray-400 mr-1" />
                    <span>{car.seats} chỗ</span>
                  </div>
                  <div className="flex items-center">
                    <Settings className="h-4 w-4 text-gray-400 mr-1" />
                    <span>{car.transmission === 'automatic' ? 'Tự động' : 'Số sàn'}</span>
                  </div>
                  <div className="flex items-center">
                    <Fuel className="h-4 w-4 text-gray-400 mr-1" />
                    <span className="capitalize">{car.fuelType}</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 text-gray-400 mr-1" />
                    <span>{car.insurance ? 'Có bảo hiểm' : 'Không bảo hiểm'}</span>
                  </div>
                </div>
                
                <div className="flex items-center mb-3">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium ml-1">{car.rating}</span>
                  <span className="text-sm text-gray-500 ml-1">({car.reviewCount} đánh giá)</span>
                </div>
                
                <p className="text-sm text-gray-600 mb-3">{car.description}</p>
                
                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">Tiện nghi:</p>
                  <div className="flex flex-wrap gap-1">
                    {car.features.slice(0, 3).map((feature, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {car.features.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{car.features.length - 3} khác
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="mb-3">
                  <p className="text-xs text-gray-500">Giới hạn: {car.mileageLimit}</p>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    {car.originalPrice && (
                      <span className="text-sm text-gray-400 line-through">
                        {car.originalPrice.toLocaleString('vi-VN')}đ
                      </span>
                    )}
                    <div className="text-lg font-bold text-pink-600">
                      {car.pricePerDay.toLocaleString('vi-VN')}đ
                    </div>
                    <div className="text-xs text-gray-500">
                      /ngày
                    </div>
                  </div>
                  
                  <Button 
                    onClick={() => handleBooking(car.id)}
                    className="bg-pink-600 hover:bg-pink-700"
                    size="sm"
                  >
                    Thuê ngay
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {filteredCars.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Không tìm thấy xe phù hợp với tiêu chí tìm kiếm.</p>
          </div>
        )}
      </div>
    </div>
  );
}