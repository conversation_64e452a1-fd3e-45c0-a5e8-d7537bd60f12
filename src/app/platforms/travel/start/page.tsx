'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Star, 
  Plane, 
  Building2, 
  Car, 
  MapPin,
  User,
  Calendar,
  Users,
  Camera,
  Headphones,
  Shield,
  CreditCard,
  Clock,
  Globe,
  Phone,
  Mail
} from 'lucide-react';
import { useOneID } from '@/app/backbone/oneid/hooks/useOneID';
import Link from 'next/link';

interface Service {
  id: string;
  name: string;
  type: 'hotel' | 'flight' | 'tour' | 'car';
  price: number;
  rating: number;
  location: string;
  image: string;
  description: string;
  features: string[];
}

interface Destination {
  id: string;
  name: string;
  country: string;
  image: string;
  description: string;
  popularTours: number;
  averagePrice: number;
}

export default function SafeTravelHome() {
  const { user, isAuthenticated, login } = useOneID();
  const [services, setServices] = useState<Service[]>([]);
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('hotels');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fallback data in case API fails
        const fallbackServices = [
          {
            id: '1',
            name: 'Centara Mirage Resort Mũi Né',
            type: 'hotel' as const,
            price: 3472509,
            rating: 9.2,
            location: 'Thành Phố Phan Thiết',
            image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945',
            description: 'Khách sạn sang trọng với view biển tuyệt đẹp',
            features: ['Pool', 'Spa', 'Beach']
          },
          {
            id: '2',
            name: 'Khách Sạn ibis Styles Vũng Tàu',
            type: 'hotel' as const,
            price: 4630463,
            rating: 9.4,
            location: 'Thành Phố Vũng Tàu',
            image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d',
            description: 'Khách sạn hiện đại trong trung tâm thành phố',
            features: ['Gym', 'Restaurant', 'WiFi']
          }
        ];

        const fallbackDestinations = [
          {
            id: '1',
            name: 'Đà Nẵng',
            country: 'Việt Nam',
            image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19',
            description: 'Thành phố biển xinh đẹp',
            popularTours: 45,
            averagePrice: 2500000
          },
          {
            id: '2',
            name: 'Phú Quốc',
            country: 'Việt Nam',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
            description: 'Đảo ngọc của Việt Nam',
            popularTours: 32,
            averagePrice: 3200000
          }
        ];

        try {
          const [servicesRes, destinationsRes] = await Promise.all([
            fetch('/api/platforms/travel/start/services'),
            fetch('/api/platforms/travel/start/destinations')
          ]);

          if (servicesRes.ok && destinationsRes.ok) {
            const servicesData = await servicesRes.json();
            const destinationsData = await destinationsRes.json();
            setServices(Array.isArray(servicesData) ? servicesData.slice(0, 6) : fallbackServices);
            setDestinations(Array.isArray(destinationsData) ? destinationsData.slice(0, 4) : fallbackDestinations);
          } else {
            // Use fallback data if API fails
            setServices(fallbackServices);
            setDestinations(fallbackDestinations);
          }
        } catch (fetchError) {
          console.warn('API fetch failed, using fallback data:', fetchError);
          setServices(fallbackServices);
          setDestinations(fallbackDestinations);
        }
      } catch (error) {
        console.error('Error in fetchData:', error);
        // Set empty arrays as final fallback
        setServices([]);
        setDestinations([]);
      } finally {
        setLoading(false);
      }
    };

    if (mounted) {
      fetchData();
    }
  }, [mounted]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      const response = await fetch(`/api/platforms/travel/start/search?q=${encodeURIComponent(searchQuery)}&type=${activeTab}`);
      if (response.ok) {
        const data = await response.json();
        setServices(data.services || []);
        setDestinations(data.destinations || []);
      }
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  const handleBooking = (serviceId: string) => {
    if (!isAuthenticated) {
      login();
      return;
    }
    // Handle booking logic
    console.log('Booking service:', serviceId);
  };

  if (!mounted || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải SafeTravel...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Promotional Banner - Exact SeaGo.vn style */}
      <div className="bg-gradient-to-r from-pink-500 to-red-500 text-white py-2 text-center text-sm">
        <span>🎉 Giới thiệu doanh nghiệp mới để nhận thưởng tới 35 triệu đồng. Chi tiết →</span>
      </div>

      {/* Header - Exact SeaGo.vn style */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="text-2xl font-bold text-pink-500 mr-8">
                MyTour
              </div>
              <span className="text-xs text-gray-500">Nền tảng du lịch nhiều người truy cập nhất Việt Nam</span>
            </div>
            
            {/* Navigation Menu */}
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/platforms/travel/start/hotels" className="text-gray-700 hover:text-pink-600 font-medium">
                Khách sạn
              </Link>
              <Link href="/platforms/travel/start/flights" className="text-gray-700 hover:text-pink-600 font-medium">
                Vé máy bay
              </Link>
              <Link href="/platforms/travel/start/tours" className="text-gray-700 hover:text-pink-600 font-medium">
                Tour
              </Link>
              <Link href="/platforms/travel/start/cars" className="text-gray-700 hover:text-pink-600 font-medium">
                Thuê xe
              </Link>
              <Link href="/platforms/travel/start/services" className="text-gray-700 hover:text-pink-600 font-medium">
                Dịch vụ
              </Link>
            </nav>
            
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">1900 2083</span>
              </div>
              <span className="text-sm text-gray-600">VND</span>
              <div className="h-6 w-6 bg-gray-200 rounded-full"></div>
              {mounted && (
                <>
                  {isAuthenticated && user ? (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-700">Xin chào, {user.firstName || 'User'}</span>
                      <Button variant="outline" className="border-pink-500 text-pink-500 hover:bg-pink-50 px-4 py-2 rounded text-sm">
                        Đơn hàng của tôi
                      </Button>
                    </div>
                  ) : (
                    <>
                      <Button 
                        onClick={login}
                        className="bg-pink-500 hover:bg-pink-600 text-white px-4 py-2 rounded text-sm"
                      >
                        Đăng nhập
                      </Button>
                      <Button variant="outline" className="border-pink-500 text-pink-500 hover:bg-pink-50 px-4 py-2 rounded text-sm">
                        Đăng ký
                      </Button>
                    </>
                  )}
                </>
              )}
              {!mounted && (
                <>
                  <Button className="bg-pink-500 hover:bg-pink-600 text-white px-4 py-2 rounded text-sm">
                    Đăng nhập
                  </Button>
                  <Button variant="outline" className="border-pink-500 text-pink-500 hover:bg-pink-50 px-4 py-2 rounded text-sm">
                    Đăng ký
                  </Button>
                </>
              )}
              <div className="h-6 w-6 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
        
        {/* Navigation Bar */}
        <div className="border-t border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center space-x-8 h-12">
              <a href="#" className="text-gray-700 hover:text-pink-500 font-medium text-sm">Khách sạn</a>
              <a href="#" className="text-gray-700 hover:text-pink-500 font-medium text-sm">Vé máy bay</a>
              <a href="#" className="text-gray-700 hover:text-pink-500 font-medium text-sm flex items-center">
                Top thương hiệu
                <span className="bg-red-500 text-white text-xs px-1 rounded ml-1">HOT</span>
              </a>
              <a href="#" className="text-gray-700 hover:text-pink-500 font-medium text-sm flex items-center">
                Biệt thự, Homestay
                <span className="bg-red-500 text-white text-xs px-1 rounded ml-1">PLUS</span>
              </a>
              <a href="#" className="text-gray-700 hover:text-pink-500 font-medium text-sm">Khách sạn tiết kiệm</a>
              <a href="#" className="text-gray-700 hover:text-pink-500 font-medium text-sm">Tour nước ngoài</a>
              <div className="flex-1"></div>
              <div className="text-gray-400">⋯</div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section with Background Image - Exact SeaGo.vn style */}
      <section className="relative h-96 bg-cover bg-center" style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80')"
      }}>
        <div className="absolute inset-0 bg-black bg-opacity-30"></div>
        
        {/* Search Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl p-6">
          <div className="max-w-6xl mx-auto">
            {/* Tab Navigation */}
            <div className="flex space-x-0 mb-6">
              <div className="bg-pink-500 text-white px-6 py-3 rounded-t-lg flex items-center space-x-2">
                <Building2 className="h-4 w-4" />
                <span className="font-medium">Khách sạn</span>
                <span className="bg-red-500 text-white text-xs px-1 rounded">-10%</span>
              </div>
              <div className="bg-gray-100 text-gray-600 px-6 py-3 flex items-center space-x-2">
                <Plane className="h-4 w-4" />
                <span className="font-medium">Vé máy bay</span>
                <span className="bg-red-500 text-white text-xs px-1 rounded">-50%</span>
              </div>
              <div className="bg-gray-100 text-gray-600 px-6 py-3 flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span className="font-medium">Tour nước ngoài</span>
                <span className="bg-red-500 text-white text-xs px-1 rounded">-15%</span>
              </div>
              <div className="bg-gray-100 text-gray-600 px-6 py-3 rounded-t-lg flex items-center space-x-2">
                <Building2 className="h-4 w-4" />
                <span className="font-medium">Biệt thự, Homestay</span>
              </div>
            </div>

            {/* Search Form */}
            <div className="grid grid-cols-4 gap-4">
              <div className="col-span-1">
                <label className="block text-sm text-gray-600 mb-1">Điểm đến</label>
                <Input
                  placeholder="Địa điểm, khách sạn trong nước hoặc quốc tế"
                  className="w-full border-gray-300"
                />
              </div>
              <div className="col-span-1">
                <label className="block text-sm text-gray-600 mb-1">Ngày đến</label>
                <Input
                  placeholder="Chọn ngày đi"
                  className="w-full border-gray-300"
                />
              </div>
              <div className="col-span-1">
                <label className="block text-sm text-gray-600 mb-1">Ngày về</label>
                <Input
                  placeholder="Chọn ngày về"
                  className="w-full border-gray-300"
                />
              </div>
              <div className="col-span-1">
                <label className="block text-sm text-gray-600 mb-1">Số phòng, số khách</label>
                <Input
                  placeholder="1 phòng, 2 người lớn, 0 trẻ em"
                  className="w-full border-gray-300"
                />
              </div>
            </div>
            
            <div className="flex justify-center mt-6">
              <Button className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-3 rounded-lg font-medium">
                <Search className="h-4 w-4 mr-2" />
                Tìm kiếm
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Promotional Banners - Exact SeaGo.vn style */}
      <section className="py-8 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Banner */}
            <div className="bg-gradient-to-r from-blue-400 to-purple-500 rounded-2xl p-8 text-white relative overflow-hidden">
              <div className="absolute right-0 top-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
              <h3 className="text-2xl font-bold mb-2">NÂNG VÀNG LÊN GIẢI THIẾT HẠ</h3>
              <h4 className="text-3xl font-bold mb-2">DEAL GIẢM 67%</h4>
              <p className="text-blue-100 mb-4">Thời gian có hạn: 01/07 - 31/07/2025</p>
              <div className="flex items-center space-x-2">
                <span className="bg-yellow-400 text-blue-900 px-3 py-1 rounded-full text-sm font-bold">HANGVANG01</span>
              </div>
            </div>

            {/* Right Banner */}
            <div className="bg-gradient-to-r from-pink-400 to-red-500 rounded-2xl p-8 text-white relative overflow-hidden">
              <div className="absolute right-0 bottom-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -mr-12 -mb-12"></div>
              <h3 className="text-lg font-bold mb-2">UU ĐÃI HAY</h3>
              <h4 className="text-2xl font-bold mb-2">BAY NỘI ĐỊA THEO NHÓM</h4>
              <div className="bg-white text-pink-600 px-3 py-1 rounded-full text-sm font-bold inline-block mb-2">
                Nhập mã NHOMT8
              </div>
              <p className="text-pink-100 text-sm">Giảm 300K cho đơn hàng từ 2 triệu đồng</p>
            </div>
          </div>
        </div>
      </section>

      {/* Flash Sale Section - Exact SeaGo.vn style */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <h2 className="text-3xl font-bold">
                <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">FLASH</span>
                <span className="text-gray-900 ml-2">SALE</span>
              </h2>
              <div className="text-sm text-gray-600">
                Chương trình sẽ kết thúc trong
                <div className="inline-flex items-center ml-2 space-x-1">
                  <span className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">10</span>
                  <span>:</span>
                  <span className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">15</span>
                  <span>:</span>
                  <span className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">30</span>
                </div>
              </div>
            </div>
            <div className="text-pink-500 font-medium cursor-pointer">Xem thêm</div>
          </div>

          {/* Hotel Cards Grid - Exact SeaGo.vn style */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Hotel Card 1 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                  alt="Hotel" 
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 bg-orange-500 text-white px-2 py-1 rounded text-sm font-bold">
                  -29%
                </div>
                <div className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                  ♡
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1">Centara Mirage Resort Mũi Né</h3>
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>Thành Phố Phan Thiết</span>
                </div>
                <div className="flex items-center mb-2">
                  <div className="flex items-center bg-pink-500 text-white px-2 py-1 rounded text-xs">
                    <span className="mr-1">★</span>
                    <span className="font-bold">9.2</span>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">Tuyệt vời (28 đánh giá)</span>
                </div>
                <p className="text-xs text-green-600 mb-2">Vừa được đặt 5 giờ trước</p>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-gray-400 line-through text-sm">2.901.906 ₫</span>
                    <div className="text-lg font-bold text-pink-500">3.472.509 ₫</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Hotel Card 2 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                  alt="Hotel" 
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 bg-orange-500 text-white px-2 py-1 rounded text-sm font-bold">
                  -22%
                </div>
                <div className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                  ♡
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1">Khách Sạn ibis Styles Vũng Tàu</h3>
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>Thành Phố Vũng Tàu</span>
                </div>
                <div className="flex items-center mb-2">
                  <div className="flex items-center bg-pink-500 text-white px-2 py-1 rounded text-xs">
                    <span className="mr-1">★</span>
                    <span className="font-bold">9.4</span>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">Tuyệt vời (9 đánh giá)</span>
                </div>
                <p className="text-xs text-green-600 mb-2">Vừa được đặt 9 giờ trước</p>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-gray-400 line-through text-sm">5.841.346 ₫</span>
                    <div className="text-lg font-bold text-pink-500">4.630.463 ₫</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Hotel Card 3 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                  alt="Hotel" 
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 bg-orange-500 text-white px-2 py-1 rounded text-sm font-bold">
                  -30%
                </div>
                <div className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                  ♡
                </div>
                <div className="absolute bottom-3 right-3 bg-pink-500 rounded-full p-1">
                  <span className="text-white text-xs">💎</span>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1">Khách sạn Mường Thanh Luxury Sài Gòn</h3>
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>Quận Phú Nhuận</span>
                </div>
                <div className="flex items-center mb-2">
                  <div className="flex items-center bg-pink-500 text-white px-2 py-1 rounded text-xs">
                    <span className="mr-1">★</span>
                    <span className="font-bold">9.2</span>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">Tuyệt vời (62 đánh giá)</span>
                </div>
                <p className="text-xs text-green-600 mb-2">Vừa được đặt một giờ trước</p>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-gray-400 line-through text-sm">2.217.146 ₫</span>
                    <div className="text-lg font-bold text-pink-500">1.550.227 ₫</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Hotel Card 4 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                  alt="Hotel" 
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 bg-orange-500 text-white px-2 py-1 rounded text-sm font-bold">
                  -30%
                </div>
                <div className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                  ♡
                </div>
                <div className="absolute bottom-3 right-3 bg-pink-500 rounded-full p-1">
                  <span className="text-white text-xs">💎</span>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1">Khách sạn Golf Valley Đà Lạt</h3>
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>Thành Phố Đà Lạt</span>
                </div>
                <div className="flex items-center mb-2">
                  <div className="flex items-center bg-pink-500 text-white px-2 py-1 rounded text-xs">
                    <span className="mr-1">★</span>
                    <span className="font-bold">9.2</span>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">Tuyệt vời (26 đánh giá)</span>
                </div>
                <p className="text-xs text-green-600 mb-2">Vừa được đặt 13 giờ trước</p>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-gray-400 line-through text-sm">2.697.290 ₫</span>
                    <div className="text-lg font-bold text-pink-500">1.753.096 ₫</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-8">
            <Button variant="outline" className="border-pink-500 text-pink-500 hover:bg-pink-50 px-8 py-2">
              Xem thêm
            </Button>
          </div>
        </div>
      </section>

      {/* Quick Service Icons - SeaGo.vn style */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center group cursor-pointer">
              <div className="bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl p-6 mb-3 group-hover:shadow-lg transition-all">
                <Building2 className="h-12 w-12 text-orange-500 mx-auto" />
              </div>
              <h4 className="font-semibold text-gray-900">Khách sạn</h4>
              <p className="text-sm text-gray-500">Hơn 100,000 khách sạn</p>
            </div>
            <div className="text-center group cursor-pointer">
              <div className="bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-6 mb-3 group-hover:shadow-lg transition-all">
                <Plane className="h-12 w-12 text-blue-500 mx-auto" />
              </div>
              <h4 className="font-semibold text-gray-900">Vé máy bay</h4>
              <p className="text-sm text-gray-500">Giá tốt nhất thị trường</p>
            </div>
            <div className="text-center group cursor-pointer">
              <div className="bg-gradient-to-br from-green-100 to-teal-100 rounded-2xl p-6 mb-3 group-hover:shadow-lg transition-all">
                <MapPin className="h-12 w-12 text-green-500 mx-auto" />
              </div>
              <h4 className="font-semibold text-gray-900">Tour nước ngoài</h4>
              <p className="text-sm text-gray-500">Trải nghiệm đẳng cấp</p>
            </div>
            <div className="text-center group cursor-pointer">
              <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-6 mb-3 group-hover:shadow-lg transition-all">
                <Car className="h-12 w-12 text-purple-500 mx-auto" />
              </div>
              <h4 className="font-semibold text-gray-900">Biệt thự, Homestay</h4>
              <p className="text-sm text-gray-500">Không gian riêng tư</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Services - SeaGo.vn style */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-3xl font-bold text-gray-900">Dịch vụ nổi bật</h3>
            <div className="flex items-center gap-2 text-orange-500">
              <span className="text-sm font-medium">Xem thêm</span>
              <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-500 text-xs">→</span>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service) => (
              <Card key={service.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 border-0 shadow-md group">
                <CardContent className="p-0">
                  <div className="relative overflow-hidden">
                    <div className="h-48 bg-gradient-to-br from-orange-100 to-red-100 flex items-center justify-center">
                      <div className="text-orange-500">
                        {service.type === 'hotel' && <Building2 className="h-16 w-16" />}
                        {service.type === 'flight' && <Plane className="h-16 w-16" />}
                        {service.type === 'tour' && <MapPin className="h-16 w-16" />}
                        {service.type === 'car' && <Car className="h-16 w-16" />}
                      </div>
                    </div>
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-orange-500 hover:bg-orange-600 text-white border-0 font-medium">
                        {service.type === 'hotel' ? 'Khách sạn' : 
                         service.type === 'flight' ? 'Vé máy bay' : 
                         service.type === 'tour' ? 'Tour' : 'Xe thuê'}
                      </Badge>
                    </div>
                    <div className="absolute top-3 right-3 bg-white rounded-full p-2 shadow-sm">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm font-medium text-gray-700">{service.rating}</span>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-semibold mb-2 text-gray-900 group-hover:text-orange-600 transition-colors">
                      {service.name}
                    </h4>
                    <p className="text-gray-600 mb-4 text-sm leading-relaxed">{service.description}</p>
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold text-orange-600">{service.price.toLocaleString()}₫</span>
                        <span className="text-gray-500 ml-1 text-sm">/đêm</span>
                      </div>
                      <Button 
                        onClick={() => handleBooking(service.id)}
                        className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-medium px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all"
                      >
                        Đặt ngay
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Flight Deals Section - Exact SeaGo.vn style */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">
              Chuyến bay giá tốt từ <span className="text-pink-500">Hồ Chí Minh</span>
            </h2>
            <div className="text-pink-500 font-medium cursor-pointer">Khám phá ngay</div>
          </div>
          
          <p className="text-gray-600 mb-8">Những chuyến bay giá tốt nhất trong tháng khởi hành từ Hồ Chí Minh</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Flight Card 1 */}
            <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="text-red-500 font-bold text-lg">Vietjet Air</div>
              </div>
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Tân Sơn Nhất</span>
                <Plane className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Nội Bài</span>
              </div>
              <div className="text-sm text-gray-500 mb-4">Khởi hành: 20/07/2025</div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-gray-400 line-through text-sm">1.060.000 ₫</div>
                  <div className="font-bold text-lg">1.010.000 ₫</div>
                  <div className="text-xs text-gray-500">Giá sau thuế: 1.730.200 ₫</div>
                </div>
              </div>
            </div>

            {/* Flight Card 2 */}
            <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="text-yellow-500 font-bold text-lg">Vietnam Airlines</div>
              </div>
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Tân Sơn Nhất</span>
                <Plane className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Cam Ranh</span>
              </div>
              <div className="text-sm text-gray-500 mb-4">Khởi hành: 21/07/2025</div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-gray-400 line-through text-sm">639.000 ₫</div>
                  <div className="font-bold text-lg">489.000 ₫</div>
                  <div className="text-xs text-gray-500">Giá sau thuế: 1.154.000 ₫</div>
                </div>
              </div>
            </div>

            {/* Flight Card 3 */}
            <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="text-red-500 font-bold text-lg">Vietjet Air</div>
              </div>
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Tân Sơn Nhất</span>
                <Plane className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Đà Nẵng</span>
              </div>
              <div className="text-sm text-gray-500 mb-4">Khởi hành: 21/07/2025</div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-gray-400 line-through text-sm">1.140.000 ₫</div>
                  <div className="font-bold text-lg">390.000 ₫</div>
                  <div className="text-xs text-gray-500">Giá sau thuế: 1.060.600 ₫</div>
                </div>
              </div>
            </div>

            {/* Flight Card 4 */}
            <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="text-red-500 font-bold text-lg">Vietjet Air</div>
              </div>
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Tân Sơn Nhất</span>
                <Plane className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Phú Quốc</span>
              </div>
              <div className="text-sm text-gray-500 mb-4">Khởi hành: 20/07/2025</div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-gray-400 line-through text-sm">1.140.000 ₫</div>
                  <div className="font-bold text-lg">390.000 ₫</div>
                  <div className="text-xs text-gray-500">Giá sau thuế: 1.060.600 ₫</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Tại sao chọn MyTour?
            </h2>
            <p className="text-lg text-gray-600">
              Chúng tôi cam kết mang đến trải nghiệm du lịch tuyệt vời nhất
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-pink-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Đảm bảo giá tốt nhất</h3>
              <p className="text-gray-600">Cam kết hoàn tiền nếu tìm thấy giá rẻ hơn</p>
            </div>
            
            <div className="text-center">
              <div className="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <CreditCard className="h-8 w-8 text-pink-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Thanh toán an toàn</h3>
              <p className="text-gray-600">Bảo mật thông tin với công nghệ SSL</p>
            </div>
            
            <div className="text-center">
              <div className="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-pink-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Hỗ trợ 24/7</h3>
              <p className="text-gray-600">Đội ngũ tư vấn chuyên nghiệp luôn sẵn sàng</p>
            </div>
            
            <div className="text-center">
              <div className="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="h-8 w-8 text-pink-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Đa dạng lựa chọn</h3>
              <p className="text-gray-600">Hàng triệu khách sạn và chuyến bay</p>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Destinations */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Điểm đến phổ biến
            </h2>
            <p className="text-lg text-gray-600">
              Khám phá những địa điểm du lịch được yêu thích nhất
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {destinations.map((destination) => (
              <Card key={destination.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="aspect-square bg-gradient-to-br from-pink-400 to-purple-500 rounded-lg mb-3 flex items-center justify-center">
                    <span className="text-white text-2xl font-bold">
                      {destination.name.charAt(0)}
                    </span>
                  </div>
                  <h3 className="font-semibold text-center">{destination.name}</h3>
                  <p className="text-sm text-gray-600 text-center">{destination.country}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Promotional Banners */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ưu đãi khách sạn</h3>
              <p className="mb-6">Giảm đến 50% cho hàng nghìn khách sạn trên toàn thế giới</p>
              <Link href="/platforms/travel/start/hotels">
                <Button className="bg-white text-blue-600 hover:bg-gray-100">
                  Xem ngay
                </Button>
              </Link>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Tour du lịch</h3>
              <p className="mb-6">Khám phá các tour du lịch hấp dẫn với giá ưu đãi</p>
              <Link href="/platforms/travel/start/tours">
                <Button className="bg-white text-green-600 hover:bg-gray-100">
                  Khám phá
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Dịch vụ của chúng tôi
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Link href="/platforms/travel/start/hotels" className="group">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                    <Building2 className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Khách sạn</h3>
                  <p className="text-gray-600">Đặt phòng khách sạn với giá tốt nhất</p>
                </CardContent>
              </Card>
            </Link>
            
            <Link href="/platforms/travel/start/flights" className="group">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                    <Plane className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Vé máy bay</h3>
                  <p className="text-gray-600">Tìm và đặt vé máy bay giá rẻ</p>
                </CardContent>
              </Card>
            </Link>
            
            <Link href="/platforms/travel/start/tours" className="group">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                    <Camera className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Tour du lịch</h3>
                  <p className="text-gray-600">Khám phá các tour hấp dẫn</p>
                </CardContent>
              </Card>
            </Link>
            
            <Link href="/platforms/travel/start/cars" className="group">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                    <Car className="h-8 w-8 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Thuê xe</h3>
                  <p className="text-gray-600">Thuê xe du lịch tiện lợi</p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </section>

      {/* Hotel Search by City Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Khách sạn giá sốc chỉ có trên MyTour</h2>
          <p className="text-gray-600 mb-8">
            Tiết kiệm chi phí với các khách sạn hấp dẫn chiến lược cùng MyTour, cam kết giá tốt nhất 
            và chất lượng dịch vụ tốt nhất dành cho bạn.
          </p>
          
          <div className="flex flex-wrap gap-2 mb-6">
            {[
              'Hồ Chí Minh', 'Vũng Tàu', 'Đà Lạt', 'Phan Thiết', 'Quy Nhon', 'Phú Quốc', 
              'Đà Nẵng', 'Nha Trang', 'Hà Nội', 'Sa Pa'
            ].map((city) => (
              <Button
                key={city}
                variant="outline"
                className="border-gray-300 text-gray-600 hover:border-pink-500 hover:text-pink-500 rounded-full px-4 py-2 text-sm"
              >
                {city}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Footer - MyTour style */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-pink-400 mb-4">MyTour</div>
              <p className="text-gray-400 leading-relaxed mb-4">
                Công ty cổ phần du lịch Việt Nam VNTravel
              </p>
              <div className="space-y-2 text-sm text-gray-400">
                <p>Tổng đài chăm sóc: <span className="text-pink-400 font-medium">1900 2083</span></p>
                <p>Email: <span className="text-pink-400"><EMAIL></span></p>
                <p>Văn phòng Hà Nội: Tầng 20, Tòa A, HUD Tower, 37 Lê Văn Lương, Quận Thanh Xuân, Hà Nội, Việt Nam</p>
                <p>Văn phòng HCM: Tầng 6, 67 Lý Chính Thắng, Phường Võ Thị Sáu, Quận 3, HCM</p>
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4 text-pink-400">Chính sách & Quy định</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><a href="#" className="hover:text-pink-400 transition-colors">Điều khoản và điều kiện</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Quy định về thanh toán</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Chính sách bảo mật thông tin</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Quy chế hoạt động</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Chương trình khách hàng thân thiết</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Chương trình đánh giá trải nghiệm khách sạn</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4 text-pink-400">Khách hàng và đối tác</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><a href="#" className="hover:text-pink-400 transition-colors">Giới thiệu nhận quà</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Hợp tác với chúng tôi</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Đăng nhập HMS</a></li>
                <li><a href="#" className="hover:text-pink-400 transition-colors">Tuyển dụng</a></li>
              </ul>
            </div>
            <div>
              <div className="mb-6">
                <div className="bg-gradient-to-r from-pink-500 to-red-500 text-white px-4 py-2 rounded-lg text-sm font-medium inline-block mb-4">
                  🎉 Yên tâm du lịch quốc tế
                </div>
                <div className="space-y-2">
                  <a href="#" className="block text-pink-400 hover:text-pink-300 text-sm">Mã giảm giá và ưu đãi</a>
                  <a href="#" className="block text-pink-400 hover:text-pink-300 text-sm">MyTour for Business Hoàn tiền tới 5%</a>
                </div>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8">
            <div className="text-center text-gray-400 text-sm">
              <p className="mb-2">
                MyTour là thành viên của VNTravel Group - Một trong những tập đoàn đứng đầu Đông Nam Á về du lịch trực tuyến và các dịch vụ liên quan.
              </p>
              <p>
                Copyright © 2020 - CÔNG TY CỔ PHẦN DU LỊCH VIỆT NAM VNTRAVEL - Đăng ký kinh doanh số 0108886908 - do Sở Kế hoạch và Đầu tư thành phố Hà Nội cấp lần đầu ngày 04 tháng 09 năm 2019
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}