'use client';

import React, { useState, useEffect } from 'react';
import { PageLayout, SidebarLayout } from '../../components/layout';
import { HotelCard } from '../../components/cards';
import { SearchBar, LocationSearch, DatePicker, GuestSelector, FilterPanel } from '../../components/search';
import { LoadingSpinner, EmptyState } from '../../components/ui';
import { useSearch, useFavorites } from '../../hooks';
import { api } from '../../services/api';
import type { Hotel, SearchFilters } from '../../types';
import { MapPin } from 'lucide-react';

export default function HotelsPage() {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [location, setLocation] = useState('');
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [adults, setAdults] = useState(2);
  const [children, setChildren] = useState(0);

  const [searchQuery, setSearchQuery] = useState('');
  const { isFavorite, toggleFavorite } = useFavorites();

  // Mock data - replace with actual API call
  const mockHotels: Hotel[] = [
    {
      id: '1',
      name: 'Khách sạn Lotte Hà Nội',
      location: {
        address: 'Ba Đình, Hà Nội',
        city: 'Hà Nội',
        country: 'Việt Nam',
        coordinates: { lat: 21.0285, lng: 105.8542 }
      },
      rating: 4.8,
      reviewCount: 1250,
      pricePerNight: 2500000,
      originalPrice: 3000000,
      images: ['/images/hotels/lotte-hanoi.jpg'],
      amenities: ['wifi', 'parking', 'breakfast', 'pool', 'gym'],
      description: 'Khách sạn 5 sao sang trọng tại trung tâm Hà Nội',
      roomTypes: [],
      policies: {
        checkIn: '14:00',
        checkOut: '12:00',
        cancellation: 'Miễn phí hủy trong 24h'
      },
      contact: {
        phone: '024-3333-1000',
        email: '<EMAIL>'
      },
      discount: 17
    },
    {
      id: '2',
      name: 'InterContinental Saigon',
      location: {
        address: 'Quận 1, TP.HCM',
        city: 'TP.HCM',
        country: 'Việt Nam',
        coordinates: { lat: 10.7769, lng: 106.7009 }
      },
      rating: 4.7,
      reviewCount: 980,
      pricePerNight: 3200000,
      images: ['/images/hotels/intercontinental-saigon.jpg'],
      amenities: ['wifi', 'parking', 'breakfast', 'spa', 'pool'],
      description: 'Khách sạn cao cấp với view sông Sài Gòn tuyệt đẹp',
      roomTypes: [],
      policies: {
        checkIn: '15:00',
        checkOut: '12:00',
        cancellation: 'Miễn phí hủy trong 48h'
      },
      contact: {
        phone: '028-3520-9999',
        email: '<EMAIL>'
      }
    },
    {
      id: '3',
      name: 'Vinpearl Resort Phú Quốc',
      location: {
        address: 'Phú Quốc, Kiên Giang',
        city: 'Phú Quốc',
        country: 'Việt Nam',
        coordinates: { lat: 10.2899, lng: 103.9840 }
      },
      rating: 4.6,
      reviewCount: 2100,
      pricePerNight: 1800000,
      originalPrice: 2200000,
      images: ['/images/hotels/vinpearl-phuquoc.jpg'],
      amenities: ['wifi', 'beach', 'pool', 'spa', 'restaurant'],
      description: 'Resort biển với bãi cát trắng và dịch vụ đẳng cấp',
      roomTypes: [],
      policies: {
        checkIn: '14:00',
        checkOut: '12:00',
        cancellation: 'Miễn phí hủy trong 72h'
      },
      contact: {
        phone: '0297-3519-999',
        email: '<EMAIL>'
      },
      discount: 18
    }
  ];

  useEffect(() => {
    loadHotels();
  }, [filters, searchQuery]);

  const loadHotels = async () => {
    setLoading(true);
    try {
      // Replace with actual API call
      // const result = await api.hotels.search({ ...filters, location, checkIn, checkOut, guests: adults + children });
      
      // Simulate API call
      setTimeout(() => {
        let filtered = mockHotels;
        
        if (searchQuery) {
          filtered = filtered.filter(hotel => 
            hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            hotel.location.address.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }
        
        if (location) {
          filtered = filtered.filter(hotel => 
            hotel.location.address.toLowerCase().includes(location.toLowerCase())
          );
        }
        
        if (filters.priceRange) {
          filtered = filtered.filter(hotel => 
            hotel.pricePerNight >= filters.priceRange!.min && 
            hotel.pricePerNight <= filters.priceRange!.max
          );
        }
        
        if (filters.rating) {
          filtered = filtered.filter(hotel => hotel.rating >= filters.rating!);
        }
        
        setHotels(filtered);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading hotels:', error);
      setLoading(false);
    }
  };

  const handleSearch = () => {
    const searchFilters: SearchFilters = {
      location,
      checkIn,
      checkOut,
      guests: adults + children,
      ...filters
    };
    setFilters(searchFilters);
  };

  const handleFavorite = (hotelId: string) => {
    toggleFavorite(hotelId);
  };

  const breadcrumbs = [
    { label: 'Trang chủ', href: '/platforms/travel/start' },
    { label: 'Khách sạn' }
  ];

  const searchSection = (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <LocationSearch
          value={location}
          onChange={setLocation}
          placeholder="Điểm đến"
        />
        
        <DatePicker
          value={checkIn}
          onChange={setCheckIn}
          placeholder="Ngày nhận phòng"
        />
        
        <DatePicker
          value={checkOut}
          onChange={setCheckOut}
          placeholder="Ngày trả phòng"
          minDate={checkIn}
        />
        
        <GuestSelector
          adults={adults}
          children={children}
          onAdultsChange={setAdults}
          onChildrenChange={setChildren}
        />
        
        <button
          onClick={handleSearch}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          Tìm kiếm
        </button>
      </div>
    </div>
  );

  const sidebar = (
    <div className="space-y-6">
      <SearchBar
        placeholder="Tìm kiếm khách sạn..."
        onSearch={setSearchQuery}
      />
      
      <FilterPanel
        filters={filters}
        onFiltersChange={setFilters}
      />
    </div>
  );

  const content = (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          {loading ? 'Đang tìm kiếm...' : `Tìm thấy ${hotels.length} khách sạn`}
        </h2>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
              <div className="h-48 bg-gray-200"></div>
              <div className="p-4 space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      ) : hotels.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {hotels.map((hotel) => (
            <HotelCard
              key={hotel.id}
              hotel={hotel}
              onFavorite={handleFavorite}
            />
          ))}
        </div>
      ) : (
        <EmptyState
          title="Không tìm thấy khách sạn"
          description="Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm"
          icon={<MapPin className="w-16 h-16" />}
        />
      )}
    </div>
  );

  return (
    <PageLayout
      title="Khách sạn"
      breadcrumbs={breadcrumbs}
    >
      {searchSection}
      
      <SidebarLayout sidebar={sidebar}>
        {content}
      </SidebarLayout>
    </PageLayout>
  );
}