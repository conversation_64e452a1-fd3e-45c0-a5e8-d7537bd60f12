import { API_CONFIG } from '../constants';
import type { 
  Hotel, 
  Flight, 
  Tour, 
  CarRental, 
  TravelService, 
  SearchFilters, 
  SearchResult, 
  Booking, 
  BookingRequest 
} from '../types';

// Base API client
class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`API request failed: ${error.message}`);
      }
      throw new Error('Unknown API error');
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

const apiClient = new ApiClient();

// Hotel Services
export const hotelService = {
  search: async (filters: SearchFilters): Promise<SearchResult<Hotel>> => {
    const params = new URLSearchParams();
    
    if (filters.location) params.append('location', filters.location);
    if (filters.checkIn) params.append('checkIn', filters.checkIn);
    if (filters.checkOut) params.append('checkOut', filters.checkOut);
    if (filters.guests) params.append('guests', filters.guests.toString());
    if (filters.priceRange) {
      params.append('minPrice', filters.priceRange.min.toString());
      params.append('maxPrice', filters.priceRange.max.toString());
    }
    if (filters.rating) params.append('rating', filters.rating.toString());
    
    return apiClient.get<SearchResult<Hotel>>(`/hotels/search?${params}`);
  },

  getById: async (id: string): Promise<Hotel> => {
    return apiClient.get<Hotel>(`/hotels/${id}`);
  },

  getFeatured: async (): Promise<Hotel[]> => {
    return apiClient.get<Hotel[]>('/hotels/featured');
  },

  getByLocation: async (location: string): Promise<Hotel[]> => {
    return apiClient.get<Hotel[]>(`/hotels/location/${location}`);
  },

  checkAvailability: async (hotelId: string, checkIn: string, checkOut: string): Promise<boolean> => {
    return apiClient.post<boolean>(`/hotels/${hotelId}/availability`, {
      checkIn,
      checkOut
    });
  }
};

// Flight Services
export const flightService = {
  search: async (filters: SearchFilters): Promise<SearchResult<Flight>> => {
    const params = new URLSearchParams();
    
    if (filters.origin) params.append('origin', filters.origin);
    if (filters.destination) params.append('destination', filters.destination);
    if (filters.departureDate) params.append('departureDate', filters.departureDate);
    if (filters.returnDate) params.append('returnDate', filters.returnDate);
    if (filters.passengers) params.append('passengers', filters.passengers.toString());
    if (filters.class) params.append('class', filters.class);
    
    return apiClient.get<SearchResult<Flight>>(`/flights/search?${params}`);
  },

  getById: async (id: string): Promise<Flight> => {
    return apiClient.get<Flight>(`/flights/${id}`);
  },

  getPopularRoutes: async (): Promise<Flight[]> => {
    return apiClient.get<Flight[]>('/flights/popular-routes');
  },

  getAirports: async (query?: string): Promise<any[]> => {
    const params = query ? `?q=${encodeURIComponent(query)}` : '';
    return apiClient.get<any[]>(`/flights/airports${params}`);
  }
};

// Tour Services
export const tourService = {
  search: async (filters: SearchFilters): Promise<SearchResult<Tour>> => {
    const params = new URLSearchParams();
    
    if (filters.destination) params.append('destination', filters.destination);
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.duration) params.append('duration', filters.duration.toString());
    if (filters.groupSize) params.append('groupSize', filters.groupSize.toString());
    if (filters.category) params.append('category', filters.category);
    if (filters.priceRange) {
      params.append('minPrice', filters.priceRange.min.toString());
      params.append('maxPrice', filters.priceRange.max.toString());
    }
    
    return apiClient.get<SearchResult<Tour>>(`/tours/search?${params}`);
  },

  getById: async (id: string): Promise<Tour> => {
    return apiClient.get<Tour>(`/tours/${id}`);
  },

  getFeatured: async (): Promise<Tour[]> => {
    return apiClient.get<Tour[]>('/tours/featured');
  },

  getByCategory: async (category: string): Promise<Tour[]> => {
    return apiClient.get<Tour[]>(`/tours/category/${category}`);
  },

  getPopular: async (): Promise<Tour[]> => {
    return apiClient.get<Tour[]>('/tours/popular');
  }
};

// Car Rental Services
export const carService = {
  search: async (filters: SearchFilters): Promise<SearchResult<CarRental>> => {
    const params = new URLSearchParams();
    
    if (filters.location) params.append('location', filters.location);
    if (filters.pickupDate) params.append('pickupDate', filters.pickupDate);
    if (filters.returnDate) params.append('returnDate', filters.returnDate);
    if (filters.carType) params.append('carType', filters.carType);
    if (filters.priceRange) {
      params.append('minPrice', filters.priceRange.min.toString());
      params.append('maxPrice', filters.priceRange.max.toString());
    }
    
    return apiClient.get<SearchResult<CarRental>>(`/cars/search?${params}`);
  },

  getById: async (id: string): Promise<CarRental> => {
    return apiClient.get<CarRental>(`/cars/${id}`);
  },

  getByLocation: async (location: string): Promise<CarRental[]> => {
    return apiClient.get<CarRental[]>(`/cars/location/${location}`);
  },

  getPopular: async (): Promise<CarRental[]> => {
    return apiClient.get<CarRental[]>('/cars/popular');
  }
};

// Travel Services
export const travelService = {
  search: async (filters: SearchFilters): Promise<SearchResult<TravelService>> => {
    const params = new URLSearchParams();
    
    if (filters.location) params.append('location', filters.location);
    if (filters.serviceType) params.append('serviceType', filters.serviceType);
    if (filters.date) params.append('date', filters.date);
    if (filters.priceRange) {
      params.append('minPrice', filters.priceRange.min.toString());
      params.append('maxPrice', filters.priceRange.max.toString());
    }
    
    return apiClient.get<SearchResult<TravelService>>(`/services/search?${params}`);
  },

  getById: async (id: string): Promise<TravelService> => {
    return apiClient.get<TravelService>(`/services/${id}`);
  },

  getByCategory: async (category: string): Promise<TravelService[]> => {
    return apiClient.get<TravelService[]>(`/services/category/${category}`);
  },

  getFeatured: async (): Promise<TravelService[]> => {
    return apiClient.get<TravelService[]>('/services/featured');
  }
};

// Booking Services
export const bookingService = {
  create: async (bookingData: BookingRequest): Promise<Booking> => {
    return apiClient.post<Booking>('/bookings', bookingData);
  },

  getById: async (id: string): Promise<Booking> => {
    return apiClient.get<Booking>(`/bookings/${id}`);
  },

  getUserBookings: async (userId: string): Promise<Booking[]> => {
    return apiClient.get<Booking[]>(`/bookings/user/${userId}`);
  },

  cancel: async (id: string): Promise<void> => {
    return apiClient.delete<void>(`/bookings/${id}`);
  },

  confirm: async (id: string): Promise<Booking> => {
    return apiClient.put<Booking>(`/bookings/${id}/confirm`, {});
  },

  updatePayment: async (id: string, paymentData: any): Promise<Booking> => {
    return apiClient.put<Booking>(`/bookings/${id}/payment`, paymentData);
  }
};

// User Services
export const userService = {
  getProfile: async (userId: string): Promise<any> => {
    return apiClient.get<any>(`/users/${userId}`);
  },

  updateProfile: async (userId: string, profileData: any): Promise<any> => {
    return apiClient.put<any>(`/users/${userId}`, profileData);
  },

  getFavorites: async (userId: string): Promise<any[]> => {
    return apiClient.get<any[]>(`/users/${userId}/favorites`);
  },

  addFavorite: async (userId: string, itemId: string, itemType: string): Promise<void> => {
    return apiClient.post<void>(`/users/${userId}/favorites`, { itemId, itemType });
  },

  removeFavorite: async (userId: string, itemId: string): Promise<void> => {
    return apiClient.delete<void>(`/users/${userId}/favorites/${itemId}`);
  }
};

// Location Services
export const locationService = {
  searchDestinations: async (query: string): Promise<any[]> => {
    return apiClient.get<any[]>(`/locations/search?q=${encodeURIComponent(query)}`);
  },

  getPopularDestinations: async (): Promise<any[]> => {
    return apiClient.get<any[]>('/locations/popular');
  },

  getDestinationDetails: async (id: string): Promise<any> => {
    return apiClient.get<any>(`/locations/${id}`);
  },

  getNearbyAttractions: async (lat: number, lng: number, radius: number = 10): Promise<any[]> => {
    return apiClient.get<any[]>(`/locations/nearby?lat=${lat}&lng=${lng}&radius=${radius}`);
  }
};

// Review Services
export const reviewService = {
  getByItem: async (itemId: string, itemType: string): Promise<any[]> => {
    return apiClient.get<any[]>(`/reviews/${itemType}/${itemId}`);
  },

  create: async (reviewData: any): Promise<any> => {
    return apiClient.post<any>('/reviews', reviewData);
  },

  update: async (reviewId: string, reviewData: any): Promise<any> => {
    return apiClient.put<any>(`/reviews/${reviewId}`, reviewData);
  },

  delete: async (reviewId: string): Promise<void> => {
    return apiClient.delete<void>(`/reviews/${reviewId}`);
  }
};

// Export all services
export const api = {
  hotels: hotelService,
  flights: flightService,
  tours: tourService,
  cars: carService,
  services: travelService,
  bookings: bookingService,
  users: userService,
  locations: locationService,
  reviews: reviewService
};