'use client';

import { useState, useEffect } from 'react';
import { Globe, RefreshCw, Plus, Check } from 'lucide-react';
import TravelServicesList from '@/components/platforms/travel/planner/TravelServicesList';
import { TravelService } from '@/lib/platforms/travel/planner/services/types';

export default function TravelServicesPage() {
  const [services, setServices] = useState<TravelService[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  
  // Fetch services on component mount
  useEffect(() => {
    fetchServices();
  }, []);
  
  // Fetch all services
  const fetchServices = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/platforms/travel/planner/services');
      if (!response.ok) {
        throw new Error('Failed to fetch services');
      }
      
      const data = await response.json();
      setServices(data);
    } catch (error) {
      console.error('Error fetching services:', error);
      // In a real app, show an error notification to the user
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle service selection
  const handleServiceSelect = (service: TravelService) => {
    setSelectedServices(prev => {
      if (prev.includes(service.id)) {
        return prev.filter(id => id !== service.id);
      } else {
        return [...prev, service.id];
      }
    });
  };
  
  // Create a trip plan with selected services
  const createTripPlan = async () => {
    if (selectedServices.length === 0) {
      alert('Please select at least one service');
      return;
    }
    
    // In a real app, this would redirect to a trip planning page
    // with the selected services pre-selected
    alert(`Creating trip plan with ${selectedServices.length} selected services`);
  };
  
  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <Globe className="mr-2 h-8 w-8 text-blue-600" />
          Travel Services Repository
        </h1>
        
        <div className="flex space-x-3">
          <button
            className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            onClick={fetchServices}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          
          <button
            className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            onClick={() => {/* In a real app, this would open a modal to add a new service */}}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Service
          </button>
          
          <button
            className="flex items-center px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
            onClick={createTripPlan}
            disabled={selectedServices.length === 0}
          >
            <Check className="mr-2 h-4 w-4" />
            Use Selected ({selectedServices.length})
          </button>
        </div>
      </div>
      
      <p className="text-gray-600 mb-6">
        Browse and manage travel services that can be used when building trip plans. Select services to include them in a new trip plan.
      </p>
      
      {isLoading ? (
        <div className="h-64 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <TravelServicesList 
          services={services}
          onServiceSelect={handleServiceSelect}
          selectedServiceIds={selectedServices}
        />
      )}
    </div>
  );
}