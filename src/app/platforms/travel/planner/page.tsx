'use client';

import { useState, useEffect } from 'react';
import { 
  Calendar,
  DollarSign,
  Globe,
  Layers,
  Map,
  PlaneTakeoff,
  Timer,
  Users,
  Zap,
  Database,
  Settings,
  User
} from 'lucide-react';
import Link from 'next/link';
import TripOptionCard from '@/components/platforms/travel/planner/TripOptionCard';
import TripComparisonModal from '@/components/platforms/travel/planner/TripComparisonModal';
import TripTreeView from '@/components/platforms/travel/planner/TripTreeView';
import UserPreferencesModal from '@/components/platforms/travel/planner/UserPreferencesModal';
import AIRecommendations from '@/components/platforms/travel/planner/AIRecommendations';
import { TripPlan, UserPreferences } from '@/types/travel-planner';
import { generateTripOptions } from '@/lib/platforms/travel/planner/trip-generator';

export default function TravelPlannerPage() {
  // Form state
  const [destination, setDestination] = useState('');
  const [duration, setDuration] = useState(7); // days
  const [budget, setBudget] = useState(2000);
  const [groupSize, setGroupSize] = useState(2);
  const [holdDuration] = useState(30); // minutes
  const [naturalLanguageInput, setNaturalLanguageInput] = useState('');

  // User state (in a real app, this would come from authentication)
  const [currentUserId] = useState('user-1');
  
  // Trip planning state
  const [isLoading, setIsLoading] = useState(false);
  const [tripPlans, setTripPlans] = useState<TripPlan[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [showComparisonModal, setShowComparisonModal] = useState(false);
  const [comparisonPlans, setComparisonPlans] = useState<TripPlan[]>([]);
  const [holdExpiryTime, setHoldExpiryTime] = useState<Date | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [servicesAvailable, setServicesAvailable] = useState<boolean>(false);
  const [showPreferencesModal, setShowPreferencesModal] = useState(false);
  const [userPreferences, setUserPreferences] = useState<UserPreferences | null>(null);
  const [aiRecommendations, setAiRecommendations] = useState<any[]>([]);
  const [aiInsights, setAiInsights] = useState<any>(null);

  // Check if travel services are available
  useEffect(() => {
    const checkServices = async () => {
      try {
        const response = await fetch('/api/platforms/travel/planner/services');
        if (response.ok) {
          const data = await response.json();
          setServicesAvailable(data.length > 0);
        }
      } catch (error) {
        console.error('Error checking travel services:', error);
      }
    };
    
    checkServices();
  }, []);

  // Handle natural language processing
  const handleNaturalLanguageSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Call AI service to parse natural language input
      const response = await fetch('/api/platforms/travel/planner/ai/parse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: naturalLanguageInput,
          userId: currentUserId
        })
      });

      if (response.ok) {
        const data = await response.json();
        const { criteria, recommendations, insights } = data;

        // Update form fields with parsed criteria
        if (criteria.destination) setDestination(criteria.destination);
        if (criteria.duration) setDuration(criteria.duration);
        if (criteria.budget) setBudget(criteria.budget);
        if (criteria.groupSize) setGroupSize(criteria.groupSize);

        // Store AI recommendations and insights
        setAiRecommendations(recommendations || []);
        setAiInsights(insights);

        // Generate trip options with the parsed criteria
        handleGenerateOptions();
      } else {
        throw new Error('Failed to parse natural language input');
      }
    } catch (error) {
      console.error('Error processing natural language input:', error);
      alert('Failed to process your request. Please try again or use the manual form.');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate trip options
  const handleGenerateOptions = () => {
    setIsLoading(true);
    
    try {
      // In a real implementation, this would call an API to generate options
      // For now, we'll use a mock function
      const options = generateTripOptions(destination, duration, budget, groupSize);
      
      const newPlan: TripPlan = {
        id: `plan-${Date.now()}`,
        createdAt: new Date(),
        parentId: null,
        options,
        destination,
        duration,
        budget,
        groupSize,
        isSelected: false,
      };
      
      setTripPlans([newPlan]);
      setSelectedPlanId(newPlan.id);
      
      // Set hold expiry time
      const expiryTime = new Date();
      expiryTime.setMinutes(expiryTime.getMinutes() + holdDuration);
      setHoldExpiryTime(expiryTime);
    } catch (error) {
      console.error('Error generating trip options:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate options using prioritized services
  const handleGenerateOptionsWithServices = async () => {
    if (!destination) {
      alert('Please enter a destination');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Fetch services for the selected destination
      const servicesResponse = await fetch(`/api/platforms/travel/planner/services?destination=${encodeURIComponent(destination)}`);
      const servicesData = await servicesResponse.json();
      
      // Check if services are available for the destination
      if (
        !servicesData.flights?.length &&
        !servicesData.accommodations?.length &&
        !servicesData.activities?.length
      ) {
        alert(`No travel services available for ${destination}. Please choose another destination or use the standard option generator.`);
        setIsLoading(false);
        return;
      }
      
      // Create a trip plan
      const planResponse = await fetch('/api/platforms/travel/planner/trip-plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId: currentUserId,
          options: {
            destination,
            duration,
            budget,
            groupSize,
            userId: currentUserId,
            preferences: {
              // Additional preferences could be added here
            }
          }
        })
      });
      
      if (!planResponse.ok) {
        throw new Error('Failed to create trip plan');
      }
      
      const newPlan = await planResponse.json();
      
      setTripPlans([newPlan]);
      setSelectedPlanId(newPlan.id);
      
      // Set hold expiry time
      const expiryTime = new Date();
      expiryTime.setMinutes(expiryTime.getMinutes() + holdDuration);
      setHoldExpiryTime(expiryTime);
    } catch (error) {
      console.error('Error generating trip options with services:', error);
      alert('An error occurred while generating trip options. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate variations of a selected option
  const handleGenerateVariations = (optionId: string) => {
    if (!selectedPlanId) return;
    
    setIsLoading(true);
    
    try {
      const selectedPlan = tripPlans.find(plan => plan.id === selectedPlanId);
      if (!selectedPlan) return;
      
      const selectedOption = selectedPlan.options.find(option => option.id === optionId);
      if (!selectedOption) return;
      
      // Generate variations based on the selected option
      const variations = generateTripOptions(
        selectedPlan.destination,
        selectedPlan.duration,
        selectedPlan.budget,
        selectedPlan.groupSize,
        selectedOption
      );
      
      const newPlan: TripPlan = {
        id: `plan-${Date.now()}`,
        createdAt: new Date(),
        parentId: selectedPlanId,
        options: variations,
        destination: selectedPlan.destination,
        duration: selectedPlan.duration,
        budget: selectedPlan.budget,
        groupSize: selectedPlan.groupSize,
        isSelected: false,
        baseOption: selectedOption
      };
      
      setTripPlans([...tripPlans, newPlan]);
      setSelectedPlanId(newPlan.id);
    } catch (error) {
      console.error('Error generating variations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Select a plan for viewing
  const handleSelectPlan = (planId: string) => {
    setSelectedPlanId(planId);
  };

  // Add plans to comparison
  const handleAddToComparison = (planId: string) => {
    const plan = tripPlans.find(p => p.id === planId);
    if (plan && !comparisonPlans.some(p => p.id === planId)) {
      setComparisonPlans([...comparisonPlans, plan]);
    }
  };

  // Remove plan from comparison
  const handleRemoveFromComparison = (planId: string) => {
    setComparisonPlans(comparisonPlans.filter(p => p.id !== planId));
  };

  // Handle AI destination selection
  const handleAIDestinationSelect = (destination: string, suggestedDuration: number, suggestedBudget: number) => {
    setDestination(destination);
    setDuration(suggestedDuration);
    setBudget(suggestedBudget);

    // Automatically generate options with the AI-suggested parameters
    setTimeout(() => {
      handleGenerateOptions();
    }, 100);
  };

  // Finalize booking
  const handleFinalizeBooking = (optionId: string) => {
    if (!selectedPlanId) return;

    const selectedPlan = tripPlans.find(plan => plan.id === selectedPlanId);
    if (!selectedPlan) return;

    const selectedOption = selectedPlan.options.find(option => option.id === optionId);
    if (!selectedOption) return;

    // In a real implementation, this would call an API to finalize the booking
    alert(`Booking finalized for option ${optionId}!`);

    // Update the selected plan
    const updatedPlans = tripPlans.map(plan => ({
      ...plan,
      isSelected: plan.id === selectedPlanId,
      options: plan.id === selectedPlanId
        ? plan.options.map(option => ({
            ...option,
            isSelected: option.id === optionId
          }))
        : plan.options
    }));

    setTripPlans(updatedPlans);
  };

  // Update time remaining for hold
  useEffect(() => {
    if (!holdExpiryTime) return;
    
    const interval = setInterval(() => {
      const now = new Date();
      const diff = holdExpiryTime.getTime() - now.getTime();
      
      if (diff <= 0) {
        setTimeRemaining('Expired');
        clearInterval(interval);
      } else {
        const minutes = Math.floor(diff / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);
        setTimeRemaining(`${minutes}m ${seconds}s`);
      }
    }, 1000);
    
    return () => clearInterval(interval);
  }, [holdExpiryTime]);

  // Get the currently selected plan
  const selectedPlan = tripPlans.find(plan => plan.id === selectedPlanId);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
          <Globe className="mr-2 h-8 w-8 text-blue-600" />
          AI Travel Planner
        </h1>
        
        {/* Services Repository Link */}
        <div className="mb-6 flex justify-between items-center">
          <p className="text-gray-600">
            Plan your perfect trip with our AI-powered travel planner.
          </p>

          <div className="flex space-x-3">
            <Link
              href="/platforms/travel/planner/dashboard"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <User className="mr-2 h-4 w-4" />
              Dashboard
            </Link>

            <button
              onClick={() => setShowPreferencesModal(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Settings className="mr-2 h-4 w-4" />
              Preferences
            </button>

            <Link
              href="/platforms/travel/planner/services"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Database className="mr-2 h-4 w-4" />
              Services Repository
            </Link>
          </div>
        </div>

        {/* AI Recommendations */}
        {(aiRecommendations.length > 0 || aiInsights) && (
          <AIRecommendations
            recommendations={aiRecommendations}
            insights={aiInsights}
            onSelectDestination={handleAIDestinationSelect}
          />
        )}

        {/* Natural Language Input */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Tell us about your trip</h2>
          <form onSubmit={handleNaturalLanguageSubmit} className="space-y-4">
            <div>
              <label htmlFor="naturalLanguageInput" className="block text-sm font-medium text-gray-700 mb-1">
                Describe your trip in natural language
              </label>
              <textarea
                id="naturalLanguageInput"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="E.g., Plan a trip to Paris for 2 weeks with a budget of $3000 for 2 people. Hold reservations for 30 minutes."
                value={naturalLanguageInput}
                onChange={(e) => setNaturalLanguageInput(e.target.value)}
              />
            </div>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Generate Trip Options
                </>
              )}
            </button>
          </form>
          
          {/* Add a button to generate options using services */}
          {servicesAvailable && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h3 className="text-lg font-medium mb-2">Use Travel Services</h3>
              <p className="text-sm text-gray-600 mb-3">
                Generate trip options using our curated travel services for better pricing and availability.
              </p>
              <button
                onClick={handleGenerateOptionsWithServices}
                disabled={isLoading}
                className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <Database className="mr-2 h-4 w-4" />
                Generate with Services
                {isLoading && <span className="ml-2 animate-pulse">...</span>}
              </button>
            </div>
          )}
        </div>
        
        {/* Manual Input Form */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Or specify details manually</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label htmlFor="destination" className="block text-sm font-medium text-gray-700 mb-1">
                Destination
              </label>
              <input
                type="text"
                id="destination"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="E.g., Paris, Tokyo, Bali"
                value={destination}
                onChange={(e) => setDestination(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
                Duration (days)
              </label>
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                <input
                  type="number"
                  id="duration"
                  min={1}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value))}
                />
              </div>
            </div>
            <div>
              <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-1">
                Budget ($)
              </label>
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-gray-400 mr-2" />
                <input
                  type="number"
                  id="budget"
                  min={100}
                  step={100}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={budget}
                  onChange={(e) => setBudget(parseInt(e.target.value))}
                />
              </div>
            </div>
            <div>
              <label htmlFor="groupSize" className="block text-sm font-medium text-gray-700 mb-1">
                Group Size
              </label>
              <div className="flex items-center">
                <Users className="h-5 w-5 text-gray-400 mr-2" />
                <input
                  type="number"
                  id="groupSize"
                  min={1}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={groupSize}
                  onChange={(e) => setGroupSize(parseInt(e.target.value))}
                />
              </div>
            </div>
          </div>
          <div className="mt-4">
            <button
              onClick={handleGenerateOptions}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isLoading || !destination}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </>
              ) : (
                <>
                  <PlaneTakeoff className="mr-2 h-4 w-4" />
                  Generate Trip Options
                </>
              )}
            </button>
          </div>
        </div>
        
        {/* Trip Planning Interface */}
        {tripPlans.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Trip Tree View */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Trip History</h2>
                  {holdExpiryTime && (
                    <div className="flex items-center text-sm">
                      <Timer className="h-4 w-4 text-amber-500 mr-1" />
                      <span className={`font-medium ${timeRemaining === 'Expired' ? 'text-red-500' : 'text-amber-500'}`}>
                        {timeRemaining}
                      </span>
                    </div>
                  )}
                </div>
                <TripTreeView 
                  plans={tripPlans} 
                  selectedPlanId={selectedPlanId}
                  onSelectPlan={handleSelectPlan}
                  onAddToComparison={handleAddToComparison}
                />
                {comparisonPlans.length > 0 && (
                  <div className="mt-4">
                    <button
                      onClick={() => setShowComparisonModal(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <Layers className="mr-2 h-4 w-4" />
                      Compare ({comparisonPlans.length})
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            {/* Trip Options */}
            <div className="lg:col-span-2">
              {selectedPlan && (
                <div>
                  <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 className="text-xl font-semibold mb-2">
                      {selectedPlan.parentId 
                        ? 'Variations' 
                        : `Trip Options for ${selectedPlan.destination}`}
                    </h2>
                    <div className="flex flex-wrap gap-2 mb-4">
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <Calendar className="h-4 w-4 mr-1" />
                        {selectedPlan.duration} days
                      </div>
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <DollarSign className="h-4 w-4 mr-1" />
                        ${selectedPlan.budget}
                      </div>
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        <Users className="h-4 w-4 mr-1" />
                        {selectedPlan.groupSize} {selectedPlan.groupSize === 1 ? 'person' : 'people'}
                      </div>
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                        <Map className="h-4 w-4 mr-1" />
                        {selectedPlan.destination}
                      </div>
                    </div>
                    {selectedPlan.baseOption && (
                      <div className="mb-4 p-3 bg-gray-50 rounded-md">
                        <h3 className="text-sm font-medium text-gray-700 mb-1">Based on:</h3>
                        <p className="text-sm text-gray-600">
                          {selectedPlan.baseOption.title} - ${selectedPlan.baseOption.price}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {selectedPlan.options.map((option) => (
                      <TripOptionCard
                        key={option.id}
                        option={option}
                        destination={selectedPlan.destination}
                        duration={selectedPlan.duration}
                        groupSize={selectedPlan.groupSize}
                        onGenerateVariations={() => handleGenerateVariations(option.id)}
                        onFinalizeBooking={() => handleFinalizeBooking(option.id)}
                        holdExpired={timeRemaining === 'Expired'}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      
      {/* Comparison Modal */}
      {showComparisonModal && (
        <TripComparisonModal
          plans={comparisonPlans}
          onClose={() => setShowComparisonModal(false)}
          onRemovePlan={handleRemoveFromComparison}
          onFinalizeBooking={handleFinalizeBooking}
          holdExpired={timeRemaining === 'Expired'}
        />
      )}

      {/* User Preferences Modal */}
      <UserPreferencesModal
        isOpen={showPreferencesModal}
        onClose={() => setShowPreferencesModal(false)}
        userId={currentUserId}
        onSave={(preferences) => {
          setUserPreferences(preferences);
          console.log('Preferences saved:', preferences);
        }}
      />
    </div>
  );
}