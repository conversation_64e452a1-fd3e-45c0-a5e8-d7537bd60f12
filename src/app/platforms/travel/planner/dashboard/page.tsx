'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  User as UserIcon, 
  MapPin, 
  Calendar, 
  TrendingUp,
  Globe
} from 'lucide-react';
import UserProfile from '@/components/platforms/travel/planner/UserProfile';
import TripHistory from '@/components/platforms/travel/planner/TripHistory';
import { User } from '@/lib/platforms/travel/planner/services/user-service';

export default function TravelPlannerDashboard() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'profile' | 'history' | 'stats'>('profile');
  const [currentUserId] = useState('user-1'); // In a real app, this would come from authentication

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/platforms/travel/planner/users?userId=${currentUserId}`);
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else {
        throw new Error('Failed to load user data');
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      // In a real app, redirect to login page
      alert('Failed to load user data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateUser = (updatedUser: User) => {
    setUser(updatedUser);
  };

  const handleLogout = async () => {
    try {
      // In a real app, this would invalidate the session
      const response = await fetch('/api/platforms/travel/planner/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'logout',
          sessionId: 'current-session-id' // Would come from actual session
        })
      });

      if (response.ok) {
        // Redirect to main travel planner page
        router.push('/platforms/travel/planner');
      }
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">User Not Found</h1>
          <p className="text-gray-600 mb-4">Unable to load user data.</p>
          <button
            onClick={() => router.push('/platforms/travel/planner')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Travel Planner
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                <Globe className="mr-2 h-8 w-8 text-blue-600" />
                Travel Dashboard
              </h1>
              <p className="text-gray-600">
                Welcome back, {user.name}! Manage your travel profile and view your trip history.
              </p>
            </div>
            
            <button
              onClick={() => router.push('/platforms/travel/planner')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <MapPin className="mr-2 h-4 w-4" />
              Plan New Trip
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('profile')}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'profile'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <UserIcon className="mr-2 h-4 w-4" />
              Profile
            </button>
            
            <button
              onClick={() => setActiveTab('history')}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'history'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Calendar className="mr-2 h-4 w-4" />
              Trip History
            </button>
            
            <button
              onClick={() => setActiveTab('stats')}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'stats'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              Statistics
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'profile' && (
            <UserProfile
              user={user}
              onUpdateUser={handleUpdateUser}
              onLogout={handleLogout}
            />
          )}
          
          {activeTab === 'history' && (
            <TripHistory userId={user.id} />
          )}
          
          {activeTab === 'stats' && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-6">Travel Statistics</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="text-center p-6 bg-blue-50 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600 mb-2">0</div>
                  <div className="text-sm text-gray-600">Total Trips</div>
                </div>
                
                <div className="text-center p-6 bg-green-50 rounded-lg">
                  <div className="text-3xl font-bold text-green-600 mb-2">$0</div>
                  <div className="text-sm text-gray-600">Total Spent</div>
                </div>
                
                <div className="text-center p-6 bg-purple-50 rounded-lg">
                  <div className="text-3xl font-bold text-purple-600 mb-2">0</div>
                  <div className="text-sm text-gray-600">Countries Visited</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Favorite Destinations</h3>
                  <div className="text-center py-8 text-gray-500">
                    <MapPin className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No trips yet. Start planning to see your favorite destinations!</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-4">Travel Timeline</h3>
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>Your travel timeline will appear here after your first trip.</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
