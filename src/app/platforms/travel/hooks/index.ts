import { useState, useEffect, useCallback } from 'react';
import { SearchFilters, SearchResult } from '../types';

// Simple search hook for search components
export function useSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const performSearch = useCallback((query: string) => {
    setIsLoading(true);
    setSearchQuery(query);
    // Simulate search delay
    setTimeout(() => {
      setIsLoading(false);
    }, 300);
  }, []);

  return {
    searchQuery,
    setSearchQuery: performSearch,
    isLoading
  };
}

// Generic search hook
export function useSearchGeneric<T>(
  searchFunction: (query: string, filters: SearchFilters) => Promise<SearchResult<T>>,
  initialFilters: SearchFilters = {}
) {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>(initialFilters);
  const [results, setResults] = useState<SearchResult<T> | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async () => {
    if (!query.trim() && Object.keys(filters).length === 0) {
      setResults(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const searchResults = await searchFunction(query, filters);
      setResults(searchResults);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
      setResults(null);
    } finally {
      setLoading(false);
    }
  }, [query, filters, searchFunction]);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      search();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [search]);

  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
  }, [initialFilters]);

  return {
    query,
    setQuery,
    filters,
    updateFilters,
    clearFilters,
    results,
    loading,
    error,
    search
  };
}

// Booking management hook
export function useBooking() {
  const [bookings, setBookings] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createBooking = useCallback(async (bookingData: Record<string, any>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/platforms/travel/start/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      if (!response.ok) {
        throw new Error('Failed to create booking');
      }

      const booking = await response.json();
      setBookings(prev => [...prev, booking]);
      return booking;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Booking failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const cancelBooking = useCallback(async (bookingId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/platforms/travel/start/bookings/${bookingId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to cancel booking');
      }

      setBookings(prev => prev.filter((booking) => booking.id !== bookingId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Cancellation failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    bookings,
    loading,
    error,
    createBooking,
    cancelBooking
  };
}

// Local storage hook for user preferences
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window !== 'undefined') {
        const item = window.localStorage.getItem(key);
        return item ? JSON.parse(item) : initialValue;
      }
      return initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  return [storedValue, setValue] as const;
}

// Favorites management hook
export function useFavorites() {
  const [favorites, setFavorites] = useLocalStorage<string[]>('travel_favorites', []);

  const addToFavorites = useCallback((id: string) => {
    setFavorites(prev => [...new Set([...prev, id])]);
  }, [setFavorites]);

  const removeFromFavorites = useCallback((id: string) => {
    setFavorites(prev => prev.filter(fav => fav !== id));
  }, [setFavorites]);

  const isFavorite = useCallback((id: string) => {
    return favorites.includes(id);
  }, [favorites]);

  const toggleFavorite = useCallback((id: string) => {
    if (isFavorite(id)) {
      removeFromFavorites(id);
    } else {
      addToFavorites(id);
    }
  }, [isFavorite, addToFavorites, removeFromFavorites]);

  return {
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite
  };
}