// Travel Platform Type Definitions
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Location {
  id: string;
  name: string;
  country: string;
  city?: string;
  address?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface PriceRange {
  min: number;
  max: number;
  currency: string;
}

export interface Rating {
  score: number;
  count: number;
  breakdown?: {
    [key: string]: number;
  };
}

// Hotel Types
export interface Hotel extends BaseEntity {
  name: string;
  description: string;
  location: Location;
  rating: Rating;
  reviewCount: number;
  amenities: string[];
  images: string[];
  priceRange: PriceRange;
  availability: boolean;
  roomTypes: RoomType[];
  policies: {
    checkIn: string;
    checkOut: string;
    cancellation: string;
  };
  contact: {
    phone: string;
    email: string;
  };
  // Additional properties for UI
  discount?: number;
  originalPrice?: number;
}

export interface RoomType {
  id: string;
  name: string;
  description: string;
  capacity: number;
  price: number;
  amenities: string[];
  images: string[];
}

// Flight Types
export interface Flight extends BaseEntity {
  airline: string;
  flightNumber: string;
  departure: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  arrival: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  duration: string;
  price: number;
  originalPrice?: number;
  class: string;
  aircraft: string;
  amenities: string[];
  stops: number;
  discount?: number;
  // Additional properties for UI
  baggage?: {
    cabin: string;
    checked: string;
  };
}

// Tour Types
export interface Tour extends BaseEntity {
  name: string;
  description: string;
  destinations: Location[];
  duration: number; // in days
  price: number;
  currency: string;
  rating: Rating;
  images: string[];
  inclusions: string[];
  exclusions: string[];
  itinerary: TourDay[];
  maxGroupSize: number;
  difficulty: 'easy' | 'moderate' | 'challenging';
  // Additional properties for UI
  title?: string;
  category?: string;
  isPopular?: boolean;
  destination?: string;
  reviewCount?: number;
  originalPrice?: number;
}

export interface TourDay {
  day: number;
  title: string;
  description: string;
  activities: string[];
  meals: string[];
  accommodation?: string;
}

// Car Rental Types
export interface CarRental extends BaseEntity {
  make: string;
  model: string;
  year: number;
  category: 'economy' | 'compact' | 'midsize' | 'fullsize' | 'luxury' | 'suv';
  transmission: 'manual' | 'automatic';
  fuelType: 'petrol' | 'diesel' | 'electric' | 'hybrid';
  seats: number;
  doors: number;
  pricePerDay: number;
  currency: string;
  features: string[];
  images: string[];
  location: Location;
  availability: boolean;
  // Additional properties for UI
  brand?: string;
  luggage?: number;
}

// Service Types
export interface TravelService extends BaseEntity {
  name: string;
  description: string;
  category: 'guide' | 'transport' | 'activity' | 'insurance' | 'visa' | 'other';
  provider: ServiceProvider;
  location: Location;
  price: number;
  currency: string;
  rating: Rating;
  duration?: number; // in hours
  languages: string[];
  groupSize?: {
    min: number;
    max: number;
  };
  // Additional properties for UI
  priceRange?: PriceRange;
  images?: string[];
}

export interface ServiceProvider {
  id: string;
  name: string;
  description: string;
  verified: boolean;
  rating: Rating;
  contact: {
    phone?: string;
    email?: string;
    website?: string;
  };
}

// Search & Filter Types
export interface SearchFilters {
  location?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  priceRange?: PriceRange;
  rating?: number;
  amenities?: string[];
  category?: string;
  sortBy?: 'price' | 'rating' | 'distance' | 'popularity';
  sortOrder?: 'asc' | 'desc';
  
  // Hotel specific
  checkIn?: string;
  checkOut?: string;
  guests?: number;
  
  // Flight specific
  origin?: string;
  destination?: string;
  departureDate?: string;
  returnDate?: string;
  passengers?: number;
  class?: 'economy' | 'business' | 'first';
  
  // Tour specific
  startDate?: string;
  duration?: number;
  groupSize?: number;
  
  // Car rental specific
  pickupDate?: string;
  carType?: string;
  
  // Service specific
  serviceType?: string;
  date?: string;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Booking Types
export interface BookingRequest {
  serviceType: 'hotel' | 'flight' | 'tour' | 'car' | 'service';
  serviceId: string;
  userId: string;
  guests: Guest[];
  dateRange: {
    start: Date;
    end: Date;
  };
  specialRequests?: string;
  contactInfo: ContactInfo;
}

export interface Guest {
  firstName: string;
  lastName: string;
  age: number;
  type: 'adult' | 'child' | 'infant';
  documents?: {
    passport?: string;
    visa?: string;
  };
}

export interface ContactInfo {
  email: string;
  phone: string;
  address?: {
    street: string;
    city: string;
    country: string;
    postalCode: string;
  };
}

export interface Booking extends BaseEntity {
  bookingNumber: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  serviceType: 'hotel' | 'flight' | 'tour' | 'car' | 'service';
  serviceId: string;
  userId: string;
  guests: Guest[];
  dateRange: {
    start: Date;
    end: Date;
  };
  totalPrice: number;
  currency: string;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  specialRequests?: string;
  contactInfo: ContactInfo;
}