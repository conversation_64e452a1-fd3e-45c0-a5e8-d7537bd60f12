'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import FlightBooking from '../modules/booking/FlightBooking';
import HotelBooking from '../modules/booking/HotelBooking';

interface Trip {
  id: string;
  name: string;
  purpose: 'business' | 'personal' | 'mixed';
  startDate: string;
  endDate: string;
  destination: string;
  status: 'planning' | 'upcoming' | 'completed';
  bookings: {
    flights: boolean;
    hotels: boolean;
    transportation: boolean;
  };
}

export default function BookingPage() {
  const [activeTab, setActiveTab] = useState<'trips' | 'flight' | 'hotel' | 'add-trip'>('trips');
  const [trips, setTrips] = useState<Trip[]>([
    {
      id: 'trip1',
      name: 'Q4 Client Meeting',
      purpose: 'business',
      startDate: '2023-11-15',
      endDate: '2023-11-18',
      destination: 'Ho Chi Minh City',
      status: 'upcoming',
      bookings: {
        flights: true,
        hotels: true,
        transportation: false,
      },
    },
    {
      id: 'trip2',
      name: 'Training Seminar',
      purpose: 'business',
      startDate: '2023-12-05',
      endDate: '2023-12-10',
      destination: 'Hanoi',
      status: 'planning',
      bookings: {
        flights: false,
        hotels: false,
        transportation: false,
      },
    },
    {
      id: 'trip3',
      name: 'New Year Team Retreat',
      purpose: 'mixed',
      startDate: '2024-01-10',
      endDate: '2024-01-15',
      destination: 'Da Nang',
      status: 'planning',
      bookings: {
        flights: false,
        hotels: true,
        transportation: false,
      },
    },
  ]);
  
  const [newTrip, setNewTrip] = useState<Partial<Trip>>({
    purpose: 'business',
    bookings: {
      flights: false,
      hotels: false,
      transportation: false,
    },
  });
  
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);

  const handleTripSelection = (tripId: string) => {
    setSelectedTripId(tripId);
  };

  const handleAddTrip = () => {
    if (newTrip.name && newTrip.destination && newTrip.startDate && newTrip.endDate) {
      const trip: Trip = {
        id: `trip-${Date.now()}`,
        name: newTrip.name,
        purpose: newTrip.purpose as 'business' | 'personal' | 'mixed',
        startDate: newTrip.startDate,
        endDate: newTrip.endDate,
        destination: newTrip.destination,
        status: 'planning',
        bookings: {
          flights: false,
          hotels: false,
          transportation: false,
        },
      };
      
      setTrips([...trips, trip]);
      setNewTrip({
        purpose: 'business',
        bookings: {
          flights: false,
          hotels: false,
          transportation: false,
        },
      });
      setActiveTab('trips');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning':
        return 'bg-blue-100 text-blue-800';
      case 'upcoming':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewTrip({
      ...newTrip,
      [name]: value,
    });
  };

  const getSelectedTrip = () => {
    return trips.find(trip => trip.id === selectedTripId);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-blue-800">Corporate Travel Booking</h1>
        <p className="text-gray-600 mt-2">
          Plan and book your business trips efficiently with Vntrip TMS.
        </p>
      </div>

      {/* Main Tabs */}
      <div className="mb-6">
        <div className="flex border-b border-gray-200">
          <button
            className={`py-3 px-6 text-sm font-medium ${
              activeTab === 'trips'
                ? 'bg-white text-blue-600 border-b-2 border-blue-600'
                : 'bg-gray-50 text-gray-600 hover:text-blue-600'
            }`}
            onClick={() => setActiveTab('trips')}
          >
            My Trips
          </button>
          <button
            className={`py-3 px-6 text-sm font-medium ${
              activeTab === 'flight'
                ? 'bg-white text-blue-600 border-b-2 border-blue-600'
                : 'bg-gray-50 text-gray-600 hover:text-blue-600'
            }`}
            onClick={() => {
              setActiveTab('flight');
              setSelectedTripId(null);
            }}
          >
            Book Flights
          </button>
          <button
            className={`py-3 px-6 text-sm font-medium ${
              activeTab === 'hotel'
                ? 'bg-white text-blue-600 border-b-2 border-blue-600'
                : 'bg-gray-50 text-gray-600 hover:text-blue-600'
            }`}
            onClick={() => {
              setActiveTab('hotel');
              setSelectedTripId(null);
            }}
          >
            Book Hotels
          </button>
          <button
            className={`py-3 px-6 text-sm font-medium ${
              activeTab === 'add-trip'
                ? 'bg-white text-blue-600 border-b-2 border-blue-600'
                : 'bg-gray-50 text-gray-600 hover:text-blue-600'
            }`}
            onClick={() => {
              setActiveTab('add-trip');
              setSelectedTripId(null);
            }}
          >
            + New Trip
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-md p-6">
        {/* My Trips Tab */}
        {activeTab === 'trips' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">My Business Trips</h2>
              <Button 
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => setActiveTab('add-trip')}
              >
                Create New Trip
              </Button>
            </div>
            
            {trips.length > 0 ? (
              <div className="space-y-4">
                {trips.map(trip => (
                  <div 
                    key={trip.id} 
                    className={`p-4 border rounded-lg cursor-pointer hover:border-blue-400 ${selectedTripId === trip.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                    onClick={() => handleTripSelection(trip.id)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg">{trip.name}</h3>
                        <p className="text-gray-600">{trip.destination}</p>
                        <div className="flex items-center mt-2 text-sm text-gray-500">
                          <span className="mr-4">{trip.startDate} to {trip.endDate}</span>
                          <span className="capitalize">{trip.purpose}</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-col items-end">
                        <span className={`px-3 py-1 text-xs rounded-full ${getStatusColor(trip.status)}`}>
                          {trip.status.charAt(0).toUpperCase() + trip.status.slice(1)}
                        </span>
                        
                        <div className="flex space-x-2 mt-3">
                          <span className={`w-2 h-2 rounded-full mt-1 ${trip.bookings.flights ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span className="text-xs">{trip.bookings.flights ? 'Flights Booked' : 'No Flights'}</span>
                          
                          <span className={`w-2 h-2 rounded-full mt-1 ${trip.bookings.hotels ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span className="text-xs">{trip.bookings.hotels ? 'Hotels Booked' : 'No Hotels'}</span>
                        </div>
                      </div>
                    </div>
                    
                    {selectedTripId === trip.id && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <Button 
                            className={`${trip.bookings.flights ? 'bg-gray-100 text-gray-800' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                            onClick={() => setActiveTab('flight')}
                            disabled={trip.bookings.flights}
                          >
                            {trip.bookings.flights ? 'Flights Booked' : 'Book Flights'}
                          </Button>
                          
                          <Button 
                            className={`${trip.bookings.hotels ? 'bg-gray-100 text-gray-800' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                            onClick={() => setActiveTab('hotel')}
                            disabled={trip.bookings.hotels}
                          >
                            {trip.bookings.hotels ? 'Hotels Booked' : 'Book Hotels'}
                          </Button>
                          
                          <Button 
                            className="bg-gray-100 hover:bg-gray-200 text-gray-800"
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10 bg-gray-50 rounded-lg">
                <p className="text-gray-500 mb-4">You don't have any trips yet.</p>
                <Button 
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={() => setActiveTab('add-trip')}
                >
                  Create Your First Trip
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Flight Booking Tab */}
        {activeTab === 'flight' && (
          <div>
            {selectedTripId ? (
              <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold">Booking Flight for Trip: {getSelectedTrip()?.name}</h3>
                <p className="text-sm text-gray-600">
                  Destination: {getSelectedTrip()?.destination} | {getSelectedTrip()?.startDate} to {getSelectedTrip()?.endDate}
                </p>
              </div>
            ) : (
              <div className="mb-6 flex justify-between items-center">
                <h3 className="font-semibold text-xl">Book Flights</h3>
                <div>
                  <select 
                    className="px-3 py-2 border border-gray-300 rounded-md mr-2"
                    onChange={(e) => setSelectedTripId(e.target.value)}
                  >
                    <option value="">Book for a Trip</option>
                    {trips.filter(t => !t.bookings.flights).map(trip => (
                      <option key={trip.id} value={trip.id}>{trip.name}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}
            
            <FlightBooking />
          </div>
        )}

        {/* Hotel Booking Tab */}
        {activeTab === 'hotel' && (
          <div>
            {selectedTripId ? (
              <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold">Booking Hotel for Trip: {getSelectedTrip()?.name}</h3>
                <p className="text-sm text-gray-600">
                  Destination: {getSelectedTrip()?.destination} | {getSelectedTrip()?.startDate} to {getSelectedTrip()?.endDate}
                </p>
              </div>
            ) : (
              <div className="mb-6 flex justify-between items-center">
                <h3 className="font-semibold text-xl">Book Hotels</h3>
                <div>
                  <select 
                    className="px-3 py-2 border border-gray-300 rounded-md mr-2"
                    onChange={(e) => setSelectedTripId(e.target.value)}
                  >
                    <option value="">Book for a Trip</option>
                    {trips.filter(t => !t.bookings.hotels).map(trip => (
                      <option key={trip.id} value={trip.id}>{trip.name}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}
            
            <HotelBooking />
          </div>
        )}

        {/* Add Trip Tab */}
        {activeTab === 'add-trip' && (
          <div>
            <h2 className="text-xl font-semibold mb-6">Create New Business Trip</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Trip Name *
                </label>
                <input
                  type="text"
                  name="name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Q4 Client Meeting"
                  value={newTrip.name || ''}
                  onChange={handleInputChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Trip Purpose
                </label>
                <select
                  name="purpose"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={newTrip.purpose}
                  onChange={handleInputChange}
                >
                  <option value="business">Business</option>
                  <option value="personal">Personal</option>
                  <option value="mixed">Mixed</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date *
                </label>
                <input
                  type="date"
                  name="startDate"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={newTrip.startDate || ''}
                  onChange={handleInputChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date *
                </label>
                <input
                  type="date"
                  name="endDate"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={newTrip.endDate || ''}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Destination *
                </label>
                <input
                  type="text"
                  name="destination"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Ho Chi Minh City"
                  value={newTrip.destination || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3">
              <Button 
                className="bg-gray-200 hover:bg-gray-300 text-gray-800"
                onClick={() => setActiveTab('trips')}
              >
                Cancel
              </Button>
              <Button 
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={handleAddTrip}
                disabled={!newTrip.name || !newTrip.destination || !newTrip.startDate || !newTrip.endDate}
              >
                Create Trip
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Help Section */}
      <div className="mt-8 bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Need Help Planning Your Business Trip?</h3>
        <p className="text-gray-600 mb-4">Our travel specialists can help you arrange complex itineraries and provide personalized assistance.</p>
        <div className="flex gap-3">
          <Button className="bg-white hover:bg-gray-100 text-gray-800 border border-gray-300">
            Chat with Support
          </Button>
          <Button className="bg-blue-50 hover:bg-blue-100 text-blue-700">
            View Travel Policy
          </Button>
        </div>
      </div>
    </div>
  );
} 