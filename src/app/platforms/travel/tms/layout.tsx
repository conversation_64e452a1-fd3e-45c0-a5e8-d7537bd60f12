import React from 'react';
import { Button } from '@/components/ui/button';

export const metadata = {
  title: 'ABN TMS - Travel Management Solution for Businesses',
  description: 'The sole platform in Vietnam offering an online booking system exclusively designed for business customers to self-manage and reserve services.',
};

export default function TMSLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="container mx-auto px-4 flex items-center justify-between h-16">
          <div className="flex items-center">
            <a href="/platforms/travel/tms" className="text-blue-600 font-bold text-xl">
              ABN TMS
            </a>
            
            <nav className="hidden ml-10 md:flex space-x-8">
              <a href="/platforms/travel/tms/features" className="text-gray-600 hover:text-blue-600">
                Features
              </a>
              <a href="/platforms/travel/tms/pricing" className="text-gray-600 hover:text-blue-600">
                Pricing
              </a>
              <a href="/platforms/travel/tms/modules" className="text-gray-600 hover:text-blue-600">
                Modules
              </a>
              <a href="/platforms/travel/tms/booking" className="text-gray-600 hover:text-blue-600">
                Book Travel
              </a>
              <a href="/platforms/travel/tms/directory/corporate" className="text-gray-600 hover:text-blue-600">
                Corporate Directory
              </a>
              <a href="/platforms/travel/tms/directory/services" className="text-gray-600 hover:text-blue-600">
                Services
              </a>
              <a href="/platforms/travel/tms/demo" className="text-gray-600 hover:text-blue-600">
                Contact Us
              </a>
            </nav>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:block">
              <Button className="border-blue-600 text-blue-600">
                English
              </Button>
            </div>
            <div className="md:hidden">
              <button className="text-gray-600 hover:text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-grow">
        {children}
      </main>
    </div>
  );
} 