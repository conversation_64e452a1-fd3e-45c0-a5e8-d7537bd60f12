'use client';

import { Button } from '@/components/ui/button';

export default function TMSPricingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-blue-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6 text-blue-800">
              Pricing Plans
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Choose the perfect plan to streamline your business travel management
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-16 container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {/* Standard Plan */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="bg-gray-50 p-6 text-center border-b">
              <h2 className="text-2xl font-bold text-gray-800">Standard</h2>
              <div className="mt-4">
                <span className="text-4xl font-bold">$499</span>
                <span className="text-gray-600">/month</span>
              </div>
              <p className="text-sm mt-2 text-gray-600">For small businesses</p>
            </div>
            
            <div className="p-6">
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Up to 50 active users</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Basic reporting tools</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Basic approval workflows</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Standard travel policy options</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Email support</span>
                </li>
              </ul>
              
              <Button className="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>
                Get Started
              </Button>
            </div>
          </div>
          
          {/* Business Plan */}
          <div className="bg-white border-2 border-blue-500 rounded-lg shadow-lg overflow-hidden transform scale-105 hover:shadow-xl transition-shadow">
            <div className="bg-blue-600 p-6 text-center text-white">
              <span className="inline-block px-3 py-1 rounded-full bg-yellow-400 text-blue-900 text-xs font-semibold mb-2">MOST POPULAR</span>
              <h2 className="text-2xl font-bold">Business</h2>
              <div className="mt-4">
                <span className="text-4xl font-bold">$999</span>
                <span>/month</span>
              </div>
              <p className="text-sm mt-2 text-blue-100">For growing companies</p>
            </div>
            
            <div className="p-6">
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Up to 200 active users</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Advanced analytics dashboard</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Multi-level approval workflows</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Customizable travel policies</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>24/7 priority support</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Basic system integrations</span>
                </li>
              </ul>
              
              <Button className="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>
                Get Started
              </Button>
            </div>
          </div>
          
          {/* Enterprise Plan */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="bg-gray-50 p-6 text-center border-b">
              <h2 className="text-2xl font-bold text-gray-800">Enterprise</h2>
              <div className="mt-4">
                <span className="text-4xl font-bold">Custom</span>
              </div>
              <p className="text-sm mt-2 text-gray-600">For large organizations</p>
            </div>
            
            <div className="p-6">
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Unlimited users</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Custom reporting & analytics</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Advanced approval workflows</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Fully customizable policies</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Dedicated account manager</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Full API access & integrations</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>SSO & advanced security features</span>
                </li>
              </ul>
              
              <Button className="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Compare Features */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Compare Plan Features</h2>
          
          <div className="max-w-5xl mx-auto overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-100">
                  <th className="text-left p-4 border-b-2 border-gray-200">Feature</th>
                  <th className="text-center p-4 border-b-2 border-gray-200">Standard</th>
                  <th className="text-center p-4 border-b-2 border-gray-200 bg-blue-50">Business</th>
                  <th className="text-center p-4 border-b-2 border-gray-200">Enterprise</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-4 border-b border-gray-200 font-medium">Users</td>
                  <td className="text-center p-4 border-b border-gray-200">Up to 50</td>
                  <td className="text-center p-4 border-b border-gray-200 bg-blue-50">Up to 200</td>
                  <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                </tr>
                <tr>
                  <td className="p-4 border-b border-gray-200 font-medium">Reporting</td>
                  <td className="text-center p-4 border-b border-gray-200">Basic</td>
                  <td className="text-center p-4 border-b border-gray-200 bg-blue-50">Advanced</td>
                  <td className="text-center p-4 border-b border-gray-200">Custom</td>
                </tr>
                <tr>
                  <td className="p-4 border-b border-gray-200 font-medium">Approval Workflows</td>
                  <td className="text-center p-4 border-b border-gray-200">Basic</td>
                  <td className="text-center p-4 border-b border-gray-200 bg-blue-50">Multi-level</td>
                  <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                </tr>
                <tr>
                  <td className="p-4 border-b border-gray-200 font-medium">Travel Policies</td>
                  <td className="text-center p-4 border-b border-gray-200">Standard</td>
                  <td className="text-center p-4 border-b border-gray-200 bg-blue-50">Customizable</td>
                  <td className="text-center p-4 border-b border-gray-200">Fully Custom</td>
                </tr>
                <tr>
                  <td className="p-4 border-b border-gray-200 font-medium">Support</td>
                  <td className="text-center p-4 border-b border-gray-200">Email</td>
                  <td className="text-center p-4 border-b border-gray-200 bg-blue-50">24/7 Priority</td>
                  <td className="text-center p-4 border-b border-gray-200">Dedicated Manager</td>
                </tr>
                <tr>
                  <td className="p-4 border-b border-gray-200 font-medium">System Integrations</td>
                  <td className="text-center p-4 border-b border-gray-200">
                    <span className="text-red-500">✗</span>
                  </td>
                  <td className="text-center p-4 border-b border-gray-200 bg-blue-50">Basic</td>
                  <td className="text-center p-4 border-b border-gray-200">Full API Access</td>
                </tr>
                <tr>
                  <td className="p-4 border-b border-gray-200 font-medium">SSO Authentication</td>
                  <td className="text-center p-4 border-b border-gray-200">
                    <span className="text-red-500">✗</span>
                  </td>
                  <td className="text-center p-4 border-b border-gray-200 bg-blue-50">
                    <span className="text-green-500">✓</span>
                  </td>
                  <td className="text-center p-4 border-b border-gray-200">
                    <span className="text-green-500">✓</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-12 text-center">Frequently Asked Questions</h2>
        
        <div className="max-w-3xl mx-auto space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-lg mb-2">Can I switch plans later?</h3>
            <p className="text-gray-700">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-lg mb-2">Do you offer a free trial?</h3>
            <p className="text-gray-700">Yes, we offer a 14-day free trial for all our plans. No credit card required to start your trial.</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-lg mb-2">What kind of support is included?</h3>
            <p className="text-gray-700">All plans include some level of support. Standard plans get email support, Business plans receive 24/7 priority support, and Enterprise plans include a dedicated account manager.</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-lg mb-2">Can I customize the system to match our brand?</h3>
            <p className="text-gray-700">Basic branding is available on Business plans, while Enterprise plans offer full white-labeling and customization options.</p>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-700 to-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to get started?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Start streamlining your business travel management today with ABN TMS.
          </p>
          <div className="flex justify-center gap-4">
            <Button className="bg-white text-blue-800 hover:bg-gray-100" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>
              Request a Demo
            </Button>
            <Button className="bg-transparent border border-white hover:bg-white/10" onClick={() => window.location.href = '/platforms/travel/tms'}>
              Learn More
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
} 