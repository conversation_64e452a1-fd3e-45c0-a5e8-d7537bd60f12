'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { useTMSData } from './hooks/useTMSData';

export default function TMSPage() {
  const data = useTMSData();
  
  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative w-full bg-gradient-to-r from-blue-600 to-blue-800 py-16 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {data.company.companyInfo.fullName} for Businesses
            </h1>
            <p className="text-xl mb-8">
              {data.company.companyInfo.description}
            </p>
            <div className="flex gap-4">
              <Button className="bg-white text-blue-800 hover:bg-gray-100 text-lg px-8 py-6 rounded-md font-medium" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>
                Request a demo
              </Button>
              <Button className="bg-blue-700 text-white hover:bg-blue-800 text-lg px-8 py-6 rounded-md font-medium" onClick={() => window.location.href = '/platforms/travel/tms/modules'}>
                Explore modules
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Who We Are Section */}
      <section className="py-16 container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-6 text-center">Who are we?</h2>
        <div className="max-w-4xl mx-auto">
          <p className="text-lg text-gray-700 mb-6">
            With a desire to pioneer a product that aids in the digital transformation of businesses in the field of business travel and tourism, ABN began researching and developing the ABN TMS (Travel Management Solution).
          </p>
          <p className="text-lg text-gray-700 mb-6">
            In December 2020, ABN marked its first significant milestone by successfully organizing an event to introduce the ABN TMS technology platform exclusively for Business Customers, with the presence of over 500 partner businesses.
          </p>
          <p className="text-lg text-gray-700 mb-6">
            Since then, ABN has remained steadfast in its development direction, focusing on Business Customers (B2B) and delivering the most optimal digital transformation solution to help businesses save costs and resources in travel management.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-8 mt-12">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{data.platformInfo.stats.corporateCustomers}</div>
            <div className="text-gray-600">Corporate customers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{data.platformInfo.stats.internationalHotels}</div>
            <div className="text-gray-600">International hotels</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{data.platformInfo.stats.domesticHotels}</div>
            <div className="text-gray-600">Domestic hotels</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{data.platformInfo.stats.airMiles}</div>
            <div className="text-gray-600">Air miles</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{data.platformInfo.stats.customersServed}</div>
            <div className="text-gray-600">Number of customers served</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{data.platformInfo.stats.ticketsSold}</div>
            <div className="text-gray-600">Number of tickets sold</div>
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Comprehensive solutions for business</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-4">For employees</h3>
              <ul className="space-y-3">
                {data.platformInfo.solutions.employees.map((solution, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>{solution}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-4">For managers</h3>
              <ul className="space-y-3">
                {data.platformInfo.solutions.managers.map((solution, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>{solution}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-4">For company</h3>
              <ul className="space-y-3">
                {data.platformInfo.solutions.company.map((solution, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>{solution}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Feature Groups */}
          <div className="space-y-16">
            {/* Reporting and Analysis */}
            <div>
              <h3 className="text-2xl font-bold mb-8 text-center">Reporting and Analysis</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {data.platformInfo.features.reporting.map((feature, index) => (
                  <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <h4 className="font-semibold mb-3">{feature.name}</h4>
                    <p className="text-gray-700">{feature.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Integrations */}
            <div>
              <h3 className="text-2xl font-bold mb-8 text-center">Integrations</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {data.platformInfo.features.integrations.map((feature, index) => (
                  <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <h4 className="font-semibold mb-3">{feature.name}</h4>
                    <p className="text-gray-700">{feature.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-16 container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-10 text-center">Our Partners</h2>
        
        <div className="space-y-8">
          <div>
            <h3 className="text-xl font-semibold mb-4">Airline Partners</h3>
            <div className="grid grid-cols-3 gap-6">
              {data.partners.airline.map((partner, index) => (
                <div key={index} className="p-4 border rounded-md flex items-center justify-center text-center">
                  <p>{partner.rank}</p>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold mb-4">Hotel Partners</h3>
            <div className="grid grid-cols-2 gap-6">
              {data.partners.hotel.map((hotel, index) => (
                <div key={index} className="p-4 border rounded-md flex items-center justify-center text-center">
                  <p>{hotel.count} {hotel.description}</p>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold mb-4">Payment Partners</h3>
            <div className="p-4 border rounded-md text-center">
              <p>{data.partners.payment.join(', ')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-6 text-center">ABN TMS is the trusted choice of {data.marketing.clients.total} corporations</h2>
          <p className="text-lg text-center max-w-4xl mx-auto mb-12">
            {data.marketing.clients.description}, including {data.marketing.clients.examples.join(', ')}, and more.<br/><br/>
            Not only is ABN highly regarded by domestic customers, but it has also gained the trust of international clients from countries such as {data.marketing.clients.international.countries.join(', ')}, and others.<br/><br/>
            This sets the stage for ABN TMS to expand beyond the borders of Vietnam.
          </p>
        </div>
      </section>

      {/* Hot Deal Section */}
      <section className="py-16 container mx-auto px-4">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-10 text-white">
          <h2 className="text-3xl font-bold mb-6 text-center">Hot Deal for Corporate customer</h2>
          <ul className="max-w-2xl mx-auto space-y-4 mb-8">
            {data.marketing.offers.map((offer, index) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-300 mr-2">*</span>
                <span>{offer.description}</span>
              </li>
            ))}
          </ul>
          <div className="flex justify-center gap-4">
            <Button className="bg-white text-blue-600 hover:bg-gray-100" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>Free Trial</Button>
            <Button className="bg-transparent border border-white hover:bg-white/10" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>Request a demo</Button>
          </div>
        </div>
      </section>
    </div>
  );
} 