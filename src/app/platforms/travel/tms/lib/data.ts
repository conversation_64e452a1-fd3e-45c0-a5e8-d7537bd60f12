// TMS Data Access Helper
// This file provides easy access to TMS data stored in JSON files

import fs from 'fs';
import path from 'path';

const DATA_PATH = path.join(process.cwd(), 'data/apps/platforms/travel/tms');

export interface TMSModule {
  id: string;
  name: string;
  description: string;
  icon: string;
  features: string[];
}

export interface TMSPartner {
  airline: Array<{
    name: string;
    rank: string;
  }>;
  hotel: Array<{
    type: string;
    count: string;
    description: string;
  }>;
  payment: string[];
}

export interface TMSCompany {
  companyInfo: {
    name: string;
    fullName: string;
    description: string;
    history: Array<{
      date: string;
      milestone: string;
    }>;
    mission: string;
    focus: string;
  };
  metadata: {
    title: string;
    description: string;
  };
}

export interface TMSPlatformInfo {
  stats: {
    corporateCustomers: string;
    internationalHotels: string;
    domesticHotels: string;
    airMiles: string;
    customersServed: string;
    ticketsSold: string;
  };
  solutions: {
    employees: string[];
    managers: string[];
    company: string[];
  };
  features: {
    reporting: Array<{
      name: string;
      description: string;
    }>;
    integrations: Array<{
      name: string;
      description: string;
    }>;
  };
}

export interface TMSMarketing {
  offers: Array<{
    title: string;
    description: string;
    value?: string;
  }>;
  clients: {
    total: string;
    description: string;
    examples: string[];
    international: {
      countries: string[];
    };
  };
}

// Data loading functions
export function getTMSModules(): TMSModule[] {
  const filePath = path.join(DATA_PATH, 'modules.json');
  const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  return data.modules;
}

export function getTMSPartners(): TMSPartner {
  const filePath = path.join(DATA_PATH, 'partners.json');
  return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

export function getTMSCompany(): TMSCompany {
  const filePath = path.join(DATA_PATH, 'company.json');
  return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

export function getTMSPlatformInfo(): TMSPlatformInfo {
  const filePath = path.join(DATA_PATH, 'platform-info.json');
  return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

export function getTMSMarketing(): TMSMarketing {
  const filePath = path.join(DATA_PATH, 'marketing.json');
  return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

// Convenience function to get all TMS data
export function getAllTMSData() {
  return {
    modules: getTMSModules(),
    partners: getTMSPartners(),
    company: getTMSCompany(),
    platformInfo: getTMSPlatformInfo(),
    marketing: getTMSMarketing(),
  };
}
