'use client';

import { Button } from '@/components/ui/button';

export default function TMSFeaturesPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-blue-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6 text-blue-800">
              Comprehensive Travel Management Features
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Explore the powerful features of ABN TMS designed to streamline your business travel management process
            </p>
          </div>
        </div>
      </section>

      {/* Feature Categories */}
      <section className="py-16 container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
            <h2 className="text-2xl font-semibold mb-4 text-blue-700">Reporting and Analysis</h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Comprehensive real-time dashboards</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Customizable expense reports</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Advanced analytics for cost optimization</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Monthly business intelligence reports</span>
              </li>
            </ul>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
            <h2 className="text-2xl font-semibold mb-4 text-blue-700">Approval Workflow</h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Customizable approval chains</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Automated approval for policy-compliant bookings</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>One-click approval from email</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Exception handling for out-of-policy requests</span>
              </li>
            </ul>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
            <h2 className="text-2xl font-semibold mb-4 text-blue-700">Travel Policies</h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Custom policy creation and enforcement</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Dynamic budget settings by department/role</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Geo-restricted travel zones for safety</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span>Policy compliance monitoring and alerts</span>
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Detailed Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Detailed Features</h2>
          
          <div className="space-y-12">
            {/* Additional Features */}
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold mb-6 text-blue-700 border-b pb-4">System Integration</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-medium mb-4 text-lg">Enterprise System Integration</h4>
                  <p className="text-gray-700 mb-4">
                    Seamlessly connect ABN TMS with your existing enterprise systems including:
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>HR management systems</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Accounting and ERP platforms</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Expense management tools</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Corporate payment systems</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-4 text-lg">Authentication and Security</h4>
                  <p className="text-gray-700 mb-4">
                    Enterprise-grade security with advanced authentication options:
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Single Sign-On (SSO) with SAML standards</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Two-factor authentication</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Role-based access control</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>ISO 27001 certified data protection</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold mb-6 text-blue-700 border-b pb-4">Booking and Services</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-medium mb-4 text-lg">Comprehensive Travel Services</h4>
                  <ul className="space-y-4">
                    <li>
                      <div className="font-medium text-blue-600">Flights</div>
                      <p className="text-gray-700">Direct connections with all major Vietnamese airlines and 450+ international carriers</p>
                    </li>
                    <li>
                      <div className="font-medium text-blue-600">Hotels</div>
                      <p className="text-gray-700">12,000+ domestic and 1,000,000+ international properties with negotiated corporate rates</p>
                    </li>
                    <li>
                      <div className="font-medium text-blue-600">Transportation</div>
                      <p className="text-gray-700">Car rentals, airport transfers, and ground transportation options</p>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-4 text-lg">Booking Experience</h4>
                  <ul className="space-y-4">
                    <li>
                      <div className="font-medium text-blue-600">Multi-Platform Access</div>
                      <p className="text-gray-700">Book via web portal, mobile app, or through your dedicated travel coordinator</p>
                    </li>
                    <li>
                      <div className="font-medium text-blue-600">Traveler Profiles</div>
                      <p className="text-gray-700">Save preferences, loyalty memberships, and documents for quick booking</p>
                    </li>
                    <li>
                      <div className="font-medium text-blue-600">Trip Planning</div>
                      <p className="text-gray-700">Create comprehensive itineraries including meetings and activities</p>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-700 to-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to transform your business travel management?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Join over 1,500 corporations that trust ABN TMS to streamline their travel operations and save costs.
          </p>
          <div className="flex justify-center gap-4">
                        <Button className="bg-white text-blue-800 hover:bg-gray-100" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>
              Request a Demo
            </Button>
                        <Button className="bg-transparent border border-white hover:bg-white/10" onClick={() => window.location.href = '/platforms/travel/tms'}>
              Learn More
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
} 