// Client-side data hooks for TMS
'use client';

import { useState, useEffect } from 'react';

// Static data for client-side usage
// In a real app, this could be loaded from API endpoints

export const useTMSData = () => {
  const [data, setData] = useState({
    company: {
      companyInfo: {
        name: "ABN TMS",
        fullName: "ABN Travel Management Solution",
        description: "The sole platform in Vietnam offering an online booking system exclusively designed for business customers to self-manage and reserve services",
        history: [
          {
            date: "December 2020",
            milestone: "Successfully organized an event to introduce the ABN TMS technology platform exclusively for Business Customers, with the presence of over 500 partner businesses"
          }
        ],
        mission: "To pioneer a product that aids in the digital transformation of businesses in the field of business travel and tourism",
        focus: "Business Customers (B2B) and delivering the most optimal digital transformation solution to help businesses save costs and resources in travel management"
      }
    },
    platformInfo: {
      stats: {
        corporateCustomers: "1,500+",
        internationalHotels: "1M+",
        domesticHotels: "12K+",
        airMiles: "400M+",
        customersServed: "3M+",
        ticketsSold: "300K+"
      },
      solutions: {
        employees: [
          "Easy booking of flights and hotels",
          "Mobile app access anywhere",
          "Automatic expense reporting"
        ],
        managers: [
          "Approval workflow management",
          "Budget control and policies",
          "Detailed reporting and analytics"
        ],
        company: [
          "Cost optimization and savings",
          "Centralized travel management",
          "Policy compliance enforcement"
        ]
      },
      features: {
        reporting: [
          {
            name: "Report",
            description: "Tracking & transparent access to all orders & expenses."
          },
          {
            name: "Advanced Report",
            description: "ABN compiles and analyzes reports at the end of each month, sending them to the business."
          },
          {
            name: "Dashboard",
            description: "Real-time access to employee work situation analysis reports."
          }
        ],
        integrations: [
          {
            name: "System Integration",
            description: "Effective data management & concentration by integrating internal systems with ABN TMS."
          },
          {
            name: "Single Sign-On (SSO)",
            description: "Grant quick and secure access for employees through SSO with SAML standards."
          }
        ]
      }
    },
    partners: {
      airline: [
        {
          name: "Vietnam Airlines",
          rank: "Top 20 best partners of Vietnam Airlines"
        },
        {
          name: "Bamboo Airways", 
          rank: "Top 10 best partners of Bamboo Airways"
        },
        {
          name: "Vietjet Air",
          rank: "Top 15 best partners of Vietjet Air"
        }
      ],
      hotel: [
        {
          type: "domestic",
          count: "12,000+",
          description: "domestic hotels"
        },
        {
          type: "international", 
          count: "1,000,000+",
          description: "international hotels"
        }
      ],
      payment: [
        "Visa",
        "Momo",
        "Zalopay", 
        "Shopee pay",
        "Other banks"
      ]
    },
    marketing: {
      offers: [
        {
          title: "Free TMS System Installation",
          description: "Get the TMS system installed and used for free, worth up to 100,000,000 VND when you sign up today",
          value: "100,000,000 VND"
        },
        {
          title: "Special Corporate Rates",
          description: "Special room rates and ticket discounts exclusively for corporate customer"
        },
        {
          title: "Free First Year Maintenance", 
          description: "Maintenance fee for ABN TMS is free for the first year"
        }
      ],
      clients: {
        total: "1,500+",
        description: "ABN TMS is currently serving over 1500 corporate clients and has successfully implemented TMS systems for large domestic and international companies",
        examples: [
          "Unilever",
          "TH True Milk", 
          "Kimberly-Clark",
          "KIDO",
          "Decathlon",
          "F88",
          "Ahamove",
          "Kone",
          "Hayat"
        ],
        international: {
          countries: [
            "Germany",
            "Singapore", 
            "Thailand",
            "Japan"
          ]
        }
      }
    }
  });

  return data;
};
