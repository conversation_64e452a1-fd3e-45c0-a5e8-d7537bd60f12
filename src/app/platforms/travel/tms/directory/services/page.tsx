'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
}

interface Service {
  id: string;
  name: string;
  description: string;
  category: string;
  features: string[];
  image: string;
  popular: boolean;
}

const categories: ServiceCategory[] = [
  { id: 'all', name: 'All Services', icon: '🌐' },
  { id: 'flight', name: 'Flight Services', icon: '✈️' },
  { id: 'hotel', name: 'Hotel Services', icon: '🏨' },
  { id: 'transportation', name: 'Transportation', icon: '🚕' },
  { id: 'packages', name: 'Travel Packages', icon: '📦' },
  { id: 'management', name: 'Management Tools', icon: '⚙️' },
  { id: 'support', name: 'Support Services', icon: '🛎️' },
];

const services: Service[] = [
  {
    id: '1',
    name: 'Domestic Flight Booking',
    description: 'Book flights across all major Vietnamese airlines with corporate rates and policies.',
    category: 'flight',
    features: [
      'Corporate rates with Vietnam Airlines, Bamboo Airways, and Vietjet',
      'Policy compliance enforcement',
      'Flexible booking and cancellation',
      '24/7 flight support',
    ],
    image: '/services/domestic-flights.jpg',
    popular: true,
  },
  {
    id: '2',
    name: 'International Flight Booking',
    description: 'Access to global airlines with special corporate fares and travel policy integration.',
    category: 'flight',
    features: [
      'Access to 900+ airlines worldwide',
      'Corporate fare negotiation',
      'Multi-city booking options',
      'Class upgrade management',
    ],
    image: '/services/international-flights.jpg',
    popular: true,
  },
  {
    id: '3',
    name: 'Domestic Hotel Booking',
    description: 'Book accommodations at over 12,000 hotels across Vietnam with corporate rates.',
    category: 'hotel',
    features: [
      'Access to 12,000+ domestic hotels',
      'Corporate rate agreements',
      'Last-minute booking options',
      'Policy-compliant recommendations',
    ],
    image: '/services/domestic-hotels.jpg',
    popular: true,
  },
  {
    id: '4',
    name: 'International Hotel Booking',
    description: 'Access to over 1 million international hotels worldwide with competitive rates.',
    category: 'hotel',
    features: [
      'Access to 1M+ international hotels',
      'Corporate loyalty programs',
      'Price guarantee',
      'Verified reviews from business travelers',
    ],
    image: '/services/international-hotels.jpg',
    popular: false,
  },
  {
    id: '5',
    name: 'Airport Transfer',
    description: 'Door-to-airport and airport-to-hotel transfer services in major cities.',
    category: 'transportation',
    features: [
      'Pre-booked airport transfers',
      'Corporate billing options',
      'Flight tracking',
      'Premium vehicle options',
    ],
    image: '/services/airport-transfer.jpg',
    popular: false,
  },
  {
    id: '6',
    name: 'Car Rental',
    description: 'Corporate car rental services with or without drivers across Vietnam and globally.',
    category: 'transportation',
    features: [
      'Self-drive options',
      'Chauffeur services',
      'Long-term rental agreements',
      'Transparent pricing',
    ],
    image: '/services/car-rental.jpg',
    popular: false,
  },
  {
    id: '7',
    name: 'MICE Services',
    description: 'Full-service solutions for Meetings, Incentives, Conferences, and Exhibitions.',
    category: 'packages',
    features: [
      'Venue selection assistance',
      'Group travel arrangements',
      'Event planning support',
      'Corporate retreat packages',
    ],
    image: '/services/mice.jpg',
    popular: true,
  },
  {
    id: '8',
    name: 'Expense Management',
    description: 'Comprehensive tools for tracking, reporting, and managing business travel expenses.',
    category: 'management',
    features: [
      'Automated expense reporting',
      'Receipt capture',
      'Approval workflows',
      'Budget tracking',
    ],
    image: '/services/expense-management.jpg',
    popular: true,
  },
  {
    id: '9',
    name: 'Travel Policy Compliance',
    description: 'Tools to create, enforce, and manage corporate travel policies effectively.',
    category: 'management',
    features: [
      'Policy builder tools',
      'Automated compliance checks',
      'Exception management',
      'Policy performance analytics',
    ],
    image: '/services/policy-compliance.jpg',
    popular: false,
  },
  {
    id: '10',
    name: 'Visa Application Support',
    description: 'Assistance with business visa applications for international corporate travel.',
    category: 'support',
    features: [
      'Document preparation',
      'Application tracking',
      'Express processing options',
      'Visa requirement guidance',
    ],
    image: '/services/visa-support.jpg',
    popular: false,
  },
  {
    id: '11',
    name: '24/7 Travel Support',
    description: 'Round-the-clock assistance for all business travel needs and emergencies.',
    category: 'support',
    features: [
      'Dedicated corporate support line',
      'Itinerary changes assistance',
      'Emergency support',
      'Multi-lingual agents',
    ],
    image: '/services/travel-support.jpg',
    popular: true,
  },
  {
    id: '12',
    name: 'Corporate Travel Packages',
    description: 'Pre-negotiated travel packages for frequent corporate routes and destinations.',
    category: 'packages',
    features: [
      'Flight + hotel bundles',
      'Group travel packages',
      'Long-term stay options',
      'Customizable itineraries',
    ],
    image: '/services/travel-packages.jpg',
    popular: false,
  },
];

export default function ServiceDirectoryPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredServices = services.filter(service => {
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          service.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  const popularServices = filteredServices.filter(service => service.popular);
  const otherServices = filteredServices.filter(service => !service.popular);

  return (
    <div className="container mx-auto py-10 px-4">
      <div className="text-center mb-12">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Vntrip TMS Services Directory</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Discover our comprehensive range of corporate travel services designed to streamline your business travel needs.
        </p>
      </div>

      {/* Category Nav */}
      <div className="flex flex-wrap gap-2 justify-center mb-8">
        {categories.map(category => (
          <button
            key={category.id}
            className={`flex items-center px-4 py-2 rounded-full ${
              selectedCategory === category.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            onClick={() => setSelectedCategory(category.id)}
          >
            <span className="mr-2">{category.icon}</span>
            {category.name}
          </button>
        ))}
      </div>

      {/* Search Bar */}
      <div className="max-w-2xl mx-auto mb-10">
        <div className="relative">
          <input
            type="text"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pl-10"
            placeholder="Search for services..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* No Results Message */}
      {filteredServices.length === 0 && (
        <div className="text-center py-12 bg-gray-50 rounded-lg mb-10">
          <p className="text-gray-500 text-lg">No services found matching your search criteria.</p>
        </div>
      )}

      {/* Popular Services Section */}
      {popularServices.length > 0 && (
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 pb-2 border-b">Popular Business Travel Services</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popularServices.map(service => (
              <div key={service.id} className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow duration-300">
                <div className="h-48 bg-gray-200 relative">
                  {/* In a real app, this would be an actual image */}
                  <div className="absolute inset-0 flex items-center justify-center bg-blue-100">
                    <span className="text-3xl">{getCategoryIcon(service.category)}</span>
                  </div>
                  <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                    Popular
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  
                  <h4 className="font-semibold text-gray-700 mb-2">Key Features:</h4>
                  <ul className="space-y-1 mb-4">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-500 mr-2">✓</span>
                        <span className="text-sm text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    Learn More
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Other Services Section */}
      {otherServices.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6 pb-2 border-b">Additional Services</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {otherServices.map(service => (
              <div key={service.id} className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:shadow-md transition-shadow duration-300">
                <div className="h-40 bg-gray-200 relative">
                  {/* In a real app, this would be an actual image */}
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                    <span className="text-2xl">{getCategoryIcon(service.category)}</span>
                  </div>
                </div>
                
                <div className="p-4">
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-sm text-gray-600 mb-3">{service.description}</p>
                  
                  <Button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Contact Section */}
      <div className="mt-16 bg-blue-50 rounded-lg p-8 text-center">
        <h3 className="text-2xl font-bold text-blue-800 mb-4">Need a Custom Travel Solution?</h3>
        <p className="text-lg text-gray-700 mb-6 max-w-3xl mx-auto">
          Our team can create tailored travel management solutions to meet your specific business requirements.
        </p>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 text-lg" onClick={() => window.location.href = '/platforms/travel/tms/demo'}>
          Contact Our Sales Team
        </Button>
      </div>
    </div>
  );
}

// Helper function to get category icon
function getCategoryIcon(categoryId: string): string {
  const category = categories.find(c => c.id === categoryId);
  return category ? category.icon : '🌐';
} 