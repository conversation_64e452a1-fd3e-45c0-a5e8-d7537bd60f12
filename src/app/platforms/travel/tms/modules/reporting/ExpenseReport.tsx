'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

// Types
interface Expense {
  id: string;
  date: string;
  category: 'flight' | 'hotel' | 'meals' | 'transport' | 'other';
  description: string;
  amount: number;
  status: 'pending' | 'approved' | 'rejected';
  paymentMethod: 'corporate' | 'personal';
  receipt?: string;
  approver?: string;
  comments?: string;
}

interface TripReport {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  destination: string;
  purpose: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  totalAmount: number;
  expenses: Expense[];
  submittedBy: string;
  submittedDate?: string;
  approvedBy?: string;
  approvedDate?: string;
}

// Mock data
const mockExpenses: Expense[] = [
  {
    id: 'exp1',
    date: '2023-08-15',
    category: 'flight',
    description: 'Flight from Hanoi to Ho Chi Minh City',
    amount: 1200000,
    status: 'approved',
    paymentMethod: 'corporate',
    receipt: 'receipt-1.jpg',
    approver: '<PERSON><PERSON><PERSON>',
  },
  {
    id: 'exp2',
    date: '2023-08-15',
    category: 'hotel',
    description: 'Hotel New World Saigon - 3 nights',
    amount: 8400000,
    status: 'approved',
    paymentMethod: 'corporate',
    receipt: 'receipt-2.jpg',
    approver: 'Nguyen Van A',
  },
  {
    id: 'exp3',
    date: '2023-08-16',
    category: 'meals',
    description: 'Business dinner with client',
    amount: 1500000,
    status: 'approved',
    paymentMethod: 'personal',
    receipt: 'receipt-3.jpg',
    approver: 'Nguyen Van A',
  },
  {
    id: 'exp4',
    date: '2023-08-17',
    category: 'transport',
    description: 'Taxi to meeting',
    amount: 350000,
    status: 'approved',
    paymentMethod: 'personal',
    receipt: 'receipt-4.jpg',
    approver: 'Nguyen Van A',
  },
];

const mockReports: TripReport[] = [
  {
    id: 'trip1',
    title: 'Client Meeting - HCMC Aug 2023',
    startDate: '2023-08-15',
    endDate: '2023-08-18',
    destination: 'Ho Chi Minh City',
    purpose: 'Client meetings and product presentation',
    status: 'approved',
    totalAmount: 11450000,
    expenses: mockExpenses,
    submittedBy: 'Tran Thi B',
    submittedDate: '2023-08-20',
    approvedBy: 'Nguyen Van A',
    approvedDate: '2023-08-22',
  },
  {
    id: 'trip2',
    title: 'Da Nang Conference',
    startDate: '2023-09-05',
    endDate: '2023-09-08',
    destination: 'Da Nang',
    purpose: 'Annual industry conference',
    status: 'submitted',
    totalAmount: 9800000,
    expenses: [
      {
        id: 'exp5',
        date: '2023-09-05',
        category: 'flight',
        description: 'Flight from Hanoi to Da Nang',
        amount: 1500000,
        status: 'pending',
        paymentMethod: 'corporate',
        receipt: 'receipt-5.jpg',
      },
      {
        id: 'exp6',
        date: '2023-09-05',
        category: 'hotel',
        description: 'Hyatt Regency Da Nang - 3 nights',
        amount: 7500000,
        status: 'pending',
        paymentMethod: 'corporate',
        receipt: 'receipt-6.jpg',
      },
      {
        id: 'exp7',
        date: '2023-09-06',
        category: 'meals',
        description: 'Dinner',
        amount: 800000,
        status: 'pending',
        paymentMethod: 'personal',
        receipt: 'receipt-7.jpg',
      },
    ],
    submittedBy: 'Tran Thi B',
    submittedDate: '2023-09-10',
  },
  {
    id: 'trip3',
    title: 'Singapore Business Trip',
    startDate: '2023-10-10',
    endDate: '2023-10-15',
    destination: 'Singapore',
    purpose: 'Partnership negotiations',
    status: 'draft',
    totalAmount: 15000000,
    expenses: [],
    submittedBy: 'Tran Thi B',
  },
];

export default function ExpenseReport() {
  const [reports, setReports] = useState<TripReport[]>(mockReports);
  const [selectedReport, setSelectedReport] = useState<TripReport | null>(null);
  const [activeView, setActiveView] = useState<'list' | 'detail'>('list');
  const [newExpense, setNewExpense] = useState<Partial<Expense>>({
    category: 'flight',
    date: new Date().toISOString().split('T')[0],
    paymentMethod: 'corporate',
  });
  const [showNewExpenseForm, setShowNewExpenseForm] = useState(false);

  // Format price to VND
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
  };

  // Handle view report detail
  const handleViewReport = (report: TripReport) => {
    setSelectedReport(report);
    setActiveView('detail');
  };

  // Handle back to list view
  const handleBackToList = () => {
    setSelectedReport(null);
    setActiveView('list');
    setShowNewExpenseForm(false);
  };

  // Handle new expense input change
  const handleNewExpenseChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewExpense({
      ...newExpense,
      [name]: name === 'amount' ? parseInt(value, 10) : value,
    });
  };

  // Handle adding a new expense
  const handleAddExpense = () => {
    if (selectedReport && newExpense.description && newExpense.amount) {
      const updatedReport = { ...selectedReport };
      
      const newExpenseItem: Expense = {
        id: `exp-${Date.now()}`,
        date: newExpense.date || new Date().toISOString().split('T')[0],
        category: newExpense.category as 'flight' | 'hotel' | 'meals' | 'transport' | 'other',
        description: newExpense.description || '',
        amount: newExpense.amount as number,
        status: 'pending',
        paymentMethod: newExpense.paymentMethod as 'corporate' | 'personal',
        receipt: newExpense.receipt,
      };
      
      updatedReport.expenses = [...updatedReport.expenses, newExpenseItem];
      updatedReport.totalAmount = updatedReport.expenses.reduce((sum, exp) => sum + exp.amount, 0);
      
      setReports(reports.map(r => r.id === selectedReport.id ? updatedReport : r));
      setSelectedReport(updatedReport);
      setNewExpense({
        category: 'flight',
        date: new Date().toISOString().split('T')[0],
        paymentMethod: 'corporate',
      });
      setShowNewExpenseForm(false);
    }
  };

  // Handle submit report
  const handleSubmitReport = () => {
    if (selectedReport) {
      const updatedReport = { 
        ...selectedReport,
        status: 'submitted' as const,
        submittedDate: new Date().toISOString().split('T')[0],
      };
      
      setReports(reports.map(r => r.id === selectedReport.id ? updatedReport : r));
      setSelectedReport(updatedReport);
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch(status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Render the expense category icon
  const renderCategoryIcon = (category: string) => {
    switch(category) {
      case 'flight':
        return '✈️';
      case 'hotel':
        return '🏨';
      case 'meals':
        return '🍽️';
      case 'transport':
        return '🚕';
      default:
        return '📋';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      {activeView === 'list' ? (
        <>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-blue-800">Expense Reports</h2>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              Create New Report
            </Button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Report Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date Range
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Destination
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reports.map(report => (
                  <tr key={report.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{report.title}</div>
                      <div className="text-sm text-gray-500">{report.purpose}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{report.startDate}</div>
                      <div className="text-sm text-gray-500">to {report.endDate}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {report.destination}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(report.status)}`}>
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatPrice(report.totalAmount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button 
                        className="text-blue-600 hover:text-blue-900 mr-4"
                        onClick={() => handleViewReport(report)}
                      >
                        View
                      </button>
                      {report.status === 'draft' && (
                        <button className="text-green-600 hover:text-green-900">
                          Edit
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      ) : (
        <>
          {selectedReport && (
            <div>
              <div className="mb-6">
                <button 
                  className="inline-flex items-center text-blue-600 hover:text-blue-800"
                  onClick={handleBackToList}
                >
                  <svg className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Reports
                </button>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-2xl font-bold text-blue-800 mb-2">{selectedReport.title}</h2>
                    <p className="text-gray-600">{selectedReport.purpose}</p>
                    
                    <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Date Range:</span> {selectedReport.startDate} to {selectedReport.endDate}
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Destination:</span> {selectedReport.destination}
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Submitted By:</span> {selectedReport.submittedBy}
                      </div>
                      {selectedReport.submittedDate && (
                        <div>
                          <span className="font-medium text-gray-700">Submitted On:</span> {selectedReport.submittedDate}
                        </div>
                      )}
                      {selectedReport.approvedBy && (
                        <div>
                          <span className="font-medium text-gray-700">Approved By:</span> {selectedReport.approvedBy}
                        </div>
                      )}
                      {selectedReport.approvedDate && (
                        <div>
                          <span className="font-medium text-gray-700">Approved On:</span> {selectedReport.approvedDate}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getStatusBadgeClass(selectedReport.status)}`}>
                      {selectedReport.status.charAt(0).toUpperCase() + selectedReport.status.slice(1)}
                    </span>
                    <div className="mt-2 text-right">
                      <div className="text-2xl font-bold text-blue-600">{formatPrice(selectedReport.totalAmount)}</div>
                      <div className="text-sm text-gray-500">Total Expenses</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold">Expense Items</h3>
                {selectedReport.status === 'draft' && (
                  <Button 
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                    onClick={() => setShowNewExpenseForm(true)}
                    disabled={showNewExpenseForm}
                  >
                    Add Expense
                  </Button>
                )}
              </div>
              
              {/* New Expense Form */}
              {showNewExpenseForm && (
                <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                  <h4 className="font-semibold mb-3">Add New Expense</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date
                      </label>
                      <input
                        type="date"
                        name="date"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newExpense.date}
                        onChange={handleNewExpenseChange}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Category
                      </label>
                      <select
                        name="category"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newExpense.category}
                        onChange={handleNewExpenseChange}
                      >
                        <option value="flight">Flight</option>
                        <option value="hotel">Hotel</option>
                        <option value="meals">Meals & Entertainment</option>
                        <option value="transport">Transportation</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Method
                      </label>
                      <select
                        name="paymentMethod"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newExpense.paymentMethod}
                        onChange={handleNewExpenseChange}
                      >
                        <option value="corporate">Corporate Card</option>
                        <option value="personal">Personal (Reimbursable)</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <input
                      type="text"
                      name="description"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={newExpense.description || ''}
                      onChange={handleNewExpenseChange}
                      placeholder="e.g., Flight from Hanoi to HCMC"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Amount (VND)
                      </label>
                      <input
                        type="number"
                        name="amount"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newExpense.amount || ''}
                        onChange={handleNewExpenseChange}
                        placeholder="e.g., 1200000"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Receipt (Optional)
                      </label>
                      <input
                        type="text"
                        name="receipt"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newExpense.receipt || ''}
                        onChange={handleNewExpenseChange}
                        placeholder="e.g., receipt-123.jpg"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <Button 
                      className="bg-gray-200 hover:bg-gray-300 text-gray-800"
                      onClick={() => setShowNewExpenseForm(false)}
                    >
                      Cancel
                    </Button>
                    <Button 
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      onClick={handleAddExpense}
                      disabled={!newExpense.description || !newExpense.amount}
                    >
                      Add Expense
                    </Button>
                  </div>
                </div>
              )}
              
              {/* Expense List */}
              {selectedReport.expenses.length > 0 ? (
                <div className="space-y-4">
                  {selectedReport.expenses.map(expense => (
                    <div 
                      key={expense.id} 
                      className="p-4 border rounded-lg bg-white hover:bg-gray-50 transition"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start">
                          <div className="text-2xl mr-4">
                            {renderCategoryIcon(expense.category)}
                          </div>
                          
                          <div>
                            <div className="font-semibold">{expense.description}</div>
                            <div className="text-sm text-gray-500">{expense.date}</div>
                            
                            <div className="mt-2 flex items-center gap-3 text-sm">
                              <span className={`px-2 py-0.5 rounded ${expense.paymentMethod === 'corporate' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
                                {expense.paymentMethod === 'corporate' ? 'Corporate Card' : 'Personal Reimbursement'}
                              </span>
                              
                              <span className={`px-2 py-0.5 rounded ${
                                expense.status === 'approved' ? 'bg-green-100 text-green-800' :
                                expense.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {expense.status.charAt(0).toUpperCase() + expense.status.slice(1)}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="font-bold text-lg text-blue-600">{formatPrice(expense.amount)}</div>
                          
                          {expense.receipt && (
                            <button className="text-sm text-blue-600 hover:text-blue-800 mt-1">
                              View Receipt
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-8 bg-gray-50 rounded-lg text-center">
                  <p className="text-gray-500">No expenses added yet. Click "Add Expense" to get started.</p>
                </div>
              )}
              
              {/* Action Buttons */}
              <div className="mt-8 flex justify-end gap-3">
                {selectedReport.status === 'draft' && (
                  <Button 
                    className="bg-blue-600 hover:bg-blue-700 text-white" 
                    onClick={handleSubmitReport}
                    disabled={selectedReport.expenses.length === 0}
                  >
                    Submit Report
                  </Button>
                )}
                
                {selectedReport.status === 'submitted' && (
                  <>
                    <Button className="bg-red-600 hover:bg-red-700 text-white">
                      Reject
                    </Button>
                    <Button className="bg-green-600 hover:bg-green-700 text-white">
                      Approve
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
} 