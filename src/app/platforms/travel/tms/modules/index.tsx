'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useTMSData } from '../hooks/useTMSData';

// Components (imported when needed)
import BookingIndex from './booking';
import TravelPolicy from './policy/TravelPolicy';
import ExpenseReport from './reporting/ExpenseReport';
import SystemIntegration from './integration/SystemIntegration';

export default function ModulesIndex() {
  const [activeModule, setActiveModule] = useState<string | null>(null);
  const data = useTMSData();

  const componentMapping = {
    booking: BookingIndex,
    policy: TravelPolicy,
    reporting: ExpenseReport,
    integration: SystemIntegration,
  };

  const modules = data.modules.modules.map(module => ({
    ...module,
    component: componentMapping[module.id as keyof typeof componentMapping],
  }));

  const renderActiveModule = () => {
    const module = modules.find(m => m.id === activeModule);
    if (!module) return null;
    
    const ModuleComponent = module.component;
    return <ModuleComponent />;
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {!activeModule ? (
        <div>
          <h1 className="text-3xl font-bold text-blue-800 mb-8">Travel Management System Modules</h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {modules.map(module => (
              <div 
                key={module.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer"
                onClick={() => setActiveModule(module.id)}
              >
                <div className="p-6">
                  <div className="text-4xl mb-4">{module.icon}</div>
                  <h2 className="text-xl font-bold text-blue-700 mb-2">{module.name}</h2>
                  <p className="text-gray-600 mb-4">{module.description}</p>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white w-full">
                    Open Module
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-12 bg-blue-50 p-6 rounded-lg">
            <h2 className="text-xl font-bold text-blue-800 mb-3">Getting Started</h2>
            <p className="text-gray-700 mb-4">
              The Travel Management System (TMS) helps streamline business travel processes from booking to expense reporting.
              Select a module above to get started, or learn more about the system capabilities below.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h3 className="font-bold text-blue-700 mb-2">Need Help?</h3>
                <p className="text-gray-600 mb-3">
                  Our comprehensive documentation can help you navigate the TMS system effectively.
                </p>
                <Button className="bg-gray-200 hover:bg-gray-300 text-gray-800">
                  View Documentation
                </Button>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h3 className="font-bold text-blue-700 mb-2">Watch Tutorial</h3>
                <p className="text-gray-600 mb-3">
                  New to TMS? Watch our tutorial videos to learn how to use the system.
                </p>
                <Button className="bg-gray-200 hover:bg-gray-300 text-gray-800">
                  Watch Tutorials
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div>
          <div className="mb-6">
            <button 
              className="inline-flex items-center text-blue-600 hover:text-blue-800"
              onClick={() => setActiveModule(null)}
            >
              <svg className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Modules
            </button>
          </div>
          
          {renderActiveModule()}
        </div>
      )}
    </div>
  );
} 