'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

// Type definitions
interface FlightSearchParams {
  origin: string;
  destination: string;
  departDate: string;
  returnDate?: string;
  passengers: number;
  cabinClass: 'economy' | 'premium' | 'business' | 'first';
  isRoundTrip: boolean;
}

interface Airline {
  id: string;
  name: string;
  logo: string;
}

interface Flight {
  id: string;
  airline: Airline;
  flightNumber: string;
  origin: string;
  destination: string;
  departureTime: string;
  arrivalTime: string;
  duration: string;
  price: number;
  cabinClass: string;
  availableSeats: number;
}

// Mock data
const mockAirlines: Airline[] = [
  { id: '1', name: 'Vietnam Airlines', logo: '/vietnam-airlines.png' },
  { id: '2', name: 'Bamboo Airways', logo: '/bamboo-airways.png' },
  { id: '3', name: 'Vietjet Air', logo: '/vietjet.png' },
];

const mockFlights: Flight[] = [
  {
    id: '1',
    airline: mockAirlines[0],
    flightNumber: 'VN123',
    origin: 'HAN',
    destination: 'SGN',
    departureTime: '08:00',
    arrivalTime: '10:15',
    duration: '2h 15m',
    price: 1200000,
    cabinClass: 'economy',
    availableSeats: 24,
  },
  {
    id: '2',
    airline: mockAirlines[1],
    flightNumber: 'QH456',
    origin: 'HAN',
    destination: 'SGN',
    departureTime: '10:30',
    arrivalTime: '12:45',
    duration: '2h 15m',
    price: 1350000,
    cabinClass: 'economy',
    availableSeats: 18,
  },
  {
    id: '3',
    airline: mockAirlines[2],
    flightNumber: 'VJ789',
    origin: 'HAN',
    destination: 'SGN',
    departureTime: '14:15',
    arrivalTime: '16:30',
    duration: '2h 15m',
    price: 990000,
    cabinClass: 'economy',
    availableSeats: 32,
  },
];

export default function FlightBooking() {
  const [searchParams, setSearchParams] = useState<FlightSearchParams>({
    origin: '',
    destination: '',
    departDate: '',
    returnDate: '',
    passengers: 1,
    cabinClass: 'economy',
    isRoundTrip: false,
  });

  const [searchResults, setSearchResults] = useState<Flight[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedFlight, setSelectedFlight] = useState<string | null>(null);

  // Common Vietnamese airports
  const airports = [
    { code: 'HAN', name: 'Hanoi (Noi Bai)' },
    { code: 'SGN', name: 'Ho Chi Minh City (Tan Son Nhat)' },
    { code: 'DAD', name: 'Da Nang' },
    { code: 'CXR', name: 'Nha Trang (Cam Ranh)' },
    { code: 'PQC', name: 'Phu Quoc' },
    { code: 'HUI', name: 'Hue (Phu Bai)' },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSearchParams({
      ...searchParams,
      [name]: value,
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSearchParams({
      ...searchParams,
      [name]: checked,
    });
  };

  const handleSearch = () => {
    // In a real app, this would call an API
    setSearchResults(mockFlights);
    setHasSearched(true);
  };

  const handleFlightSelection = (flightId: string) => {
    setSelectedFlight(flightId);
  };

  const handleBookFlight = () => {
    // In a real app, this would proceed to the booking confirmation page
    alert('Flight booking successful! Your booking reference will be sent to your email.');
  };

  // Format price to VND
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-blue-800">Flight Booking</h2>
      
      {/* Search Form */}
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="origin" className="block text-sm font-medium text-gray-700 mb-1">
              Origin
            </label>
            <select
              id="origin"
              name="origin"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.origin}
              onChange={handleInputChange}
            >
              <option value="">Select Origin</option>
              {airports.map(airport => (
                <option key={airport.code} value={airport.code}>
                  {airport.name} ({airport.code})
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="destination" className="block text-sm font-medium text-gray-700 mb-1">
              Destination
            </label>
            <select
              id="destination"
              name="destination"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.destination}
              onChange={handleInputChange}
            >
              <option value="">Select Destination</option>
              {airports.map(airport => (
                <option key={airport.code} value={airport.code}>
                  {airport.name} ({airport.code})
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label htmlFor="departDate" className="block text-sm font-medium text-gray-700 mb-1">
              Departure Date
            </label>
            <input
              type="date"
              id="departDate"
              name="departDate"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.departDate}
              onChange={handleInputChange}
            />
          </div>
          
          {searchParams.isRoundTrip && (
            <div>
              <label htmlFor="returnDate" className="block text-sm font-medium text-gray-700 mb-1">
                Return Date
              </label>
              <input
                type="date"
                id="returnDate"
                name="returnDate"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchParams.returnDate}
                onChange={handleInputChange}
              />
            </div>
          )}
          
          <div>
            <label htmlFor="passengers" className="block text-sm font-medium text-gray-700 mb-1">
              Passengers
            </label>
            <input
              type="number"
              id="passengers"
              name="passengers"
              min="1"
              max="9"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.passengers}
              onChange={handleInputChange}
            />
          </div>
          
          <div>
            <label htmlFor="cabinClass" className="block text-sm font-medium text-gray-700 mb-1">
              Cabin Class
            </label>
            <select
              id="cabinClass"
              name="cabinClass"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.cabinClass}
              onChange={handleInputChange as any}
            >
              <option value="economy">Economy</option>
              <option value="premium">Premium Economy</option>
              <option value="business">Business</option>
              <option value="first">First Class</option>
            </select>
          </div>
        </div>
        
        <div className="flex items-center mb-4">
          <input
            type="checkbox"
            id="isRoundTrip"
            name="isRoundTrip"
            className="mr-2"
            checked={searchParams.isRoundTrip}
            onChange={handleCheckboxChange}
          />
          <label htmlFor="isRoundTrip" className="text-sm font-medium text-gray-700">
            Round Trip
          </label>
        </div>
        
        <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={handleSearch}>
          Search Flights
        </Button>
      </div>
      
      {/* Search Results */}
      {hasSearched && (
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-800">Available Flights</h3>
          
          {searchResults.length > 0 ? (
            <div className="space-y-4">
              {searchResults.map(flight => (
                <div 
                  key={flight.id} 
                  className={`p-4 border rounded-lg ${selectedFlight === flight.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                  onClick={() => handleFlightSelection(flight.id)}
                >
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                    <div className="flex items-center mb-2 md:mb-0">
                      <div className="w-12 h-12 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                        {/* In a real app, you'd use the actual airline logo */}
                        <span className="font-bold">{flight.airline.name.substring(0, 2)}</span>
                      </div>
                      <div>
                        <div className="font-semibold">{flight.airline.name}</div>
                        <div className="text-sm text-gray-500">Flight {flight.flightNumber}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="font-semibold">{flight.departureTime}</div>
                        <div className="text-sm">{flight.origin}</div>
                      </div>
                      
                      <div className="text-center flex flex-col items-center">
                        <div className="text-xs text-gray-500">{flight.duration}</div>
                        <div className="w-16 h-px bg-gray-300 my-1"></div>
                        <div className="text-xs text-gray-500">Direct</div>
                      </div>
                      
                      <div className="text-center">
                        <div className="font-semibold">{flight.arrivalTime}</div>
                        <div className="text-sm">{flight.destination}</div>
                      </div>
                    </div>
                    
                    <div className="mt-4 md:mt-0">
                      <div className="font-bold text-lg text-blue-600">{formatPrice(flight.price)}</div>
                      <div className="text-sm text-gray-500">{flight.cabinClass}</div>
                    </div>
                  </div>
                </div>
              ))}
              
              <div className="mt-6 text-right">
                <Button 
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={handleBookFlight}
                  disabled={!selectedFlight}
                >
                  Book Selected Flight
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-6 bg-gray-50 rounded-lg text-center">
              <p className="text-gray-700">No flights found for your search criteria. Please try different dates or destinations.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 