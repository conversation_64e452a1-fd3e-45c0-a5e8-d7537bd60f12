'use client';

import { useState } from 'react';
import FlightBooking from './FlightBooking';
import HotelBooking from './HotelBooking';

export default function BookingIndex() {
  const [activeTab, setActiveTab] = useState<'flights' | 'hotels'>('flights');

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          className={`py-4 px-6 text-sm font-medium ${
            activeTab === 'flights'
              ? 'bg-white text-blue-600 border-b-2 border-blue-600'
              : 'bg-gray-50 text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('flights')}
        >
          Flight Booking
        </button>
        <button
          className={`py-4 px-6 text-sm font-medium ${
            activeTab === 'hotels'
              ? 'bg-white text-blue-600 border-b-2 border-blue-600'
              : 'bg-gray-50 text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('hotels')}
        >
          Hotel Booking
        </button>
      </div>

      {/* Tab Content */}
      <div className="p-4">
        {activeTab === 'flights' ? <FlightBooking /> : <HotelBooking />}
      </div>
    </div>
  );
} 