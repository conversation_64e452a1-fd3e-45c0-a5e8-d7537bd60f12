'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

// Type definitions
interface HotelSearchParams {
  destination: string;
  checkIn: string;
  checkOut: string;
  rooms: number;
  adults: number;
  children: number;
  businessFriendly: boolean;
}

interface HotelAmenity {
  id: string;
  name: string;
  icon: string;
}

interface HotelRoom {
  id: string;
  name: string;
  description: string;
  price: number;
  capacity: number;
  bedType: string;
  images: string[];
}

interface Hotel {
  id: string;
  name: string;
  location: string;
  address: string;
  rating: number;
  price: number;
  amenities: HotelAmenity[];
  images: string[];
  description: string;
  roomTypes: HotelRoom[];
}

// Mock data
const mockAmenities: HotelAmenity[] = [
  { id: '1', name: 'Free WiFi', icon: 'wifi' },
  { id: '2', name: 'Swimming Pool', icon: 'pool' },
  { id: '3', name: 'Fitness Center', icon: 'fitness' },
  { id: '4', name: 'Restaurant', icon: 'restaurant' },
  { id: '5', name: 'Spa', icon: 'spa' },
  { id: '6', name: 'Meeting Rooms', icon: 'meeting' },
  { id: '7', name: 'Business Center', icon: 'business' },
  { id: '8', name: 'Airport Shuttle', icon: 'shuttle' },
];

const mockHotels: Hotel[] = [
  {
    id: '1',
    name: 'Vinpearl Resort & Spa',
    location: 'Nha Trang',
    address: '78-80 Tran Phu Street, Nha Trang, Vietnam',
    rating: 5,
    price: 3200000,
    amenities: [
      mockAmenities[0],
      mockAmenities[1],
      mockAmenities[2],
      mockAmenities[3],
      mockAmenities[4],
    ],
    images: ['/hotel1.jpg', '/hotel1-2.jpg', '/hotel1-3.jpg'],
    description: 'Luxury beachfront resort featuring panoramic views of Nha Trang Bay with world-class facilities.',
    roomTypes: [
      {
        id: '101',
        name: 'Deluxe Ocean View',
        description: 'Spacious room with king-size bed and ocean view',
        price: 3200000,
        capacity: 2,
        bedType: 'King',
        images: ['/room1.jpg'],
      },
      {
        id: '102',
        name: 'Executive Suite',
        description: 'Elegant suite with separate living area and premium amenities',
        price: 5400000,
        capacity: 2,
        bedType: 'King',
        images: ['/room2.jpg'],
      },
    ],
  },
  {
    id: '2',
    name: 'InterContinental Hanoi Westlake',
    location: 'Hanoi',
    address: '5 Tu Hoa, Tay Ho District, Hanoi, Vietnam',
    rating: 5,
    price: 4500000,
    amenities: [
      mockAmenities[0],
      mockAmenities[1],
      mockAmenities[2],
      mockAmenities[3],
      mockAmenities[5],
      mockAmenities[6],
    ],
    images: ['/hotel2.jpg', '/hotel2-2.jpg', '/hotel2-3.jpg'],
    description: 'Set on the edge of the picturesque West Lake, the InterContinental Hanoi Westlake provides the perfect balance between peaceful tranquility and urban convenience.',
    roomTypes: [
      {
        id: '201',
        name: 'Deluxe Room',
        description: 'Elegant room with views of the city or lake',
        price: 4500000,
        capacity: 2,
        bedType: 'King or Twin',
        images: ['/room3.jpg'],
      },
      {
        id: '202',
        name: 'Overwater Pavilion',
        description: 'Unique accommodation built on stilts over the waters of West Lake',
        price: 6800000,
        capacity: 2,
        bedType: 'King',
        images: ['/room4.jpg'],
      },
    ],
  },
  {
    id: '3',
    name: 'New World Saigon Hotel',
    location: 'Ho Chi Minh City',
    address: '76 Le Lai Street, District 1, Ho Chi Minh City, Vietnam',
    rating: 4.5,
    price: 2800000,
    amenities: [
      mockAmenities[0],
      mockAmenities[1],
      mockAmenities[2],
      mockAmenities[3],
      mockAmenities[6],
      mockAmenities[7],
    ],
    images: ['/hotel3.jpg', '/hotel3-2.jpg', '/hotel3-3.jpg'],
    description: 'Centrally located in District 1, close to Ben Thanh Market and other major attractions, offering convenient business travel facilities.',
    roomTypes: [
      {
        id: '301',
        name: 'Deluxe Room',
        description: 'Contemporary room with city views',
        price: 2800000,
        capacity: 2,
        bedType: 'King or Twin',
        images: ['/room5.jpg'],
      },
      {
        id: '302',
        name: 'Business Suite',
        description: 'Spacious suite with access to Residence Club benefits',
        price: 4200000,
        capacity: 2,
        bedType: 'King',
        images: ['/room6.jpg'],
      },
    ],
  },
];

// Main component
export default function HotelBooking() {
  const [searchParams, setSearchParams] = useState<HotelSearchParams>({
    destination: '',
    checkIn: '',
    checkOut: '',
    rooms: 1,
    adults: 1,
    children: 0,
    businessFriendly: true, // Default for corporate travel
  });

  const [searchResults, setSearchResults] = useState<Hotel[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedHotel, setSelectedHotel] = useState<string | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null);

  // Common locations in Vietnam
  const locations = [
    'Ho Chi Minh City',
    'Hanoi',
    'Da Nang',
    'Nha Trang',
    'Phu Quoc',
    'Hoi An',
    'Hue',
    'Can Tho',
    'Vung Tau',
    'Ha Long Bay',
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSearchParams({
      ...searchParams,
      [name]: value,
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSearchParams({
      ...searchParams,
      [name]: checked,
    });
  };

  const handleSearch = () => {
    // In a real app, this would call an API
    setSearchResults(mockHotels);
    setHasSearched(true);
    setSelectedHotel(null);
    setSelectedRoom(null);
  };

  const handleHotelSelection = (hotelId: string) => {
    setSelectedHotel(hotelId);
    setSelectedRoom(null);
  };

  const handleRoomSelection = (roomId: string) => {
    setSelectedRoom(roomId);
  };

  const handleBookHotel = () => {
    // In a real app, this would proceed to the booking confirmation page
    alert('Hotel booking successful! Your booking confirmation will be sent to your email.');
  };

  // Format price to VND
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
  };

  // Get selected hotel data
  const getSelectedHotel = (): Hotel | undefined => {
    return searchResults.find(hotel => hotel.id === selectedHotel);
  };

  // Calculate total nights
  const calculateNights = (): number => {
    if (!searchParams.checkIn || !searchParams.checkOut) return 0;
    
    const checkIn = new Date(searchParams.checkIn);
    const checkOut = new Date(searchParams.checkOut);
    
    const diffTime = Math.abs(checkOut.getTime() - checkIn.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-blue-800">Hotel Booking</h2>
      
      {/* Search Form */}
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label htmlFor="destination" className="block text-sm font-medium text-gray-700 mb-1">
              Destination
            </label>
            <select
              id="destination"
              name="destination"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.destination}
              onChange={handleInputChange}
            >
              <option value="">Select Destination</option>
              {locations.map(location => (
                <option key={location} value={location}>
                  {location}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="checkIn" className="block text-sm font-medium text-gray-700 mb-1">
              Check-in Date
            </label>
            <input
              type="date"
              id="checkIn"
              name="checkIn"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.checkIn}
              onChange={handleInputChange}
            />
          </div>
          
          <div>
            <label htmlFor="checkOut" className="block text-sm font-medium text-gray-700 mb-1">
              Check-out Date
            </label>
            <input
              type="date"
              id="checkOut"
              name="checkOut"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.checkOut}
              onChange={handleInputChange}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label htmlFor="rooms" className="block text-sm font-medium text-gray-700 mb-1">
              Rooms
            </label>
            <input
              type="number"
              id="rooms"
              name="rooms"
              min="1"
              max="10"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.rooms}
              onChange={handleInputChange}
            />
          </div>
          
          <div>
            <label htmlFor="adults" className="block text-sm font-medium text-gray-700 mb-1">
              Adults
            </label>
            <input
              type="number"
              id="adults"
              name="adults"
              min="1"
              max="20"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.adults}
              onChange={handleInputChange}
            />
          </div>
          
          <div>
            <label htmlFor="children" className="block text-sm font-medium text-gray-700 mb-1">
              Children
            </label>
            <input
              type="number"
              id="children"
              name="children"
              min="0"
              max="10"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchParams.children}
              onChange={handleInputChange}
            />
          </div>
        </div>
        
        <div className="flex items-center mb-4">
          <input
            type="checkbox"
            id="businessFriendly"
            name="businessFriendly"
            className="mr-2"
            checked={searchParams.businessFriendly}
            onChange={handleCheckboxChange}
          />
          <label htmlFor="businessFriendly" className="text-sm font-medium text-gray-700">
            Business-friendly hotels only
          </label>
        </div>
        
        <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={handleSearch}>
          Search Hotels
        </Button>
      </div>
      
      {/* Search Results */}
      {hasSearched && (
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-800">Available Hotels</h3>
          
          {searchResults.length > 0 ? (
            <div className="space-y-6">
              {searchResults.map(hotel => (
                <div 
                  key={hotel.id} 
                  className={`border rounded-lg overflow-hidden ${selectedHotel === hotel.id ? 'border-blue-500' : 'border-gray-200'}`}
                >
                  <div className="cursor-pointer" onClick={() => handleHotelSelection(hotel.id)}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-gray-200 h-48 md:h-full">
                        {/* In a real app, you'd display an actual image */}
                        <div className="h-full w-full flex items-center justify-center bg-blue-100">
                          <span className="text-lg font-bold text-blue-800">{hotel.name} Image</span>
                        </div>
                      </div>
                      
                      <div className="p-4 md:col-span-2">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="text-lg font-bold text-blue-800">{hotel.name}</h4>
                            <p className="text-gray-600">{hotel.location}</p>
                            <p className="text-sm text-gray-500 mt-1">{hotel.address}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-blue-600">{formatPrice(hotel.price)}</div>
                            <div className="text-sm text-gray-500">per night</div>
                          </div>
                        </div>
                        
                        <div className="mt-3">
                          <div className="flex mb-2">
                            {[...Array(Math.floor(hotel.rating))].map((_, i) => (
                              <span key={i} className="text-yellow-400">★</span>
                            ))}
                            {hotel.rating % 1 !== 0 && <span className="text-yellow-400">½</span>}
                          </div>
                          
                          <p className="text-gray-700 mt-2 line-clamp-2">{hotel.description}</p>
                          
                          <div className="flex flex-wrap gap-2 mt-3">
                            {hotel.amenities.slice(0, 5).map(amenity => (
                              <span key={amenity.id} className="inline-flex items-center px-2 py-1 bg-gray-100 text-sm rounded">
                                {amenity.name}
                              </span>
                            ))}
                            {hotel.amenities.length > 5 && (
                              <span className="inline-flex items-center px-2 py-1 bg-gray-100 text-sm rounded">
                                +{hotel.amenities.length - 5} more
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Room selection area, shown when a hotel is selected */}
                  {selectedHotel === hotel.id && (
                    <div className="bg-gray-50 p-4 border-t border-gray-200">
                      <h5 className="font-semibold mb-3">Available Room Types</h5>
                      
                      <div className="space-y-4">
                        {hotel.roomTypes.map(room => (
                          <div 
                            key={room.id} 
                            className={`p-4 bg-white border rounded-lg ${selectedRoom === room.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                            onClick={() => handleRoomSelection(room.id)}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="font-semibold">{room.name}</div>
                                <div className="text-sm text-gray-600 mt-1">
                                  {room.bedType} · Fits up to {room.capacity} {room.capacity === 1 ? 'person' : 'people'}
                                </div>
                                <p className="text-sm text-gray-700 mt-2">{room.description}</p>
                              </div>
                              <div className="text-right">
                                <div className="font-bold text-blue-600">{formatPrice(room.price)}</div>
                                <div className="text-sm text-gray-500">per night</div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      <div className="mt-4 text-right">
                        <Button 
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          onClick={handleBookHotel}
                          disabled={!selectedRoom}
                        >
                          Book Selected Room
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 bg-gray-50 rounded-lg text-center">
              <p className="text-gray-700">No hotels found for your search criteria. Please try different dates or destinations.</p>
            </div>
          )}
        </div>
      )}
      
      {/* Booking Summary - would be shown in a real booking flow */}
      {selectedHotel && selectedRoom && (
        <div className="mt-6 bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Booking Summary</h3>
          <div className="text-sm text-gray-700">
            <p><span className="font-medium">Hotel:</span> {getSelectedHotel()?.name}</p>
            <p><span className="font-medium">Room:</span> {getSelectedHotel()?.roomTypes.find(r => r.id === selectedRoom)?.name}</p>
            <p><span className="font-medium">Check-in:</span> {searchParams.checkIn}</p>
            <p><span className="font-medium">Check-out:</span> {searchParams.checkOut}</p>
            <p><span className="font-medium">Nights:</span> {calculateNights()}</p>
            <p className="font-semibold mt-2">
              <span>Total:</span> {
                formatPrice((getSelectedHotel()?.roomTypes.find(r => r.id === selectedRoom)?.price || 0) * calculateNights())
              }
            </p>
          </div>
        </div>
      )}
    </div>
  );
} 