'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

// Define types
interface PolicyRule {
  id: string;
  type: 'flight' | 'hotel' | 'other';
  title: string;
  description: string;
  condition: string;
  maxAmount?: number;
  active: boolean;
  priority: 'low' | 'medium' | 'high';
  department?: string;
  appliesTo: 'all' | 'management' | 'employees' | 'custom';
}

// Mock data
const mockPolicyRules: PolicyRule[] = [
  {
    id: '1',
    type: 'flight',
    title: 'Economy Class for Domestic Flights',
    description: 'All domestic flights must be booked in economy class.',
    condition: 'Flight destination is within Vietnam',
    active: true,
    priority: 'high',
    appliesTo: 'all',
  },
  {
    id: '2',
    type: 'flight',
    title: 'Business Class for International Flights > 6 Hours',
    description: 'Business class is allowed for international flights over 6 hours duration.',
    condition: 'Flight duration > 6 hours AND international flight',
    active: true,
    priority: 'medium',
    appliesTo: 'management',
  },
  {
    id: '3',
    type: 'hotel',
    title: 'Maximum Hotel Rate - Domestic',
    description: 'Maximum hotel rate for domestic travel is 2,500,000 VND per night.',
    condition: 'Hotel location is within Vietnam',
    maxAmount: 2500000,
    active: true,
    priority: 'high',
    appliesTo: 'all',
  },
  {
    id: '4',
    type: 'hotel',
    title: 'Maximum Hotel Rate - International',
    description: 'Maximum hotel rate for international travel is 5,000,000 VND per night.',
    condition: 'Hotel location is outside Vietnam',
    maxAmount: 5000000,
    active: true,
    priority: 'medium',
    appliesTo: 'all',
  },
  {
    id: '5',
    type: 'other',
    title: 'Book at Least 14 Days in Advance',
    description: 'All travel must be booked at least 14 days in advance unless there is a business emergency.',
    condition: 'Booking date is < 14 days before travel date',
    active: true,
    priority: 'medium',
    appliesTo: 'all',
  },
  {
    id: '6',
    type: 'hotel',
    title: 'Only Approved Hotel Partners',
    description: 'All hotel bookings must be made with approved hotel partners.',
    condition: 'Hotel is in approved partners list',
    active: true,
    priority: 'high',
    appliesTo: 'all',
  },
];

export default function TravelPolicy() {
  const [policyRules, setPolicyRules] = useState<PolicyRule[]>(mockPolicyRules);
  const [editingRule, setEditingRule] = useState<PolicyRule | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  // Handle policy rule status change (active/inactive)
  const handleToggleActive = (id: string) => {
    setPolicyRules(
      policyRules.map(rule => 
        rule.id === id ? { ...rule, active: !rule.active } : rule
      )
    );
  };

  // Begin editing a rule
  const handleEditRule = (rule: PolicyRule) => {
    setEditingRule(rule);
    setShowAddForm(false);
  };

  // Save edits to a rule
  const handleSaveEdit = () => {
    if (editingRule) {
      setPolicyRules(
        policyRules.map(rule => 
          rule.id === editingRule.id ? editingRule : rule
        )
      );
      setEditingRule(null);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingRule(null);
    setShowAddForm(false);
  };

  // Handle input changes when editing
  const handleEditChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (editingRule) {
      setEditingRule({
        ...editingRule,
        [name]: value,
      });
    }
  };

  // Handle amount change (with proper parsing)
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (editingRule) {
      setEditingRule({
        ...editingRule,
        maxAmount: value ? parseInt(value, 10) : undefined,
      });
    }
  };

  // Format price to VND
  const formatPrice = (price?: number) => {
    if (!price) return 'N/A';
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
  };

  // Initialize a new rule form
  const handleAddNewRule = () => {
    setEditingRule({
      id: `new-${Date.now()}`,
      type: 'flight',
      title: '',
      description: '',
      condition: '',
      maxAmount: undefined,
      active: true,
      priority: 'medium',
      appliesTo: 'all',
    });
    setShowAddForm(true);
  };

  // Save a new rule
  const handleSaveNewRule = () => {
    if (editingRule) {
      setPolicyRules([...policyRules, editingRule]);
      setEditingRule(null);
      setShowAddForm(false);
    }
  };

  // Delete a rule
  const handleDeleteRule = (id: string) => {
    if (confirm('Are you sure you want to delete this policy rule?')) {
      setPolicyRules(policyRules.filter(rule => rule.id !== id));
    }
  };

  // Filter rules by type
  const getFilteredRules = (type: 'flight' | 'hotel' | 'other') => {
    return policyRules.filter(rule => rule.type === type);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-blue-800">Travel Policy Management</h2>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={handleAddNewRule}>
          Add New Policy Rule
        </Button>
      </div>

      {/* Add/Edit Form */}
      {editingRule && (
        <div className="mb-8 p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-semibold mb-4">
            {showAddForm ? 'Add New Policy Rule' : 'Edit Policy Rule'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Policy Type
              </label>
              <select
                name="type"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={editingRule.type}
                onChange={handleEditChange}
              >
                <option value="flight">Flight</option>
                <option value="hotel">Hotel</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Priority
              </label>
              <select
                name="priority"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={editingRule.priority}
                onChange={handleEditChange}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title
            </label>
            <input
              type="text"
              name="title"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={editingRule.title}
              onChange={handleEditChange}
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={editingRule.description}
              onChange={handleEditChange}
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Condition
            </label>
            <input
              type="text"
              name="condition"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={editingRule.condition}
              onChange={handleEditChange}
              placeholder="e.g., Flight destination is within Vietnam"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Amount (if applicable)
              </label>
              <input
                type="number"
                name="maxAmount"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={editingRule.maxAmount || ''}
                onChange={handleAmountChange}
                placeholder="e.g., 2500000"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Applies To
              </label>
              <select
                name="appliesTo"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={editingRule.appliesTo}
                onChange={handleEditChange}
              >
                <option value="all">All Employees</option>
                <option value="management">Management Only</option>
                <option value="employees">Regular Employees</option>
                <option value="custom">Custom Group</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-6">
            <Button className="bg-gray-200 hover:bg-gray-300 text-gray-800" onClick={handleCancelEdit}>
              Cancel
            </Button>
            <Button 
              className="bg-blue-600 hover:bg-blue-700 text-white" 
              onClick={showAddForm ? handleSaveNewRule : handleSaveEdit}
            >
              Save Policy Rule
            </Button>
          </div>
        </div>
      )}

      {/* Policy Rules Sections */}
      <div className="space-y-8">
        {/* Flight Policies */}
        <div>
          <h3 className="text-xl font-semibold mb-4 text-blue-700 pb-2 border-b">Flight Policies</h3>
          
          {getFilteredRules('flight').length > 0 ? (
            <div className="space-y-4">
              {getFilteredRules('flight').map(rule => (
                <div 
                  key={rule.id} 
                  className={`p-4 border rounded-lg ${rule.active ? 'bg-white' : 'bg-gray-100 opacity-75'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h4 className="font-semibold text-lg">{rule.title}</h4>
                        <span 
                          className={`ml-3 px-2 py-1 text-xs rounded-full ${
                            rule.priority === 'high' ? 'bg-red-100 text-red-700' :
                            rule.priority === 'medium' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}
                        >
                          {rule.priority.charAt(0).toUpperCase() + rule.priority.slice(1)} Priority
                        </span>
                      </div>
                      <p className="mt-2 text-gray-700">{rule.description}</p>
                      
                      <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="font-medium">Condition:</span> {rule.condition}
                        </div>
                        {rule.maxAmount && (
                          <div>
                            <span className="font-medium">Maximum Amount:</span> {formatPrice(rule.maxAmount)}
                          </div>
                        )}
                        <div>
                          <span className="font-medium">Applies To:</span> {rule.appliesTo === 'all' ? 'All Employees' : 
                            rule.appliesTo === 'management' ? 'Management Only' : 
                            rule.appliesTo === 'employees' ? 'Regular Employees' : 'Custom Group'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        className={`px-3 py-1 text-sm rounded ${
                          rule.active ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                        }`}
                        onClick={() => handleToggleActive(rule.id)}
                      >
                        {rule.active ? 'Active' : 'Inactive'}
                      </button>
                      
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleEditRule(rule)}
                      >
                        Edit
                      </button>
                      
                      <button
                        className="text-red-600 hover:text-red-800"
                        onClick={() => handleDeleteRule(rule.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 italic">No flight policies defined.</p>
          )}
        </div>
        
        {/* Hotel Policies */}
        <div>
          <h3 className="text-xl font-semibold mb-4 text-blue-700 pb-2 border-b">Hotel Policies</h3>
          
          {getFilteredRules('hotel').length > 0 ? (
            <div className="space-y-4">
              {getFilteredRules('hotel').map(rule => (
                <div 
                  key={rule.id} 
                  className={`p-4 border rounded-lg ${rule.active ? 'bg-white' : 'bg-gray-100 opacity-75'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h4 className="font-semibold text-lg">{rule.title}</h4>
                        <span 
                          className={`ml-3 px-2 py-1 text-xs rounded-full ${
                            rule.priority === 'high' ? 'bg-red-100 text-red-700' :
                            rule.priority === 'medium' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}
                        >
                          {rule.priority.charAt(0).toUpperCase() + rule.priority.slice(1)} Priority
                        </span>
                      </div>
                      <p className="mt-2 text-gray-700">{rule.description}</p>
                      
                      <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="font-medium">Condition:</span> {rule.condition}
                        </div>
                        {rule.maxAmount && (
                          <div>
                            <span className="font-medium">Maximum Amount:</span> {formatPrice(rule.maxAmount)}
                          </div>
                        )}
                        <div>
                          <span className="font-medium">Applies To:</span> {rule.appliesTo === 'all' ? 'All Employees' : 
                            rule.appliesTo === 'management' ? 'Management Only' : 
                            rule.appliesTo === 'employees' ? 'Regular Employees' : 'Custom Group'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        className={`px-3 py-1 text-sm rounded ${
                          rule.active ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                        }`}
                        onClick={() => handleToggleActive(rule.id)}
                      >
                        {rule.active ? 'Active' : 'Inactive'}
                      </button>
                      
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleEditRule(rule)}
                      >
                        Edit
                      </button>
                      
                      <button
                        className="text-red-600 hover:text-red-800"
                        onClick={() => handleDeleteRule(rule.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 italic">No hotel policies defined.</p>
          )}
        </div>
        
        {/* Other Policies */}
        <div>
          <h3 className="text-xl font-semibold mb-4 text-blue-700 pb-2 border-b">Other Travel Policies</h3>
          
          {getFilteredRules('other').length > 0 ? (
            <div className="space-y-4">
              {getFilteredRules('other').map(rule => (
                <div 
                  key={rule.id} 
                  className={`p-4 border rounded-lg ${rule.active ? 'bg-white' : 'bg-gray-100 opacity-75'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h4 className="font-semibold text-lg">{rule.title}</h4>
                        <span 
                          className={`ml-3 px-2 py-1 text-xs rounded-full ${
                            rule.priority === 'high' ? 'bg-red-100 text-red-700' :
                            rule.priority === 'medium' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}
                        >
                          {rule.priority.charAt(0).toUpperCase() + rule.priority.slice(1)} Priority
                        </span>
                      </div>
                      <p className="mt-2 text-gray-700">{rule.description}</p>
                      
                      <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="font-medium">Condition:</span> {rule.condition}
                        </div>
                        {rule.maxAmount && (
                          <div>
                            <span className="font-medium">Maximum Amount:</span> {formatPrice(rule.maxAmount)}
                          </div>
                        )}
                        <div>
                          <span className="font-medium">Applies To:</span> {rule.appliesTo === 'all' ? 'All Employees' : 
                            rule.appliesTo === 'management' ? 'Management Only' : 
                            rule.appliesTo === 'employees' ? 'Regular Employees' : 'Custom Group'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        className={`px-3 py-1 text-sm rounded ${
                          rule.active ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                        }`}
                        onClick={() => handleToggleActive(rule.id)}
                      >
                        {rule.active ? 'Active' : 'Inactive'}
                      </button>
                      
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleEditRule(rule)}
                      >
                        Edit
                      </button>
                      
                      <button
                        className="text-red-600 hover:text-red-800"
                        onClick={() => handleDeleteRule(rule.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 italic">No other travel policies defined.</p>
          )}
        </div>
      </div>
    </div>
  );
} 