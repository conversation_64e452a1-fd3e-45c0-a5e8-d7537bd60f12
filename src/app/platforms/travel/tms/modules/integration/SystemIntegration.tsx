'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

// Types
interface IntegrationSystem {
  id: string;
  name: string;
  type: 'erp' | 'hr' | 'accounting' | 'payment' | 'crm' | 'other';
  status: 'active' | 'inactive' | 'pending' | 'failed';
  lastSync?: string;
  apiKey?: string;
  endpoint?: string;
  description: string;
  config?: Record<string, any>;
}

// Mock data
const mockIntegrations: IntegrationSystem[] = [
  {
    id: 'int1',
    name: 'SAP ERP',
    type: 'erp',
    status: 'active',
    lastSync: '2023-09-15T10:30:00Z',
    apiKey: '•••••••••••••••••',
    endpoint: 'https://api.sap-erp.example.com/v1',
    description: 'Integration with SAP ERP for financial data synchronization',
    config: {
      syncSchedule: 'hourly',
      modules: ['finance', 'accounting', 'inventory']
    }
  },
  {
    id: 'int2',
    name: 'Workday HR',
    type: 'hr',
    status: 'active',
    lastSync: '2023-09-15T09:45:00Z',
    apiKey: '•••••••••••••••••',
    endpoint: 'https://api.workday.example.com/v2',
    description: 'Employee data synchronization with Workday HR system',
    config: {
      syncSchedule: 'daily',
      modules: ['employees', 'departments', 'roles']
    }
  },
  {
    id: 'int3',
    name: 'QuickBooks',
    type: 'accounting',
    status: 'inactive',
    lastSync: '2023-09-10T14:20:00Z',
    apiKey: '•••••••••••••••••',
    endpoint: 'https://api.quickbooks.example.com/v3',
    description: 'Accounting data synchronization with QuickBooks',
    config: {
      syncSchedule: 'daily',
      modules: ['expenses', 'invoices']
    }
  },
  {
    id: 'int4',
    name: 'Stripe Payments',
    type: 'payment',
    status: 'pending',
    apiKey: '•••••••••••••••••',
    endpoint: 'https://api.stripe.com/v1',
    description: 'Payment processing integration with Stripe',
    config: {
      syncSchedule: 'realtime',
      modules: ['payments', 'refunds']
    }
  }
];

export default function SystemIntegration() {
  const [integrations, setIntegrations] = useState<IntegrationSystem[]>(mockIntegrations);
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationSystem | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [newIntegration, setNewIntegration] = useState<Partial<IntegrationSystem>>({
    type: 'erp',
    status: 'pending',
  });
  const [showAddForm, setShowAddForm] = useState(false);
  const [testingIntegrationId, setTestingIntegrationId] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // Handle selecting an integration
  const handleSelectIntegration = (integration: IntegrationSystem) => {
    setSelectedIntegration(integration);
    setIsEditMode(false);
    setTestResult(null);
  };

  // Handle edit button click
  const handleEditClick = () => {
    setIsEditMode(true);
  };

  // Handle input changes for editing
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (isEditMode && selectedIntegration) {
      setSelectedIntegration({
        ...selectedIntegration,
        [name]: value,
      });
    } else if (showAddForm) {
      setNewIntegration({
        ...newIntegration,
        [name]: value,
      });
    }
  };

  // Handle saving integration changes
  const handleSaveChanges = () => {
    if (selectedIntegration) {
      setIntegrations(
        integrations.map(integration => 
          integration.id === selectedIntegration.id ? selectedIntegration : integration
        )
      );
      setIsEditMode(false);
    }
  };

  // Handle adding new integration
  const handleAddClick = () => {
    setSelectedIntegration(null);
    setShowAddForm(true);
    setIsEditMode(false);
    setTestResult(null);
  };

  // Handle saving new integration
  const handleSaveNew = () => {
    if (newIntegration.name && newIntegration.description) {
      const newIntegrationItem: IntegrationSystem = {
        id: `int-${Date.now()}`,
        name: newIntegration.name,
        type: (newIntegration.type || 'other') as 'erp' | 'hr' | 'accounting' | 'payment' | 'crm' | 'other',
        status: 'pending',
        description: newIntegration.description,
        endpoint: newIntegration.endpoint,
        apiKey: newIntegration.apiKey,
      };
      
      setIntegrations([...integrations, newIntegrationItem]);
      setNewIntegration({
        type: 'erp',
        status: 'pending',
      });
      setShowAddForm(false);
    }
  };

  // Handle canceling edit or add
  const handleCancel = () => {
    if (isEditMode) {
      // Reset selected integration to its original state
      const originalIntegration = integrations.find(i => selectedIntegration && i.id === selectedIntegration.id);
      if (originalIntegration) {
        setSelectedIntegration(originalIntegration);
      }
      setIsEditMode(false);
    } else if (showAddForm) {
      setShowAddForm(false);
      setNewIntegration({
        type: 'erp',
        status: 'pending',
      });
    }
  };

  // Handle integration activation/deactivation
  const handleToggleStatus = (id: string) => {
    setIntegrations(
      integrations.map(integration => {
        if (integration.id === id) {
          const newStatus = integration.status === 'active' ? 'inactive' : 'active';
          
          // If selected integration is being toggled, update it too
          if (selectedIntegration && selectedIntegration.id === id) {
            setSelectedIntegration({
              ...selectedIntegration,
              status: newStatus,
            });
          }
          
          return {
            ...integration,
            status: newStatus,
          };
        }
        return integration;
      })
    );
  };

  // Handle testing integration
  const handleTestIntegration = (id: string) => {
    setTestingIntegrationId(id);
    
    // Simulate API test
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% chance of success
      setTestResult({
        success,
        message: success 
          ? 'Integration test successful! Connection established.' 
          : 'Integration test failed. Please check your API credentials and endpoint URL.'
      });
      setTestingIntegrationId(null);
    }, 1500);
  };

  // Handle deleting integration
  const handleDeleteIntegration = (id: string) => {
    if (confirm('Are you sure you want to delete this integration? This action cannot be undone.')) {
      setIntegrations(integrations.filter(i => i.id !== id));
      if (selectedIntegration && selectedIntegration.id === id) {
        setSelectedIntegration(null);
        setIsEditMode(false);
      }
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch(status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get type display name
  const getTypeDisplayName = (type: string) => {
    switch(type) {
      case 'erp':
        return 'ERP System';
      case 'hr':
        return 'HR System';
      case 'accounting':
        return 'Accounting System';
      case 'payment':
        return 'Payment Processor';
      case 'crm':
        return 'CRM System';
      default:
        return 'Other System';
    }
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-blue-800">System Integrations</h2>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={handleAddClick}>
          Add New Integration
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Integration List */}
        <div className="lg:col-span-1 border rounded-lg overflow-hidden">
          <div className="bg-gray-50 p-4 border-b">
            <h3 className="font-semibold">Available Integrations</h3>
          </div>
          
          <div className="divide-y max-h-[600px] overflow-y-auto">
            {integrations.length > 0 ? (
              integrations.map(integration => (
                <div 
                  key={integration.id} 
                  className={`p-4 hover:bg-gray-50 cursor-pointer ${selectedIntegration?.id === integration.id ? 'bg-blue-50' : ''}`}
                  onClick={() => handleSelectIntegration(integration)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-semibold">{integration.name}</div>
                      <div className="text-sm text-gray-500">{getTypeDisplayName(integration.type)}</div>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(integration.status)}`}>
                      {integration.status.charAt(0).toUpperCase() + integration.status.slice(1)}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500">
                No integrations configured yet.
              </div>
            )}
          </div>
        </div>
        
        {/* Integration Details or Form */}
        <div className="lg:col-span-2 border rounded-lg">
          {selectedIntegration ? (
            <div>
              <div className="bg-gray-50 p-4 border-b flex justify-between items-center">
                <h3 className="font-semibold">Integration Details</h3>
                {!isEditMode && (
                  <div className="flex gap-2">
                    <Button 
                      className="bg-blue-600 hover:bg-blue-700 text-white text-sm"
                      onClick={handleEditClick}
                    >
                      Edit
                    </Button>
                    <Button 
                      className={`text-sm ${selectedIntegration.status === 'active' ? 'bg-red-100 text-red-800 hover:bg-red-200' : 'bg-green-100 text-green-800 hover:bg-green-200'}`}
                      onClick={() => handleToggleStatus(selectedIntegration.id)}
                    >
                      {selectedIntegration.status === 'active' ? 'Deactivate' : 'Activate'}
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="p-6">
                {isEditMode ? (
                  /* Edit Form */
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Integration Name
                        </label>
                        <input
                          type="text"
                          name="name"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={selectedIntegration.name}
                          onChange={handleInputChange}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Integration Type
                        </label>
                        <select
                          name="type"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={selectedIntegration.type}
                          onChange={handleInputChange}
                        >
                          <option value="erp">ERP System</option>
                          <option value="hr">HR System</option>
                          <option value="accounting">Accounting System</option>
                          <option value="payment">Payment Processor</option>
                          <option value="crm">CRM System</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        name="description"
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={selectedIntegration.description}
                        onChange={handleInputChange}
                      ></textarea>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          API Endpoint URL
                        </label>
                        <input
                          type="text"
                          name="endpoint"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={selectedIntegration.endpoint || ''}
                          onChange={handleInputChange}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          API Key or Token
                        </label>
                        <input
                          type="text"
                          name="apiKey"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={selectedIntegration.apiKey || ''}
                          onChange={handleInputChange}
                          placeholder="•••••••••••••••••"
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-end gap-3 mt-6">
                      <Button 
                        className="bg-gray-200 hover:bg-gray-300 text-gray-800"
                        onClick={handleCancel}
                      >
                        Cancel
                      </Button>
                      <Button 
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        onClick={handleSaveChanges}
                      >
                        Save Changes
                      </Button>
                    </div>
                  </div>
                ) : (
                  /* Details View */
                  <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Integration Name</h4>
                        <p className="mt-1">{selectedIntegration.name}</p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Type</h4>
                        <p className="mt-1">{getTypeDisplayName(selectedIntegration.type)}</p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Status</h4>
                        <p className="mt-1">
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(selectedIntegration.status)}`}>
                            {selectedIntegration.status.charAt(0).toUpperCase() + selectedIntegration.status.slice(1)}
                          </span>
                        </p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Last Synchronized</h4>
                        <p className="mt-1">{formatDate(selectedIntegration.lastSync)}</p>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Description</h4>
                      <p className="mt-1">{selectedIntegration.description}</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">API Endpoint</h4>
                        <p className="mt-1 text-sm font-mono overflow-hidden text-ellipsis">{selectedIntegration.endpoint || 'Not configured'}</p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">API Key</h4>
                        <p className="mt-1">{selectedIntegration.apiKey ? '•••••••••••••••••' : 'Not configured'}</p>
                      </div>
                    </div>
                    
                    {/* Test Results */}
                    {testResult && (
                      <div className={`p-4 rounded-md ${testResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                        <div className="font-semibold">{testResult.success ? 'Success!' : 'Error'}</div>
                        <p>{testResult.message}</p>
                      </div>
                    )}
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        className="bg-blue-50 text-blue-700 hover:bg-blue-100"
                        onClick={() => handleTestIntegration(selectedIntegration.id)}
                        disabled={testingIntegrationId === selectedIntegration.id}
                      >
                        {testingIntegrationId === selectedIntegration.id ? 'Testing...' : 'Test Connection'}
                      </Button>
                      
                      <Button 
                        className="bg-red-50 text-red-700 hover:bg-red-100"
                        onClick={() => handleDeleteIntegration(selectedIntegration.id)}
                      >
                        Delete Integration
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : showAddForm ? (
            <div>
              <div className="bg-gray-50 p-4 border-b">
                <h3 className="font-semibold">Add New Integration</h3>
              </div>
              
              <div className="p-6">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Integration Name *
                      </label>
                      <input
                        type="text"
                        name="name"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newIntegration.name || ''}
                        onChange={handleInputChange}
                        placeholder="e.g., SAP ERP"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Integration Type
                      </label>
                      <select
                        name="type"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newIntegration.type}
                        onChange={handleInputChange}
                      >
                        <option value="erp">ERP System</option>
                        <option value="hr">HR System</option>
                        <option value="accounting">Accounting System</option>
                        <option value="payment">Payment Processor</option>
                        <option value="crm">CRM System</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description *
                    </label>
                    <textarea
                      name="description"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={newIntegration.description || ''}
                      onChange={handleInputChange}
                      placeholder="Describe what this integration does and how it's used..."
                    ></textarea>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        API Endpoint URL
                      </label>
                      <input
                        type="text"
                        name="endpoint"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newIntegration.endpoint || ''}
                        onChange={handleInputChange}
                        placeholder="https://api.example.com/v1"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        API Key or Token
                      </label>
                      <input
                        type="text"
                        name="apiKey"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={newIntegration.apiKey || ''}
                        onChange={handleInputChange}
                        placeholder="Enter API key or token"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end gap-3 mt-6">
                    <Button 
                      className="bg-gray-200 hover:bg-gray-300 text-gray-800"
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                    <Button 
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      onClick={handleSaveNew}
                      disabled={!newIntegration.name || !newIntegration.description}
                    >
                      Add Integration
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full py-16 px-6 text-center text-gray-500">
              <div>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="mb-4">Select an integration from the list or create a new one.</p>
                <Button 
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={handleAddClick}
                >
                  Add New Integration
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 