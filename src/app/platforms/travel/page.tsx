'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Plane, 
  Hotel, 
  Car, 
  Calendar,
  Users,
  Star,
  ArrowRight,
  Globe,
  Compass
} from 'lucide-react';
import Link from 'next/link';

const travelServices = [
  {
    id: 'hotels',
    title: 'Khách sạn',
    description: 'Tìm và đặt khách sạn với giá tốt nhất',
    icon: Hotel,
    href: '/platforms/travel/hotels',
    color: 'bg-blue-500',
    stats: '10,000+ khách sạn'
  },
  {
    id: 'flights',
    title: 'Vé máy bay',
    description: 'Đặt vé máy bay nội địa và quốc tế',
    icon: Plane,
    href: '/platforms/travel/flights',
    color: 'bg-green-500',
    stats: '500+ hãng hàng không'
  },
  {
    id: 'cars',
    title: '<PERSON><PERSON><PERSON> xe',
    description: '<PERSON><PERSON><PERSON> xe tự lái với nhiều lựa chọn',
    icon: Car,
    href: '/platforms/travel/cars',
    color: 'bg-purple-500',
    stats: '1,000+ xe có sẵn'
  },
  {
    id: 'tours',
    title: 'Tour du lịch',
    description: 'Khám phá các tour du lịch hấp dẫn',
    icon: Compass,
    href: '/platforms/travel/start/tours',
    color: 'bg-orange-500',
    stats: '500+ tour'
  }
];

const popularDestinations = [
  {
    name: 'Hà Nội',
    image: '/api/placeholder/300/200',
    description: 'Thủ đô ngàn năm văn hiến'
  },
  {
    name: 'TP. Hồ Chí Minh',
    image: '/api/placeholder/300/200',
    description: 'Thành phố năng động nhất Việt Nam'
  },
  {
    name: 'Đà Nẵng',
    image: '/api/placeholder/300/200',
    description: 'Thành phố đáng sống bậc nhất'
  },
  {
    name: 'Nha Trang',
    image: '/api/placeholder/300/200',
    description: 'Thiên đường biển đảo'
  }
];

export default function TravelPlatformPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Khám phá thế giới cùng ABN Travel
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Nền tảng du lịch toàn diện với hàng ngàn lựa chọn tốt nhất
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/platforms/travel/start">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                  Bắt đầu khám phá
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/platforms/travel/planner">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  Lập kế hoạch du lịch
                  <Calendar className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Services Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Dịch vụ du lịch toàn diện
          </h2>
          <p className="text-gray-600 text-lg">
            Tất cả những gì bạn cần cho chuyến du lịch hoàn hảo
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {travelServices.map((service) => {
            const IconComponent = service.icon;
            return (
              <Link key={service.id} href={service.href}>
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                  <CardContent className="p-6 text-center">
                    <div className={`w-16 h-16 ${service.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                    <p className="text-gray-600 mb-3">{service.description}</p>
                    <p className="text-sm text-blue-600 font-medium">{service.stats}</p>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Popular Destinations */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Điểm đến phổ biến
            </h2>
            <p className="text-gray-600 text-lg">
              Khám phá những địa điểm du lịch hấp dẫn nhất Việt Nam
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {popularDestinations.map((destination) => (
              <Card key={destination.name} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative">
                  <img 
                    src={destination.image} 
                    alt={destination.name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">{destination.name}</h3>
                    <p className="text-sm opacity-90">{destination.description}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Tại sao chọn ABN Travel?
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Star className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Chất lượng đảm bảo</h3>
            <p className="text-gray-600">
              Tất cả dịch vụ được kiểm duyệt kỹ lưỡng để đảm bảo chất lượng tốt nhất
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Globe className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Phủ sóng toàn quốc</h3>
            <p className="text-gray-600">
              Dịch vụ có mặt tại tất cả các tỉnh thành trên toàn quốc
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Hỗ trợ 24/7</h3>
            <p className="text-gray-600">
              Đội ngũ chăm sóc khách hàng luôn sẵn sàng hỗ trợ bạn mọi lúc
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Sẵn sàng cho chuyến du lịch tiếp theo?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Hãy bắt đầu lên kế hoạch cho chuyến du lịch trong mơ của bạn
          </p>
          <Link href="/platforms/travel/start">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              Bắt đầu ngay
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}