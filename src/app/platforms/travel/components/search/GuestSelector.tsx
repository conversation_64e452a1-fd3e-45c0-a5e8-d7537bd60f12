import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Users, Plus, Minus } from 'lucide-react';

interface GuestSelectorProps {
  adults: number;
  children: number;
  onAdultsChange: (count: number) => void;
  onChildrenChange: (count: number) => void;
}

export default function GuestSelector({ adults, children, onAdultsChange, onChildrenChange }: GuestSelectorProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-gray-400" />
          <span className="text-sm font-medium">Người lớn</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAdultsChange(Math.max(1, adults - 1))}
            disabled={adults <= 1}
          >
            <Minus className="h-3 w-3" />
          </Button>
          <span className="w-8 text-center">{adults}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAdultsChange(adults + 1)}
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-gray-400" />
          <span className="text-sm font-medium">Trẻ em</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onChildrenChange(Math.max(0, children - 1))}
            disabled={children <= 0}
          >
            <Minus className="h-3 w-3" />
          </Button>
          <span className="w-8 text-center">{children}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onChildrenChange(children + 1)}
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}