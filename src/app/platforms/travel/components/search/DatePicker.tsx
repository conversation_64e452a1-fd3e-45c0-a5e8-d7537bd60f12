import React from 'react';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';

interface DatePickerProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minDate?: string;
}

export default function DatePicker({ value, onChange, placeholder = "Chọn ngày...", minDate }: DatePickerProps) {
  return (
    <div className="relative">
      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      <Input
        type="date"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="pl-10"
        min={minDate}
      />
    </div>
  );
}