'use client';

import React, { useState, useEffect } from 'react';
import { Search, MapPin, Calendar, Users, Plane, Car, Camera } from 'lucide-react';
import { useSearch } from '../../hooks';
import { LoadingSpinner } from './ui';

interface SearchBarProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  className?: string;
  autoFocus?: boolean;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Tìm kiếm...',
  onSearch,
  className = '',
  autoFocus = false
}) => {
  const [query, setQuery] = useState('');
  const { searchQuery, setSearchQuery, isLoading } = useSearch();

  useEffect(() => {
    if (searchQuery !== query) {
      onSearch(searchQuery);
    }
  }, [searchQuery, onSearch]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchQuery(query);
  };

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={placeholder}
          autoFocus={autoFocus}
          className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <LoadingSpinner size="sm" />
          </div>
        )}
      </div>
    </form>
  );
};

interface LocationSearchProps {
  value: string;
  onChange: (location: string) => void;
  placeholder?: string;
  className?: string;
}

export const LocationSearch: React.FC<LocationSearchProps> = ({
  value,
  onChange,
  placeholder = 'Chọn điểm đến',
  className = ''
}) => {
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Mock suggestions - replace with actual API call
  const mockSuggestions = [
    { id: 'hanoi', name: 'Hà Nội', country: 'Việt Nam' },
    { id: 'hochiminh', name: 'TP. Hồ Chí Minh', country: 'Việt Nam' },
    { id: 'danang', name: 'Đà Nẵng', country: 'Việt Nam' },
    { id: 'phuquoc', name: 'Phú Quốc', country: 'Việt Nam' },
    { id: 'nhatrang', name: 'Nha Trang', country: 'Việt Nam' },
    { id: 'sapa', name: 'Sa Pa', country: 'Việt Nam' }
  ];

  useEffect(() => {
    if (value.length > 1) {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        const filtered = mockSuggestions.filter(item =>
          item.name.toLowerCase().includes(value.toLowerCase())
        );
        setSuggestions(filtered);
        setIsLoading(false);
        setIsOpen(true);
      }, 300);
    } else {
      setSuggestions([]);
      setIsOpen(false);
    }
  }, [value]);

  const handleSelect = (location: any) => {
    onChange(location.name);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          onFocus={() => value.length > 1 && setIsOpen(true)}
          onBlur={() => setTimeout(() => setIsOpen(false), 200)}
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <LoadingSpinner size="sm" />
          </div>
        )}
      </div>

      {isOpen && suggestions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {suggestions.map((suggestion) => (
            <button
              key={suggestion.id}
              onClick={() => handleSelect(suggestion)}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center gap-3"
            >
              <MapPin className="w-4 h-4 text-gray-400" />
              <div>
                <div className="font-medium">{suggestion.name}</div>
                <div className="text-sm text-gray-500">{suggestion.country}</div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

interface DatePickerProps {
  value: string;
  onChange: (date: string) => void;
  placeholder?: string;
  minDate?: string;
  className?: string;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  placeholder = 'Chọn ngày',
  minDate,
  className = ''
}) => {
  const today = new Date().toISOString().split('T')[0];
  const min = minDate || today;

  return (
    <div className={`relative ${className}`}>
      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
      <input
        type="date"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        min={min}
        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder={placeholder}
      />
    </div>
  );
};

interface GuestSelectorProps {
  adults: number;
  children: number;
  onAdultsChange: (count: number) => void;
  onChildrenChange: (count: number) => void;
  className?: string;
}

export const GuestSelector: React.FC<GuestSelectorProps> = ({
  adults,
  children,
  onAdultsChange,
  onChildrenChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const total = adults + children;

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left"
      >
        <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        {total > 0 ? `${total} khách` : 'Số khách'}
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Người lớn</div>
                <div className="text-sm text-gray-500">Từ 12 tuổi trở lên</div>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => onAdultsChange(Math.max(1, adults - 1))}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  disabled={adults <= 1}
                >
                  -
                </button>
                <span className="w-8 text-center">{adults}</span>
                <button
                  onClick={() => onAdultsChange(adults + 1)}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  +
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Trẻ em</div>
                <div className="text-sm text-gray-500">Từ 2-11 tuổi</div>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => onChildrenChange(Math.max(0, children - 1))}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  disabled={children <= 0}
                >
                  -
                </button>
                <span className="w-8 text-center">{children}</span>
                <button
                  onClick={() => onChildrenChange(children + 1)}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  +
                </button>
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t">
            <button
              onClick={() => setIsOpen(false)}
              className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
            >
              Xác nhận
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

interface QuickSearchProps {
  onHotelSearch: () => void;
  onFlightSearch: () => void;
  onTourSearch: () => void;
  onCarSearch: () => void;
  className?: string;
}

export const QuickSearch: React.FC<QuickSearchProps> = ({
  onHotelSearch,
  onFlightSearch,
  onTourSearch,
  onCarSearch,
  className = ''
}) => {
  const quickOptions = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: 'Khách sạn',
      description: 'Tìm chỗ nghỉ tốt nhất',
      onClick: onHotelSearch,
      color: 'bg-blue-500'
    },
    {
      icon: <Plane className="w-6 h-6" />,
      title: 'Vé máy bay',
      description: 'Đặt vé giá tốt',
      onClick: onFlightSearch,
      color: 'bg-green-500'
    },
    {
      icon: <Camera className="w-6 h-6" />,
      title: 'Tour du lịch',
      description: 'Khám phá điểm đến mới',
      onClick: onTourSearch,
      color: 'bg-purple-500'
    },
    {
      icon: <Car className="w-6 h-6" />,
      title: 'Thuê xe',
      description: 'Di chuyển tự do',
      onClick: onCarSearch,
      color: 'bg-orange-500'
    }
  ];

  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 ${className}`}>
      {quickOptions.map((option, index) => (
        <button
          key={index}
          onClick={option.onClick}
          className="p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow text-left"
        >
          <div className={`w-12 h-12 ${option.color} rounded-lg flex items-center justify-center text-white mb-3`}>
            {option.icon}
          </div>
          <h3 className="font-semibold text-gray-900 mb-1">{option.title}</h3>
          <p className="text-sm text-gray-600">{option.description}</p>
        </button>
      ))}
    </div>
  );
};

interface FilterPanelProps {
  filters: any;
  onFiltersChange: (filters: any) => void;
  className?: string;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFiltersChange,
  className = ''
}) => {
  const priceRanges = [
    { label: 'Dưới 500k', min: 0, max: 500000 },
    { label: '500k - 1tr', min: 500000, max: 1000000 },
    { label: '1tr - 2tr', min: 1000000, max: 2000000 },
    { label: '2tr - 5tr', min: 2000000, max: 5000000 },
    { label: 'Trên 5tr', min: 5000000, max: 999999999 }
  ];

  const ratings = [5, 4, 3, 2, 1];

  return (
    <div className={`bg-white p-6 rounded-lg shadow-md ${className}`}>
      <h3 className="font-semibold text-gray-900 mb-4">Bộ lọc</h3>

      {/* Price Range */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-700 mb-3">Khoảng giá</h4>
        <div className="space-y-2">
          {priceRanges.map((range, index) => (
            <label key={index} className="flex items-center">
              <input
                type="radio"
                name="priceRange"
                checked={filters.priceRange?.min === range.min}
                onChange={() => onFiltersChange({
                  ...filters,
                  priceRange: { min: range.min, max: range.max }
                })}
                className="mr-2"
              />
              <span className="text-sm">{range.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Rating */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-700 mb-3">Đánh giá</h4>
        <div className="space-y-2">
          {ratings.map((rating) => (
            <label key={rating} className="flex items-center">
              <input
                type="radio"
                name="rating"
                checked={filters.rating === rating}
                onChange={() => onFiltersChange({
                  ...filters,
                  rating: rating
                })}
                className="mr-2"
              />
              <div className="flex items-center">
                {Array.from({ length: rating }).map((_, i) => (
                  <span key={i} className="text-yellow-400">★</span>
                ))}
                <span className="text-sm ml-1">trở lên</span>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Clear Filters */}
      <button
        onClick={() => onFiltersChange({})}
        className="w-full py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50"
      >
        Xóa bộ lọc
      </button>
    </div>
  );
};