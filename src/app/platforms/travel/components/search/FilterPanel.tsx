import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Filter, Star } from 'lucide-react';

interface FilterPanelProps {
  onFilterChange: (filters: any) => void;
  filters: any;
}

export default function FilterPanel({ onFilterChange, filters }: FilterPanelProps) {
  const handlePriceRangeChange = (min: number, max: number) => {
    onFilterChange({ ...filters, priceRange: { min, max } });
  };

  const handleRatingChange = (rating: number) => {
    onFilterChange({ ...filters, rating });
  };

  const handleAmenityChange = (amenity: string, checked: boolean) => {
    const amenities = filters.amenities || [];
    if (checked) {
      onFilterChange({ ...filters, amenities: [...amenities, amenity] });
    } else {
      onFilterChange({ ...filters, amenities: amenities.filter((a: string) => a !== amenity) });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Bộ lọc
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Price Range */}
        <div>
          <h4 className="font-medium mb-3">Khoảng giá</h4>
          <div className="space-y-2">
            <label className="flex items-center">
              <input 
                type="checkbox" 
                className="mr-2"
                onChange={(e) => e.target.checked ? handlePriceRangeChange(0, 1000000) : handlePriceRangeChange(0, Infinity)}
              />
              Dưới 1 triệu
            </label>
            <label className="flex items-center">
              <input 
                type="checkbox" 
                className="mr-2"
                onChange={(e) => e.target.checked ? handlePriceRangeChange(1000000, 3000000) : handlePriceRangeChange(0, Infinity)}
              />
              1-3 triệu
            </label>
            <label className="flex items-center">
              <input 
                type="checkbox" 
                className="mr-2"
                onChange={(e) => e.target.checked ? handlePriceRangeChange(3000000, Infinity) : handlePriceRangeChange(0, Infinity)}
              />
              Trên 3 triệu
            </label>
          </div>
        </div>

        {/* Rating */}
        <div>
          <h4 className="font-medium mb-3">Đánh giá</h4>
          <div className="space-y-2">
            {[5, 4, 3].map((rating) => (
              <label key={rating} className="flex items-center">
                <input 
                  type="checkbox" 
                  className="mr-2"
                  onChange={(e) => e.target.checked ? handleRatingChange(rating) : handleRatingChange(0)}
                />
                <div className="flex items-center">
                  {Array.from({ length: rating }).map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-current text-yellow-400" />
                  ))}
                  <span className="ml-1 text-sm">trở lên</span>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Amenities */}
        <div>
          <h4 className="font-medium mb-3">Tiện nghi</h4>
          <div className="space-y-2">
            {['wifi', 'parking', 'breakfast', 'pool', 'gym', 'spa'].map((amenity) => (
              <label key={amenity} className="flex items-center">
                <input 
                  type="checkbox" 
                  className="mr-2"
                  onChange={(e) => handleAmenityChange(amenity, e.target.checked)}
                />
                <span className="text-sm capitalize">{amenity}</span>
              </label>
            ))}
          </div>
        </div>

        <Button 
          variant="outline" 
          className="w-full"
          onClick={() => onFilterChange({})}
        >
          Xóa bộ lọc
        </Button>
      </CardContent>
    </Card>
  );
}