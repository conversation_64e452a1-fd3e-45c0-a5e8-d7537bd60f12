import React from 'react';

interface SidebarLayoutProps {
  children: React.ReactNode;
  sidebar: React.ReactNode;
}

export default function SidebarLayout({ children, sidebar }: SidebarLayoutProps) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
      {/* Sidebar */}
      <div className="lg:col-span-1">
        {sidebar}
      </div>

      {/* Main Content */}
      <div className="lg:col-span-3">
        {children}
      </div>
    </div>
  );
}