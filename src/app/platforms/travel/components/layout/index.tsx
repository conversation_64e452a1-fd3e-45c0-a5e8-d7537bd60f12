'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Menu, 
  X, 
  Phone, 
  Mail, 
  MapPin, 
  Building2, 
  Plane, 
  Camera, 
  Car, 
  Headphones,
  User,
  Heart,
  Settings,
  LogOut,
  Search
} from 'lucide-react';

interface HeaderProps {
  className?: string;
}

export const Header: React.FC<HeaderProps> = ({ className = '' }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const pathname = usePathname();

  const navigation = [
    { name: '<PERSON>h<PERSON>ch sạn', href: '/platforms/travel/start/hotels', icon: Building2 },
    { name: 'Vé máy bay', href: '/platforms/travel/start/flights', icon: Plane },
    { name: 'Tour du lịch', href: '/platforms/travel/start/tours', icon: Camera },
    { name: '<PERSON><PERSON><PERSON> xe', href: '/platforms/travel/start/cars', icon: Car },
    { name: '<PERSON>ị<PERSON> vụ', href: '/platforms/travel/start/services', icon: Headphones },
  ];

  const userMenuItems = [
    { name: 'Hồ sơ', href: '/profile', icon: User },
    { name: 'Yêu thích', href: '/favorites', icon: Heart },
    { name: 'Cài đặt', href: '/settings', icon: Settings },
    { name: 'Đăng xuất', href: '/logout', icon: LogOut },
  ];

  return (
    <header className={`bg-white shadow-sm border-b ${className}`}>
      {/* Top bar */}
      <div className="bg-blue-600 text-white py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-1">
                <Phone className="w-4 h-4" />
                <span>1900 1234</span>
              </div>
              <div className="flex items-center gap-1">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className="hidden md:flex items-center gap-4">
              <Link href="/help" className="hover:text-blue-200">
                Trợ giúp
              </Link>
              <Link href="/about" className="hover:text-blue-200">
                Về chúng tôi
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/platforms/travel/start" className="flex items-center">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
              <MapPin className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">MyTour</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Search and User Menu */}
          <div className="flex items-center gap-4">
            {/* Search Button */}
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <Search className="w-5 h-5" />
            </button>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center gap-2 p-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4" />
                </div>
                <span className="hidden md:block text-sm font-medium">Tài khoản</span>
              </button>

              {isUserMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border py-1 z-50">
                  {userMenuItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Icon className="w-4 h-4" />
                        {item.name}
                      </Link>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 text-gray-400 hover:text-gray-600"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t bg-white">
          <div className="px-4 py-2 space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium ${
                    isActive
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Icon className="w-4 h-4" />
                  {item.name}
                </Link>
              );
            })}
          </div>
        </div>
      )}
    </header>
  );
};

interface FooterProps {
  className?: string;
}

export const Footer: React.FC<FooterProps> = ({ className = '' }) => {
  const footerSections = [
    {
      title: 'Dịch vụ',
      links: [
        { name: 'Đặt khách sạn', href: '/hotels' },
        { name: 'Vé máy bay', href: '/flights' },
        { name: 'Tour du lịch', href: '/tours' },
        { name: 'Thuê xe', href: '/cars' },
        { name: 'Dịch vụ khác', href: '/services' },
      ]
    },
    {
      title: 'Hỗ trợ',
      links: [
        { name: 'Trung tâm trợ giúp', href: '/help' },
        { name: 'Liên hệ', href: '/contact' },
        { name: 'Hướng dẫn đặt chỗ', href: '/booking-guide' },
        { name: 'Chính sách hủy', href: '/cancellation' },
        { name: 'Câu hỏi thường gặp', href: '/faq' },
      ]
    },
    {
      title: 'Về MyTour',
      links: [
        { name: 'Giới thiệu', href: '/about' },
        { name: 'Tuyển dụng', href: '/careers' },
        { name: 'Tin tức', href: '/news' },
        { name: 'Đối tác', href: '/partners' },
        { name: 'Chương trình khuyến mãi', href: '/promotions' },
      ]
    },
    {
      title: 'Chính sách',
      links: [
        { name: 'Điều khoản sử dụng', href: '/terms' },
        { name: 'Chính sách bảo mật', href: '/privacy' },
        { name: 'Chính sách cookie', href: '/cookies' },
        { name: 'Quy chế hoạt động', href: '/regulations' },
        { name: 'Giải quyết khiếu nại', href: '/complaints' },
      ]
    }
  ];

  return (
    <footer className={`bg-gray-900 text-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/platforms/travel/start" className="flex items-center mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <MapPin className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold">MyTour</span>
            </Link>
            <p className="text-gray-400 text-sm mb-4">
              Nền tảng du lịch hàng đầu Việt Nam, mang đến trải nghiệm du lịch tuyệt vời cho mọi gia đình.
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                <span>1900 1234</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span>Hà Nội, Việt Nam</span>
              </div>
            </div>
          </div>

          {/* Footer Sections */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className="font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white text-sm transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <p className="text-gray-400 text-sm">
              © 2024 MyTour. Tất cả quyền được bảo lưu.
            </p>
            <div className="flex items-center gap-6 mt-4 md:mt-0">
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm">
                Điều khoản
              </Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm">
                Bảo mật
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white text-sm">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

interface BreadcrumbProps {
  items: Array<{
    label: string;
    href?: string;
  }>;
  className?: string;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  className = ''
}) => {
  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <span className="text-gray-400">/</span>
          )}
          {item.href ? (
            <Link
              href={item.href}
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              {item.label}
            </Link>
          ) : (
            <span className="text-gray-600">{item.label}</span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

interface PageLayoutProps {
  children: React.ReactNode;
  title?: string;
  breadcrumbs?: Array<{ label: string; href?: string }>;
  className?: string;
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  breadcrumbs,
  className = ''
}) => {
  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {(title || breadcrumbs) && (
          <div className="mb-8">
            {breadcrumbs && (
              <Breadcrumb items={breadcrumbs} className="mb-4" />
            )}
            {title && (
              <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
            )}
          </div>
        )}
        
        {children}
      </main>
      
      <Footer />
    </div>
  );
};

interface SidebarLayoutProps {
  children: React.ReactNode;
  sidebar: React.ReactNode;
  className?: string;
}

export const SidebarLayout: React.FC<SidebarLayoutProps> = ({
  children,
  sidebar,
  className = ''
}) => {
  return (
    <div className={`flex gap-8 ${className}`}>
      <aside className="w-80 flex-shrink-0">
        {sidebar}
      </aside>
      <main className="flex-1 min-w-0">
        {children}
      </main>
    </div>
  );
};