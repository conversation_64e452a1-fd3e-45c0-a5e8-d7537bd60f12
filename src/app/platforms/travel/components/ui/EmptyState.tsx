import React from 'react';
import { Search } from 'lucide-react';

interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
}

export default function EmptyState({ title, description, icon }: EmptyStateProps) {
  return (
    <div className="text-center py-12">
      <div className="text-gray-500 mb-4">
        {icon || <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />}
        <p className="text-lg font-medium">{title}</p>
        {description && <p className="text-sm mt-1">{description}</p>}
      </div>
    </div>
  );
}