import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, MapPin, Heart, Wifi, Car, Coffee, Waves, Dumbbell, Sparkles } from 'lucide-react';
import { formatPrice } from '../../utils';
import type { Hotel } from '../../types';

interface HotelCardProps {
  hotel: Hotel;
  onFavorite: (hotelId: string) => void;
  isFavorite: boolean;
}

const amenityIcons: Record<string, React.ReactNode> = {
  wifi: <Wifi className="h-3 w-3" />,
  parking: <Car className="h-3 w-3" />,
  breakfast: <Coffee className="h-3 w-3" />,
  pool: <Waves className="h-3 w-3" />,
  gym: <Dumbbell className="h-3 w-3" />,
  spa: <Sparkles className="h-3 w-3" />
};

export default function HotelCard({ hotel, onFavorite, isFavorite }: HotelCardProps) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative">
        <img 
          src={hotel.images[0] || '/api/placeholder/400/250'} 
          alt={hotel.name}
          className="w-full h-48 object-cover"
        />
        {hotel.discount && (
          <Badge className="absolute top-2 left-2 bg-red-500">
            -{hotel.discount}%
          </Badge>
        )}
        <Button
          onClick={() => onFavorite(hotel.id)}
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 bg-white/80 hover:bg-white"
        >
          <Heart 
            className={`h-4 w-4 ${
              isFavorite ? 'fill-current text-red-500' : ''
            }`} 
          />
        </Button>
      </div>

      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div>
            <h3 className="font-semibold text-lg">{hotel.name}</h3>
            <div className="flex items-center gap-1 mt-1">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">{hotel.location.address}</span>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center gap-1 mb-1">
              <Star className="h-4 w-4 fill-current text-yellow-400" />
              <span className="text-sm font-medium">{hotel.rating}</span>
              <span className="text-xs text-gray-500">({hotel.reviewCount})</span>
            </div>
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-3">{hotel.description}</p>

        {/* Amenities */}
        <div className="flex flex-wrap gap-1 mb-4">
          {hotel.amenities.slice(0, 4).map((amenity) => (
            <Badge key={amenity} variant="secondary" className="text-xs flex items-center gap-1">
              {amenityIcons[amenity]}
              {amenity}
            </Badge>
          ))}
          {hotel.amenities.length > 4 && (
            <Badge variant="secondary" className="text-xs">
              +{hotel.amenities.length - 4}
            </Badge>
          )}
        </div>

        {/* Price */}
        <div className="flex items-center justify-between">
          <div>
            {hotel.originalPrice && (
              <div className="text-gray-500 line-through text-sm">
                {formatPrice(hotel.originalPrice)}
              </div>
            )}
            <div className="text-xl font-bold text-blue-600">
              {formatPrice(hotel.pricePerNight)}
            </div>
            <div className="text-sm text-gray-500">/ đêm</div>
          </div>
          <Button>
            Đặt phòng
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}