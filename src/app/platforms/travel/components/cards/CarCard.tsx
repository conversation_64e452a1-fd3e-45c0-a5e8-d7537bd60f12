import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Car, Users, Fuel, Settings, Heart } from 'lucide-react';
import { formatPrice } from '../../utils';
import type { Car as CarType } from '../../types';

interface CarCardProps {
  car: CarType;
  onFavorite: (carId: string) => void;
  isFavorite: boolean;
}

export default function CarCard({ car, onFavorite, isFavorite }: CarCardProps) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative">
        <img 
          src={car.images[0] || '/api/placeholder/400/250'} 
          alt={car.name}
          className="w-full h-48 object-cover"
        />
        <Button
          onClick={() => onFavorite(car.id)}
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 bg-white/80 hover:bg-white"
        >
          <Heart 
            className={`h-4 w-4 ${
              isFavorite ? 'fill-current text-red-500' : ''
            }`} 
          />
        </Button>
      </div>

      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div>
            <h3 className="font-semibold text-lg">{car.name}</h3>
            <div className="text-sm text-gray-600">{car.category}</div>
          </div>
          <Badge variant="outline">
            {car.company}
          </Badge>
        </div>

        <p className="text-gray-600 text-sm mb-3">{car.description}</p>

        {/* Car Features */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4 text-gray-400" />
            <span>{car.seats} chỗ ngồi</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Settings className="h-4 w-4 text-gray-400" />
            <span>{car.transmission}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Fuel className="h-4 w-4 text-gray-400" />
            <span>{car.fuelType}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Car className="h-4 w-4 text-gray-400" />
            <span>{car.year}</span>
          </div>
        </div>

        {/* Features */}
        <div className="flex flex-wrap gap-1 mb-4">
          {car.features.slice(0, 3).map((feature) => (
            <Badge key={feature} variant="secondary" className="text-xs">
              {feature}
            </Badge>
          ))}
          {car.features.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{car.features.length - 3}
            </Badge>
          )}
        </div>

        {/* Price */}
        <div className="flex items-center justify-between">
          <div>
            {car.originalPrice && (
              <div className="text-gray-500 line-through text-sm">
                {formatPrice(car.originalPrice)}
              </div>
            )}
            <div className="text-xl font-bold text-blue-600">
              {formatPrice(car.pricePerDay)}
            </div>
            <div className="text-sm text-gray-500">/ ngày</div>
          </div>
          <Button>
            Thuê xe
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}