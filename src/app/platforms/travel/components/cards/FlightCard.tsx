import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plane, Clock, Heart } from 'lucide-react';
import { formatPrice } from '../../utils';
import type { Flight } from '../../types';

interface FlightCardProps {
  flight: Flight;
  onFavorite: (flightId: string) => void;
  isFavorite: boolean;
}

export default function FlightCard({ flight, onFavorite, isFavorite }: FlightCardProps) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Plane className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="font-semibold">{flight.airline}</div>
              <div className="text-sm text-gray-500">{flight.flightNumber}</div>
            </div>
          </div>
          <Button
            onClick={() => onFavorite(flight.id)}
            variant="ghost"
            size="sm"
          >
            <Heart 
              className={`h-4 w-4 ${
                isFavorite ? 'fill-current text-red-500' : ''
              }`} 
            />
          </Button>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="text-center">
            <div className="text-lg font-bold">{flight.departure.time}</div>
            <div className="text-sm text-gray-600">{flight.departure.airport}</div>
            <div className="text-xs text-gray-500">{flight.departure.city}</div>
          </div>
          
          <div className="flex-1 mx-4 text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">{flight.duration}</span>
            </div>
            <div className="h-px bg-gray-300 relative">
              <Plane className="h-4 w-4 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white text-gray-400" />
            </div>
            {flight.stops > 0 && (
              <div className="text-xs text-gray-500 mt-1">
                {flight.stops} điểm dừng
              </div>
            )}
          </div>

          <div className="text-center">
            <div className="text-lg font-bold">{flight.arrival.time}</div>
            <div className="text-sm text-gray-600">{flight.arrival.airport}</div>
            <div className="text-xs text-gray-500">{flight.arrival.city}</div>
          </div>
        </div>

        <div className="flex items-center gap-2 mb-4">
          <Badge variant={flight.class === 'Economy' ? 'secondary' : 'default'}>
            {flight.class}
          </Badge>
          {flight.baggage && (
            <Badge variant="outline">
              {flight.baggage}
            </Badge>
          )}
          {flight.refundable && (
            <Badge variant="outline" className="text-green-600">
              Hoàn tiền
            </Badge>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div>
            {flight.originalPrice && (
              <div className="text-gray-500 line-through text-sm">
                {formatPrice(flight.originalPrice)}
              </div>
            )}
            <div className="text-xl font-bold text-blue-600">
              {formatPrice(flight.price)}
            </div>
            <div className="text-sm text-gray-500">/ người</div>
          </div>
          <Button>
            Chọn chuyến bay
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}