'use client';

import React from 'react';
import Link from 'next/link';
import { 
  MapPin, 
  Users, 
  Calendar, 
  Wifi, 
  Car, 
  Coffee,
  Heart,
  Share2,
  Plane
} from 'lucide-react';
import { RatingDisplay, PriceDisplay, Badge, ImageWithFallback } from '../ui';
import type { Hotel, Flight, Tour, CarRental, TravelService } from '../../types';

interface HotelCardProps {
  hotel: Hotel;
  onFavorite?: (id: string) => void;
  onShare?: (id: string) => void;
  className?: string;
}

export const HotelCard: React.FC<HotelCardProps> = ({
  hotel,
  onFavorite,
  onShare,
  className = ''
}) => {
  const amenityIcons: { [key: string]: React.ReactNode } = {
    wifi: <Wifi className="w-4 h-4" />,
    parking: <Car className="w-4 h-4" />,
    breakfast: <Coffee className="w-4 h-4" />
  };

  return (
    <div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden ${className}`}>
      <div className="relative">
        <ImageWithFallback
          src={hotel.images[0]}
          alt={hotel.name}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-3 right-3 flex gap-2">
          {onFavorite && (
            <button
              onClick={() => onFavorite(hotel.id)}
              className="p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
            >
              <Heart className="w-4 h-4" />
            </button>
          )}
          {onShare && (
            <button
              onClick={() => onShare(hotel.id)}
              className="p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
            >
              <Share2 className="w-4 h-4" />
            </button>
          )}
        </div>
        {hotel.discount && (
          <Badge
            variant="error"
            className="absolute top-3 left-3"
          >
            -{hotel.discount}%
          </Badge>
        )}
      </div>

      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 text-lg line-clamp-1">
            {hotel.name}
          </h3>
          <RatingDisplay rating={hotel.rating.score} size="sm" />
        </div>

        <div className="flex items-center text-gray-600 mb-3">
          <MapPin className="w-4 h-4 mr-1" />
          <span className="text-sm line-clamp-1">{hotel.location.address}</span>
        </div>

        <div className="flex items-center gap-2 mb-3">
          {hotel.amenities.slice(0, 3).map((amenity) => (
            <div
              key={amenity}
              className="flex items-center gap-1 text-xs text-gray-500"
            >
              {amenityIcons[amenity] || <span>•</span>}
              <span className="capitalize">{amenity}</span>
            </div>
          ))}
          {hotel.amenities.length > 3 && (
            <span className="text-xs text-gray-400">
              +{hotel.amenities.length - 3} tiện ích
            </span>
          )}
        </div>

        <div className="flex items-center justify-between">
          <PriceDisplay
            price={hotel.pricePerNight}
            originalPrice={hotel.originalPrice}
            size="md"
          />
          <Link
            href={`/platforms/travel/start/hotels/${hotel.id}`}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Xem chi tiết
          </Link>
        </div>
      </div>
    </div>
  );
};

interface FlightCardProps {
  flight: Flight;
  onSelect?: (id: string) => void;
  className?: string;
}

export const FlightCard: React.FC<FlightCardProps> = ({
  flight,
  onSelect,
  className = ''
}) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  return (
    <div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Plane className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <div className="font-semibold text-gray-900">{flight.airline}</div>
            <div className="text-sm text-gray-500">{flight.flightNumber}</div>
          </div>
        </div>
        <Badge variant={flight.class === 'economy' ? 'default' : 'info'}>
          {flight.class === 'economy' ? 'Phổ thông' : 'Thương gia'}
        </Badge>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div className="text-center">
          <div className="text-xl font-bold text-gray-900">
            {formatTime(flight.departureTime)}
          </div>
          <div className="text-sm text-gray-600">{flight.origin}</div>
        </div>

        <div className="flex-1 mx-4">
          <div className="relative">
            <div className="h-px bg-gray-300 w-full"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2">
              <Plane className="w-4 h-4 text-gray-400" />
            </div>
          </div>
          <div className="text-center text-xs text-gray-500 mt-1">
            {formatDuration(flight.duration)}
          </div>
        </div>

        <div className="text-center">
          <div className="text-xl font-bold text-gray-900">
            {formatTime(flight.arrivalTime)}
          </div>
          <div className="text-sm text-gray-600">{flight.destination}</div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            <span className="font-medium">Hành lý:</span> {flight.baggage || '7kg'}
          </div>
          {flight.stops === 0 && (
            <Badge variant="success" size="sm">Bay thẳng</Badge>
          )}
        </div>
        <div className="flex items-center gap-3">
          <PriceDisplay price={flight.price} size="md" />
          {onSelect && (
            <button
              onClick={() => onSelect(flight.id)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Chọn
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

interface TourCardProps {
  tour: Tour;
  onFavorite?: (id: string) => void;
  className?: string;
}

export const TourCard: React.FC<TourCardProps> = ({
  tour,
  onFavorite,
  className = ''
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden ${className}`}>
      <div className="relative">
        <ImageWithFallback
          src={tour.images[0]}
          alt={tour.title || tour.name}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-3 right-3">
          {onFavorite && (
            <button
              onClick={() => onFavorite(tour.id)}
              className="p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
            >
              <Heart className="w-4 h-4" />
            </button>
          )}
        </div>
        <div className="absolute top-3 left-3 flex gap-2">
          <Badge variant="info">{tour.category}</Badge>
          {tour.isPopular && (
            <Badge variant="warning">Phổ biến</Badge>
          )}
        </div>
      </div>

      <div className="p-4">
        <h3 className="font-semibold text-gray-900 text-lg mb-2 line-clamp-2">
          {tour.title || tour.name}
        </h3>

        <div className="flex items-center text-gray-600 mb-3">
          <MapPin className="w-4 h-4 mr-1" />
          <span className="text-sm">{tour.destination || tour.destinations[0]?.name}</span>
        </div>

        <div className="flex items-center gap-4 mb-3 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            <span>{tour.duration} ngày</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>Tối đa {tour.maxGroupSize} người</span>
          </div>
        </div>

        <div className="flex items-center gap-2 mb-3">
          <RatingDisplay rating={tour.rating.score} size="sm" />
          <span className="text-sm text-gray-500">
            ({tour.reviewCount || tour.rating.count} đánh giá)
          </span>
        </div>

        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
          {tour.description}
        </p>

        <div className="flex items-center justify-between">
          <PriceDisplay
            price={tour.price}
            originalPrice={tour.originalPrice}
            size="md"
          />
          <Link
            href={`/platforms/travel/start/tours/${tour.id}`}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Xem tour
          </Link>
        </div>
      </div>
    </div>
  );
};

interface CarCardProps {
  car: CarRental;
  onSelect?: (id: string) => void;
  className?: string;
}

export const CarCard: React.FC<CarCardProps> = ({
  car,
  onSelect,
  className = ''
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden ${className}`}>
      <ImageWithFallback
        src={car.images[0]}
        alt={`${car.brand || car.make} ${car.model}`}
        className="w-full h-40 object-cover"
      />

      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div>
            <h3 className="font-semibold text-gray-900">
              {car.brand || car.make} {car.model}
            </h3>
            <p className="text-sm text-gray-600">{car.category}</p>
          </div>
          <Badge variant="default">{car.transmission}</Badge>
        </div>

        <div className="flex items-center text-gray-600 mb-3">
          <MapPin className="w-4 h-4 mr-1" />
          <span className="text-sm">{car.location.address || car.location.name}</span>
        </div>

        <div className="grid grid-cols-3 gap-2 mb-4 text-xs text-gray-600">
          <div className="flex items-center gap-1">
            <Users className="w-3 h-3" />
            <span>{car.seats} chỗ</span>
          </div>
          <div className="flex items-center gap-1">
            <span>⛽</span>
            <span>{car.fuelType}</span>
          </div>
          <div className="flex items-center gap-1">
            <span>🧳</span>
            <span>{car.luggage} vali</span>
          </div>
        </div>

        <div className="flex items-center gap-2 mb-4">
          {car.features.slice(0, 2).map((feature, index) => (
            <Badge key={index} variant="default" size="sm">
              {feature}
            </Badge>
          ))}
          {car.features.length > 2 && (
            <span className="text-xs text-gray-400">
              +{car.features.length - 2}
            </span>
          )}
        </div>

        <div className="flex items-center justify-between">
          <PriceDisplay
            price={car.pricePerDay}
            size="md"
          />
          {onSelect && (
            <button
              onClick={() => onSelect(car.id)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Thuê xe
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

interface ServiceCardProps {
  service: TravelService;
  onContact?: (id: string) => void;
  className?: string;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onContact,
  className = ''
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden ${className}`}>
      {service.images && service.images.length > 0 && (
        <ImageWithFallback
          src={service.images[0]}
          alt={service.name}
          className="w-full h-40 object-cover"
        />
      )}

      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 line-clamp-1">
            {service.name}
          </h3>
          <Badge variant="info">{service.category}</Badge>
        </div>

        <div className="flex items-center text-gray-600 mb-2">
          <MapPin className="w-4 h-4 mr-1" />
          <span className="text-sm line-clamp-1">{service.location.address || service.location.name}</span>
        </div>

        <div className="flex items-center gap-2 mb-3">
          <RatingDisplay rating={service.rating.score} size="sm" />
          <span className="text-sm text-gray-500">
            ({service.rating.count} đánh giá)
          </span>
        </div>

        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
          {service.description}
        </p>

        {service.languages && service.languages.length > 0 && (
          <div className="flex items-center gap-2 mb-4">
            {service.languages.slice(0, 2).map((language, index) => (
              <Badge key={index} variant="default" size="sm">
                {language}
              </Badge>
            ))}
            {service.languages.length > 2 && (
              <span className="text-xs text-gray-400">
                +{service.languages.length - 2}
              </span>
            )}
          </div>
        )}

        <div className="flex items-center justify-between">
          <PriceDisplay
            price={service.priceRange?.min || service.price}
            size="sm"
          />
          {onContact && (
            <button
              onClick={() => onContact(service.id)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Liên hệ
            </button>
          )}
        </div>
      </div>
    </div>
  );
};