import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Clock, Users, Star, Heart } from 'lucide-react';
import { formatPrice } from '../../utils';
import type { Tour } from '../../types';

interface TourCardProps {
  tour: Tour;
  onFavorite: (tourId: string) => void;
  isFavorite: boolean;
}

export default function TourCard({ tour, onFavorite, isFavorite }: TourCardProps) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative">
        <img 
          src={tour.images[0] || '/api/placeholder/400/250'} 
          alt={tour.name}
          className="w-full h-48 object-cover"
        />
        {tour.discount && (
          <Badge className="absolute top-2 left-2 bg-red-500">
            -{tour.discount}%
          </Badge>
        )}
        <Button
          onClick={() => onFavorite(tour.id)}
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 bg-white/80 hover:bg-white"
        >
          <Heart 
            className={`h-4 w-4 ${
              isFavorite ? 'fill-current text-red-500' : ''
            }`} 
          />
        </Button>
      </div>

      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div>
            <h3 className="font-semibold text-lg">{tour.name}</h3>
            <div className="flex items-center gap-1 mt-1">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">{tour.destination}</span>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center gap-1 mb-1">
              <Star className="h-4 w-4 fill-current text-yellow-400" />
              <span className="text-sm font-medium">{tour.rating}</span>
              <span className="text-xs text-gray-500">({tour.reviewCount})</span>
            </div>
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-3">{tour.description}</p>

        {/* Tour Details */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4 text-gray-400" />
            <span>{tour.duration}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4 text-gray-400" />
            <span>Tối đa {tour.maxGroupSize} người</span>
          </div>
        </div>

        {/* Highlights */}
        <div className="flex flex-wrap gap-1 mb-4">
          {tour.highlights.slice(0, 3).map((highlight) => (
            <Badge key={highlight} variant="secondary" className="text-xs">
              {highlight}
            </Badge>
          ))}
          {tour.highlights.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{tour.highlights.length - 3}
            </Badge>
          )}
        </div>

        {/* Price */}
        <div className="flex items-center justify-between">
          <div>
            {tour.originalPrice && (
              <div className="text-gray-500 line-through text-sm">
                {formatPrice(tour.originalPrice)}
              </div>
            )}
            <div className="text-xl font-bold text-blue-600">
              {formatPrice(tour.price)}
            </div>
            <div className="text-sm text-gray-500">/ người</div>
          </div>
          <Button>
            Đặt tour
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}