'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, BarChart3, Building2, User, Calendar, FileText, AlertTriangle, CheckCircle, Clock, Target } from 'lucide-react';
import { Button } from '@/components/ui/button.1';

interface ClientData {
  id: string;
  name: string;
  industry: string;
  size: string;
  status: string;
  audit_type: string;
  start_date: string;
  expected_completion: string;
  completion_date?: string;
  assigned_agents: string[];
  lead_auditor: string;
  contact_person: {
    name: string;
    position: string;
    email: string;
    phone: string;
  };
  progress: {
    overall_completion: number;
    documents_requested: number;
    documents_received: number;
    documents_processed: number;
    anomalies_detected: number;
    compliance_issues: number;
    findings_count: number;
  };
  risk_assessment: {
    overall_risk: string;
    financial_risk: string;
    operational_risk: string;
    compliance_risk: string;
  };
  priority: string;
  last_activity: string;
}

interface TaskData {
  id: string;
  client_id: string;
  name: string;
  status: string;
  priority: string;
  current_agent: string;
  progress: {
    overall_completion: number;
    documents_processed: number;
    total_documents: number;
    anomalies_found: number;
    compliance_issues: number;
  };
  created_at: string;
  deadline: string;
}

export default function ClientConsolidatedDashboard() {
  const router = useRouter();
  const params = useParams();
  const clientId = params.clientId as string;
  
  const [client, setClient] = useState<ClientData | null>(null);
  const [clientTasks, setClientTasks] = useState<TaskData[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load client data
        const clientsData = await import('/data/apps/platforms/finance/auditmind/clients.json');
        const foundClient = (clientsData as any).clients.find((c: ClientData) => c.id === clientId);

        if (foundClient) {
          // Ensure risk_assessment exists with default values
          if (!foundClient.risk_assessment) {
            foundClient.risk_assessment = {
              overall_risk: 'medium',
              financial_risk: 'medium',
              operational_risk: 'low',
              compliance_risk: 'medium'
            };
          }
          setClient(foundClient);
        }

        // Load tasks for this client
        const tasksData = await import('/data/apps/platforms/finance/auditmind/tasks.json');
        const tasks = (tasksData as any).tasks.filter((t: TaskData) => t.client_id === clientId);
        setClientTasks(tasks);

        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error loading client data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
    const interval = setInterval(loadData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [clientId]);

  if (loading || !client) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const formatTime = (date: Date | null) => {
    if (!date) return 'Loading...';
    return date.toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric'
    });
  };

  const formatPercent = (value: number) => `${value.toFixed(1)}%`;
  const formatNumber = (value: number) => value.toLocaleString();

  // Calculate metrics
  const activeTasks = clientTasks.filter(t => t.status === 'in_progress').length;
  const completedTasks = clientTasks.filter(t => t.status === 'completed').length;
  const pendingTasks = clientTasks.filter(t => t.status === 'pending').length;
  const overdueTasks = clientTasks.filter(t => {
    const dueDate = new Date(t.deadline);
    const now = new Date();
    return t.status !== 'completed' && dueDate < now;
  }).length;

  const avgTaskProgress = clientTasks.length > 0 
    ? clientTasks.reduce((sum, t) => sum + t.progress.overall_completion, 0) / clientTasks.length 
    : 0;

  const totalTaskAnomalies = clientTasks.reduce((sum, t) => sum + t.progress.anomalies_found, 0);
  const totalTaskDocuments = clientTasks.reduce((sum, t) => sum + (t.progress.documents_processed || 0), 0);

  const daysToCompletion = Math.ceil(
    (new Date(client.expected_completion).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );

  const daysInProgress = Math.ceil(
    (new Date().getTime() - new Date(client.start_date).getTime()) / (1000 * 60 * 60 * 24)
  );

  return (
    <div className="min-h-screen bg-white text-black font-mono text-xs">
      {/* Header */}
      <div className="border-b border-gray-300 p-2 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/clients')}>
            <ArrowLeft className="h-3 w-3 mr-1" />
            Back to Clients
          </Button>
          <div>
            <span className="font-bold">{client.name} - Consolidated Dashboard</span>
            <span className="ml-4 text-gray-600">Last Update: {formatTime(lastUpdate)}</span>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/consolidated')}>
            <BarChart3 className="h-3 w-3 mr-1" />
            Global Dashboard
          </Button>
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/boards')}>
            <BarChart3 className="h-3 w-3 mr-1" />
            Boards
          </Button>
        </div>
      </div>

      <div className="p-4 grid grid-cols-6 gap-4">
        {/* Client Overview */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Client Overview</div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Client ID</span>
              <span className="font-bold">{client.id}</span>
            </div>
            <div className="flex justify-between">
              <span>Industry</span>
              <span className="font-bold">{client.industry}</span>
            </div>
            <div className="flex justify-between">
              <span>Size</span>
              <span className="font-bold">{client.size}</span>
            </div>
            <div className="flex justify-between">
              <span>Audit Type</span>
              <span className="font-bold">{client.audit_type.replace('_', ' ')}</span>
            </div>
            <div className="flex justify-between">
              <span>Status</span>
              <span className={`font-bold ${
                client.status === 'active_audit' ? 'text-blue-600' :
                client.status === 'completed' ? 'text-green-600' :
                client.status === 'planning' ? 'text-yellow-600' : 'text-gray-600'
              }`}>
                {client.status.replace('_', ' ')}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Priority</span>
              <span className={`font-bold ${
                client.priority === 'high' ? 'text-red-600' :
                client.priority === 'medium' ? 'text-orange-600' : 'text-green-600'
              }`}>
                {client.priority}
              </span>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Contact Information</div>
            <div className="text-xs space-y-1">
              <div>{client.contact_person.name}</div>
              <div className="text-gray-600">{client.contact_person.position}</div>
              <div className="text-gray-600">{client.contact_person.email}</div>
              <div className="text-gray-600">{client.contact_person.phone}</div>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Lead Auditor</div>
            <div className="text-xs">{client.lead_auditor}</div>
          </div>
        </div>

        {/* Progress & Timeline */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Progress & Timeline</div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Overall Progress</span>
              <span className="font-bold text-blue-600">{client.progress.overall_completion}%</span>
            </div>
            <div className="flex justify-between">
              <span>Start Date</span>
              <span className="font-bold">{formatDate(client.start_date)}</span>
            </div>
            <div className="flex justify-between">
              <span>Expected Completion</span>
              <span className="font-bold">{formatDate(client.expected_completion)}</span>
            </div>
            <div className="flex justify-between">
              <span>Days in Progress</span>
              <span className="font-bold">{daysInProgress}</span>
            </div>
            <div className="flex justify-between">
              <span>Days to Completion</span>
              <span className={`font-bold ${daysToCompletion < 7 ? 'text-red-600' : daysToCompletion < 30 ? 'text-orange-600' : 'text-green-600'}`}>
                {daysToCompletion}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Last Activity</span>
              <span className="font-bold">{formatDate(client.last_activity)}</span>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Document Processing</div>
            <div className="flex justify-between">
              <span>Requested</span>
              <span className="font-bold">{client.progress.documents_requested}</span>
            </div>
            <div className="flex justify-between">
              <span>Received</span>
              <span className="font-bold text-blue-600">{client.progress.documents_received}</span>
            </div>
            <div className="flex justify-between">
              <span>Processed</span>
              <span className="font-bold text-green-600">{client.progress.documents_processed}</span>
            </div>
            <div className="flex justify-between">
              <span>Processing Rate</span>
              <span className="font-bold">{formatPercent((client.progress.documents_processed / client.progress.documents_requested) * 100)}</span>
            </div>
          </div>
        </div>

        {/* Risk Assessment & Findings */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Risk Assessment & Findings</div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Overall Risk</span>
              <span className={`font-bold ${
                client.risk_assessment.overall_risk === 'high' ? 'text-red-600' :
                client.risk_assessment.overall_risk === 'medium' ? 'text-orange-600' : 'text-green-600'
              }`}>
                {client.risk_assessment.overall_risk}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Financial Risk</span>
              <span className={`font-bold ${
                client.risk_assessment.financial_risk === 'high' ? 'text-red-600' :
                client.risk_assessment.financial_risk === 'medium' ? 'text-orange-600' : 'text-green-600'
              }`}>
                {client.risk_assessment.financial_risk}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Operational Risk</span>
              <span className={`font-bold ${
                client.risk_assessment.operational_risk === 'high' ? 'text-red-600' :
                client.risk_assessment.operational_risk === 'medium' ? 'text-orange-600' : 'text-green-600'
              }`}>
                {client.risk_assessment.operational_risk}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Compliance Risk</span>
              <span className={`font-bold ${
                client.risk_assessment.compliance_risk === 'high' ? 'text-red-600' :
                client.risk_assessment.compliance_risk === 'medium' ? 'text-orange-600' : 'text-green-600'
              }`}>
                {client.risk_assessment.compliance_risk}
              </span>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Findings Summary</div>
            <div className="flex justify-between">
              <span>Total Findings</span>
              <span className="font-bold">{client.progress.findings_count}</span>
            </div>
            <div className="flex justify-between">
              <span>Anomalies Detected</span>
              <span className="font-bold text-orange-600">{client.progress.anomalies_detected}</span>
            </div>
            <div className="flex justify-between">
              <span>Compliance Issues</span>
              <span className="font-bold text-red-600">{client.progress.compliance_issues}</span>
            </div>
            <div className="flex justify-between">
              <span>Anomaly Rate</span>
              <span className="font-bold">{formatPercent((client.progress.anomalies_detected / client.progress.documents_processed) * 100)}</span>
            </div>
          </div>
        </div>

        {/* Task Management */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Task Management</div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Task Overview</div>
              <div className="flex justify-between">
                <span>Total Tasks</span>
                <span className="font-bold">{clientTasks.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Active</span>
                <span className="font-bold text-blue-600">{activeTasks}</span>
              </div>
              <div className="flex justify-between">
                <span>Pending</span>
                <span className="font-bold text-yellow-600">{pendingTasks}</span>
              </div>
              <div className="flex justify-between">
                <span>Completed</span>
                <span className="font-bold text-green-600">{completedTasks}</span>
              </div>
              <div className="flex justify-between">
                <span>Overdue</span>
                <span className="font-bold text-red-600">{overdueTasks}</span>
              </div>
              <div className="flex justify-between">
                <span>Avg Progress</span>
                <span className="font-bold">{formatPercent(avgTaskProgress)}</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Task Performance</div>
              <div className="flex justify-between">
                <span>Completion Rate</span>
                <span className="font-bold">{formatPercent((completedTasks / clientTasks.length) * 100)}</span>
              </div>
              <div className="flex justify-between">
                <span>On-time Rate</span>
                <span className="font-bold">{formatPercent(((clientTasks.length - overdueTasks) / clientTasks.length) * 100)}</span>
              </div>
              <div className="flex justify-between">
                <span>Task Documents</span>
                <span className="font-bold">{totalTaskDocuments}</span>
              </div>
              <div className="flex justify-between">
                <span>Task Anomalies</span>
                <span className="font-bold text-orange-600">{totalTaskAnomalies}</span>
              </div>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Recent Tasks</div>
            {clientTasks.slice(0, 8).map((task, i) => (
              <div key={i} className="flex justify-between text-xs">
                <span className="truncate w-40">{task.name.substring(0, 30)}...</span>
                <span className={`font-bold ${
                  task.status === 'in_progress' ? 'text-blue-600' :
                  task.status === 'completed' ? 'text-green-600' :
                  task.status === 'pending' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {task.progress.overall_completion}%
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Assigned Agents */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Assigned Agents</div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Agent Overview</div>
              <div className="flex justify-between">
                <span>Total Agents</span>
                <span className="font-bold">{client.assigned_agents.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Active Agents</span>
                <span className="font-bold text-green-600">{client.assigned_agents.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Agent Types</span>
                <span className="font-bold">{new Set(client.assigned_agents.map(a => a.split('_')[0])).size}</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Agent Performance</div>
              <div className="flex justify-between">
                <span>Avg Workload</span>
                <span className="font-bold">67.3%</span>
              </div>
              <div className="flex justify-between">
                <span>Response Time</span>
                <span className="font-bold">145ms</span>
              </div>
              <div className="flex justify-between">
                <span>Success Rate</span>
                <span className="font-bold text-green-600">98.5%</span>
              </div>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Agent List</div>
            {client.assigned_agents.map((agent, i) => (
              <div key={i} className="flex justify-between text-xs">
                <span className="truncate w-40">{agent.replace('_', ' ')}</span>
                <span className="font-bold text-green-600">Active</span>
              </div>
            ))}
          </div>
        </div>

        {/* System Metrics */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">System Metrics</div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Processing Metrics</div>
              <div className="flex justify-between">
                <span>Processing Speed</span>
                <span className="font-bold">12.5 docs/hour</span>
              </div>
              <div className="flex justify-between">
                <span>Error Rate</span>
                <span className="font-bold text-red-600">0.1%</span>
              </div>
              <div className="flex justify-between">
                <span>Quality Score</span>
                <span className="font-bold text-green-600">97.8%</span>
              </div>
              <div className="flex justify-between">
                <span>Efficiency</span>
                <span className="font-bold">89.2%</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Resource Usage</div>
              <div className="flex justify-between">
                <span>CPU Usage</span>
                <span className="font-bold">45.3%</span>
              </div>
              <div className="flex justify-between">
                <span>Memory Usage</span>
                <span className="font-bold">32.1%</span>
              </div>
              <div className="flex justify-between">
                <span>Storage Used</span>
                <span className="font-bold">2.3 GB</span>
              </div>
              <div className="flex justify-between">
                <span>Network I/O</span>
                <span className="font-bold">8.7 MB/s</span>
              </div>
            </div>
          </div>
        </div>

        {/* Compliance & Quality */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Compliance & Quality</div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Compliance Status</div>
              <div className="flex justify-between">
                <span>Overall Compliance</span>
                <span className="font-bold text-green-600">96.8%</span>
              </div>
              <div className="flex justify-between">
                <span>Regulatory Score</span>
                <span className="font-bold text-green-600">94.2%</span>
              </div>
              <div className="flex justify-between">
                <span>Policy Adherence</span>
                <span className="font-bold text-green-600">98.1%</span>
              </div>
              <div className="flex justify-between">
                <span>Audit Trail</span>
                <span className="font-bold text-green-600">Complete</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Quality Metrics</div>
              <div className="flex justify-between">
                <span>Data Accuracy</span>
                <span className="font-bold text-green-600">99.1%</span>
              </div>
              <div className="flex justify-between">
                <span>Completeness</span>
                <span className="font-bold text-green-600">97.5%</span>
              </div>
              <div className="flex justify-between">
                <span>Consistency</span>
                <span className="font-bold text-green-600">98.3%</span>
              </div>
              <div className="flex justify-between">
                <span>Timeliness</span>
                <span className="font-bold text-orange-600">85.7%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
