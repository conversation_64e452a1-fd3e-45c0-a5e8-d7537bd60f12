'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Building2,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Calendar,
  FileText,
  Shield,
  Activity,
  ArrowLeft,
  Filter,
  Search,
  MoreVertical,
  BarChart3,
  PlayCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button.1';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';

interface Client {
  id: string;
  name: string;
  industry: string;
  size: string;
  status: string;
  audit_type: string;
  start_date: string;
  expected_completion: string;
  completion_date?: string;
  assigned_agents: string[];
  lead_auditor: string;
  contact_person: {
    name: string;
    position: string;
    email: string;
    phone: string;
  };
  progress: {
    overall_completion: number;
    documents_requested: number;
    documents_received: number;
    documents_processed: number;
    anomalies_detected: number;
    compliance_issues: number;
    findings_count: number;
  };
  current_stage: string;
  risk_level: string;
  priority: string;
  last_activity: string;
  created_at: string;
}

interface ClientStatistics {
  total_clients: number;
  active_audits: number;
  planning_stage: number;
  completed_audits: number;
  total_documents_processed: number;
  total_anomalies_detected: number;
  total_compliance_issues: number;
  average_completion_time_days: number;
}

export default function ClientDashboard() {
  const router = useRouter();
  const [clients, setClients] = useState<Client[]>([]);
  const [statistics, setStatistics] = useState<ClientStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const loadData = async () => {
      try {
        const clientsData = await import('/data/apps/platforms/finance/auditmind/clients.json');
        setClients(clientsData.clients || []);
        setStatistics(clientsData.client_statistics || null);
      } catch (error) {
        console.error('Error loading client data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [mounted]);

  const getStatusColor = (status: string) => {
    if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';
    switch (status) {
      case 'active_audit': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'planning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'on_hold': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRiskColor = (risk: string) => {
    if (!risk) return 'bg-gray-100 text-gray-800';
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    if (!priority) return 'bg-gray-100 text-gray-800';
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      // Use a consistent format to avoid hydration mismatches
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const filteredClients = clients.filter(client => {
    if (!client) return false;
    const matchesSearch = (client.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (client.industry || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || client.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (!mounted || loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-1">
              <span>Boards</span>
              <span>/</span>
              <span className="text-gray-900">Client Board</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
            <p className="text-gray-600">Quản lý khách hàng kiểm toán</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/boards')}>
            <BarChart3 className="h-4 w-4 mr-2" />
            All Boards
          </Button>
          <Button size="sm" onClick={() => router.push('/platforms/finance/auditmind/onboarding')}>
            <PlayCircle className="h-4 w-4 mr-2" />
            New Audit
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
              <Building2 className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total_clients}</div>
              <p className="text-xs text-gray-600">
                {statistics.active_audits} active audits
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Documents Processed</CardTitle>
              <FileText className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total_documents_processed}</div>
              <p className="text-xs text-gray-600">
                Across all clients
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Anomalies Detected</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total_anomalies_detected}</div>
              <p className="text-xs text-gray-600">
                Requires investigation
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-red-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Completion</CardTitle>
              <Clock className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.average_completion_time_days}</div>
              <p className="text-xs text-gray-600">
                Days per audit
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="active_audit">Active Audit</option>
          <option value="planning">Planning</option>
          <option value="completed">Completed</option>
        </select>
      </div>

      {/* Client Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredClients.map((client) => (
          <Card key={client.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg">{client.name || 'Unnamed Client'}</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">{client.industry || 'Unknown'} • {client.size || 'Unknown'}</p>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <Badge className={getStatusColor(client.status)}>
                    {client.status ? client.status.replace('_', ' ') : 'Unknown'}
                  </Badge>
                  <Badge size="sm" className={getPriorityColor(client.priority)}>
                    {client.priority || 'Unknown'}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Progress */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Overall Progress</span>
                  <span>{client.progress?.overall_completion || 0}%</span>
                </div>
                <Progress value={client.progress?.overall_completion || 0} className="h-2" />
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-blue-600">{client.progress?.documents_processed || 0}</div>
                  <div className="text-gray-600">Documents</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-orange-600">{client.progress?.anomalies_detected || 0}</div>
                  <div className="text-gray-600">Anomalies</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-red-600">{client.progress?.compliance_issues || 0}</div>
                  <div className="text-gray-600">Issues</div>
                </div>
              </div>

              {/* Details */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Lead Auditor:</span>
                  <span>{client.lead_auditor || 'Not Assigned'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Current Stage:</span>
                  <span className="capitalize">{client.current_stage ? client.current_stage.replace('_', ' ') : 'Not Set'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Expected Completion:</span>
                  <span>{formatDate(client.expected_completion)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Risk Level:</span>
                  <Badge size="sm" className={getRiskColor(client.risk_level)}>
                    {client.risk_level || 'Unknown'}
                  </Badge>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-between items-center pt-2 border-t">
                <div className="text-xs text-gray-500">
                  Last activity: {formatDate(client.last_activity)}
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => router.push(`/platforms/finance/auditmind/clients/${client.id || 'unknown'}/consolidated`)}
                >
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredClients.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
}
