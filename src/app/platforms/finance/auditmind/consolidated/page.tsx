'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button.1';

interface ConsolidatedData {
  clients: any[];
  tasks: any[];
  agents: any[];
  agentWorkloads: Record<string, any>;
  statistics: any;
}

export default function ConsolidatedDashboard() {
  const router = useRouter();
  const [data, setData] = useState<ConsolidatedData | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    const loadData = async () => {
      try {
        const [clientsData, tasksData, agentsData] = await Promise.all([
          import('/data/apps/platforms/finance/auditmind/clients.json'),
          import('/data/apps/platforms/finance/auditmind/tasks.json'),
          import('/data/apps/platforms/finance/auditmind/agents.json')
        ]);

        setData({
          clients: (clientsData as any).clients || [],
          tasks: (tasksData as any).tasks || [],
          agents: (agentsData as any).agents || [],
          agentWorkloads: (tasksData as any).agent_workload || {},
          statistics: (tasksData as any).task_statistics || {}
        });
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
    const interval = setInterval(loadData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading || !data) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const formatTime = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatPercent = (value: number) => `${value.toFixed(1)}%`;
  const formatNumber = (value: number) => value.toLocaleString();

  // Calculate metrics
  const activeClients = data.clients.filter(c => c.status === 'active_audit').length;
  const totalDocuments = data.clients.reduce((sum, c) => sum + c.progress.documents_processed, 0);
  const totalAnomalies = data.clients.reduce((sum, c) => sum + c.progress.anomalies_detected, 0);
  const avgProgress = data.clients.reduce((sum, c) => sum + c.progress.overall_completion, 0) / data.clients.length;
  
  const activeTasks = data.tasks.filter(t => t.status === 'in_progress').length;
  const completedTasks = data.tasks.filter(t => t.status === 'completed').length;
  const pendingTasks = data.tasks.filter(t => t.status === 'pending').length;
  
  const activeAgents = data.agents.filter(a => a.status === 'active').length;
  const avgWorkload = Object.values(data.agentWorkloads).reduce((sum: number, w: any) => sum + w.workload_percentage, 0) / data.agents.length;

  return (
    <div className="min-h-screen bg-white text-black font-mono text-xs">
      {/* Header */}
      <div className="border-b border-gray-300 p-2 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind')}>
            <ArrowLeft className="h-3 w-3 mr-1" />
            Back
          </Button>
          <div>
            <span className="font-bold">ABN AuditMind Consolidated Dashboard</span>
            <span className="ml-4 text-gray-600">Last Update: {formatTime(lastUpdate)}</span>
          </div>
        </div>
        <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/boards')}>
          <BarChart3 className="h-3 w-3 mr-1" />
          Boards
        </Button>
      </div>

      <div className="p-4 grid grid-cols-6 gap-4">
        {/* Clients Section */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Clients</div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Total Clients</span>
              <span className="font-bold">{data.clients.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Active Audits</span>
              <span className="font-bold text-green-600">{activeClients}</span>
            </div>
            <div className="flex justify-between">
              <span>Avg Progress</span>
              <span className="font-bold">{formatPercent(avgProgress)}</span>
            </div>
            <div className="flex justify-between">
              <span>Documents Processed</span>
              <span className="font-bold">{formatNumber(totalDocuments)}</span>
            </div>
            <div className="flex justify-between">
              <span>Anomalies Detected</span>
              <span className="font-bold text-orange-600">{totalAnomalies}</span>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Client Status</div>
            {data.clients.map((client, i) => (
              <div key={i} className="flex justify-between text-xs">
                <span className="truncate w-32">{client.name.substring(0, 20)}...</span>
                <span className={`font-bold ${
                  client.status === 'active_audit' ? 'text-blue-600' :
                  client.status === 'completed' ? 'text-green-600' :
                  client.status === 'planning' ? 'text-yellow-600' : 'text-gray-600'
                }`}>
                  {client.progress.overall_completion}%
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Tasks Section */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Tasks</div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Total Tasks</span>
              <span className="font-bold">{data.tasks.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Active</span>
              <span className="font-bold text-blue-600">{activeTasks}</span>
            </div>
            <div className="flex justify-between">
              <span>Pending</span>
              <span className="font-bold text-yellow-600">{pendingTasks}</span>
            </div>
            <div className="flex justify-between">
              <span>Completed</span>
              <span className="font-bold text-green-600">{completedTasks}</span>
            </div>
            <div className="flex justify-between">
              <span>Completion Rate</span>
              <span className="font-bold">{formatPercent((completedTasks / data.tasks.length) * 100)}</span>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Task Progress</div>
            {data.tasks.map((task, i) => (
              <div key={i} className="flex justify-between text-xs">
                <span className="truncate w-32">{task.name.substring(0, 20)}...</span>
                <span className={`font-bold ${
                  task.status === 'in_progress' ? 'text-blue-600' :
                  task.status === 'completed' ? 'text-green-600' :
                  task.status === 'pending' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {task.progress.overall_completion}%
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Agents Section */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Agents</div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Total Agents</span>
              <span className="font-bold">{data.agents.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Active</span>
              <span className="font-bold text-green-600">{activeAgents}</span>
            </div>
            <div className="flex justify-between">
              <span>Avg Workload</span>
              <span className="font-bold">{formatPercent(avgWorkload)}</span>
            </div>
            <div className="flex justify-between">
              <span>Agent Types</span>
              <span className="font-bold">{new Set(data.agents.map(a => a.type)).size}</span>
            </div>
          </div>

          <div className="mt-3 space-y-1">
            <div className="font-semibold text-gray-700">Agent Workloads</div>
            {data.agents.map((agent, i) => {
              const workload = data.agentWorkloads[agent.id] || { workload_percentage: 0 };
              return (
                <div key={i} className="flex justify-between text-xs">
                  <span className="truncate w-32">{agent.name}</span>
                  <span className={`font-bold ${
                    workload.workload_percentage >= 80 ? 'text-red-600' :
                    workload.workload_percentage >= 60 ? 'text-orange-600' :
                    workload.workload_percentage >= 40 ? 'text-yellow-600' : 'text-green-600'
                  }`}>
                    {workload.workload_percentage}%
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Performance Metrics</div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Document Processing</div>
              <div className="flex justify-between">
                <span>Total Processed</span>
                <span className="font-bold">{formatNumber(totalDocuments)}</span>
              </div>
              <div className="flex justify-between">
                <span>Processing Rate</span>
                <span className="font-bold">145/day</span>
              </div>
              <div className="flex justify-between">
                <span>Error Rate</span>
                <span className="font-bold text-red-600">0.2%</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Quality Metrics</div>
              <div className="flex justify-between">
                <span>Anomaly Detection Rate</span>
                <span className="font-bold">{formatPercent((totalAnomalies / totalDocuments) * 100)}</span>
              </div>
              <div className="flex justify-between">
                <span>False Positive Rate</span>
                <span className="font-bold">2.1%</span>
              </div>
              <div className="flex justify-between">
                <span>Compliance Score</span>
                <span className="font-bold text-green-600">98.5%</span>
              </div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">System Status</div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Resource Utilization</div>
              <div className="flex justify-between">
                <span>CPU Usage</span>
                <span className="font-bold">67.3%</span>
              </div>
              <div className="flex justify-between">
                <span>Memory Usage</span>
                <span className="font-bold">45.8%</span>
              </div>
              <div className="flex justify-between">
                <span>Storage Usage</span>
                <span className="font-bold">23.1%</span>
              </div>
              <div className="flex justify-between">
                <span>Network I/O</span>
                <span className="font-bold">12.4 MB/s</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="font-semibold text-gray-700">Uptime & Reliability</div>
              <div className="flex justify-between">
                <span>System Uptime</span>
                <span className="font-bold text-green-600">99.97%</span>
              </div>
              <div className="flex justify-between">
                <span>Last Restart</span>
                <span className="font-bold">7 days ago</span>
              </div>
              <div className="flex justify-between">
                <span>Error Count (24h)</span>
                <span className="font-bold">3</span>
              </div>
              <div className="flex justify-between">
                <span>Response Time</span>
                <span className="font-bold">145ms</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
