'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { 
  BarChart3,
  Building2,
  CheckSquare,
  Brain,
  Users,
  ArrowLeft,
  ArrowRight,
  Activity,
  TrendingUp,
  Clock,
  Target
} from 'lucide-react';
import { Button } from '@/components/ui/button.1';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

export default function BoardsPage() {
  const router = useRouter();

  const boards = [
    {
      id: 'clients',
      title: 'Client Board',
      description: 'Comprehensive client management and audit progress tracking',
      icon: Building2,
      color: 'blue',
      path: '/platforms/finance/auditmind/clients',
      features: [
        'Client status monitoring',
        'Audit progress tracking',
        'Document processing metrics',
        'Risk assessment overview'
      ],
      stats: {
        label: 'Active Clients',
        value: '4',
        trend: '+2 this month'
      }
    },
    {
      id: 'tasks',
      title: 'Task Board',
      description: 'Detailed task management with stage-by-stage progress monitoring',
      icon: CheckSquare,
      color: 'green',
      path: '/platforms/finance/auditmind/tasks',
      features: [
        'Multi-stage task workflows',
        'Agent assignment tracking',
        'Progress visualization',
        'Deadline management'
      ],
      stats: {
        label: 'Active Tasks',
        value: '12',
        trend: '75% completion rate'
      }
    },
    {
      id: 'agents',
      title: 'Agent Board',
      description: 'AI agent monitoring with workload and performance analytics',
      icon: Brain,
      color: 'purple',
      path: '/platforms/finance/auditmind/agents',
      features: [
        'Real-time agent status',
        'Workload distribution',
        'Performance metrics',
        'Capability management'
      ],
      stats: {
        label: 'Active Agents',
        value: '8',
        trend: '95% uptime'
      }
    },
    {
      id: 'assignments',
      title: 'Assignment Board',
      description: 'Task-agent assignment matrix with optimization insights',
      icon: Users,
      color: 'orange',
      path: '/platforms/finance/auditmind/assignments',
      features: [
        'Assignment visualization',
        'Workload balancing',
        'Resource optimization',
        'Bottleneck identification'
      ],
      stats: {
        label: 'Assignments',
        value: '24',
        trend: 'Balanced workload'
      }
    }
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return {
          border: 'border-l-blue-500 hover:border-l-blue-600',
          icon: 'text-blue-600',
          text: 'text-blue-600',
          bg: 'bg-blue-50'
        };
      case 'green':
        return {
          border: 'border-l-green-500 hover:border-l-green-600',
          icon: 'text-green-600',
          text: 'text-green-600',
          bg: 'bg-green-50'
        };
      case 'purple':
        return {
          border: 'border-l-purple-500 hover:border-l-purple-600',
          icon: 'text-purple-600',
          text: 'text-purple-600',
          bg: 'bg-purple-50'
        };
      case 'orange':
        return {
          border: 'border-l-orange-500 hover:border-l-orange-600',
          icon: 'text-orange-600',
          text: 'text-orange-600',
          bg: 'bg-orange-50'
        };
      default:
        return {
          border: 'border-l-gray-500 hover:border-l-gray-600',
          icon: 'text-gray-600',
          text: 'text-gray-600',
          bg: 'bg-gray-50'
        };
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/consolidated')}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Consolidated
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">AuditMind Boards</h1>
            <p className="text-gray-600">Specialized dashboards for comprehensive audit monitoring</p>
          </div>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Boards</p>
                <p className="text-2xl font-bold text-gray-900">{boards.length}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Monitoring</p>
                <p className="text-2xl font-bold text-gray-900">24/7</p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Data Sources</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Update Frequency</p>
                <p className="text-2xl font-bold text-gray-900">Real-time</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Boards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {boards.map((board) => {
          const colors = getColorClasses(board.color);
          const IconComponent = board.icon;
          
          return (
            <Card 
              key={board.id}
              className={`cursor-pointer hover:shadow-lg transition-all duration-200 border-l-4 ${colors.border}`}
              onClick={() => router.push(board.path)}
            >
              <CardHeader className={colors.bg}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-white rounded-lg shadow-sm">
                      <IconComponent className={`h-6 w-6 ${colors.icon}`} />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{board.title}</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">{board.description}</p>
                    </div>
                  </div>
                  <ArrowRight className={`h-5 w-5 ${colors.icon}`} />
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Stats */}
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="text-sm text-gray-600">{board.stats.label}</p>
                      <p className="text-lg font-semibold text-gray-900">{board.stats.value}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">{board.stats.trend}</p>
                      <TrendingUp className="h-4 w-4 text-green-500 ml-auto" />
                    </div>
                  </div>

                  {/* Features */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Key Features</h4>
                    <ul className="space-y-1">
                      {board.features.map((feature, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-center">
                          <div className={`w-1.5 h-1.5 rounded-full ${colors.icon.replace('text-', 'bg-')} mr-2`} />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action */}
                  <div className="pt-2 border-t">
                    <Button 
                      className="w-full" 
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(board.path);
                      }}
                    >
                      <span>Open {board.title}</span>
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Access */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Access</CardTitle>
          <p className="text-sm text-gray-600">Jump directly to specific board sections</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push('/platforms/finance/auditmind/clients')}
            >
              <Building2 className="h-4 w-4 mr-2" />
              Active Clients
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push('/platforms/finance/auditmind/tasks')}
            >
              <CheckSquare className="h-4 w-4 mr-2" />
              Running Tasks
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push('/platforms/finance/auditmind/agents')}
            >
              <Brain className="h-4 w-4 mr-2" />
              Agent Status
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push('/platforms/finance/auditmind/assignments')}
            >
              <Users className="h-4 w-4 mr-2" />
              Assignments
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
