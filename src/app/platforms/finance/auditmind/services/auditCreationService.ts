import fs from 'fs/promises';
import path from 'path';

interface ClientData {
  id: string;
  name: string;
  industry: string;
  size: string;
  status: string;
  audit_type: string;
  start_date: string;
  expected_completion: string;
  assigned_agents: string[];
  lead_auditor: string;
  contact_person: {
    name: string;
    position: string;
    email: string;
    phone: string;
  };
  progress: {
    overall_completion: number;
    documents_requested: number;
    documents_received: number;
    documents_processed: number;
    anomalies_detected: number;
    compliance_issues: number;
    findings_count: number;
  };
  risk_assessment: {
    overall_risk: string;
    financial_risk: string;
    operational_risk: string;
    compliance_risk: string;
  };
  priority: string;
  last_activity: string;
  created_at: string;
}

interface TaskData {
  id: string;
  name: string;
  description: string;
  type: string;
  client_id: string;
  status: string;
  priority: string;
  assigned_agents: string[];
  current_agent: string;
  stage: string;
  stages: Array<{
    name: string;
    status: string;
    progress: number;
    agent: string;
    start_time?: string;
  }>;
  progress: {
    overall_completion: number;
    documents_processed: number;
    total_documents: number;
    anomalies_found: number;
    compliance_issues: number;
  };
  estimated_time: number;
  elapsed_time: number;
  created_at: string;
  updated_at: string;
  deadline: string;
}

interface AuditCreationRequest {
  clientInfo: {
    name: string;
    industry: string;
    size: string;
    contactPerson: string;
    email: string;
    phone: string;
    auditTypes: string[];
  };
  selectedCategories: string[];
  scheduleInfo: {
    startDate: string;
    expectedCompletion: string;
    priority: string;
    leadAuditor: string;
  };
}

export class AuditCreationService {
  private static readonly DATA_PATH = path.join(process.cwd(), 'data/apps/platforms/finance/auditmind');

  static async createAudit(request: AuditCreationRequest): Promise<{ clientId: string; taskIds: string[] }> {
    const clientId = `client_${Date.now().toString().slice(-3)}`;
    const taskIds: string[] = [];

    // Create client record
    const newClient = await this.createClientRecord(clientId, request);
    
    // Generate tasks based on selected categories
    const tasks = await this.generateTasksForCategories(clientId, request.selectedCategories, request.scheduleInfo);
    taskIds.push(...tasks.map(t => t.id));

    // Update data files
    await this.updateClientsFile(newClient);
    await this.updateTasksFile(tasks);
    
    // Initialize AI agent coordination
    await this.initializeAICoordination(clientId, tasks);

    return { clientId, taskIds };
  }

  private static async createClientRecord(clientId: string, request: AuditCreationRequest): Promise<ClientData> {
    const riskLevel = this.calculateRiskLevel(request.clientInfo.industry, request.clientInfo.size);
    
    return {
      id: clientId,
      name: request.clientInfo.name,
      industry: request.clientInfo.industry.toLowerCase(),
      size: request.clientInfo.size.toLowerCase().split(' ')[0],
      status: 'planning',
      audit_type: request.clientInfo.auditTypes.map(type => type.toLowerCase().replace(' ', '_')).join(','),
      start_date: new Date(request.scheduleInfo.startDate).toISOString(),
      expected_completion: new Date(request.scheduleInfo.expectedCompletion).toISOString(),
      assigned_agents: this.getRequiredAgents(request.selectedCategories),
      lead_auditor: request.scheduleInfo.leadAuditor,
      contact_person: {
        name: request.clientInfo.contactPerson,
        position: 'Contact Person',
        email: request.clientInfo.email,
        phone: request.clientInfo.phone || ''
      },
      progress: {
        overall_completion: 0,
        documents_requested: 0,
        documents_received: 0,
        documents_processed: 0,
        anomalies_detected: 0,
        compliance_issues: 0,
        findings_count: 0
      },
      risk_assessment: riskLevel,
      priority: request.scheduleInfo.priority,
      last_activity: new Date().toISOString(),
      created_at: new Date().toISOString()
    };
  }

  private static calculateRiskLevel(industry: string, size: string) {
    const highRiskIndustries = ['banking', 'healthcare', 'government'];
    const isHighRisk = highRiskIndustries.includes(industry.toLowerCase());
    const isLargeCompany = size.toLowerCase().includes('large') || size.toLowerCase().includes('enterprise');

    return {
      overall_risk: isHighRisk && isLargeCompany ? 'high' : isHighRisk || isLargeCompany ? 'medium' : 'low',
      financial_risk: isHighRisk ? 'high' : 'medium',
      operational_risk: isLargeCompany ? 'medium' : 'low',
      compliance_risk: isHighRisk ? 'high' : 'medium'
    };
  }

  private static getRequiredAgents(selectedCategories: string[]): string[] {
    const agentMap: Record<string, string[]> = {
      'financial_compliance': ['scheduler_agent', 'extractor_agent', 'compliance_agent', 'reasoning_agent'],
      'operational_audit': ['scheduler_agent', 'watcher_agent', 'anomaly_agent', 'reasoning_agent'],
      'risk_assessment': ['scheduler_agent', 'knowledge_agent', 'anomaly_agent', 'alert_review_agent'],
      'it_security_audit': ['scheduler_agent', 'extractor_agent', 'anomaly_agent', 'compliance_agent']
    };

    const allAgents = new Set<string>();
    selectedCategories.forEach(category => {
      const agents = agentMap[category] || ['scheduler_agent'];
      agents.forEach(agent => allAgents.add(agent));
    });

    return Array.from(allAgents);
  }

  private static async generateTasksForCategories(
    clientId: string, 
    categories: string[], 
    scheduleInfo: AuditCreationRequest['scheduleInfo']
  ): Promise<TaskData[]> {
    const tasks: TaskData[] = [];
    let taskCounter = 1;

    const taskTemplates: Record<string, Array<{
      name: string;
      description: string;
      type: string;
      estimatedHours: number;
      priority: string;
      agents: string[];
      stages: string[];
    }>> = {
      'financial_compliance': [
        {
          name: `Phân tích báo cáo tài chính - ${clientId}`,
          description: 'Phân tích chi tiết báo cáo tài chính và kiểm tra tuân thủ',
          type: 'document_analysis',
          estimatedHours: 24,
          priority: 'high',
          agents: ['extractor_agent', 'compliance_agent'],
          stages: ['data_extraction', 'compliance_check', 'anomaly_detection']
        },
        {
          name: `Kiểm tra tuân thủ quy định - ${clientId}`,
          description: 'Xác minh tuân thủ các quy định tài chính',
          type: 'compliance_check',
          estimatedHours: 16,
          priority: 'high',
          agents: ['compliance_agent', 'reasoning_agent'],
          stages: ['regulation_review', 'compliance_verification', 'reporting']
        }
      ],
      'operational_audit': [
        {
          name: `Đánh giá quy trình vận hành - ${clientId}`,
          description: 'Phân tích quy trình và kiểm soát nội bộ',
          type: 'process_review',
          estimatedHours: 20,
          priority: 'medium',
          agents: ['watcher_agent', 'anomaly_agent'],
          stages: ['process_mapping', 'control_testing', 'efficiency_analysis']
        }
      ],
      'risk_assessment': [
        {
          name: `Đánh giá rủi ro toàn diện - ${clientId}`,
          description: 'Xác định và phân tích các rủi ro kinh doanh',
          type: 'risk_analysis',
          estimatedHours: 16,
          priority: 'high',
          agents: ['knowledge_agent', 'anomaly_agent'],
          stages: ['risk_identification', 'risk_analysis', 'mitigation_planning']
        }
      ],
      'it_security_audit': [
        {
          name: `Kiểm tra bảo mật IT - ${clientId}`,
          description: 'Đánh giá hệ thống bảo mật công nghệ thông tin',
          type: 'security_audit',
          estimatedHours: 24,
          priority: 'high',
          agents: ['extractor_agent', 'anomaly_agent'],
          stages: ['security_assessment', 'vulnerability_scan', 'compliance_check']
        }
      ]
    };

    for (const category of categories) {
      const templates = taskTemplates[category] || [];
      
      for (const template of templates) {
        const taskId = `task_${clientId}_${taskCounter.toString().padStart(3, '0')}`;
        
        const task: TaskData = {
          id: taskId,
          name: template.name,
          description: template.description,
          type: template.type,
          client_id: clientId,
          status: 'pending',
          priority: template.priority,
          assigned_agents: template.agents,
          current_agent: template.agents[0],
          stage: template.stages[0],
          stages: template.stages.map((stageName, index) => ({
            name: stageName,
            status: index === 0 ? 'pending' : 'pending',
            progress: 0,
            agent: template.agents[Math.min(index, template.agents.length - 1)]
          })),
          progress: {
            overall_completion: 0,
            documents_processed: 0,
            total_documents: Math.floor(Math.random() * 20) + 10,
            anomalies_found: 0,
            compliance_issues: 0
          },
          estimated_time: template.estimatedHours * 60, // Convert to minutes
          elapsed_time: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          deadline: new Date(Date.now() + template.estimatedHours * 60 * 60 * 1000).toISOString()
        };

        tasks.push(task);
        taskCounter++;
      }
    }

    return tasks;
  }

  private static async updateClientsFile(newClient: ClientData): Promise<void> {
    try {
      const clientsPath = path.join(this.DATA_PATH, 'clients.json');
      const data = await fs.readFile(clientsPath, 'utf-8');
      const clientsData = JSON.parse(data);
      
      clientsData.clients.push(newClient);
      clientsData.client_statistics.total_clients = clientsData.clients.length;
      clientsData.client_statistics.planning_stage = clientsData.clients.filter((c: any) => c.status === 'planning').length;
      clientsData.metadata.last_updated = new Date().toISOString();
      
      await fs.writeFile(clientsPath, JSON.stringify(clientsData, null, 2));
    } catch (error) {
      console.error('Error updating clients file:', error);
      throw error;
    }
  }

  private static async updateTasksFile(newTasks: TaskData[]): Promise<void> {
    try {
      const tasksPath = path.join(this.DATA_PATH, 'tasks.json');
      const data = await fs.readFile(tasksPath, 'utf-8');
      const tasksData = JSON.parse(data);
      
      tasksData.tasks.push(...newTasks);
      tasksData.metadata.last_updated = new Date().toISOString();
      
      await fs.writeFile(tasksPath, JSON.stringify(tasksData, null, 2));
    } catch (error) {
      console.error('Error updating tasks file:', error);
      throw error;
    }
  }

  private static async initializeAICoordination(clientId: string, tasks: TaskData[]): Promise<void> {
    // Simulate Master Audit AI initialization
    console.log(`🤖 Master Audit AI initialized for client ${clientId}`);
    console.log(`📋 Created ${tasks.length} tasks for AI agents`);
    console.log(`🎯 Agents assigned: ${Array.from(new Set(tasks.flatMap(t => t.assigned_agents))).join(', ')}`);
    
    // In a real implementation, this would:
    // 1. Initialize agent workload distribution
    // 2. Set up monitoring and coordination
    // 3. Create agent communication channels
    // 4. Schedule initial task assignments
    
    // For now, we'll simulate by updating the first task to "in_progress"
    if (tasks.length > 0) {
      tasks[0].status = 'in_progress';
      tasks[0].stages[0].status = 'in_progress';
      tasks[0].stages[0].start_time = new Date().toISOString();
    }
  }
}
