'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  ArrowLeft, 
  Building2, 
  Search, 
  CheckCircle,
  Plus,
  Users,
  MapPin,
  Phone,
  Mail
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button.1';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ExistingCompany {
  id: string;
  name: string;
  industry: string;
  size: string;
  location: string;
  contactPerson: string;
  email: string;
  phone: string;
  lastAudit?: string;
  totalAudits: number;
  status: 'active' | 'inactive' | 'new';
}

export default function SelectCompanyPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [companies, setCompanies] = useState<ExistingCompany[]>([]);
  const [filteredCompanies, setFilteredCompanies] = useState<ExistingCompany[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('all');
  const [loading, setLoading] = useState(true);

  // Mock data for existing companies
  const mockCompanies: ExistingCompany[] = [
    {
      id: 'comp_001',
      name: 'Công ty TNHH ABC Manufacturing',
      industry: 'Manufacturing',
      size: 'Large (251-1000)',
      location: 'Ho Chi Minh City',
      contactPerson: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '+84-***********',
      lastAudit: '2024-01-15',
      totalAudits: 3,
      status: 'active'
    },
    {
      id: 'comp_002',
      name: 'Ngân hàng XYZ',
      industry: 'Banking',
      size: 'Enterprise (1000+)',
      location: 'Hanoi',
      contactPerson: 'Phạm Thị D',
      email: '<EMAIL>',
      phone: '+84-***********',
      lastAudit: '2024-03-20',
      totalAudits: 5,
      status: 'active'
    },
    {
      id: 'comp_003',
      name: 'Tập đoàn DEF Holdings',
      industry: 'Conglomerate',
      size: 'Enterprise (1000+)',
      location: 'Da Nang',
      contactPerson: 'Vũ Thị F',
      email: '<EMAIL>',
      phone: '+84-***********',
      lastAudit: '2023-11-10',
      totalAudits: 2,
      status: 'inactive'
    },
    {
      id: 'comp_004',
      name: 'Công ty CP GHI Technology',
      industry: 'Technology',
      size: 'Medium (51-250)',
      location: 'Ho Chi Minh City',
      contactPerson: 'Bùi Thị H',
      email: '<EMAIL>',
      phone: '+84-333-789-012',
      lastAudit: '2024-06-28',
      totalAudits: 1,
      status: 'active'
    },
    {
      id: 'comp_005',
      name: 'Bệnh viện JKL Healthcare',
      industry: 'Healthcare',
      size: 'Large (251-1000)',
      location: 'Hanoi',
      contactPerson: 'Nguyễn Văn I',
      email: '<EMAIL>',
      phone: '+84-444-567-890',
      totalAudits: 0,
      status: 'new'
    },
    {
      id: 'comp_006',
      name: 'Công ty Xây dựng MNO',
      industry: 'Construction',
      size: 'Medium (51-250)',
      location: 'Can Tho',
      contactPerson: 'Lê Thị K',
      email: '<EMAIL>',
      phone: '+84-666-123-789',
      lastAudit: '2023-08-15',
      totalAudits: 2,
      status: 'inactive'
    }
  ];

  const industries = ['all', 'Manufacturing', 'Banking', 'Technology', 'Healthcare', 'Construction', 'Conglomerate'];

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setCompanies(mockCompanies);
      setFilteredCompanies(mockCompanies);
      setLoading(false);
    }, 500);
  }, []);

  useEffect(() => {
    let filtered = companies;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(company =>
        company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by industry
    if (selectedIndustry !== 'all') {
      filtered = filtered.filter(company => company.industry === selectedIndustry);
    }

    setFilteredCompanies(filtered);
  }, [searchTerm, selectedIndustry, companies]);

  const handleSelectCompany = (company: ExistingCompany) => {
    // Pass company data back to onboarding
    const companyData = {
      name: company.name,
      industry: company.industry,
      size: company.size,
      contactPerson: company.contactPerson,
      email: company.email,
      phone: company.phone
    };

    // Encode the data and navigate back
    const encodedData = encodeURIComponent(JSON.stringify(companyData));
    router.push(`/platforms/finance/auditmind/onboarding?company=${encodedData}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'new': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/onboarding')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Onboarding
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Select Existing Company</h1>
              <p className="text-gray-600">Choose from previously registered companies or create a new one</p>
            </div>
          </div>
          <Button onClick={() => router.push('/platforms/finance/auditmind/onboarding')}>
            <Plus className="h-4 w-4 mr-2" />
            Add New Company
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search companies, contacts, or emails..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="w-full md:w-48">
            <select
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={selectedIndustry}
              onChange={(e) => setSelectedIndustry(e.target.value)}
            >
              {industries.map(industry => (
                <option key={industry} value={industry}>
                  {industry === 'all' ? 'All Industries' : industry}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Companies List */}
      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Found {filteredCompanies.length} companies
            {searchTerm && ` matching "${searchTerm}"`}
            {selectedIndustry !== 'all' && ` in ${selectedIndustry}`}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredCompanies.map((company) => (
            <Card 
              key={company.id} 
              className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:border-blue-300"
              onClick={() => handleSelectCompany(company)}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center">
                      <Building2 className="h-5 w-5 mr-2 text-blue-600" />
                      {company.name}
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">{company.industry} • {company.size}</p>
                  </div>
                  <Badge className={getStatusColor(company.status)}>
                    {company.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex items-center text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    {company.location}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    {company.contactPerson}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    {company.email}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Phone className="h-4 w-4 mr-2" />
                    {company.phone}
                  </div>
                </div>

                <div className="pt-3 border-t border-gray-100">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Total Audits:</span>
                      <div className="font-medium">{company.totalAudits}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Last Audit:</span>
                      <div className="font-medium">{formatDate(company.lastAudit)}</div>
                    </div>
                  </div>
                </div>

                <div className="pt-2">
                  <Button className="w-full" variant="outline">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Select This Company
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredCompanies.length === 0 && (
          <div className="text-center py-12">
            <Building2 className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No companies found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedIndustry !== 'all' 
                ? 'Try adjusting your search or filter criteria.' 
                : 'No companies have been registered yet.'}
            </p>
            <Button onClick={() => router.push('/platforms/finance/auditmind/onboarding')}>
              <Plus className="h-4 w-4 mr-2" />
              Add New Company
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
