'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  ArrowLeft, 
  ArrowRight, 
  Building2, 
  Calendar, 
  CheckSquare, 
  Clock, 
  FileText, 
  Users, 
  AlertTriangle,
  Target,
  Brain,
  Settings,
  Play,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button.1';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface AuditCategory {
  id: string;
  name: string;
  description: string;
  tasks: AuditTask[];
  estimatedDays: number;
  complexity: 'low' | 'medium' | 'high';
  requiredAgents: string[];
}

interface AuditTask {
  id: string;
  name: string;
  description: string;
  estimatedHours: number;
  priority: 'low' | 'medium' | 'high';
  dependencies: string[];
  requiredDocuments: string[];
  assignedAgent: string;
}

interface ClientInfo {
  name: string;
  industry: string;
  size: string;
  contactPerson: string;
  email: string;
  phone: string;
  auditTypes: string[];
}

export default function AuditOnboarding() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [clientInfo, setClientInfo] = useState<ClientInfo>({
    name: '',
    industry: '',
    size: '',
    contactPerson: '',
    email: '',
    phone: '',
    auditTypes: []
  });
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [scheduleInfo, setScheduleInfo] = useState({
    startDate: new Date().toISOString().split('T')[0], // Default to today
    expectedCompletion: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Default to 30 days from now
    priority: 'medium',
    leadAuditor: 'Nguyễn Văn A' // Default lead auditor
  });
  const [isCreating, setIsCreating] = useState(false);

  // Check if company data was passed from company selection
  useEffect(() => {
    const companyParam = searchParams.get('company');
    if (companyParam) {
      try {
        const companyData = JSON.parse(decodeURIComponent(companyParam));
        setClientInfo(prev => ({
          ...prev,
          ...companyData,
          auditTypes: prev.auditTypes // Keep existing audit types
        }));
      } catch (error) {
        console.error('Error parsing company data:', error);
      }
    }
  }, [searchParams]);

  const auditCategories: AuditCategory[] = [
    {
      id: 'financial_compliance',
      name: 'Financial Compliance',
      description: 'Comprehensive financial statement and regulatory compliance audit',
      estimatedDays: 15,
      complexity: 'high',
      requiredAgents: ['extractor_agent', 'compliance_agent', 'reasoning_agent'],
      tasks: [
        {
          id: 'financial_statements_review',
          name: 'Financial Statements Review',
          description: 'Analyze balance sheet, income statement, and cash flow',
          estimatedHours: 24,
          priority: 'high',
          dependencies: [],
          requiredDocuments: ['Balance Sheet', 'Income Statement', 'Cash Flow Statement'],
          assignedAgent: 'extractor_agent'
        },
        {
          id: 'regulatory_compliance_check',
          name: 'Regulatory Compliance Check',
          description: 'Verify compliance with financial regulations',
          estimatedHours: 16,
          priority: 'high',
          dependencies: ['financial_statements_review'],
          requiredDocuments: ['Regulatory Filings', 'Compliance Reports'],
          assignedAgent: 'compliance_agent'
        }
      ]
    },
    {
      id: 'operational_audit',
      name: 'Operational Audit',
      description: 'Review of operational processes and internal controls',
      estimatedDays: 12,
      complexity: 'medium',
      requiredAgents: ['watcher_agent', 'anomaly_agent', 'reasoning_agent'],
      tasks: [
        {
          id: 'process_review',
          name: 'Process Review',
          description: 'Analyze operational processes and workflows',
          estimatedHours: 20,
          priority: 'medium',
          dependencies: [],
          requiredDocuments: ['Process Documentation', 'Workflow Charts'],
          assignedAgent: 'watcher_agent'
        },
        {
          id: 'internal_controls_assessment',
          name: 'Internal Controls Assessment',
          description: 'Evaluate effectiveness of internal controls',
          estimatedHours: 18,
          priority: 'medium',
          dependencies: ['process_review'],
          requiredDocuments: ['Control Documentation', 'Risk Assessments'],
          assignedAgent: 'anomaly_agent'
        }
      ]
    },
    {
      id: 'risk_assessment',
      name: 'Risk Assessment',
      description: 'Comprehensive risk analysis and mitigation strategies',
      estimatedDays: 8,
      complexity: 'medium',
      requiredAgents: ['knowledge_agent', 'anomaly_agent', 'alert_review_agent'],
      tasks: [
        {
          id: 'risk_identification',
          name: 'Risk Identification',
          description: 'Identify and categorize business risks',
          estimatedHours: 12,
          priority: 'high',
          dependencies: [],
          requiredDocuments: ['Risk Register', 'Historical Data'],
          assignedAgent: 'knowledge_agent'
        },
        {
          id: 'risk_analysis',
          name: 'Risk Analysis',
          description: 'Analyze probability and impact of identified risks',
          estimatedHours: 16,
          priority: 'high',
          dependencies: ['risk_identification'],
          requiredDocuments: ['Risk Assessments', 'Impact Analysis'],
          assignedAgent: 'anomaly_agent'
        }
      ]
    },
    {
      id: 'it_security_audit',
      name: 'IT Security Audit',
      description: 'Information technology and cybersecurity assessment',
      estimatedDays: 10,
      complexity: 'high',
      requiredAgents: ['extractor_agent', 'anomaly_agent', 'compliance_agent'],
      tasks: [
        {
          id: 'security_controls_review',
          name: 'Security Controls Review',
          description: 'Evaluate IT security controls and policies',
          estimatedHours: 20,
          priority: 'high',
          dependencies: [],
          requiredDocuments: ['Security Policies', 'Access Controls', 'Network Diagrams'],
          assignedAgent: 'extractor_agent'
        },
        {
          id: 'vulnerability_assessment',
          name: 'Vulnerability Assessment',
          description: 'Identify and assess security vulnerabilities',
          estimatedHours: 24,
          priority: 'high',
          dependencies: ['security_controls_review'],
          requiredDocuments: ['Vulnerability Scans', 'Penetration Test Reports'],
          assignedAgent: 'anomaly_agent'
        }
      ]
    }
  ];

  const industries = [
    'Manufacturing', 'Banking', 'Technology', 'Healthcare', 'Retail', 
    'Construction', 'Education', 'Government', 'Non-profit', 'Other'
  ];

  const companySizes = ['Small (1-50)', 'Medium (51-250)', 'Large (251-1000)', 'Enterprise (1000+)'];
  const auditTypes = ['Financial Audit', 'Compliance Audit', 'Operational Audit', 'IT Audit', 'Comprehensive Audit'];
  const leadAuditors = ['Nguyễn Văn A', 'Lê Văn C', 'Hoàng Văn E', 'Đặng Văn G'];

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleTaskToggle = (taskId: string) => {
    setSelectedTasks(prev =>
      prev.includes(taskId)
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    );
  };

  const handleAuditTypeToggle = (auditType: string) => {
    setClientInfo(prev => ({
      ...prev,
      auditTypes: prev.auditTypes.includes(auditType)
        ? prev.auditTypes.filter(type => type !== auditType)
        : [...prev.auditTypes, auditType]
    }));
  };

  const calculateTotalEstimate = () => {
    const selectedCats = auditCategories.filter(cat => selectedCategories.includes(cat.id));
    const totalDays = selectedCats.reduce((sum, cat) => sum + cat.estimatedDays, 0);
    const totalTasks = selectedCats.reduce((sum, cat) => sum + cat.tasks.length, 0);
    return { totalDays, totalTasks };
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-blue-100 text-blue-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canProceedToNext = () => {
    switch (currentStep) {
      case 1:
        return clientInfo.name && clientInfo.industry && clientInfo.size &&
               clientInfo.contactPerson && clientInfo.email && clientInfo.auditTypes.length > 0;
      case 2:
        return selectedCategories.length > 0;
      case 3:
        return true; // All validation is already done in previous steps
      default:
        return false;
    }
  };

  const handleCreateAudit = async () => {
    setIsCreating(true);

    try {
      const response = await fetch('/api/platforms/finance/auditmind/create-audit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientInfo,
          selectedCategories,
          scheduleInfo
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create audit');
      }

      // Show success message
      alert(`✅ Audit created successfully!\n\nClient ID: ${result.data.clientId}\nTasks Created: ${result.data.taskIds.length}\n\nThe Master Audit AI has been initialized and will begin coordinating the assigned agents.`);

      // Navigate to the new client's dashboard
      router.push(`/platforms/finance/auditmind/clients/${result.data.clientId}/consolidated`);
    } catch (error) {
      console.error('Error creating audit:', error);
      alert(`❌ Error creating audit: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsCreating(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold mb-4">Client Information</h2>
              <p className="text-gray-600 mb-4">Enter the basic information about the client to be audited.</p>

              <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-blue-900">Select from Existing Companies</h3>
                    <p className="text-sm text-blue-700">Choose from previously registered companies to save time</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/platforms/finance/auditmind/onboarding/select-company')}
                    className="border-blue-300 text-blue-700 hover:bg-blue-100"
                  >
                    <Building2 className="h-4 w-4 mr-2" />
                    Browse Companies
                  </Button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">Company Name *</label>
                <input
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter company name"
                  value={clientInfo.name}
                  onChange={(e) => setClientInfo(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Industry *</label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={clientInfo.industry}
                  onChange={(e) => setClientInfo(prev => ({ ...prev, industry: e.target.value }))}
                >
                  <option value="">Select industry</option>
                  {industries.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Company Size *</label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={clientInfo.size}
                  onChange={(e) => setClientInfo(prev => ({ ...prev, size: e.target.value }))}
                >
                  <option value="">Select company size</option>
                  {companySizes.map(size => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-3">Audit Types * (Select one or more)</label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {auditTypes.map(type => (
                    <div
                      key={type}
                      className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                        clientInfo.auditTypes.includes(type)
                          ? 'border-blue-500 bg-blue-50 text-blue-900'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      onClick={() => handleAuditTypeToggle(type)}
                    >
                      <div className="flex items-center">
                        <div className={`w-4 h-4 rounded border-2 mr-2 flex items-center justify-center ${
                          clientInfo.auditTypes.includes(type)
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {clientInfo.auditTypes.includes(type) && (
                            <CheckSquare className="h-3 w-3 text-white" />
                          )}
                        </div>
                        <span className="text-sm font-medium">{type}</span>
                      </div>
                    </div>
                  ))}
                </div>
                {clientInfo.auditTypes.length > 0 && (
                  <p className="text-sm text-blue-600 mt-2">
                    Selected: {clientInfo.auditTypes.join(', ')}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Contact Person *</label>
                <input
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter contact person name"
                  value={clientInfo.contactPerson}
                  onChange={(e) => setClientInfo(prev => ({ ...prev, contactPerson: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Email *</label>
                <input
                  type="email"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter email address"
                  value={clientInfo.email}
                  onChange={(e) => setClientInfo(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-2">Phone</label>
                <input
                  type="tel"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter phone number"
                  value={clientInfo.phone}
                  onChange={(e) => setClientInfo(prev => ({ ...prev, phone: e.target.value }))}
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold mb-4">Select Audit Categories & Tasks</h2>
              <p className="text-gray-600 mb-6">Choose the audit categories and specific tasks to be performed.</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {auditCategories.map((category) => (
                <Card 
                  key={category.id} 
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedCategories.includes(category.id) 
                      ? 'ring-2 ring-blue-500 bg-blue-50' 
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => handleCategoryToggle(category.id)}
                >
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <CardTitle className="text-lg flex items-center">
                          <CheckSquare className={`h-5 w-5 mr-2 ${
                            selectedCategories.includes(category.id) ? 'text-blue-600' : 'text-gray-400'
                          }`} />
                          {category.name}
                        </CardTitle>
                        <p className="text-sm text-gray-600 mt-2">{category.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 mt-3">
                      <Badge className={getComplexityColor(category.complexity)}>
                        {category.complexity}
                      </Badge>
                      <Badge variant="outline">
                        <Clock className="h-3 w-3 mr-1" />
                        {category.estimatedDays} days
                      </Badge>
                      <Badge variant="outline">
                        <Users className="h-3 w-3 mr-1" />
                        {category.requiredAgents.length} agents
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  {selectedCategories.includes(category.id) && (
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        <h4 className="font-medium text-sm">Included Tasks:</h4>
                        {category.tasks.map((task) => (
                          <div key={task.id} className="bg-white p-3 rounded border">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h5 className="font-medium text-sm">{task.name}</h5>
                                <p className="text-xs text-gray-600 mt-1">{task.description}</p>
                                <div className="flex items-center space-x-2 mt-2">
                                  <Badge size="sm" className={getPriorityColor(task.priority)}>
                                    {task.priority}
                                  </Badge>
                                  <Badge size="sm" variant="outline">
                                    {task.estimatedHours}h
                                  </Badge>
                                  <Badge size="sm" variant="outline">
                                    {task.assignedAgent.replace('_', ' ')}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>

            {selectedCategories.length > 0 && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <h3 className="font-medium mb-2">Audit Summary</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Categories:</span>
                      <div className="font-bold">{selectedCategories.length}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Total Tasks:</span>
                      <div className="font-bold">{calculateTotalEstimate().totalTasks}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Estimated Duration:</span>
                      <div className="font-bold">{calculateTotalEstimate().totalDays} days</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Required Agents:</span>
                      <div className="font-bold">
                        {new Set(
                          auditCategories
                            .filter(cat => selectedCategories.includes(cat.id))
                            .flatMap(cat => cat.requiredAgents)
                        ).size}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-bold mb-4">Review & Confirm</h2>
              <p className="text-gray-600 mb-6">Please review all information before creating the audit.</p>
            </div>

            <div className="space-y-6">
              {/* Client Information Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building2 className="h-5 w-5 mr-2" />
                    Client Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <span className="text-gray-600">Company:</span>
                    <span className="font-medium">{clientInfo.name}</span>
                    <span className="text-gray-600">Industry:</span>
                    <span className="font-medium">{clientInfo.industry}</span>
                    <span className="text-gray-600">Size:</span>
                    <span className="font-medium">{clientInfo.size}</span>
                    <span className="text-gray-600">Audit Types:</span>
                    <span className="font-medium">{clientInfo.auditTypes.join(', ')}</span>
                    <span className="text-gray-600">Contact:</span>
                    <span className="font-medium">{clientInfo.contactPerson}</span>
                    <span className="text-gray-600">Email:</span>
                    <span className="font-medium">{clientInfo.email}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Audit Categories Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Selected Audit Categories
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {auditCategories
                      .filter(cat => selectedCategories.includes(cat.id))
                      .map(category => (
                        <div key={category.id} className="bg-gray-50 p-4 rounded">
                          <h4 className="font-medium mb-2">{category.name}</h4>
                          <p className="text-sm text-gray-600 mb-3">{category.description}</p>
                          <div className="flex items-center space-x-2">
                            <Badge className={getComplexityColor(category.complexity)}>
                              {category.complexity}
                            </Badge>
                            <Badge variant="outline">
                              {category.estimatedDays} days
                            </Badge>
                            <Badge variant="outline">
                              {category.tasks.length} tasks
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                  
                  <div className="mt-6 p-4 bg-blue-50 rounded">
                    <h4 className="font-medium mb-2">Total Estimate</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Categories:</span>
                        <div className="font-bold">{selectedCategories.length}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Tasks:</span>
                        <div className="font-bold">{calculateTotalEstimate().totalTasks}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <div className="font-bold">{calculateTotalEstimate().totalDays} days</div>
                      </div>
                      <div>
                        <span className="text-gray-600">AI Agents:</span>
                        <div className="font-bold">
                          {new Set(
                            auditCategories
                              .filter(cat => selectedCategories.includes(cat.id))
                              .flatMap(cat => cat.requiredAgents)
                          ).size}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">New Audit Onboarding</h1>
              <p className="text-gray-600">Set up a new client audit with AI-powered task management</p>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-center space-x-8">
          {[
            { step: 1, title: 'Client Info', icon: Building2 },
            { step: 2, title: 'Categories & Tasks', icon: CheckSquare },
            { step: 3, title: 'Review', icon: FileText }
          ].map(({ step, title, icon: Icon }) => (
            <div key={step} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                currentStep >= step 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'border-gray-300 text-gray-400'
              }`}>
                {currentStep > step ? (
                  <CheckSquare className="h-5 w-5" />
                ) : (
                  <Icon className="h-5 w-5" />
                )}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                currentStep >= step ? 'text-gray-900' : 'text-gray-400'
              }`}>
                {title}
              </span>
              {step < 3 && (
                <ArrowRight className="h-4 w-4 ml-4 text-gray-300" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-8">
        {renderStepContent()}

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
          <Button 
            variant="outline" 
            onClick={() => setCurrentStep(prev => Math.max(1, prev - 1))}
            disabled={currentStep === 1}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          {currentStep < 3 ? (
            <Button
              onClick={() => setCurrentStep(prev => prev + 1)}
              disabled={!canProceedToNext()}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button 
              onClick={handleCreateAudit}
              disabled={isCreating || !canProceedToNext()}
              className="bg-green-600 hover:bg-green-700"
            >
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating Audit...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Create Audit & Start AI Processing
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
