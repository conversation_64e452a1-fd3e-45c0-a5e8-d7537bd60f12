'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Users,
  Brain,
  CheckSquare,
  Clock,
  Activity,
  ArrowLeft,
  Filter,
  Search,
  ArrowRight,
  Play,
  Pause,
  AlertTriangle,
  Target,
  BarChart3
} from 'lucide-react';
import { Button } from '@/components/ui/button.1';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';

interface Task {
  id: string;
  name: string;
  type: string;
  client_id: string;
  status: string;
  priority: string;
  assigned_agents: string[];
  current_agent: string | null;
  stage: string;
  progress: {
    overall_completion: number;
    documents_processed: number;
    total_documents: number;
  };
  deadline: string;
}

interface Agent {
  id: string;
  name: string;
  type: string;
  status: string;
}

interface AgentWorkload {
  active_tasks: string[];
  pending_tasks: string[];
  workload_percentage: number;
}

export default function TaskAgentAssignmentDashboard() {
  const router = useRouter();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [agentWorkloads, setAgentWorkloads] = useState<Record<string, AgentWorkload>>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [agentFilter, setAgentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    const loadData = async () => {
      try {
        const tasksData = await import('/data/apps/platforms/finance/auditmind/tasks.json');
        const agentsData = await import('/data/apps/platforms/finance/auditmind/agents.json');
        
        setTasks(tasksData.tasks || []);
        setAgents(agentsData.agents || []);
        setAgentWorkloads(tasksData.agent_workload || {});
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAgentName = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : agentId;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.client_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesAgent = agentFilter === 'all' || 
                        task.assigned_agents.includes(agentFilter) ||
                        task.current_agent === agentFilter;
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    return matchesSearch && matchesAgent && matchesStatus;
  });

  // Group tasks by agent
  const tasksByAgent = agents.reduce((acc, agent) => {
    const agentTasks = tasks.filter(task => 
      task.assigned_agents.includes(agent.id) || task.current_agent === agent.id
    );
    acc[agent.id] = agentTasks;
    return acc;
  }, {} as Record<string, Task[]>);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-1">
              <span>Boards</span>
              <span>/</span>
              <span className="text-gray-900">Assignment Board</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Task-Agent Assignments</h1>
            <p className="text-gray-600">Phân công nhiệm vụ cho tác nhân</p>
          </div>
        </div>
        <Button variant="outline" size="sm" onClick={() => router.push('/platforms/finance/auditmind/boards')}>
          <BarChart3 className="h-4 w-4 mr-2" />
          All Boards
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tasks.filter(t => t.status === 'in_progress').length}
            </div>
            <p className="text-xs text-gray-600">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Agents Working</CardTitle>
            <Brain className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(agentWorkloads).filter(w => w.active_tasks.length > 0).length}
            </div>
            <p className="text-xs text-gray-600">
              Out of {agents.length} total
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Tasks</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tasks.filter(t => t.status === 'pending').length}
            </div>
            <p className="text-xs text-gray-600">
              Awaiting assignment
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Workload</CardTitle>
            <Target className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(Object.values(agentWorkloads).reduce((sum, w) => sum + w.workload_percentage, 0) / agents.length)}%
            </div>
            <p className="text-xs text-gray-600">
              Across all agents
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search tasks or clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <select
          value={agentFilter}
          onChange={(e) => setAgentFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Agents</option>
          {agents.map(agent => (
            <option key={agent.id} value={agent.id}>{agent.name}</option>
          ))}
        </select>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="pending">Pending</option>
          <option value="in_progress">In Progress</option>
          <option value="completed">Completed</option>
        </select>
      </div>

      {/* Assignment Matrix */}
      <div className="space-y-6">
        {agents.map(agent => {
          const agentTasks = tasksByAgent[agent.id] || [];
          const workload = agentWorkloads[agent.id] || { active_tasks: [], pending_tasks: [], workload_percentage: 0 };
          
          if (agentFilter !== 'all' && agentFilter !== agent.id) return null;
          
          return (
            <Card key={agent.id} className="overflow-hidden">
              <CardHeader className="bg-gray-50">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Brain className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{agent.name}</CardTitle>
                      <p className="text-sm text-gray-600 capitalize">{agent.type.replace('_', ' ')} Agent</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-sm font-medium">Workload: {workload.workload_percentage}%</div>
                      <div className="text-xs text-gray-600">
                        {workload.active_tasks.length} active, {workload.pending_tasks.length} pending
                      </div>
                    </div>
                    <div className="w-24">
                      <Progress value={workload.workload_percentage} className="h-2" />
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {agentTasks.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    <CheckSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p>No tasks assigned to this agent</p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {agentTasks.map(task => (
                      <div key={task.id} className="p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-medium">{task.name}</h4>
                              <Badge className={getStatusColor(task.status)}>
                                {task.status.replace('_', ' ')}
                              </Badge>
                              <Badge size="sm" className={getPriorityColor(task.priority)}>
                                {task.priority}
                              </Badge>
                              {task.current_agent === agent.id && (
                                <Badge variant="outline" size="sm">
                                  <Activity className="h-3 w-3 mr-1" />
                                  Current
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center space-x-6 text-sm text-gray-600">
                              <span>Client: {task.client_id}</span>
                              <span>Stage: {task.stage.replace('_', ' ')}</span>
                              <span>Deadline: {formatDate(task.deadline)}</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="text-sm font-medium">{task.progress.overall_completion}%</div>
                              <div className="text-xs text-gray-600">
                                {task.progress.documents_processed}/{task.progress.total_documents} docs
                              </div>
                            </div>
                            <div className="w-20">
                              <Progress value={task.progress.overall_completion} className="h-2" />
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredTasks.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No assignments found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
}
