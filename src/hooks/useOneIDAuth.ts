'use client';

import { useState, useEffect, useCallback } from 'react';

export interface OneIDUser {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email?: string;
  userType: 'individual' | 'employee' | 'company_admin';
  status: string;
}

export interface OneIDSession {
  token: string;
  user: OneIDUser;
  expiresAt: string;
}

export interface UseOneIDAuthReturn {
  user: OneIDUser | null;
  session: OneIDSession | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<boolean>;
}

export function useOneIDAuth(): UseOneIDAuthReturn {
  const [session, setSession] = useState<OneIDSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load session from localStorage on mount
  useEffect(() => {
    const loadSession = () => {
      try {
        const storedSession = localStorage.getItem('oneid-session');
        if (storedSession) {
          const parsedSession = JSON.parse(storedSession);
          
          // Check if session is expired
          if (new Date(parsedSession.expiresAt) > new Date()) {
            setSession(parsedSession);
          } else {
            localStorage.removeItem('oneid-session');
          }
        }
      } catch (error) {
        console.error('Error loading session:', error);
        localStorage.removeItem('oneid-session');
      } finally {
        setIsLoading(false);
      }
    };

    loadSession();
  }, []);

  // Save session to localStorage when it changes
  useEffect(() => {
    if (session) {
      localStorage.setItem('oneid-session', JSON.stringify(session));
    } else {
      localStorage.removeItem('oneid-session');
    }
  }, [session]);

  const login = useCallback(async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/backbone/oneid/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (data.success && data.data) {
        const newSession: OneIDSession = {
          token: data.data.session.token,
          user: data.data.user,
          expiresAt: data.data.session.expiresAt
        };
        
        setSession(newSession);
        return true;
      } else {
        console.error('Login failed:', data.error);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      if (session?.token) {
        await fetch('/api/backbone/oneid/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setSession(null);
    }
  }, [session]);

  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      if (!session?.token) {
        return false;
      }

      const response = await fetch('/api/backbone/oneid/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.token}`,
        },
      });

      const data = await response.json();

      if (data.success && data.data) {
        const refreshedSession: OneIDSession = {
          token: data.data.session.token,
          user: data.data.user,
          expiresAt: data.data.session.expiresAt
        };
        
        setSession(refreshedSession);
        return true;
      } else {
        // Session refresh failed, logout
        setSession(null);
        return false;
      }
    } catch (error) {
      console.error('Session refresh error:', error);
      setSession(null);
      return false;
    }
  }, [session]);

  // Auto-refresh session when it's close to expiring
  useEffect(() => {
    if (!session) return;

    const checkSessionExpiry = () => {
      const expiresAt = new Date(session.expiresAt);
      const now = new Date();
      const timeUntilExpiry = expiresAt.getTime() - now.getTime();
      
      // Refresh if session expires in less than 5 minutes
      if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {
        refreshSession();
      } else if (timeUntilExpiry <= 0) {
        // Session has expired
        setSession(null);
      }
    };

    // Check immediately
    checkSessionExpiry();

    // Check every minute
    const interval = setInterval(checkSessionExpiry, 60 * 1000);

    return () => clearInterval(interval);
  }, [session, refreshSession]);

  return {
    user: session?.user || null,
    session,
    isAuthenticated: !!session,
    isLoading,
    login,
    logout,
    refreshSession,
  };
}

// Helper hook for wallet operations
export function useOneIDWallet() {
  const { session, isAuthenticated } = useOneIDAuth();

  const makeWalletRequest = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    if (!session?.token) {
      throw new Error('Not authenticated');
    }

    const response = await fetch(`/api/backbone/oneid/wallet${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.token}`,
        ...options.headers,
      },
    });

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Request failed');
    }

    return data.data;
  }, [session]);

  const getWallet = useCallback(async () => {
    return await makeWalletRequest('');
  }, [makeWalletRequest]);

  const getBalance = useCallback(async () => {
    return await makeWalletRequest('/balance');
  }, [makeWalletRequest]);

  const getTransactions = useCallback(async (limit = 50, offset = 0) => {
    return await makeWalletRequest(`/transactions?limit=${limit}&offset=${offset}`);
  }, [makeWalletRequest]);

  const topUp = useCallback(async (amount: number, paymentMethod: string, description?: string) => {
    return await makeWalletRequest('', {
      method: 'POST',
      body: JSON.stringify({ amount, paymentMethod, description }),
    });
  }, [makeWalletRequest]);

  const withdraw = useCallback(async (amount: number, withdrawalMethod: string, withdrawalDetails: any, description?: string) => {
    return await makeWalletRequest('', {
      method: 'PUT',
      body: JSON.stringify({ amount, withdrawalMethod, withdrawalDetails, description }),
    });
  }, [makeWalletRequest]);

  const transfer = useCallback(async (toUserId: string, amount: number, description?: string) => {
    return await makeWalletRequest('/transfer', {
      method: 'POST',
      body: JSON.stringify({ toUserId, amount, description }),
    });
  }, [makeWalletRequest]);

  const updateSettings = useCallback(async (autoTopUp: any) => {
    return await makeWalletRequest('', {
      method: 'PATCH',
      body: JSON.stringify({ autoTopUp }),
    });
  }, [makeWalletRequest]);

  return {
    isAuthenticated,
    getWallet,
    getBalance,
    getTransactions,
    topUp,
    withdraw,
    transfer,
    updateSettings,
  };
}
