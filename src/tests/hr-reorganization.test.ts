import { describe, it, expect } from 'vitest';
import fs from 'fs';
import path from 'path';

describe('HR Apps Reorganization', () => {
  describe('Directory Structure', () => {
    it('should have moved abn-talents to hr/headhunter', () => {
      const headhunterPath = path.join(process.cwd(), 'src/app/hr/headhunter');
      expect(fs.existsSync(headhunterPath)).toBe(true);
      
      // Check that main files exist
      expect(fs.existsSync(path.join(headhunterPath, 'page.tsx'))).toBe(true);
      expect(fs.existsSync(path.join(headhunterPath, 'layout.tsx'))).toBe(true);
    });

    it('should have removed the old abn-talents directory', () => {
      const oldPath = path.join(process.cwd(), 'src/app/abn-talents');
      expect(fs.existsSync(oldPath)).toBe(false);
    });

    it('should have hunter app in hr/hunter', () => {
      const hunterPath = path.join(process.cwd(), 'src/app/hr/hunter');
      expect(fs.existsSync(hunterPath)).toBe(true);
      expect(fs.existsSync(path.join(hunterPath, 'page.tsx'))).toBe(true);
    });
  });

  describe('Data Structure', () => {
    it('should have created headhunter data directory', () => {
      const dataPath = path.join(process.cwd(), 'data/apps/hr/headhunter');
      expect(fs.existsSync(dataPath)).toBe(true);
    });

    it('should have headhunter data files', () => {
      const dataPath = path.join(process.cwd(), 'data/apps/hr/headhunter');
      expect(fs.existsSync(path.join(dataPath, 'candidates.json'))).toBe(true);
      expect(fs.existsSync(path.join(dataPath, 'clients.json'))).toBe(true);
      expect(fs.existsSync(path.join(dataPath, 'placements.json'))).toBe(true);
    });

    it('should have valid JSON data files', () => {
      const dataPath = path.join(process.cwd(), 'data/apps/hr/headhunter');
      
      // Test candidates.json
      const candidatesData = fs.readFileSync(path.join(dataPath, 'candidates.json'), 'utf8');
      const candidates = JSON.parse(candidatesData);
      expect(candidates).toHaveProperty('candidates');
      expect(candidates).toHaveProperty('metadata');
      expect(Array.isArray(candidates.candidates)).toBe(true);

      // Test clients.json
      const clientsData = fs.readFileSync(path.join(dataPath, 'clients.json'), 'utf8');
      const clients = JSON.parse(clientsData);
      expect(clients).toHaveProperty('clients');
      expect(clients).toHaveProperty('metadata');
      expect(Array.isArray(clients.clients)).toBe(true);
    });
  });

  describe('API Routes', () => {
    it('should have headhunter API routes', () => {
      const apiPath = path.join(process.cwd(), 'src/app/api/hr/headhunter');
      expect(fs.existsSync(apiPath)).toBe(true);
      expect(fs.existsSync(path.join(apiPath, 'route.ts'))).toBe(true);
      expect(fs.existsSync(path.join(apiPath, 'candidates/route.ts'))).toBe(true);
      expect(fs.existsSync(path.join(apiPath, 'clients/route.ts'))).toBe(true);
    });
  });

  describe('Configuration Updates', () => {
    it('should have updated HR suite configuration', () => {
      const configPath = path.join(process.cwd(), 'data/apps/hr/suite-config.json');
      expect(fs.existsSync(configPath)).toBe(true);
      
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      
      // Check that headhunter solution exists
      const headhunterSolution = config.solutions.find((s: any) => s.id === 'headhunter');
      expect(headhunterSolution).toBeDefined();
      expect(headhunterSolution.path).toBe('/hr/headhunter');
      expect(headhunterSolution.category).toBe('Recruiters');
      
      // Check that hunter is updated for job seekers
      const hunterSolution = config.solutions.find((s: any) => s.id === 'hunter');
      expect(hunterSolution).toBeDefined();
      expect(hunterSolution.category).toBe('Job Seekers');
      
      // Check categories
      const categories = config.categories;
      const jobSeekersCategory = categories.find((c: any) => c.name === 'Job Seekers');
      const recruitersCategory = categories.find((c: any) => c.name === 'Recruiters');
      
      expect(jobSeekersCategory).toBeDefined();
      expect(recruitersCategory).toBeDefined();
      expect(jobSeekersCategory.solutions).toContain('hunter');
      expect(recruitersCategory.solutions).toContain('headhunter');
    });
  });

  describe('Path References', () => {
    it('should have updated internal links in headhunter layout', () => {
      const layoutPath = path.join(process.cwd(), 'src/app/hr/headhunter/layout.tsx');
      const layoutContent = fs.readFileSync(layoutPath, 'utf8');
      
      // Should not contain old abn-talents paths
      expect(layoutContent).not.toContain('/abn-talents/');
      
      // Should contain new hr/headhunter paths
      expect(layoutContent).toContain('/hr/headhunter/');
    });

    it('should have updated redirect in headhunter main page', () => {
      const pagePath = path.join(process.cwd(), 'src/app/hr/headhunter/page.tsx');
      const pageContent = fs.readFileSync(pagePath, 'utf8');
      
      // Should redirect to new path
      expect(pageContent).toContain('/hr/headhunter/headhunter');
      expect(pageContent).not.toContain('/abn-talents/');
    });
  });
});
