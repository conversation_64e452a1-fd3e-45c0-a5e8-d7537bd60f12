#!/usr/bin/env tsx

// <PERSON>ript to add WISE users to OneID and grant access to eduwise system
// This script reads users from the eduwise users.json file and creates them in OneID
// with appropriate permissions for the eduwise system

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { initializeOneID } from '../app/backbone/oneid';

// Interface for WISE user data from JSON file
interface WiseUser {
  id: string;
  username: string;
  email: string;
  fullName: string;
  phone: string;
  password: string;
  organization: string;
  business_areas: string;
  references: string;
  point: number;
  status: number;
  createdAt: string;
}

// OneID user interface for registration
interface OneIDRegisterData {
  username: string;
  email?: string;
  password: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
}

// Configuration
const WISE_USERS_FILE = '/Users/<USER>/Workspace/abn.green/data/apps/web/wise/eduwise/users.json';
const EDUWISE_ROLE_NAME = 'eduwise_user';
const EDUWISE_PERMISSION_NAME = 'eduwise_access';
const DEFAULT_PASSWORD = 'WiseEdu2024!'; // Users should change this on first login

// Helper functions for data validation and cleanup
function cleanUsername(username: string | null): string | null {
  if (!username) return null;
  
  // Remove any non-alphanumeric characters except underscore and dash
  const cleaned = username.replace(/[^a-zA-Z0-9_-]/g, '');
  
  // If username starts with a number, prefix with 'user_'
  if (/^\d/.test(cleaned)) {
    return `user_${cleaned}`;
  }
  
  // Must be at least 3 characters
  if (cleaned.length < 3) {
    return null;
  }
  
  return cleaned;
}

function createSecurePassword(originalPassword: string): string {
  // If original password already meets requirements, use it
  if (originalPassword && 
      /[A-Z]/.test(originalPassword) && 
      /[!@#$%^&*(),.?":{}|<>]/.test(originalPassword) &&
      originalPassword.length >= 8) {
    return originalPassword;
  }
  
  // Otherwise, create a secure version based on username
  return DEFAULT_PASSWORD;
}

function validateUserData(user: WiseUser): { isValid: boolean; cleanedUsername: string | null; reason?: string } {
  // Check if username is valid
  const cleanedUsername = cleanUsername(user.username);
  if (!cleanedUsername) {
    return { 
      isValid: false, 
      cleanedUsername: null, 
      reason: `Invalid username: "${user.username}"` 
    };
  }
  
  // Check if fullName exists
  if (!user.fullName || user.fullName.trim().length === 0) {
    return { 
      isValid: false, 
      cleanedUsername, 
      reason: 'Missing full name' 
    };
  }
  
  return { isValid: true, cleanedUsername };
}

// Main execution function
async function main() {
  try {
    console.log('🚀 Starting WISE users migration to OneID...\n');

    // Initialize OneID system
    console.log('⚙️  Initializing OneID system...');
    const oneID = await initializeOneID();
    console.log('✅ OneID system initialized\n');

    // Load WISE users
    console.log('📁 Loading WISE users from file...');
    const wiseUsers = await loadWiseUsers();
    console.log(`✅ Loaded ${wiseUsers.length} WISE users\n`);

    // Setup eduwise permissions and role
    console.log('🔐 Setting up eduwise permissions and role...');
    await setupEduwiseAccess(oneID);
    console.log('✅ Eduwise access setup complete\n');

    // Process users in batches to avoid overwhelming the system
    const batchSize = 10;
    let successCount = 0;
    let errorCount = 0;
    const errors: Array<{ user: WiseUser; error: string }> = [];

    console.log('👥 Processing users in batches...\n');

    for (let i = 0; i < wiseUsers.length; i += batchSize) {
      const batch = wiseUsers.slice(i, i + batchSize);
      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(wiseUsers.length / batchSize)} (${batch.length} users)...`);

      for (const wiseUser of batch) {
        try {
          await processUser(oneID, wiseUser);
          successCount++;
          console.log(`  ✅ ${wiseUser.username} (${wiseUser.fullName})`);
        } catch (error) {
          errorCount++;
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          errors.push({ user: wiseUser, error: errorMsg });
          console.log(`  ❌ ${wiseUser.username}: ${errorMsg}`);
        }
      }

      // Small delay between batches to be nice to the system
      if (i + batchSize < wiseUsers.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Final summary
    console.log('\n🎉 Migration complete!');
    console.log(`✅ Successfully processed: ${successCount} users`);
    console.log(`❌ Errors encountered: ${errorCount} users`);

    if (errors.length > 0) {
      console.log('\n❌ Users with errors:');
      errors.forEach(({ user, error }) => {
        console.log(`  - ${user.username} (${user.fullName}): ${error}`);
      });

      // Write errors to file for review
      const errorLog = {
        timestamp: new Date().toISOString(),
        totalUsers: wiseUsers.length,
        successCount,
        errorCount,
        errors: errors.map(({ user, error }) => ({
          id: user.id,
          username: user.username,
          fullName: user.fullName,
          email: user.email,
          error
        }))
      };

      const errorLogPath = path.join(__dirname, `wise-migration-errors-${Date.now()}.json`);
      await fs.writeFile(errorLogPath, JSON.stringify(errorLog, null, 2), 'utf8');
      console.log(`\n📝 Error details written to: ${errorLogPath}`);
    }

    console.log('\n🔑 Important notes:');
    console.log(`  - All users have been assigned the default password: ${DEFAULT_PASSWORD}`);
    console.log('  - Users should change their password on first login');
    console.log('  - All users have been granted access to the eduwise system');
    console.log('  - Email verification may be required for users with email addresses');

  } catch (error) {
    console.error('💥 Fatal error during migration:', error);
    process.exit(1);
  }
}

// Load WISE users from JSON file
async function loadWiseUsers(): Promise<WiseUser[]> {
  try {
    const fileContent = await fs.readFile(WISE_USERS_FILE, 'utf8');
    const users = JSON.parse(fileContent) as WiseUser[];
    
    if (!Array.isArray(users)) {
      throw new Error('Users file does not contain an array');
    }

    // Filter only active users (status = 1)
    const activeUsers = users.filter(user => user.status === 1);
    console.log(`  📊 Total users in file: ${users.length}`);
    console.log(`  ✅ Active users to migrate: ${activeUsers.length}`);

    return activeUsers;
  } catch (error) {
    throw new Error(`Failed to load WISE users: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Setup eduwise permissions and role in OneID
async function setupEduwiseAccess(oneID: Awaited<ReturnType<typeof initializeOneID>>) {
  try {
    // Check if eduwise permission already exists
    const existingPermissions = await oneID.accessControl.listPermissions();
    const eduwisePermission = existingPermissions.find(p => p.name === EDUWISE_PERMISSION_NAME);

    if (!eduwisePermission) {
      console.log('  🔐 Creating eduwise access permission...');
      await oneID.accessControl.createPermission({
        name: EDUWISE_PERMISSION_NAME,
        description: 'Access to WISE eduwise learning platform',
        resource: 'module',
        action: 'read',
        accessLevel: 'own'
      });
      console.log('  ✅ Eduwise permission created');
    } else {
      console.log('  ✅ Eduwise permission already exists');
    }

    // Check if eduwise role already exists
    const existingRoles = await oneID.accessControl.listRoles();
    const eduwiseRole = existingRoles.find(r => r.name === EDUWISE_ROLE_NAME);

    if (!eduwiseRole) {
      console.log('  👥 Creating eduwise user role...');
      
      // Get the permission ID
      const permissions = await oneID.accessControl.listPermissions();
      const permission = permissions.find(p => p.name === EDUWISE_PERMISSION_NAME);
      
      if (!permission) {
        throw new Error('Could not find eduwise permission after creation');
      }

      await oneID.accessControl.createRole({
        name: EDUWISE_ROLE_NAME,
        description: 'Standard user role for WISE eduwise platform',
        permissions: [permission.id],
        isSystemRole: false,
        createdBy: 'system'
      });
      console.log('  ✅ Eduwise role created');
    } else {
      console.log('  ✅ Eduwise role already exists');
    }
  } catch (error) {
    throw new Error(`Failed to setup eduwise access: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Process a single WISE user and add them to OneID
async function processUser(oneID: Awaited<ReturnType<typeof initializeOneID>>, wiseUser: WiseUser) {
  // Parse full name into first and last name
  const nameParts = wiseUser.fullName.trim().split(' ');
  const firstName = nameParts[0] || wiseUser.username;
  const lastName = nameParts.slice(1).join(' ') || '';

  // Prepare registration data
  const registerData: OneIDRegisterData = {
    username: wiseUser.username,
    email: wiseUser.email || undefined,
    password: wiseUser.password || DEFAULT_PASSWORD, // Use existing password or default
    firstName,
    lastName,
    acceptTerms: true
  };

  // Check if user already exists
  const existingUser = await oneID.user.getUserProfile(wiseUser.id);
  if (existingUser) {
    throw new Error('User already exists in OneID');
  }

  // Register the user
  const registrationResult = await oneID.auth.registerIndividual(registerData);
  
  if (!registrationResult.success) {
    throw new Error(`Registration failed: ${registrationResult.error || 'Unknown error'}`);
  }

  const newUser = registrationResult.data;
  if (!newUser) {
    throw new Error('User registration succeeded but no user data returned');
  }

  // Get the eduwise role
  const roles = await oneID.accessControl.listRoles();
  const eduwiseRole = roles.find(r => r.name === EDUWISE_ROLE_NAME);
  
  if (!eduwiseRole) {
    throw new Error('Eduwise role not found');
  }

  // Assign the eduwise role to the user
  await oneID.accessControl.assignRole(newUser.id, eduwiseRole.id, 'system', undefined, undefined);

  // Update user metadata with original WISE data
  await oneID.user.updateUserProfile(newUser.id, {
    metadata: {
      ...newUser.metadata,
      originalWiseId: wiseUser.id,
      organization: wiseUser.organization,
      businessAreas: wiseUser.business_areas,
      references: wiseUser.references,
      wisePoints: wiseUser.point,
      originalCreatedAt: wiseUser.createdAt,
      migratedAt: new Date().toISOString(),
      phone: wiseUser.phone
    }
  });
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { main as addWiseUsersToOneID };
