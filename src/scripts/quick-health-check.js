#!/usr/bin/env node

/**
 * Quick Health Check for Minigames
 * 
 * This script performs a quick health check on all minigames
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Quick health check
function quickHealthCheck() {
  log('🏥 Quick Health Check for Minigames', 'bright');
  log('===================================', 'cyan');
  
  const registryPath = path.join(__dirname, '../components/Minigames/gameRegistry.ts');
  
  if (!fs.existsSync(registryPath)) {
    log('❌ gameRegistry.ts not found!', 'red');
    return;
  }
  
  const content = fs.readFileSync(registryPath, 'utf8');
  
  // Basic checks
  const checks = [
    {
      name: 'Registry file exists',
      test: () => fs.existsSync(registryPath),
      status: true
    },
    {
      name: 'Has games array export',
      test: () => content.includes('export const games'),
      status: content.includes('export const games')
    },
    {
      name: 'Has proper imports',
      test: () => content.includes('import') && content.includes('from'),
      status: content.includes('import') && content.includes('from')
    },
    {
      name: 'No obvious syntax errors',
      test: () => {
        const openBrackets = (content.match(/\{/g) || []).length;
        const closeBrackets = (content.match(/\}/g) || []).length;
        return Math.abs(openBrackets - closeBrackets) <= 2; // Allow small variance
      },
      status: (() => {
        const openBrackets = (content.match(/\{/g) || []).length;
        const closeBrackets = (content.match(/\}/g) || []).length;
        return Math.abs(openBrackets - closeBrackets) <= 2;
      })()
    }
  ];
  
  // Run checks
  let passed = 0;
  checks.forEach(check => {
    if (check.status) {
      log(`✅ ${check.name}`, 'green');
      passed++;
    } else {
      log(`❌ ${check.name}`, 'red');
    }
  });
  
  // Count games
  const gamesMatch = content.match(/export const games: GameType\[\] = \[([\s\S]*?)\];/);
  if (gamesMatch) {
    const gameCount = (gamesMatch[1].match(/id:\s*['"][^'"]+['"]/g) || []).length;
    log(`📊 Found ${gameCount} games in registry`, 'blue');
  }
  
  // Summary
  log('\n📋 Health Check Summary:', 'bright');
  log(`Passed: ${passed}/${checks.length} checks`, passed === checks.length ? 'green' : 'yellow');
  
  if (passed === checks.length) {
    log('🎉 All basic health checks passed!', 'green');
    log('💡 Run comprehensive tests for detailed analysis:', 'blue');
    log('   node src/scripts/test-all-games-comprehensive.js', 'cyan');
  } else {
    log('⚠️  Some health checks failed. Please review the issues above.', 'yellow');
  }
}

// Run health check
quickHealthCheck();
