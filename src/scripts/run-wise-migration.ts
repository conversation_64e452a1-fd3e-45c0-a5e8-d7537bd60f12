#!/usr/bin/env tsx

// Run WISE to OneID Migration
// This is a simple runner script that executes the main migration

import * as readline from 'readline';
import { addWiseUsersToOneID } from './add-wise-users-to-oneid';

console.log('🚀 Starting WISE to OneID Migration...\n');
console.log('This script will:');
console.log('1. Read WISE users from data/apps/web/wise/eduwise/users.json');
console.log('2. Create OneID accounts for active users');
console.log('3. Assign eduwise access permissions');
console.log('4. Generate migration reports\n');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Do you want to proceed? (y/N): ', async (answer: string) => {
  if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
    try {
      await addWiseUsersToOneID();
      console.log('\n✅ Migration completed successfully!');
    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      process.exit(1);
    }
  } else {
    console.log('Migration cancelled.');
  }
  
  rl.close();
  process.exit(0);
});
