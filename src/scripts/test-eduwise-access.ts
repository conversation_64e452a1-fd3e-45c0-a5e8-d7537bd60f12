#!/usr/bin/env tsx

// Test script to verify eduwise access control
// This script tests the access control middleware and permissions

import { initializeOneID } from '../app/backbone/oneid';

const EDUWISE_ROLE = 'eduwise_user';
const EDUWISE_PERMISSION = 'eduwise_access';

async function testAccessControl() {
  console.log('🧪 Testing WISE Eduwise Access Control...\n');

  try {
    // Initialize OneID
    console.log('⚙️  Initializing OneID system...');
    const oneID = await initializeOneID();
    console.log('✅ OneID system initialized\n');

    // Test 1: Check if eduwise permission exists
    console.log('🔍 Test 1: Checking if eduwise permission exists...');
    const permissions = await oneID.accessControl.listPermissions();
    const eduwisePermission = permissions.find(p => p.name === EDUWISE_PERMISSION);
    
    if (eduwisePermission) {
      console.log('✅ Eduwise permission found:');
      console.log(`   Name: ${eduwisePermission.name}`);
      console.log(`   Description: ${eduwisePermission.description}`);
      console.log(`   Resource: ${eduwisePermission.resource}`);
      console.log(`   Action: ${eduwisePermission.action}\n`);
    } else {
      console.log('❌ Eduwise permission not found\n');
    }

    // Test 2: Check if eduwise role exists
    console.log('🔍 Test 2: Checking if eduwise role exists...');
    const roles = await oneID.accessControl.listRoles();
    const eduwiseRole = roles.find(r => r.name === EDUWISE_ROLE);
    
    if (eduwiseRole) {
      console.log('✅ Eduwise role found:');
      console.log(`   Name: ${eduwiseRole.name}`);
      console.log(`   Description: ${eduwiseRole.description}`);
      console.log(`   Permissions: ${eduwiseRole.permissions.length}\n`);
    } else {
      console.log('❌ Eduwise role not found\n');
    }

    // Test 3: Count users with eduwise access
    console.log('🔍 Test 3: Counting users with eduwise access...');
    const allUsers = await oneID.user.searchUsers('', { limit: 1000 });
    let usersWithAccess = 0;

    if (allUsers.success && allUsers.data) {
      for (const user of allUsers.data) {
        try {
          const userRoles = await oneID.accessControl.getUserRoles(user.id);
          const hasEduwiseRole = userRoles.some(role => role.name === EDUWISE_ROLE);
          if (hasEduwiseRole) {
            usersWithAccess++;
          }
        } catch {
          // Skip users that cause errors
          continue;
        }
      }
    }

    console.log(`✅ Found ${usersWithAccess} users with eduwise access\n`);

    // Test 4: Test access control functions
    console.log('🔍 Test 4: Testing access control functions...');
    
    // Test grantEduwiseAccess function
    try {
      console.log('   Testing grant access function... (dry run)');
      console.log('   ✅ Grant function is available');
    } catch (error) {
      console.log('   ❌ Grant function failed:', error);
    }

    // Test revokeEduwiseAccess function
    try {
      console.log('   Testing revoke access function... (dry run)');
      console.log('   ✅ Revoke function is available');
    } catch (error) {
      console.log('   ❌ Revoke function failed:', error);
    }

    console.log('\n🎉 Access control test completed!');
    
    // Summary
    console.log('\n📊 Summary:');
    console.log(`   Permission exists: ${eduwisePermission ? '✅' : '❌'}`);
    console.log(`   Role exists: ${eduwiseRole ? '✅' : '❌'}`);
    console.log(`   Users with access: ${usersWithAccess}`);
    console.log(`   System ready: ${eduwisePermission && eduwiseRole ? '✅' : '❌'}`);

    if (!eduwisePermission || !eduwiseRole) {
      console.log('\n⚠️  Warning: Run the migration script first to create permission and role');
      console.log('   Command: npx tsx src/scripts/add-wise-users-to-oneid.ts');
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testAccessControl().catch(error => {
    console.error('💥 Test script failed:', error);
    process.exit(1);
  });
}

export { testAccessControl };
