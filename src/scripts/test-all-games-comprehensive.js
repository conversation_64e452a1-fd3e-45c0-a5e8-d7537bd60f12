#!/usr/bin/env node

/**
 * Comprehensive Game Test Script
 * 
 * This script tests ALL minigames by attempting to render them and checking for errors.
 * It provides detailed reporting on which games work and which have issues.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✓ ${message}`, 'green');
}

function logError(message) {
  log(`✗ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ ${message}`, 'blue');
}

// Test results
const results = {
  total: 0,
  passed: 0,
  failed: 0,
  warnings: 0,
  errors: [],
  warnings_list: [],
  detailed_results: []
};

// Read and parse the gameRegistry
function getGameInfo() {
  const registryPath = path.join(__dirname, '../components/Minigames/gameRegistry.ts');
  
  if (!fs.existsSync(registryPath)) {
    logError('gameRegistry.ts not found!');
    process.exit(1);
  }

  const content = fs.readFileSync(registryPath, 'utf8');
  
  // Extract game definitions
  const gamesArrayMatch = content.match(/export const games: GameType\[\] = \[([\s\S]*?)\];/);
  
  if (!gamesArrayMatch) {
    logError('Could not find games array in gameRegistry.ts');
    return [];
  }

  const gamesContent = gamesArrayMatch[1];
  
  // Extract individual game objects with more details
  const gameObjects = [];
  const gameRegex = /{[\s\S]*?id:\s*['"]([^'"]+)['"][\s\S]*?name:\s*['"]([^'"]+)['"][\s\S]*?component:\s*(\w+)[\s\S]*?category:\s*['"]([^'"]+)['"][\s\S]*?}/g;
  let match;

  while ((match = gameRegex.exec(gamesContent)) !== null) {
    gameObjects.push({
      id: match[1],
      name: match[2],
      component: match[3],
      category: match[4]
    });
  }

  return gameObjects;
}

// Test individual game components with enhanced validation
async function testGameComponent(gameInfo) {
  const testResult = {
    gameId: gameInfo.id,
    gameName: gameInfo.name,
    component: gameInfo.component,
    category: gameInfo.category,
    status: 'unknown',
    tests: {
      fileExists: false,
      syntaxValid: false,
      componentExported: false,
      metadataComplete: false,
      categoryValid: false
    },
    errors: [],
    warnings: []
  };

  try {
    // Test 1: Check if component file exists
    const componentPath = findComponentFile(gameInfo.component);
    if (componentPath) {
      testResult.tests.fileExists = true;
      logInfo(`  ✓ File found: ${componentPath}`);
    } else {
      testResult.errors.push('Component file not found');
      logError(`  ✗ File not found for component: ${gameInfo.component}`);
    }

    // Test 2: Basic syntax validation
    if (componentPath) {
      const syntaxResult = validateSyntax(componentPath);
      if (syntaxResult.valid) {
        testResult.tests.syntaxValid = true;
        logInfo(`  ✓ Syntax validation passed`);
      } else {
        testResult.tests.syntaxValid = false;
        testResult.errors.push(`Syntax issues: ${syntaxResult.issues.join(', ')}`);
        logError(`  ✗ Syntax issues: ${syntaxResult.issues.join(', ')}`);
      }
    }

    // Test 3: Component export validation
    if (componentPath) {
      const exportResult = validateComponentExport(componentPath);
      if (exportResult.valid) {
        testResult.tests.componentExported = true;
        logInfo(`  ✓ Component properly exported`);
      } else {
        testResult.tests.componentExported = false;
        testResult.errors.push(`Export issues: ${exportResult.issues.join(', ')}`);
        logError(`  ✗ Export issues: ${exportResult.issues.join(', ')}`);
      }
    }

    // Test 4: Metadata validation
    const metadataResult = validateMetadata(gameInfo);
    if (metadataResult.valid) {
      testResult.tests.metadataComplete = true;
      logInfo(`  ✓ Metadata complete`);
    } else {
      testResult.tests.metadataComplete = false;
      testResult.warnings.push(`Metadata issues: ${metadataResult.issues.join(', ')}`);
      logWarning(`  ⚠ Metadata issues: ${metadataResult.issues.join(', ')}`);
    }

    // Test 5: Category validation
    const validCategories = [
      'puzzle', 'arcade', 'educational', 'finance', 'health', 'autism',
      'card', 'office-workers', 'kids', 'language', 'math', 'space',
      'family', 'survival', 'travel', 'rules', 'geography', 'career',
      'casual', 'survival-skills'
    ];
    
    if (validCategories.includes(gameInfo.category)) {
      testResult.tests.categoryValid = true;
      logInfo(`  ✓ Category valid: ${gameInfo.category}`);
    } else {
      testResult.tests.categoryValid = false;
      testResult.warnings.push(`Unknown category: ${gameInfo.category}`);
      logWarning(`  ⚠ Unknown category: ${gameInfo.category}`);
    }

    // Determine overall status
    const passedTests = Object.values(testResult.tests).filter(Boolean).length;
    const totalTests = Object.keys(testResult.tests).length;
    
    if (passedTests === totalTests && testResult.errors.length === 0) {
      testResult.status = 'passed';
      logSuccess(`${gameInfo.name} - ALL TESTS PASSED`);
    } else if (testResult.errors.length === 0) {
      testResult.status = 'passed_with_warnings';
      logWarning(`${gameInfo.name} - PASSED WITH WARNINGS`);
    } else {
      testResult.status = 'failed';
      logError(`${gameInfo.name} - FAILED`);
    }

    return testResult;
    
  } catch (error) {
    testResult.status = 'error';
    testResult.errors.push(`Test error: ${error.message}`);
    logError(`${gameInfo.name} - TEST ERROR: ${error.message}`);
    return testResult;
  }
}

// Helper function to find component file
function findComponentFile(componentName) {
  const basePath = path.join(__dirname, '../components/Minigames');
  const possiblePaths = [
    `games/${componentName}.tsx`,
    `games/${componentName}.ts`,
    `${componentName}.tsx`,
    `${componentName}.ts`
  ];

  // Also check subdirectories
  const subdirs = [
    'games/office', 'games/finance', 'games/health', 'games/autism',
    'games/education', 'games/family', 'games/language', 'games/math',
    'games/education/kids', 'games/education/space', 'games/survival',
    'games/travel', 'games/rules', 'games/geography', 'games/career',
    'games/first-principles', 'games/visualizations', 'games/challenges',
    '', 'finance', 'travel', 'rules'
  ];

  for (const subdir of subdirs) {
    possiblePaths.push(`${subdir}/${componentName}.tsx`);
    possiblePaths.push(`${subdir}/${componentName}.ts`);
  }

  for (const relativePath of possiblePaths) {
    const fullPath = path.join(basePath, relativePath);
    if (fs.existsSync(fullPath)) {
      return fullPath;
    }
  }

  return null;
}

// Validate syntax
function validateSyntax(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // Check for unmatched brackets
    const openBrackets = (content.match(/\{/g) || []).length;
    const closeBrackets = (content.match(/\}/g) || []).length;
    if (openBrackets !== closeBrackets) {
      issues.push('Unmatched curly brackets');
    }
    
    // Check for unmatched parentheses
    const openParens = (content.match(/\(/g) || []).length;
    const closeParens = (content.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push('Unmatched parentheses');
    }
    
    // Check for basic TypeScript/React patterns
    if (!content.includes('React') && !content.includes('react')) {
      issues.push('No React import found');
    }
    
    return { valid: issues.length === 0, issues };
  } catch (error) {
    return { valid: false, issues: [`File read error: ${error.message}`] };
  }
}

// Validate component export
function validateComponentExport(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // Check for export patterns
    const hasDefaultExport = content.includes('export default') || content.includes('export { ') && content.includes(' as default');
    const hasNamedExport = content.includes('export const') || content.includes('export function') || content.includes('export class');
    
    if (!hasDefaultExport && !hasNamedExport) {
      issues.push('No export found');
    }
    
    // Check for component patterns
    const hasComponent = content.includes('function') || content.includes('const') || content.includes('class');
    if (!hasComponent) {
      issues.push('No component definition found');
    }
    
    return { valid: issues.length === 0, issues };
  } catch (error) {
    return { valid: false, issues: [`File read error: ${error.message}`] };
  }
}

// Validate metadata
function validateMetadata(gameInfo) {
  const issues = [];
  
  if (!gameInfo.id || gameInfo.id.trim() === '') {
    issues.push('Missing or empty ID');
  }
  
  if (!gameInfo.name || gameInfo.name.trim() === '') {
    issues.push('Missing or empty name');
  }
  
  if (!gameInfo.component || gameInfo.component.trim() === '') {
    issues.push('Missing or empty component');
  }
  
  if (!gameInfo.category || gameInfo.category.trim() === '') {
    issues.push('Missing or empty category');
  }
  
  return { valid: issues.length === 0, issues };
}

// Main test function
async function runComprehensiveTests() {
  log('🎮 Starting Comprehensive Game Tests...', 'bright');
  log('========================================', 'cyan');
  
  const allGames = getGameInfo();
  logInfo(`Found ${allGames.length} total games to test`);
  
  if (allGames.length === 0) {
    logError('No games found to test!');
    process.exit(1);
  }
  
  results.total = allGames.length;
  
  log('\n🧪 Running Comprehensive Tests...', 'bright');
  log('----------------------------------', 'cyan');
  
  // Test each game
  for (let i = 0; i < allGames.length; i++) {
    const game = allGames[i];
    log(`\n[${i + 1}/${allGames.length}] Testing: ${game.name} (${game.id})`, 'magenta');
    
    const result = await testGameComponent(game);
    results.detailed_results.push(result);
    
    if (result.status === 'passed') {
      results.passed++;
    } else if (result.status === 'passed_with_warnings') {
      results.passed++;
      results.warnings++;
      results.warnings_list.push(...result.warnings);
    } else {
      results.failed++;
      results.errors.push({
        game: game.name,
        id: game.id,
        errors: result.errors
      });
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 50));
  }
  
  // Print comprehensive summary
  printSummary();
  
  // Exit with appropriate code
  if (results.failed > 0) {
    log('\n💥 Some tests failed!', 'red');
    process.exit(1);
  } else {
    log('\n🎉 All tests passed!', 'green');
    process.exit(0);
  }
}

// Print detailed summary
function printSummary() {
  log('\n📊 Comprehensive Test Summary', 'bright');
  log('=============================', 'cyan');
  log(`Total games tested: ${results.total}`, 'blue');
  log(`Passed: ${results.passed}`, 'green');
  log(`Failed: ${results.failed}`, 'red');
  log(`Warnings: ${results.warnings}`, 'yellow');
  
  // Category breakdown
  const categoryStats = {};
  results.detailed_results.forEach(result => {
    if (!categoryStats[result.category]) {
      categoryStats[result.category] = { total: 0, passed: 0, failed: 0 };
    }
    categoryStats[result.category].total++;
    if (result.status === 'passed' || result.status === 'passed_with_warnings') {
      categoryStats[result.category].passed++;
    } else {
      categoryStats[result.category].failed++;
    }
  });
  
  log('\n📈 Results by Category:', 'bright');
  Object.entries(categoryStats).forEach(([category, stats]) => {
    const percentage = ((stats.passed / stats.total) * 100).toFixed(1);
    log(`  ${category}: ${stats.passed}/${stats.total} (${percentage}%)`, 
        stats.failed === 0 ? 'green' : 'yellow');
  });
  
  // Failed games details
  if (results.errors.length > 0) {
    log('\n❌ Failed Games:', 'red');
    results.errors.forEach(error => {
      log(`  ${error.game} (${error.id}):`, 'red');
      error.errors.forEach(err => {
        log(`    - ${err}`, 'red');
      });
    });
  }
  
  // Warnings summary
  if (results.warnings_list.length > 0) {
    log('\n⚠️  Warnings Summary:', 'yellow');
    const uniqueWarnings = [...new Set(results.warnings_list)];
    uniqueWarnings.forEach(warning => {
      const count = results.warnings_list.filter(w => w === warning).length;
      log(`  ${warning} (${count} games)`, 'yellow');
    });
  }
}

// Run the comprehensive tests
runComprehensiveTests().catch(error => {
  logError(`Test runner failed: ${error.message}`);
  console.error(error.stack);
  process.exit(1);
});
