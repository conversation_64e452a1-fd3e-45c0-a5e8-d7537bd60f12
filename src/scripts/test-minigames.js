#!/usr/bin/env node

/**
 * Minigame Test Script
 *
 * This script tests all minigames to ensure they can be imported and don't have
 * obvious syntax errors or missing dependencies.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✓ ${message}`, 'green');
}

function logError(message) {
  log(`✗ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ ${message}`, 'blue');
}

// Test results
const results = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
  warnings: []
};

// Read the gameRegistry file
function readGameRegistry() {
  const registryPath = path.join(__dirname, '../components/Minigames/gameRegistry.ts');
  
  if (!fs.existsSync(registryPath)) {
    logError('gameRegistry.ts not found!');
    process.exit(1);
  }

  const content = fs.readFileSync(registryPath, 'utf8');
  return content;
}

// Extract game imports from the registry
function extractGameImports(content) {
  const importRegex = /import\s+(?:{[^}]+}|\w+)\s+from\s+['"]([^'"]+)['"];?/g;
  const imports = [];
  let match;

  while ((match = importRegex.exec(content)) !== null) {
    const importPath = match[1];
    if (importPath.startsWith('./games/') || importPath.includes('games/')) {
      imports.push({
        path: importPath,
        fullMatch: match[0]
      });
    }
  }

  return imports;
}

// Extract game definitions from the registry
function extractGameDefinitions(content) {
  // Look for the games array
  const gamesArrayMatch = content.match(/export const games: GameType\[\] = \[([\s\S]*?)\];/);
  
  if (!gamesArrayMatch) {
    logError('Could not find games array in gameRegistry.ts');
    return [];
  }

  const gamesContent = gamesArrayMatch[1];
  
  // Extract individual game objects
  const gameObjects = [];
  const gameRegex = /{[\s\S]*?id:\s*['"]([^'"]+)['"][\s\S]*?name:\s*['"]([^'"]+)['"][\s\S]*?component:\s*(\w+)[\s\S]*?}/g;
  let match;

  while ((match = gameRegex.exec(gamesContent)) !== null) {
    gameObjects.push({
      id: match[1],
      name: match[2],
      component: match[3]
    });
  }

  return gameObjects;
}

// Test if a file exists
function testFileExists(importPath) {
  const basePath = path.join(__dirname, '../components/Minigames');
  const possibleExtensions = ['.tsx', '.ts', '.jsx', '.js'];

  for (const ext of possibleExtensions) {
    const fullPath = path.join(basePath, importPath + ext);
    if (fs.existsSync(fullPath)) {
      return { exists: true, path: fullPath };
    }
  }

  return { exists: false, path: null };
}

// Test if a file has basic syntax issues
function testFileSyntax(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Basic syntax checks
    const issues = [];
    
    // Check for unmatched brackets
    const openBrackets = (content.match(/\{/g) || []).length;
    const closeBrackets = (content.match(/\}/g) || []).length;
    if (openBrackets !== closeBrackets) {
      issues.push('Unmatched curly brackets');
    }
    
    // Check for unmatched parentheses
    const openParens = (content.match(/\(/g) || []).length;
    const closeParens = (content.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push('Unmatched parentheses');
    }
    
    // Check for React component export
    if (!content.includes('export') || (!content.includes('function') && !content.includes('const') && !content.includes('class'))) {
      issues.push('No exported component found');
    }
    
    // Check for React import
    if (!content.includes("import React") && !content.includes("import { ") && !content.includes("'react'")) {
      issues.push('No React import found');
    }
    
    return { valid: issues.length === 0, issues };
  } catch (error) {
    return { valid: false, issues: [`File read error: ${error.message}`] };
  }
}

// Main test function
function runTests() {
  log('🧪 Starting Minigame Tests...', 'bright');
  log('================================', 'cyan');
  
  const registryContent = readGameRegistry();
  const imports = extractGameImports(registryContent);
  const games = extractGameDefinitions(registryContent);
  
  logInfo(`Found ${imports.length} game imports`);
  logInfo(`Found ${games.length} game definitions`);
  
  results.total = imports.length;
  
  log('\n📁 Testing File Existence...', 'bright');
  log('-----------------------------', 'cyan');
  
  // Test file existence
  for (const importItem of imports) {
    const fileTest = testFileExists(importItem.path);
    
    if (fileTest.exists) {
      logSuccess(`${importItem.path} exists`);
      
      // Test syntax
      const syntaxTest = testFileSyntax(fileTest.path);
      if (syntaxTest.valid) {
        logSuccess(`${importItem.path} syntax OK`);
        results.passed++;
      } else {
        logError(`${importItem.path} syntax issues: ${syntaxTest.issues.join(', ')}`);
        results.failed++;
        results.errors.push({
          file: importItem.path,
          issues: syntaxTest.issues
        });
      }
    } else {
      logError(`${importItem.path} NOT FOUND`);
      results.failed++;
      results.errors.push({
        file: importItem.path,
        issues: ['File not found']
      });
    }
  }
  
  log('\n🎮 Testing Game Definitions...', 'bright');
  log('-------------------------------', 'cyan');
  
  // Test game definitions
  for (const game of games) {
    const correspondingImport = imports.find(imp => 
      imp.fullMatch.includes(game.component)
    );
    
    if (correspondingImport) {
      logSuccess(`Game "${game.name}" (${game.id}) has matching import`);
    } else {
      logWarning(`Game "${game.name}" (${game.id}) component "${game.component}" not found in imports`);
      results.warnings.push(`Missing import for component: ${game.component}`);
    }
  }
  
  // Print summary
  log('\n📊 Test Summary', 'bright');
  log('===============', 'cyan');
  log(`Total files tested: ${results.total}`, 'blue');
  log(`Passed: ${results.passed}`, 'green');
  log(`Failed: ${results.failed}`, 'red');
  log(`Warnings: ${results.warnings.length}`, 'yellow');
  
  if (results.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    results.errors.forEach(error => {
      log(`  ${error.file}: ${error.issues.join(', ')}`, 'red');
    });
  }
  
  if (results.warnings.length > 0) {
    log('\n⚠️  Warnings:', 'yellow');
    results.warnings.forEach(warning => {
      log(`  ${warning}`, 'yellow');
    });
  }
  
  // Exit with appropriate code
  if (results.failed > 0) {
    log('\n💥 Tests failed!', 'red');
    process.exit(1);
  } else {
    log('\n🎉 All tests passed!', 'green');
    process.exit(0);
  }
}

// Run the tests
runTests();
