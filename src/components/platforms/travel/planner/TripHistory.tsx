'use client';

import { useState, useEffect } from 'react';
import { 
  Calendar, 
  MapPin, 
  DollarSign, 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Eye,
  Download,
  Filter,
  Search
} from 'lucide-react';
import { CustomerTripPlan } from '@/types/travel-planner';
import { BookingHistoryEntry } from '@/lib/platforms/travel/planner/services/booking-history';

interface TripHistoryProps {
  userId: string;
}

export default function TripHistory({ userId }: TripHistoryProps) {
  const [tripPlans, setTripPlans] = useState<CustomerTripPlan[]>([]);
  const [bookingHistory, setBookingHistory] = useState<BookingHistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'draft' | 'confirmed' | 'completed' | 'cancelled'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadTripHistory();
  }, [userId]);

  const loadTripHistory = async () => {
    setIsLoading(true);
    try {
      // Load trip plans
      const plansResponse = await fetch(`/api/platforms/travel/planner/trip-plans?customerId=${userId}`);
      if (plansResponse.ok) {
        const plans = await plansResponse.json();
        setTripPlans(plans);
      }

      // Load booking history
      const bookingsResponse = await fetch(`/api/platforms/travel/planner/bookings?customerId=${userId}`);
      if (bookingsResponse.ok) {
        const bookings = await bookingsResponse.json();
        setBookingHistory(bookings);
      }
    } catch (error) {
      console.error('Error loading trip history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'confirmed':
        return <CheckCircle className="w-5 h-5 text-blue-600" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'draft':
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const filteredPlans = tripPlans.filter(plan => {
    const matchesFilter = filter === 'all' || plan.status === filter;
    const matchesSearch = searchTerm === '' || 
      plan.destination.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Trip History</h2>
        <div className="flex space-x-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search destinations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Trips</option>
            <option value="draft">Draft</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {filteredPlans.length === 0 ? (
        <div className="text-center py-8">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No trips found</h3>
          <p className="text-gray-600">
            {searchTerm || filter !== 'all' 
              ? 'Try adjusting your search or filter criteria.'
              : 'Start planning your first trip to see it here!'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredPlans.map((plan) => (
            <div key={plan.id} className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <MapPin className="w-5 h-5 text-blue-600 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">{plan.destination}</h3>
                    <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(plan.status)}`}>
                      {plan.status.charAt(0).toUpperCase() + plan.status.slice(1)}
                    </span>
                  </div>
                  
                  <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {plan.duration} days
                    </div>
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {plan.groupSize} {plan.groupSize === 1 ? 'person' : 'people'}
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      Budget: {formatCurrency(plan.budget)}
                    </div>
                    {plan.totalPrice && (
                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1" />
                        Total: {formatCurrency(plan.totalPrice)}
                      </div>
                    )}
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      Created {formatDate(plan.createdAt)}
                    </div>
                  </div>

                  {plan.bookedServices.length > 0 && (
                    <div className="mb-3">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Booked Services:</h4>
                      <div className="flex flex-wrap gap-2">
                        {plan.bookedServices.map((service, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                          >
                            {service.serviceType}: {formatCurrency(service.price)}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="text-sm text-gray-600">
                    {plan.options.length} trip option{plan.options.length !== 1 ? 's' : ''} generated
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  {getStatusIcon(plan.status)}
                  <button
                    onClick={() => {
                      // In a real app, this would navigate to the trip details
                      console.log('View trip details:', plan.id);
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600"
                    title="View details"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  {plan.status === 'completed' && (
                    <button
                      onClick={() => {
                        // In a real app, this would download trip documents
                        console.log('Download trip documents:', plan.id);
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600"
                      title="Download documents"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              {plan.travelDates && (
                <div className="border-t border-gray-100 pt-3 mt-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="w-4 h-4 mr-1" />
                    Travel dates: {formatDate(plan.travelDates.departure)} - {formatDate(plan.travelDates.return)}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Trip Statistics */}
      {tripPlans.length > 0 && (
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h3 className="text-lg font-medium mb-4">Travel Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {tripPlans.length}
              </div>
              <div className="text-sm text-gray-600">Total Trips</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {tripPlans.filter(p => p.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {new Set(tripPlans.map(p => p.destination)).size}
              </div>
              <div className="text-sm text-gray-600">Destinations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatCurrency(tripPlans.reduce((sum, p) => sum + (p.totalPrice || 0), 0))}
              </div>
              <div className="text-sm text-gray-600">Total Spent</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
