'use client';

import { useState } from 'react';
import { 
  Calendar, 
  ChevronDown, 
  ChevronUp, 
  CreditCard, 
  DollarSign, 
  Globe, 
  Hotel, 
  Map, 
  PlaneTakeoff, 
  Star, 
  Tag, 
  Ticket, 
  Zap 
} from 'lucide-react';
import { TripOption } from '@/types/travel-planner';
import BookingConfirmation from './BookingConfirmation';

interface TripOptionCardProps {
  option: TripOption;
  destination: string;
  duration: number;
  groupSize: number;
  onGenerateVariations: () => void;
  onFinalizeBooking: () => void;
  holdExpired: boolean;
}

export default function TripOptionCard({
  option,
  destination,
  duration,
  groupSize,
  onGenerateVariations,
  onFinalizeBooking,
  holdExpired
}: TripOptionCardProps) {
  const [expanded, setExpanded] = useState(false);
  const [showBookingConfirmation, setShowBookingConfirmation] = useState(false);
  const [isProcessingBooking, setIsProcessingBooking] = useState(false);
  const [activeTab, setActiveTab] = useState<'flights' | 'accommodations' | 'activities'>('flights');
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };
  
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleConfirmBooking = async (paymentMethodId: string) => {
    setIsProcessingBooking(true);
    try {
      // In a real app, this would call the booking API
      const currentUserId = 'user-1'; // Would come from authentication

      const response = await fetch('/api/travel-planner/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'process_payment',
          userId: currentUserId,
          bookingId: `booking-${option.id}-${Date.now()}`,
          paymentMethodId,
          paymentAmount: option.price,
          paymentCurrency: 'USD'
        })
      });

      if (response.ok) {
        const transaction = await response.json();
        console.log('Payment processed:', transaction);

        // Call the original finalize booking function
        onFinalizeBooking();
        setShowBookingConfirmation(false);

        // Show success message
        alert('Booking confirmed! You will receive a confirmation email shortly.');
      } else {
        throw new Error('Payment processing failed');
      }
    } catch (error) {
      console.error('Error processing booking:', error);
      alert('Failed to process booking. Please try again.');
    } finally {
      setIsProcessingBooking(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${option.isSelected ? 'ring-2 ring-blue-500' : ''}`}>
      <div className="relative">
        <img 
          src={option.imageUrl} 
          alt={option.title} 
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-2 right-2 bg-white bg-opacity-90 rounded-full px-3 py-1 text-sm font-medium text-gray-900 flex items-center">
          <DollarSign className="h-4 w-4 text-green-600 mr-1" />
          {formatCurrency(option.price)}
        </div>
        {option.isSelected && (
          <div className="absolute top-0 left-0 w-full h-full bg-blue-500 bg-opacity-20 flex items-center justify-center">
            <div className="bg-blue-600 text-white px-4 py-2 rounded-md font-medium">
              Selected
            </div>
          </div>
        )}
      </div>
      
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{option.title}</h3>
        
        <div className="flex items-center mb-3">
          <Star className="h-4 w-4 text-yellow-400 mr-1" />
          <span className="text-sm font-medium text-gray-900 mr-1">{option.rating}</span>
          <span className="text-sm text-gray-500">({option.reviewCount} reviews)</span>
        </div>
        
        <p className="text-sm text-gray-600 mb-3">{option.description}</p>
        
        <div className="flex flex-wrap gap-2 mb-3">
          {option.tags.map((tag, index) => (
            <span 
              key={index} 
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
            >
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </span>
          ))}
        </div>
        
        <div className="flex flex-wrap gap-2 mb-3">
          {option.platforms.slice(0, 3).map((platform, index) => (
            <span 
              key={index} 
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              <Globe className="h-3 w-3 mr-1" />
              {platform}
            </span>
          ))}
          {option.platforms.length > 3 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              +{option.platforms.length - 3} more
            </span>
          )}
        </div>
        
        <div className="flex justify-between items-center mb-3">
          <button
            onClick={() => setExpanded(!expanded)}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            {expanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-1" />
                Hide details
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-1" />
                Show details
              </>
            )}
          </button>
          
          <div className="flex space-x-2">
            <button
              onClick={onGenerateVariations}
              className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Zap className="h-4 w-4 mr-1 text-amber-500" />
              Variations
            </button>
            
            <button
              onClick={() => setShowBookingConfirmation(true)}
              disabled={holdExpired || option.isSelected}
              className={`inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                holdExpired
                  ? 'bg-gray-400 cursor-not-allowed'
                  : option.isSelected
                    ? 'bg-green-600 cursor-default'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
              }`}
            >
              <CreditCard className="h-4 w-4 mr-1" />
              {option.isSelected ? 'Booked' : holdExpired ? 'Hold Expired' : 'Book Now'}
            </button>
          </div>
        </div>
        
        {expanded && (
          <div className="mt-4 border-t pt-4">
            <div className="flex border-b mb-4">
              <button
                onClick={() => setActiveTab('flights')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'flights' 
                    ? 'text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <PlaneTakeoff className="h-4 w-4 inline mr-1" />
                Flights
              </button>
              <button
                onClick={() => setActiveTab('accommodations')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'accommodations' 
                    ? 'text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Hotel className="h-4 w-4 inline mr-1" />
                Accommodation
              </button>
              <button
                onClick={() => setActiveTab('activities')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'activities' 
                    ? 'text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Ticket className="h-4 w-4 inline mr-1" />
                Activities
              </button>
            </div>
            
            {activeTab === 'flights' && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Flights</h4>
                {option.flights.map((flight, index) => (
                  <div key={index} className="mb-4 border-b pb-4 last:border-b-0 last:pb-0">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-xs font-medium text-gray-500">
                        {index === 0 ? 'Outbound' : 'Return'}
                      </span>
                      <span className="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        {flight.class.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="flex items-center mb-2">
                      <div className="text-sm font-medium text-gray-900">{flight.airline}</div>
                      <div className="mx-2 text-xs text-gray-500">•</div>
                      <div className="text-xs text-gray-500">{flight.flightNumber}</div>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-center">
                        <div className="text-sm font-bold">{formatTime(flight.departureTime)}</div>
                        <div className="text-xs text-gray-500">{flight.departureAirport}</div>
                        <div className="text-xs text-gray-500">{formatDate(flight.departureTime)}</div>
                      </div>
                      <div className="flex-1 mx-4">
                        <div className="relative">
                          <div className="absolute inset-0 flex items-center">
                            <div className="h-0.5 w-full bg-gray-200"></div>
                          </div>
                          <div className="relative flex justify-center">
                            <span className="bg-white px-2 text-xs text-gray-500">
                              {flight.duration}
                            </span>
                          </div>
                        </div>
                        {flight.layovers.length > 0 && (
                          <div className="mt-1 text-xs text-center text-gray-500">
                            {flight.layovers.length === 1 ? '1 stop' : `${flight.layovers.length} stops`}
                          </div>
                        )}
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-bold">{formatTime(flight.arrivalTime)}</div>
                        <div className="text-xs text-gray-500">{flight.arrivalAirport}</div>
                        <div className="text-xs text-gray-500">{formatDate(flight.arrivalTime)}</div>
                      </div>
                    </div>
                    {flight.layovers.length > 0 && (
                      <div className="mt-2 text-xs text-gray-500">
                        <div className="font-medium mb-1">Layovers:</div>
                        <ul className="list-disc list-inside">
                          {flight.layovers.map((layover, idx) => (
                            <li key={idx}>
                              {layover.airport} ({layover.duration})
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
            
            {activeTab === 'accommodations' && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Accommodation</h4>
                {option.accommodations.map((accommodation, index) => (
                  <div key={index} className="mb-4">
                    <div className="flex items-start mb-2">
                      <img 
                        src={accommodation.imageUrl} 
                        alt={accommodation.name} 
                        className="w-20 h-20 object-cover rounded-md mr-3"
                      />
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">{accommodation.name}</h5>
                        <div className="flex items-center mt-1">
                          <Star className="h-3 w-3 text-yellow-400 mr-1" />
                          <span className="text-xs font-medium text-gray-900 mr-1">{accommodation.rating}</span>
                          <span className="text-xs text-gray-500">({accommodation.reviewCount} reviews)</span>
                        </div>
                        <div className="flex items-center mt-1">
                          <Map className="h-3 w-3 text-gray-400 mr-1" />
                          <span className="text-xs text-gray-500">{accommodation.location}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-xs text-gray-500">
                        <Calendar className="h-3 w-3 inline mr-1" />
                        {formatDate(accommodation.checkIn)} - {formatDate(accommodation.checkOut)}
                      </div>
                      <div className="text-xs font-medium text-gray-900">
                        {accommodation.nights} {accommodation.nights === 1 ? 'night' : 'nights'}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {accommodation.amenities.slice(0, 5).map((amenity, idx) => (
                        <span 
                          key={idx} 
                          className="inline-block px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800"
                        >
                          {amenity}
                        </span>
                      ))}
                      {accommodation.amenities.length > 5 && (
                        <span className="inline-block px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                          +{accommodation.amenities.length - 5} more
                        </span>
                      )}
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-xs text-gray-500">
                        {accommodation.type.charAt(0).toUpperCase() + accommodation.type.slice(1)}
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(accommodation.price)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {activeTab === 'activities' && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Activities ({option.activities.length})</h4>
                <div className="max-h-60 overflow-y-auto pr-2">
                  {option.activities.map((activity, index) => (
                    <div key={index} className="mb-3 pb-3 border-b last:border-b-0 last:pb-0">
                      <div className="flex items-start">
                        <img 
                          src={activity.imageUrl} 
                          alt={activity.name} 
                          className="w-16 h-16 object-cover rounded-md mr-3"
                        />
                        <div>
                          <h5 className="text-sm font-medium text-gray-900">{activity.name}</h5>
                          <div className="flex items-center mt-1">
                            <Calendar className="h-3 w-3 text-gray-400 mr-1" />
                            <span className="text-xs text-gray-500 mr-2">{formatDate(activity.date)}</span>
                            <span className="text-xs text-gray-500">{activity.duration}</span>
                          </div>
                          <div className="flex justify-between items-center mt-1">
                            <span className="text-xs bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full">
                              {activity.category}
                            </span>
                            <span className="text-xs font-medium text-gray-900">
                              {formatCurrency(activity.price)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Booking Confirmation Modal */}
      {showBookingConfirmation && (
        <BookingConfirmation
          tripOption={option}
          destination={destination}
          duration={duration}
          groupSize={groupSize}
          onConfirmBooking={handleConfirmBooking}
          onCancel={() => setShowBookingConfirmation(false)}
          isProcessing={isProcessingBooking}
        />
      )}
    </div>
  );
}