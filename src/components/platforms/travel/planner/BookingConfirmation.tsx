'use client';

import { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Lock, 
  Calendar, 
  MapPin, 
  Users, 
  Plus,
  Shield
} from 'lucide-react';
import { TripOption } from '@/types/travel-planner';
import { PaymentMethod } from '@/lib/platforms/travel/planner/services/payment-service';

interface BookingConfirmationProps {
  tripOption: TripOption;
  destination: string;
  duration: number;
  groupSize: number;
  onConfirmBooking: (paymentMethodId: string) => Promise<void>;
  onCancel: () => void;
  isProcessing?: boolean;
}

export default function BookingConfirmation({ 
  tripOption,
  destination,
  duration,
  groupSize,
  onConfirmBooking, 
  onCancel,
  isProcessing = false 
}: BookingConfirmationProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(true);
  const [newPaymentMethod, setNewPaymentMethod] = useState({
    type: 'credit_card' as const,
    holderName: '',
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    isDefault: false
  });

  const currentUserId = 'user-1'; // In a real app, this would come from authentication

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    setIsLoadingPaymentMethods(true);
    try {
      const response = await fetch(`/api/platforms/travel/planner/payments?userId=${currentUserId}&type=methods`);
      if (response.ok) {
        const methods = await response.json();
        setPaymentMethods(methods);
        
        // Auto-select default payment method
        const defaultMethod = methods.find((method: PaymentMethod) => method.isDefault);
        if (defaultMethod) {
          setSelectedPaymentMethod(defaultMethod.id);
        }
      }
    } catch (error) {
      console.error('Error loading payment methods:', error);
    } finally {
      setIsLoadingPaymentMethods(false);
    }
  };

  const handleAddPaymentMethod = async () => {
    try {
      const response = await fetch('/api/platforms/travel/planner/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'add_payment_method',
          userId: currentUserId,
          type: newPaymentMethod.type,
          details: {
            holderName: newPaymentMethod.holderName,
            last4: newPaymentMethod.cardNumber.slice(-4),
            brand: 'visa', // In a real app, this would be detected
            expiryMonth: parseInt(newPaymentMethod.expiryMonth),
            expiryYear: parseInt(newPaymentMethod.expiryYear)
          },
          isDefault: newPaymentMethod.isDefault
        })
      });

      if (response.ok) {
        const addedMethod = await response.json();
        setPaymentMethods(prev => [...prev, addedMethod]);
        setSelectedPaymentMethod(addedMethod.id);
        setShowAddPaymentMethod(false);
        setNewPaymentMethod({
          type: 'credit_card',
          holderName: '',
          cardNumber: '',
          expiryMonth: '',
          expiryYear: '',
          cvv: '',
          isDefault: false
        });
      } else {
        throw new Error('Failed to add payment method');
      }
    } catch (error) {
      console.error('Error adding payment method:', error);
      alert('Failed to add payment method. Please try again.');
    }
  };

  const handleConfirmBooking = async () => {
    if (!selectedPaymentMethod) {
      alert('Please select a payment method');
      return;
    }

    try {
      await onConfirmBooking(selectedPaymentMethod);
    } catch (error) {
      console.error('Error confirming booking:', error);
      alert('Failed to confirm booking. Please try again.');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'credit_card':
      case 'debit_card':
        return <CreditCard className="w-5 h-5" />;
      default:
        return <CreditCard className="w-5 h-5" />;
    }
  };

  const totalPrice = tripOption.flights.reduce((sum, flight) => sum + flight.price, 0) +
                   tripOption.accommodations.reduce((sum, acc) => sum + acc.price, 0) +
                   tripOption.activities.reduce((sum, act) => sum + act.price, 0);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">Confirm Your Booking</h2>
          <button 
            onClick={onCancel}
            className="p-1 rounded-full hover:bg-gray-100"
            disabled={isProcessing}
          >
            <span className="sr-only">Close</span>
            ×
          </button>
        </div>
        
        <div className="overflow-auto flex-1 p-6">
          {/* Trip Summary */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-4">Trip Summary</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <MapPin className="w-5 h-5 text-blue-600 mr-2" />
                <span className="font-medium">{destination}</span>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {duration} days
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-1" />
                  {groupSize} {groupSize === 1 ? 'person' : 'people'}
                </div>
              </div>
            </div>
          </div>

          {/* Price Breakdown */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-4">Price Breakdown</h3>
            <div className="space-y-2">
              {tripOption.flights.map((flight, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>Flight: {flight.airline}</span>
                  <span>{formatCurrency(flight.price)}</span>
                </div>
              ))}
              {tripOption.accommodations.map((acc, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>Accommodation: {acc.name}</span>
                  <span>{formatCurrency(acc.price)}</span>
                </div>
              ))}
              {tripOption.activities.map((activity, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>Activity: {activity.name}</span>
                  <span>{formatCurrency(activity.price)}</span>
                </div>
              ))}
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>Total</span>
                <span>{formatCurrency(totalPrice)}</span>
              </div>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-4">Payment Method</h3>
            
            {isLoadingPaymentMethods ? (
              <div className="animate-pulse">
                <div className="h-12 bg-gray-200 rounded mb-2"></div>
                <div className="h-12 bg-gray-200 rounded"></div>
              </div>
            ) : (
              <div className="space-y-3">
                {paymentMethods.map((method) => (
                  <label 
                    key={method.id}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer ${
                      selectedPaymentMethod === method.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value={method.id}
                      checked={selectedPaymentMethod === method.id}
                      onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                      className="mr-3"
                    />
                    <div className="flex items-center flex-1">
                      {getPaymentMethodIcon(method.type)}
                      <div className="ml-3">
                        <div className="font-medium">
                          {method.details.brand?.toUpperCase()} •••• {method.details.last4}
                        </div>
                        <div className="text-sm text-gray-600">
                          {method.details.holderName}
                          {method.isDefault && (
                            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                              Default
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </label>
                ))}
                
                {!showAddPaymentMethod && (
                  <button
                    onClick={() => setShowAddPaymentMethod(true)}
                    className="flex items-center justify-center w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Add New Payment Method
                  </button>
                )}
              </div>
            )}

            {/* Add Payment Method Form */}
            {showAddPaymentMethod && (
              <div className="mt-4 p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium mb-3">Add New Payment Method</h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Cardholder Name
                    </label>
                    <input
                      type="text"
                      value={newPaymentMethod.holderName}
                      onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, holderName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="John Doe"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Card Number
                    </label>
                    <input
                      type="text"
                      value={newPaymentMethod.cardNumber}
                      onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, cardNumber: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="1234 5678 9012 3456"
                      maxLength={19}
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Month
                      </label>
                      <select
                        value={newPaymentMethod.expiryMonth}
                        onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, expiryMonth: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      >
                        <option value="">MM</option>
                        {Array.from({ length: 12 }, (_, i) => (
                          <option key={i + 1} value={i + 1}>
                            {String(i + 1).padStart(2, '0')}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Year
                      </label>
                      <select
                        value={newPaymentMethod.expiryYear}
                        onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, expiryYear: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      >
                        <option value="">YYYY</option>
                        {Array.from({ length: 10 }, (_, i) => (
                          <option key={i} value={new Date().getFullYear() + i}>
                            {new Date().getFullYear() + i}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        CVV
                      </label>
                      <input
                        type="text"
                        value={newPaymentMethod.cvv}
                        onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, cvv: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        placeholder="123"
                        maxLength={4}
                      />
                    </div>
                  </div>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newPaymentMethod.isDefault}
                      onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, isDefault: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className="text-sm">Set as default payment method</span>
                  </label>
                  
                  <div className="flex space-x-3">
                    <button
                      onClick={handleAddPaymentMethod}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Add Payment Method
                    </button>
                    <button
                      onClick={() => setShowAddPaymentMethod(false)}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Security Notice */}
          <div className="mb-6 p-4 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <Shield className="w-5 h-5 text-green-600 mr-2" />
              <div className="text-sm text-green-800">
                <div className="font-medium">Secure Payment</div>
                <div>Your payment information is encrypted and secure.</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end space-x-3 p-6 border-t">
          <button
            onClick={onCancel}
            disabled={isProcessing}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirmBooking}
            disabled={isProcessing || !selectedPaymentMethod}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isProcessing ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </>
            ) : (
              <>
                <Lock className="mr-2 h-4 w-4" />
                Confirm Booking - {formatCurrency(totalPrice)}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
