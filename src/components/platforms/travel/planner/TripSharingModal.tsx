'use client';

import { useState } from 'react';
import { 
  X, 
  Share2, 
  Copy, 
  Mail, 
  MessageSquare, 
  Users, 
  Globe, 
  Lock, 
  Calendar,
  Check,
  ExternalLink
} from 'lucide-react';
import { TripPlan } from '@/types/travel-planner';

interface TripSharingModalProps {
  isOpen: boolean;
  onClose: () => void;
  tripPlan: TripPlan;
  userId: string;
}

export default function TripSharingModal({ 
  isOpen, 
  onClose, 
  tripPlan,
  userId 
}: TripSharingModalProps) {
  const [shareSettings, setShareSettings] = useState({
    title: `${tripPlan.destination} Trip Plan`,
    description: `A ${tripPlan.duration}-day trip to ${tripPlan.destination} for ${tripPlan.groupSize} ${tripPlan.groupSize === 1 ? 'person' : 'people'}`,
    isPublic: false,
    allowComments: true,
    allowCollaboration: false,
    expiresAt: '',
    sharedWith: [] as { email: string; accessLevel: 'view' | 'comment' | 'edit' }[]
  });
  
  const [shareToken, setShareToken] = useState<string>('');
  const [isSharing, setIsSharing] = useState(false);
  const [isShared, setIsShared] = useState(false);
  const [copied, setCopied] = useState(false);
  const [newEmail, setNewEmail] = useState('');
  const [newAccessLevel, setNewAccessLevel] = useState<'view' | 'comment' | 'edit'>('view');

  const handleShare = async () => {
    setIsSharing(true);
    try {
      const response = await fetch('/api/travel-planner/sharing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'share_trip',
          tripPlanId: tripPlan.id,
          ownerId: userId,
          title: shareSettings.title,
          description: shareSettings.description,
          isPublic: shareSettings.isPublic,
          allowComments: shareSettings.allowComments,
          allowCollaboration: shareSettings.allowCollaboration,
          expiresAt: shareSettings.expiresAt || undefined,
          sharedWith: shareSettings.sharedWith
        })
      });

      if (response.ok) {
        const sharedTrip = await response.json();
        setShareToken(sharedTrip.shareToken);
        setIsShared(true);
      } else {
        throw new Error('Failed to share trip');
      }
    } catch (error) {
      console.error('Error sharing trip:', error);
      alert('Failed to share trip. Please try again.');
    } finally {
      setIsSharing(false);
    }
  };

  const handleCopyLink = async () => {
    const shareUrl = `${window.location.origin}/travel-planner/shared/${shareToken}`;
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleAddEmail = () => {
    if (newEmail && !shareSettings.sharedWith.some(item => item.email === newEmail)) {
      setShareSettings(prev => ({
        ...prev,
        sharedWith: [...prev.sharedWith, { email: newEmail, accessLevel: newAccessLevel }]
      }));
      setNewEmail('');
    }
  };

  const handleRemoveEmail = (email: string) => {
    setShareSettings(prev => ({
      ...prev,
      sharedWith: prev.sharedWith.filter(item => item.email !== email)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center">
            <Share2 className="mr-2 h-5 w-5" />
            Share Trip Plan
          </h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        <div className="overflow-auto flex-1 p-6">
          {!isShared ? (
            <div className="space-y-6">
              {/* Trip Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium mb-2">{tripPlan.destination} Trip</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>Duration: {tripPlan.duration} days</div>
                  <div>Group size: {tripPlan.groupSize} {tripPlan.groupSize === 1 ? 'person' : 'people'}</div>
                  <div>Budget: ${tripPlan.budget.toLocaleString()}</div>
                </div>
              </div>

              {/* Share Settings */}
              <div>
                <h3 className="text-lg font-medium mb-4">Share Settings</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Share Title
                    </label>
                    <input
                      type="text"
                      value={shareSettings.title}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description (Optional)
                    </label>
                    <textarea
                      value={shareSettings.description}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={shareSettings.isPublic}
                        onChange={(e) => setShareSettings(prev => ({ ...prev, isPublic: e.target.checked }))}
                        className="mr-2"
                      />
                      <Globe className="w-4 h-4 mr-1" />
                      <span className="text-sm">Make public (discoverable by anyone)</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={shareSettings.allowComments}
                        onChange={(e) => setShareSettings(prev => ({ ...prev, allowComments: e.target.checked }))}
                        className="mr-2"
                      />
                      <MessageSquare className="w-4 h-4 mr-1" />
                      <span className="text-sm">Allow comments</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={shareSettings.allowCollaboration}
                        onChange={(e) => setShareSettings(prev => ({ ...prev, allowCollaboration: e.target.checked }))}
                        className="mr-2"
                      />
                      <Users className="w-4 h-4 mr-1" />
                      <span className="text-sm">Allow collaborative editing</span>
                    </label>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expiration Date (Optional)
                    </label>
                    <input
                      type="datetime-local"
                      value={shareSettings.expiresAt}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, expiresAt: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              </div>

              {/* Specific People */}
              <div>
                <h3 className="text-lg font-medium mb-4">Share with Specific People</h3>
                
                <div className="space-y-3">
                  <div className="flex space-x-2">
                    <input
                      type="email"
                      placeholder="Enter email address"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                    />
                    <select
                      value={newAccessLevel}
                      onChange={(e) => setNewAccessLevel(e.target.value as any)}
                      className="px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="view">View</option>
                      <option value="comment">Comment</option>
                      <option value="edit">Edit</option>
                    </select>
                    <button
                      onClick={handleAddEmail}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Add
                    </button>
                  </div>

                  {shareSettings.sharedWith.length > 0 && (
                    <div className="space-y-2">
                      {shareSettings.sharedWith.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center">
                            <Mail className="w-4 h-4 mr-2 text-gray-400" />
                            <span className="text-sm">{item.email}</span>
                            <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                              {item.accessLevel}
                            </span>
                          </div>
                          <button
                            onClick={() => handleRemoveEmail(item.email)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Success Message */}
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Check className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Trip Shared Successfully!</h3>
                <p className="text-gray-600">Your trip plan is now shared and accessible via the link below.</p>
              </div>

              {/* Share Link */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Share Link
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={`${window.location.origin}/travel-planner/shared/${shareToken}`}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                  />
                  <button
                    onClick={handleCopyLink}
                    className={`px-4 py-2 border border-gray-300 rounded-md ${
                      copied ? 'bg-green-50 text-green-700' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {copied ? (
                      <>
                        <Check className="w-4 h-4 mr-1" />
                        Copied
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-1" />
                        Copy
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Share Options */}
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => window.open(`mailto:?subject=${encodeURIComponent(shareSettings.title)}&body=${encodeURIComponent(`Check out this trip plan: ${window.location.origin}/travel-planner/shared/${shareToken}`)}`)}
                  className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Email
                </button>
                
                <button
                  onClick={() => window.open(`${window.location.origin}/travel-planner/shared/${shareToken}`, '_blank')}
                  className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Preview
                </button>
              </div>

              {/* Share Settings Summary */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium mb-2">Share Settings</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex items-center">
                    {shareSettings.isPublic ? <Globe className="w-4 h-4 mr-1" /> : <Lock className="w-4 h-4 mr-1" />}
                    {shareSettings.isPublic ? 'Public' : 'Private'}
                  </div>
                  {shareSettings.allowComments && (
                    <div className="flex items-center">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Comments enabled
                    </div>
                  )}
                  {shareSettings.allowCollaboration && (
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      Collaboration enabled
                    </div>
                  )}
                  {shareSettings.expiresAt && (
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      Expires: {new Date(shareSettings.expiresAt).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div className="flex justify-end space-x-3 p-6 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            {isShared ? 'Close' : 'Cancel'}
          </button>
          {!isShared && (
            <button
              onClick={handleShare}
              disabled={isSharing || !shareSettings.title}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSharing ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Sharing...
                </>
              ) : (
                <>
                  <Share2 className="mr-2 h-4 w-4" />
                  Share Trip
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
