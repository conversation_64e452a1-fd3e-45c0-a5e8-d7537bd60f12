'use client';

import { useState } from 'react';
import { 
  Calendar, 
  ChevronDown, 
  ChevronRight, 
  Clock, 
  DollarSign, 
  Layers, 
  Map, 
  MoreHorizontal, 
  Users 
} from 'lucide-react';
import { TripPlan } from '@/types/travel-planner';

interface TripTreeViewProps {
  plans: TripPlan[];
  selectedPlanId: string | null;
  onSelectPlan: (planId: string) => void;
  onAddToComparison: (planId: string) => void;
}

export default function TripTreeView({ 
  plans, 
  selectedPlanId, 
  onSelectPlan, 
  onAddToComparison 
}: TripTreeViewProps) {
  const [expandedPlans, setExpandedPlans] = useState<Record<string, boolean>>({});
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);
  
  // Build tree structure
  const rootPlans = plans.filter(plan => plan.parentId === null);
  
  const getChildPlans = (parentId: string) => {
    return plans.filter(plan => plan.parentId === parentId);
  };
  
  const toggleExpand = (planId: string) => {
    setExpandedPlans(prev => ({
      ...prev,
      [planId]: !prev[planId]
    }));
  };
  
  const toggleDropdown = (planId: string | null) => {
    setDropdownOpen(dropdownOpen === planId ? null : planId);
  };
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  const renderPlanNode = (plan: TripPlan, level = 0) => {
    const childPlans = getChildPlans(plan.id);
    const hasChildren = childPlans.length > 0;
    const isExpanded = expandedPlans[plan.id] || false;
    const isSelected = plan.id === selectedPlanId;
    
    return (
      <div key={plan.id} className="mb-2">
        <div 
          className={`
            flex items-center p-2 rounded-md cursor-pointer
            ${isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'}
          `}
        >
          <div 
            className="mr-2 w-5 h-5 flex items-center justify-center"
            onClick={() => hasChildren && toggleExpand(plan.id)}
          >
            {hasChildren ? (
              isExpanded ? (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronRight className="h-4 w-4 text-gray-500" />
              )
            ) : (
              <div className="w-4" />
            )}
          </div>
          
          <div 
            className="flex-1"
            onClick={() => onSelectPlan(plan.id)}
          >
            <div className="flex items-center">
              <Map className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-sm font-medium text-gray-900 truncate">
                {plan.destination}
              </span>
            </div>
            
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <Calendar className="h-3 w-3 mr-1" />
              <span className="mr-2">{plan.duration} days</span>
              
              <DollarSign className="h-3 w-3 mr-1" />
              <span className="mr-2">${plan.budget}</span>
              
              <Users className="h-3 w-3 mr-1" />
              <span className="mr-2">{plan.groupSize}</span>
              
              <Clock className="h-3 w-3 mr-1" />
              <span>{formatDate(plan.createdAt)}</span>
            </div>
          </div>
          
          <div className="relative">
            <button
              onClick={() => toggleDropdown(plan.id)}
              className="p-1 rounded-full hover:bg-gray-200"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>
            
            {dropdownOpen === plan.id && (
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onAddToComparison(plan.id);
                      toggleDropdown(null);
                    }}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <Layers className="h-4 w-4 mr-2 text-gray-500" />
                    Add to comparison
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div className="ml-6 pl-2 border-l border-gray-200 mt-1">
            {childPlans.map(childPlan => renderPlanNode(childPlan, level + 1))}
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className="space-y-2">
      {rootPlans.length === 0 ? (
        <div className="text-sm text-gray-500 text-center py-4">
          No trip plans generated yet.
        </div>
      ) : (
        rootPlans.map(plan => renderPlanNode(plan))
      )}
    </div>
  );
} 