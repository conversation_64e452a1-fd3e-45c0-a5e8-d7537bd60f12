'use client';

import { useState, useEffect } from 'react';
import { TravelService, FlightService, AccommodationService, ActivityService } from '@/lib/platforms/travel/planner/services/types';
import { Globe, PlaneTakeoff, Building, Ticket } from 'lucide-react';

interface TravelServicesListProps {
  services: TravelService[];
  onServiceSelect?: (service: TravelService) => void;
  selectedServiceIds?: string[];
}

export default function TravelServicesList({ 
  services, 
  onServiceSelect,
  selectedServiceIds = []
}: TravelServicesListProps) {
  const [filterType, setFilterType] = useState<'all' | 'flight' | 'accommodation' | 'activity'>('all');
  const [filteredServices, setFilteredServices] = useState<TravelService[]>(services);
  
  // Filter services when filterType or services change
  useEffect(() => {
    if (filterType === 'all') {
      setFilteredServices(services);
    } else {
      setFilteredServices(services.filter(service => service.type === filterType));
    }
  }, [filterType, services]);
  
  // Get icon based on service type
  const getServiceIcon = (type: string) => {
    switch (type) {
      case 'flight':
        return <PlaneTakeoff className="h-5 w-5 text-blue-600" />;
      case 'accommodation':
        return <Building className="h-5 w-5 text-green-600" />;
      case 'activity':
        return <Ticket className="h-5 w-5 text-purple-600" />;
      default:
        return <Globe className="h-5 w-5 text-gray-600" />;
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <Globe className="mr-2 h-6 w-6 text-blue-600" />
        Travel Services
      </h2>
      
      {/* Filter tabs */}
      <div className="flex space-x-2 mb-4 border-b">
        <button
          className={`px-3 py-2 ${filterType === 'all' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500'}`}
          onClick={() => setFilterType('all')}
        >
          All
        </button>
        <button
          className={`px-3 py-2 ${filterType === 'flight' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500'}`}
          onClick={() => setFilterType('flight')}
        >
          Flights
        </button>
        <button
          className={`px-3 py-2 ${filterType === 'accommodation' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500'}`}
          onClick={() => setFilterType('accommodation')}
        >
          Accommodations
        </button>
        <button
          className={`px-3 py-2 ${filterType === 'activity' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500'}`}
          onClick={() => setFilterType('activity')}
        >
          Activities
        </button>
      </div>
      
      {/* Services list */}
      <div className="space-y-3">
        {filteredServices.length === 0 ? (
          <p className="text-gray-500 py-4 text-center">No services found.</p>
        ) : (
          filteredServices.map(service => (
            <div 
              key={service.id}
              className={`border rounded-lg p-3 ${
                selectedServiceIds.includes(service.id) 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              } ${onServiceSelect ? 'cursor-pointer' : ''}`}
              onClick={() => onServiceSelect?.(service)}
            >
              <div className="flex items-start">
                <div className="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-md flex items-center justify-center">
                  {getServiceIcon(service.type)}
                </div>
                <div className="ml-3 flex-1">
                  <div className="flex justify-between">
                    <h3 className="font-medium">{service.name}</h3>
                    <span className="text-sm text-gray-500">Priority: {service.priority}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-0.5">{service.description}</p>
                  <div className="flex justify-between mt-2">
                    <span className="text-xs bg-gray-100 rounded-full px-2 py-0.5">
                      {service.platform}
                    </span>
                    <span className="text-xs text-green-600">
                      {service.commission}% commission
                    </span>
                  </div>
                  
                  {/* Service-specific details */}
                  {service.type === 'flight' && (
                    <div className="mt-2 text-xs text-gray-500">
                      <p>Airlines: {(service as FlightService).airlines.slice(0, 3).join(', ')}{(service as FlightService).airlines.length > 3 ? '...' : ''}</p>
                      <p>Features: {(service as FlightService).features.join(', ')}</p>
                    </div>
                  )}
                  
                  {service.type === 'accommodation' && (
                    <div className="mt-2 text-xs text-gray-500">
                      <p>Types: {(service as AccommodationService).accommodationTypes.join(', ')}</p>
                      <p>Destinations: {(service as AccommodationService).destinations.slice(0, 3).join(', ')}{(service as AccommodationService).destinations.length > 3 ? '...' : ''}</p>
                    </div>
                  )}
                  
                  {service.type === 'activity' && (
                    <div className="mt-2 text-xs text-gray-500">
                      <p>Types: {(service as ActivityService).activityTypes.slice(0, 3).join(', ')}{(service as ActivityService).activityTypes.length > 3 ? '...' : ''}</p>
                      <p>Destinations: {(service as ActivityService).destinations.slice(0, 3).join(', ')}{(service as ActivityService).destinations.length > 3 ? '...' : ''}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}