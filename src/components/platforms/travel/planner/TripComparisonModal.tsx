'use client';

import { useState } from 'react';
import { 
  Calendar, 
  CreditCard, 
  DollarSign, 
  Download, 
  Hotel, 
  Map, 
  PlaneTakeoff, 
  Star, 
  Tag, 
  Ticket, 
  Trash2, 
  Users, 
  X 
} from 'lucide-react';
import { TripPlan, TripOption } from '@/types/travel-planner';

interface TripComparisonModalProps {
  plans: TripPlan[];
  onClose: () => void;
  onRemovePlan: (planId: string) => void;
  onFinalizeBooking: (optionId: string) => void;
  holdExpired: boolean;
}

export default function TripComparisonModal({ 
  plans, 
  onClose, 
  onRemovePlan, 
  onFinalizeBooking,
  holdExpired
}: TripComparisonModalProps) {
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'overview' | 'flights' | 'accommodations' | 'activities'>('overview');
  
  // Helper functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(amount);
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };
  
  // Handle option selection for each plan
  const handleOptionSelect = (planId: string, optionId: string) => {
    setSelectedOptions(prev => ({
      ...prev,
      [planId]: optionId
    }));
  };
  
  // Get selected option for a plan
  const getSelectedOption = (plan: TripPlan): TripOption | undefined => {
    const optionId = selectedOptions[plan.id];
    return optionId ? plan.options.find(option => option.id === optionId) : plan.options[0];
  };
  
  // Export comparison as CSV
  const exportComparison = () => {
    const headers = ['Plan', 'Destination', 'Duration', 'Price', 'Flights', 'Accommodation', 'Activities'];
    
    const rows = plans.map(plan => {
      const option = getSelectedOption(plan);
      if (!option) return [];
      
      return [
        plan.id,
        plan.destination,
        `${plan.duration} days`,
        formatCurrency(option.price),
        option.flights.map(f => f.airline).join(', '),
        option.accommodations.map(a => a.name).join(', '),
        option.activities.length.toString()
      ];
    });
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'trip-comparison.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Compare Trip Options</h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'overview' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('flights')}
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'flights' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <PlaneTakeoff className="h-4 w-4 inline mr-1" />
            Flights
          </button>
          <button
            onClick={() => setActiveTab('accommodations')}
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'accommodations' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Hotel className="h-4 w-4 inline mr-1" />
            Accommodations
          </button>
          <button
            onClick={() => setActiveTab('activities')}
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'activities' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Ticket className="h-4 w-4 inline mr-1" />
            Activities
          </button>
        </div>
        
        <div className="overflow-auto flex-1 p-4">
          {plans.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No plans selected for comparison.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {plans.map(plan => {
                const options = plan.options;
                const selectedOption = getSelectedOption(plan);
                
                if (!selectedOption) return null;
                
                return (
                  <div key={plan.id} className="border rounded-lg overflow-hidden">
                    <div className="p-3 bg-gray-50 border-b flex justify-between items-center">
                      <div>
                        <h3 className="font-medium text-gray-900 flex items-center">
                          <Map className="h-4 w-4 text-gray-500 mr-1" />
                          {plan.destination}
                        </h3>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <Calendar className="h-3 w-3 mr-1" />
                          <span className="mr-2">{plan.duration} days</span>
                          <Users className="h-3 w-3 mr-1" />
                          <span>{plan.groupSize}</span>
                        </div>
                      </div>
                      <button
                        onClick={() => onRemovePlan(plan.id)}
                        className="p-1 rounded-full hover:bg-gray-200"
                      >
                        <Trash2 className="h-4 w-4 text-gray-500" />
                      </button>
                    </div>
                    
                    {options.length > 1 && (
                      <div className="p-3 border-b">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Select Option
                        </label>
                        <select
                          value={selectedOptions[plan.id] || options[0].id}
                          onChange={(e) => handleOptionSelect(plan.id, e.target.value)}
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          {options.map(option => (
                            <option key={option.id} value={option.id}>
                              {option.title} - {formatCurrency(option.price)}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                    
                    <div className="p-3">
                      {activeTab === 'overview' && (
                        <div>
                          <div className="mb-3">
                            <div className="text-sm font-medium text-gray-900 mb-1">{selectedOption.title}</div>
                            <div className="flex items-center mb-2">
                              <Star className="h-4 w-4 text-yellow-400 mr-1" />
                              <span className="text-sm font-medium text-gray-900 mr-1">{selectedOption.rating}</span>
                              <span className="text-xs text-gray-500">({selectedOption.reviewCount} reviews)</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mb-2">
                              {formatCurrency(selectedOption.price)}
                            </div>
                            <div className="flex flex-wrap gap-1 mb-2">
                              {selectedOption.tags.slice(0, 3).map((tag, idx) => (
                                <span 
                                  key={idx} 
                                  className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                                >
                                  <Tag className="h-3 w-3 mr-1" />
                                  {tag}
                                </span>
                              ))}
                              {selectedOption.tags.length > 3 && (
                                <span className="inline-block px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                                  +{selectedOption.tags.length - 3} more
                                </span>
                              )}
                            </div>
                            <div className="text-xs text-gray-600 line-clamp-3 mb-3">
                              {selectedOption.description}
                            </div>
                          </div>
                          
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-500">Flights:</span>
                              <span className="font-medium text-gray-900">{selectedOption.flights.length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Accommodations:</span>
                              <span className="font-medium text-gray-900">{selectedOption.accommodations.length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Activities:</span>
                              <span className="font-medium text-gray-900">{selectedOption.activities.length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Price per person:</span>
                              <span className="font-medium text-gray-900">
                                {formatCurrency(selectedOption.price / plan.groupSize)}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {activeTab === 'flights' && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Flights</h4>
                          {selectedOption.flights.map((flight, idx) => (
                            <div key={idx} className="mb-3 pb-3 border-b last:border-b-0 last:pb-0">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-xs font-medium text-gray-500">
                                  {idx === 0 ? 'Outbound' : 'Return'}
                                </span>
                                <span className="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                  {flight.class.replace('_', ' ').toUpperCase()}
                                </span>
                              </div>
                              <div className="text-xs font-medium text-gray-900 mb-1">{flight.airline}</div>
                              <div className="flex justify-between text-xs">
                                <div>
                                  <div className="font-medium">{flight.departureAirport}</div>
                                  <div className="text-gray-500">{formatDate(flight.departureTime)}</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-gray-500">{flight.duration}</div>
                                  <div className="text-gray-500">
                                    {flight.layovers.length === 0 
                                      ? 'Direct' 
                                      : flight.layovers.length === 1 
                                        ? '1 stop' 
                                        : `${flight.layovers.length} stops`}
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="font-medium">{flight.arrivalAirport}</div>
                                  <div className="text-gray-500">{formatDate(flight.arrivalTime)}</div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                      
                      {activeTab === 'accommodations' && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Accommodations</h4>
                          {selectedOption.accommodations.map((accommodation, idx) => (
                            <div key={idx} className="mb-3 pb-3 border-b last:border-b-0 last:pb-0">
                              <div className="text-xs font-medium text-gray-900 mb-1">{accommodation.name}</div>
                              <div className="flex items-center mb-1">
                                <Star className="h-3 w-3 text-yellow-400 mr-1" />
                                <span className="text-xs font-medium text-gray-900 mr-1">{accommodation.rating}</span>
                                <span className="text-xs text-gray-500">({accommodation.reviewCount})</span>
                              </div>
                              <div className="text-xs text-gray-500 mb-1">
                                {formatDate(accommodation.checkIn)} - {formatDate(accommodation.checkOut)}
                                <span className="mx-1">•</span>
                                {accommodation.nights} {accommodation.nights === 1 ? 'night' : 'nights'}
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">
                                  {accommodation.type.charAt(0).toUpperCase() + accommodation.type.slice(1)}
                                </span>
                                <span className="text-xs font-medium text-gray-900">
                                  {formatCurrency(accommodation.price)}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                      
                      {activeTab === 'activities' && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            Activities ({selectedOption.activities.length})
                          </h4>
                          <div className="max-h-60 overflow-y-auto pr-1">
                            {selectedOption.activities.map((activity, idx) => (
                              <div key={idx} className="mb-2 pb-2 border-b last:border-b-0 last:pb-0">
                                <div className="text-xs font-medium text-gray-900 mb-1">{activity.name}</div>
                                <div className="text-xs text-gray-500 mb-1">
                                  {formatDate(activity.date)} • {activity.duration}
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-xs bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full">
                                    {activity.category}
                                  </span>
                                  <span className="text-xs font-medium text-gray-900">
                                    {formatCurrency(activity.price)}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <div className="mt-4 pt-3 border-t">
                        <button
                          onClick={() => onFinalizeBooking(selectedOption.id)}
                          disabled={holdExpired || selectedOption.isSelected}
                          className={`w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                            holdExpired 
                              ? 'bg-gray-400 cursor-not-allowed' 
                              : selectedOption.isSelected 
                                ? 'bg-green-600 cursor-default'
                                : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                          }`}
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          {selectedOption.isSelected ? 'Booked' : holdExpired ? 'Hold Expired' : 'Book This Trip'}
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
        
        <div className="p-4 border-t flex justify-between">
          <button
            onClick={exportComparison}
            disabled={plans.length === 0}
            className={`inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md ${
              plans.length === 0 
                ? 'text-gray-400 bg-gray-100 cursor-not-allowed' 
                : 'text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            }`}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Comparison
          </button>
          
          <button
            onClick={onClose}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
} 