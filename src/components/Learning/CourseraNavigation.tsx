'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, ChevronDown, Globe, User, Menu, X, MessageCircle, LogOut } from 'lucide-react';

interface CourseraNavigationProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export function CourseraNavigation({ searchQuery, onSearchChange }: CourseraNavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isExploreOpen, setIsExploreOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check authentication status
    const token = localStorage.getItem('learning_token');
    const userData = localStorage.getItem('learning_user');

    if (token && userData) {
      try {
        setUser(JSON.parse(userData));
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Error parsing user data:', error);
        localStorage.removeItem('learning_token');
        localStorage.removeItem('learning_user');
      }
    }
  }, []);

  const handleLogout = async () => {
    try {
      const token = localStorage.getItem('learning_token');
      if (token) {
        await fetch('/api/platforms/edu/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('learning_token');
      localStorage.removeItem('learning_user');
      setUser(null);
      setIsAuthenticated(false);
      window.location.href = '/platforms/edu';
    }
  };

  const exploreItems = [
    { label: 'Browse by Subject', href: '/platforms/edu/subjects' },
    { label: 'Browse by University', href: '/platforms/edu/universities' },
    { label: 'Professional Certificates', href: '/platforms/edu/certificates' },
    { label: 'University Degrees', href: '/platforms/edu/degrees' },
    { label: 'Free Courses', href: '/platforms/edu/free' },
    { label: 'EduTalk Coaching', href: '/platforms/edu/apps/edutalk' },
  ];

  return (
    <nav className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-8">
            <Link href="/platforms/edu" className="text-2xl font-bold text-blue-600">
              GOODJOB
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-6">
              <div className="relative group">
                <button 
                  className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 py-2"
                  onClick={() => setIsExploreOpen(!isExploreOpen)}
                >
                  <span>Explore</span>
                  <ChevronDown className="w-4 h-4" />
                </button>
                {isExploreOpen && (
                  <div className="absolute top-full left-0 mt-1 w-64 bg-white rounded-lg shadow-lg border py-2 z-10">
                    {exploreItems.map((item) => (
                      <Link
                        key={item.label}
                        href={item.href}
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-blue-600"
                        onClick={() => setIsExploreOpen(false)}
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
              <Link href="/platforms/edu/degrees" className="text-gray-700 hover:text-blue-600">
                Online Degrees
              </Link>
              <Link href="/platforms/edu/careers" className="text-gray-700 hover:text-blue-600">
                Find your New Career
              </Link>
              <Link href="/platforms/edu/apps/edutalk" className="text-gray-700 hover:text-blue-600 flex items-center gap-2">
                <MessageCircle className="w-4 h-4" />
                EduTalk
              </Link>
            </div>
          </div>

          {/* Search Bar */}
          <div className="hidden lg:block flex-1 max-w-lg mx-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="What do you want to learn?"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
              />
            </div>
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Language */}
            <button className="hidden md:flex items-center space-x-1 text-gray-700 hover:text-blue-600">
              <Globe className="w-5 h-5" />
              <span>English</span>
            </button>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center space-x-2">
              {isAuthenticated && user ? (
                <div className="relative">
                  <button
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                    className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 p-2 rounded-lg hover:bg-gray-50"
                  >
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-blue-600" />
                    </div>
                    <span className="font-medium">{user.name}</span>
                    <ChevronDown className="w-4 h-4" />
                  </button>

                  {isUserMenuOpen && (
                    <div className="absolute top-full right-0 mt-1 w-64 bg-white rounded-lg shadow-lg border py-2 z-10">
                      <div className="px-4 py-2 border-b">
                        <p className="font-medium text-gray-900">{user.name}</p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                        <p className="text-xs text-blue-600 capitalize">{user.role}</p>
                      </div>
                      <Link
                        href={user.role === 'teacher' ? '/platforms/edu/teacher' : '/platforms/edu/dashboard'}
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-50"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        Dashboard
                      </Link>
                      <Link
                        href="/platforms/edu/profile"
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-50"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        Profile Settings
                      </Link>
                      <Link
                        href="/platforms/edu/apps/edutalk"
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-50"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        EduTalk Coaching
                      </Link>
                      <button
                        onClick={() => {
                          setIsUserMenuOpen(false);
                          handleLogout();
                        }}
                        className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 flex items-center gap-2"
                      >
                        <LogOut className="w-4 h-4" />
                        Sign Out
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <Link href="/platforms/edu/auth/login" className="text-blue-600 hover:text-blue-700 font-medium">
                    Log In
                  </Link>
                  <Link href="/platforms/edu/auth/register" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Join for Free
                  </Link>
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="lg:hidden pb-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="What do you want to learn?"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t bg-white py-4">
            <div className="space-y-4">
              <div>
                <button 
                  className="flex items-center justify-between w-full text-left text-gray-700 font-medium"
                  onClick={() => setIsExploreOpen(!isExploreOpen)}
                >
                  Explore
                  <ChevronDown className={`w-4 h-4 transition-transform ${isExploreOpen ? 'rotate-180' : ''}`} />
                </button>
                {isExploreOpen && (
                  <div className="mt-2 pl-4 space-y-2">
                    {exploreItems.map((item) => (
                      <Link
                        key={item.label}
                        href={item.href}
                        className="block text-gray-600 hover:text-blue-600"
                        onClick={() => {
                          setIsMobileMenuOpen(false);
                          setIsExploreOpen(false);
                        }}
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
              <Link
                href="/platforms/edu/degrees"
                className="block text-gray-700 hover:text-blue-600"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Online Degrees
              </Link>
              <Link
                href="/platforms/edu/careers"
                className="block text-gray-700 hover:text-blue-600"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Find your New Career
              </Link>
              <Link
                href="/platforms/edu/apps/edutalk"
                className="flex items-center gap-2 text-gray-700 hover:text-blue-600"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <MessageCircle className="w-4 h-4" />
                EduTalk Coaching
              </Link>
              <div className="flex items-center space-x-1 text-gray-700">
                <Globe className="w-5 h-5" />
                <span>English</span>
              </div>
              <div className="space-y-2 pt-4 border-t">
                {isAuthenticated && user ? (
                  <>
                    <div className="px-2 py-2 border-b">
                      <p className="font-medium text-gray-900">{user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      <p className="text-xs text-blue-600 capitalize">{user.role}</p>
                    </div>
                    <Link
                      href={user.role === 'teacher' ? '/platforms/edu/teacher' : '/platforms/edu/dashboard'}
                      className="block w-full text-left text-gray-700 hover:text-blue-600 py-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/platforms/edu/profile"
                      className="block w-full text-left text-gray-700 hover:text-blue-600 py-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Profile Settings
                    </Link>
                    <button
                      onClick={() => {
                        setIsMobileMenuOpen(false);
                        handleLogout();
                      }}
                      className="w-full text-left text-red-600 hover:text-red-700 py-2 flex items-center gap-2"
                    >
                      <LogOut className="w-4 h-4" />
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      href="/platforms/edu/auth/login"
                      className="block w-full text-left text-blue-600 hover:text-blue-700 font-medium py-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Log In
                    </Link>
                    <Link
                      href="/platforms/edu/auth/register"
                      className="block w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Join for Free
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Backdrop for dropdowns */}
      {(isExploreOpen || isMobileMenuOpen || isUserMenuOpen) && (
        <div
          className="fixed inset-0 bg-black bg-opacity-20 z-40"
          onClick={() => {
            setIsExploreOpen(false);
            setIsMobileMenuOpen(false);
            setIsUserMenuOpen(false);
          }}
        />
      )}
    </nav>
  );
} 