import React from 'react';

export const LearningFooter: React.FC = () => {
  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* AI Learning Column */}
          <div>
            <h3 className="font-bold text-gray-900 mb-6 text-lg">AI Learning</h3>
            <ul className="space-y-4">
              <li><a href="/platforms/edu" className="text-gray-600 hover:text-purple-600 transition-colors">About</a></li>
              <li><a href="/platforms/edu/courses" className="text-gray-600 hover:text-purple-600 transition-colors">What We Offer</a></li>
              <li><a href="/platforms/edu/certificates" className="text-gray-600 hover:text-purple-600 transition-colors">Leadership</a></li>
              <li><a href="/platforms/edu/careers" className="text-gray-600 hover:text-purple-600 transition-colors">Careers</a></li>
              <li><a href="/platforms/edu/catalog" className="text-gray-600 hover:text-purple-600 transition-colors">Catalog</a></li>
              <li><a href="/platforms/edu/plus" className="text-gray-600 hover:text-purple-600 transition-colors">AI Learning Plus</a></li>
              <li><a href="/platforms/edu/certificates" className="text-gray-600 hover:text-purple-600 transition-colors">Professional Certificates</a></li>
              <li><a href="/platforms/edu/masters" className="text-gray-600 hover:text-purple-600 transition-colors">MasterTrack® Certificates</a></li>
              <li><a href="/platforms/edu/degrees" className="text-gray-600 hover:text-purple-600 transition-colors">Degrees</a></li>
              <li><a href="/platforms/edu/enterprise" className="text-gray-600 hover:text-purple-600 transition-colors">For Enterprise</a></li>
              <li><a href="/platforms/edu/government" className="text-gray-600 hover:text-purple-600 transition-colors">For Government</a></li>
              <li><a href="/platforms/edu/campus" className="text-gray-600 hover:text-purple-600 transition-colors">For Campus</a></li>
              <li><a href="/platforms/edu/partner" className="text-gray-600 hover:text-purple-600 transition-colors">Become a Partner</a></li>
              <li><a href="/platforms/edu/social-impact" className="text-gray-600 hover:text-purple-600 transition-colors">Social Impact</a></li>
              <li><a href="/platforms/edu/free" className="text-gray-600 hover:text-purple-600 transition-colors">Free Courses</a></li>
              <li><a href="/platforms/edu/credit" className="text-gray-600 hover:text-purple-600 transition-colors">ECTS Credit Recommendations</a></li>
            </ul>
          </div>

          {/* Community Column */}
          <div>
            <h3 className="font-bold text-gray-900 mb-6 text-lg">Community</h3>
            <ul className="space-y-4">
              <li><a href="/community/learners" className="text-gray-600 hover:text-purple-600 transition-colors">Learners</a></li>
              <li><a href="/community/partners" className="text-gray-600 hover:text-purple-600 transition-colors">Partners</a></li>
              <li><a href="/community/beta" className="text-gray-600 hover:text-purple-600 transition-colors">Beta Testers</a></li>
              <li><a href="/community/blog" className="text-gray-600 hover:text-purple-600 transition-colors">Blog</a></li>
              <li><a href="/community/podcast" className="text-gray-600 hover:text-purple-600 transition-colors">The AI Learning Podcast</a></li>
              <li><a href="/community/tech-blog" className="text-gray-600 hover:text-purple-600 transition-colors">Tech Blog</a></li>
              <li><a href="/community/teaching" className="text-gray-600 hover:text-purple-600 transition-colors">Teaching Center</a></li>
            </ul>
          </div>

          {/* More Column */}
          <div>
            <h3 className="font-bold text-gray-900 mb-6 text-lg">More</h3>
            <ul className="space-y-4">
              <li><a href="/press" className="text-gray-600 hover:text-purple-600 transition-colors">Press</a></li>
              <li><a href="/investors" className="text-gray-600 hover:text-purple-600 transition-colors">Investors</a></li>
              <li><a href="/terms" className="text-gray-600 hover:text-purple-600 transition-colors">Terms</a></li>
              <li><a href="/privacy" className="text-gray-600 hover:text-purple-600 transition-colors">Privacy</a></li>
              <li><a href="/help" className="text-gray-600 hover:text-purple-600 transition-colors">Help</a></li>
              <li><a href="/accessibility" className="text-gray-600 hover:text-purple-600 transition-colors">Accessibility</a></li>
              <li><a href="/contact" className="text-gray-600 hover:text-purple-600 transition-colors">Contact</a></li>
              <li><a href="/articles" className="text-gray-600 hover:text-purple-600 transition-colors">Articles</a></li>
              <li><a href="/directory" className="text-gray-600 hover:text-purple-600 transition-colors">Directory</a></li>
              <li><a href="/affiliates" className="text-gray-600 hover:text-purple-600 transition-colors">Affiliates</a></li>
              <li><a href="/modern-slavery" className="text-gray-600 hover:text-purple-600 transition-colors">Modern Slavery Statement</a></li>
              <li><a href="/cookies" className="text-gray-600 hover:text-purple-600 transition-colors">Manage Cookie Preferences</a></li>
            </ul>
          </div>

          {/* Mobile Apps & Certification Column */}
          <div>
            <div className="mb-8">
              <h4 className="font-semibold text-gray-900 mb-4">Download on Mobile</h4>
              <div className="space-y-3">
                <a href="#" className="block">
                  <img 
                    src="https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg" 
                    alt="Download on the App Store" 
                    className="h-10"
                  />
                </a>
                <a href="#" className="block">
                  <img 
                    src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" 
                    alt="Get it on Google Play" 
                    className="h-10"
                  />
                </a>
              </div>
            </div>
            
            <div>
              <div className="w-20 h-20 bg-gray-900 rounded-full flex items-center justify-center mb-3">
                <span className="text-white font-bold text-lg">B</span>
              </div>
              <p className="text-xs text-gray-500 font-medium">Certified<br/>B Corporation</p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            <p className="text-sm text-gray-500">
              © 2025 AI Learning Inc. All rights reserved.
            </p>
            
            {/* Social Media Icons */}
            <div className="flex items-center gap-4">
              <a href="#" className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                <span className="text-white text-sm font-bold">f</span>
              </a>
              <a href="#" className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                <span className="text-white text-sm font-bold">in</span>
              </a>
              <a href="#" className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                <span className="text-white text-sm font-bold">𝕏</span>
              </a>
              <a href="#" className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                <span className="text-white text-sm font-bold">▶</span>
              </a>
              <a href="#" className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                <span className="text-white text-sm font-bold">📷</span>
              </a>
              <a href="#" className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                <span className="text-white text-sm font-bold">🎵</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}; 