'use client';

import React, { useState, useEffect } from 'react';
import { X, Setting<PERSON>, <PERSON>, Eye, Target, Sliders } from 'lucide-react';

interface ConsentType {
  id: string;
  name: string;
  description: string;
  required: boolean;
  enabled: boolean;
  category: string;
  purposes: string[];
}

interface BannerConfig {
  position: string;
  theme: string;
  showLogo: boolean;
  showCloseButton: boolean;
  allowDismiss: boolean;
  autoShow: boolean;
  layout: string;
}

interface ConsentBannerProps {
  visitorId?: string;
  language?: string;
  onConsentChange?: (consent: { [key: string]: boolean }) => void;
}

export default function CookieConsentBanner({ 
  visitorId = 'anonymous', 
  language = 'en',
  onConsentChange 
}: ConsentBannerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [consentTypes, setConsentTypes] = useState<ConsentType[]>([]);
  const [config, setConfig] = useState<BannerConfig | null>(null);
  const [texts, setTexts] = useState<any>({});
  const [consent, setConsent] = useState<{ [key: string]: boolean }>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadBannerConfig();
    checkExistingConsent();
  }, [visitorId]);

  const loadBannerConfig = async () => {
    try {
      const response = await fetch('/api/abnsalesops/webintelligence/privacy?action=banner-config');
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
        setConsentTypes(data.config.consentTypes || []);
        setTexts(data.config.texts[language] || data.config.texts.en || {});
        
        // Initialize consent state
        const initialConsent: { [key: string]: boolean } = {};
        data.config.consentTypes.forEach((type: ConsentType) => {
          initialConsent[type.id] = type.required; // Required types are pre-checked
        });
        setConsent(initialConsent);
      }
    } catch (error) {
      console.error('Failed to load banner config:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkExistingConsent = async () => {
    try {
      const response = await fetch(`/api/abnsalesops/webintelligence/privacy?action=consent&visitorId=${visitorId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.consent.hasConsent) {
          // User has already given consent
          setIsVisible(false);
          
          // Update consent state
          const existingConsent: { [key: string]: boolean } = {};
          consentTypes.forEach(type => {
            existingConsent[type.id] = data.consent.canTrack[type.id] || type.required;
          });
          setConsent(existingConsent);
          
          if (onConsentChange) {
            onConsentChange(existingConsent);
          }
        } else {
          // Show banner if auto-show is enabled
          if (config?.autoShow !== false) {
            setIsVisible(true);
          }
        }
      }
    } catch (error) {
      console.error('Failed to check existing consent:', error);
      // Show banner on error if auto-show is enabled
      if (config?.autoShow !== false) {
        setIsVisible(true);
      }
    }
  };

  const handleAcceptAll = async () => {
    const allConsent: { [key: string]: boolean } = {};
    consentTypes.forEach(type => {
      allConsent[type.id] = true;
    });
    
    await recordConsent(Object.keys(allConsent).filter(key => allConsent[key]));
    setConsent(allConsent);
    setIsVisible(false);
    
    if (onConsentChange) {
      onConsentChange(allConsent);
    }
  };

  const handleRejectAll = async () => {
    const minimalConsent: { [key: string]: boolean } = {};
    consentTypes.forEach(type => {
      minimalConsent[type.id] = type.required; // Only required types
    });
    
    await recordConsent(Object.keys(minimalConsent).filter(key => minimalConsent[key]));
    setConsent(minimalConsent);
    setIsVisible(false);
    
    if (onConsentChange) {
      onConsentChange(minimalConsent);
    }
  };

  const handleSavePreferences = async () => {
    const acceptedTypes = Object.keys(consent).filter(key => consent[key]);
    await recordConsent(acceptedTypes);
    setIsVisible(false);
    
    if (onConsentChange) {
      onConsentChange(consent);
    }
  };

  const recordConsent = async (consentTypes: string[]) => {
    try {
      await fetch('/api/abnsalesops/webintelligence/privacy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'record-consent',
          data: {
            visitorId,
            consentTypes,
            source: 'banner',
            ipAddress: '', // Will be filled by server
            userAgent: navigator.userAgent
          }
        })
      });
    } catch (error) {
      console.error('Failed to record consent:', error);
    }
  };

  const toggleConsentType = (typeId: string) => {
    const type = consentTypes.find(t => t.id === typeId);
    if (type && !type.required) {
      setConsent(prev => ({
        ...prev,
        [typeId]: !prev[typeId]
      }));
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'functional': return <Settings className="h-5 w-5" />;
      case 'analytics': return <Eye className="h-5 w-5" />;
      case 'marketing': return <Target className="h-5 w-5" />;
      default: return <Shield className="h-5 w-5" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'functional': return 'text-blue-600';
      case 'analytics': return 'text-green-600';
      case 'marketing': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  if (isLoading || !isVisible || !config) {
    return null;
  }

  const bannerClasses = `
    fixed ${config.position === 'top' ? 'top-0' : 'bottom-0'} left-0 right-0 
    ${config.theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}
    border-t border-gray-200 shadow-lg z-50 p-4 md:p-6
  `;

  return (
    <div className={bannerClasses}>
      <div className="max-w-7xl mx-auto">
        {!showDetails ? (
          // Simple Banner View
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div className="flex items-start gap-4 flex-1">
              {config.showLogo && (
                <Shield className="h-8 w-8 text-blue-600 flex-shrink-0 mt-1" />
              )}
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">
                  {texts.bannerTitle || 'We value your privacy'}
                </h3>
                <p className="text-sm opacity-90 mb-3">
                  {texts.bannerMessage || 'We use cookies to enhance your browsing experience, serve personalized content, and analyze our traffic.'}
                </p>
                <div className="flex flex-wrap gap-2 text-xs">
                  <a href="/privacy" className="text-blue-600 hover:underline">
                    {texts.privacyPolicyLink || 'Privacy Policy'}
                  </a>
                  <a href="/cookies" className="text-blue-600 hover:underline">
                    {texts.cookiePolicyLink || 'Cookie Policy'}
                  </a>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-2 flex-shrink-0">
              <button
                onClick={() => setShowDetails(true)}
                className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-2"
              >
                <Sliders className="h-4 w-4" />
                {texts.customizeButton || 'Customize Settings'}
              </button>
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              >
                {texts.rejectAllButton || 'Reject All'}
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {texts.acceptAllButton || 'Accept All'}
              </button>
            </div>
            
            {config.showCloseButton && config.allowDismiss && (
              <button
                onClick={() => setIsVisible(false)}
                className="absolute top-4 right-4 p-1 hover:bg-gray-100 rounded"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        ) : (
          // Detailed Settings View
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-xl">
                Cookie Preferences
              </h3>
              <button
                onClick={() => setShowDetails(false)}
                className="p-2 hover:bg-gray-100 rounded"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <p className="text-sm opacity-90">
              Choose which cookies you want to accept. You can change these settings at any time.
            </p>
            
            <div className="space-y-4">
              {consentTypes.map((type) => (
                <div key={type.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className={`mt-1 ${getCategoryColor(type.category)}`}>
                        {getCategoryIcon(type.category)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium">{type.name}</h4>
                          {type.required && (
                            <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                              Required
                            </span>
                          )}
                        </div>
                        <p className="text-sm opacity-80 mb-2">
                          {type.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {type.purposes.map((purpose) => (
                            <span
                              key={purpose}
                              className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded"
                            >
                              {purpose.replace(/_/g, ' ')}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex-shrink-0 ml-4">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={consent[type.id] || false}
                          onChange={() => toggleConsentType(type.id)}
                          disabled={type.required}
                          className="sr-only peer"
                        />
                        <div className={`
                          relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 
                          peer-focus:ring-blue-300 rounded-full peer 
                          peer-checked:after:translate-x-full peer-checked:after:border-white 
                          after:content-[''] after:absolute after:top-[2px] after:left-[2px] 
                          after:bg-white after:border-gray-300 after:border after:rounded-full 
                          after:h-5 after:w-5 after:transition-all
                          ${consent[type.id] ? 'bg-blue-600' : 'bg-gray-200'}
                          ${type.required ? 'opacity-50 cursor-not-allowed' : ''}
                        `}></div>
                      </label>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
              <button
                onClick={handleSavePreferences}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium"
              >
                {texts.savePreferencesButton || 'Save Preferences'}
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                {texts.acceptAllButton || 'Accept All'}
              </button>
              <button
                onClick={handleRejectAll}
                className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                {texts.rejectAllButton || 'Reject All'}
              </button>
            </div>
            
            {texts.poweredBy && (
              <div className="text-xs text-center opacity-60 pt-2">
                {texts.poweredBy}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
