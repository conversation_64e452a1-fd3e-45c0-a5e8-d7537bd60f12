'use client';

import { useState, useEffect } from 'react';
import { 
  X, 
  Save, 
  Settings, 
  DollarSign, 
  Star, 
  PlaneTakeoff, 
  Building, 
  Ticket,
  Bell,
  User
} from 'lucide-react';
import { UserPreferences } from '@/types/travel-planner';

interface UserPreferencesModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onSave?: (preferences: UserPreferences) => void;
}

export default function UserPreferencesModal({ 
  isOpen, 
  onClose, 
  userId,
  onSave 
}: UserPreferencesModalProps) {
  const [preferences, setPreferences] = useState<UserPreferences>({
    userId,
    preferredAirlines: [],
    preferredAccommodationTypes: [],
    preferredActivities: [],
    budgetRange: { min: 500, max: 5000 },
    travelStyle: 'comfort',
    maxFlightLayovers: 1,
    minAccommodationRating: 3.5,
    dietaryRestrictions: [],
    accessibilityNeeds: [],
    savedDestinations: [],
    notificationPreferences: {
      email: true,
      sms: false,
      priceAlerts: true,
      bookingReminders: true
    }
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Load user preferences when modal opens
  useEffect(() => {
    if (isOpen && userId) {
      loadUserPreferences();
    }
  }, [isOpen, userId]);

  const loadUserPreferences = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/travel-planner/preferences?userId=${userId}`);
      if (response.ok) {
        const userPrefs = await response.json();
        setPreferences(userPrefs);
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/travel-planner/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          preferences
        })
      });

      if (response.ok) {
        const savedPreferences = await response.json();
        onSave?.(savedPreferences);
        onClose();
      } else {
        throw new Error('Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      alert('Failed to save preferences. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const updatePreference = (key: keyof UserPreferences, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const toggleArrayItem = (array: string[], item: string) => {
    return array.includes(item) 
      ? array.filter(i => i !== item)
      : [...array, item];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center">
            <Settings className="mr-2 h-5 w-5" />
            Travel Preferences
          </h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        <div className="overflow-auto flex-1 p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="space-y-8">
              {/* Budget & Travel Style */}
              <div>
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <DollarSign className="mr-2 h-5 w-5 text-green-600" />
                  Budget & Style
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Budget Range ($)
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        placeholder="Min"
                        value={preferences.budgetRange.min}
                        onChange={(e) => updatePreference('budgetRange', {
                          ...preferences.budgetRange,
                          min: parseInt(e.target.value) || 0
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                      <input
                        type="number"
                        placeholder="Max"
                        value={preferences.budgetRange.max}
                        onChange={(e) => updatePreference('budgetRange', {
                          ...preferences.budgetRange,
                          max: parseInt(e.target.value) || 0
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Travel Style
                    </label>
                    <select
                      value={preferences.travelStyle}
                      onChange={(e) => updatePreference('travelStyle', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="budget">Budget</option>
                      <option value="comfort">Comfort</option>
                      <option value="luxury">Luxury</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Flight Preferences */}
              <div>
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <PlaneTakeoff className="mr-2 h-5 w-5 text-blue-600" />
                  Flight Preferences
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Preferred Airlines
                    </label>
                    <div className="space-y-2">
                      {['Delta Airlines', 'United Airlines', 'American Airlines', 'British Airways', 'Emirates'].map(airline => (
                        <label key={airline} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={preferences.preferredAirlines.includes(airline)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                updatePreference('preferredAirlines', [...preferences.preferredAirlines, airline]);
                              } else {
                                updatePreference('preferredAirlines', preferences.preferredAirlines.filter(a => a !== airline));
                              }
                            }}
                            className="mr-2"
                          />
                          <span className="text-sm">{airline}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Max Flight Layovers
                    </label>
                    <select
                      value={preferences.maxFlightLayovers}
                      onChange={(e) => updatePreference('maxFlightLayovers', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value={0}>Direct flights only</option>
                      <option value={1}>Up to 1 layover</option>
                      <option value={2}>Up to 2 layovers</option>
                      <option value={3}>Up to 3 layovers</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Accommodation Preferences */}
              <div>
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <Building className="mr-2 h-5 w-5 text-green-600" />
                  Accommodation Preferences
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Preferred Types
                    </label>
                    <div className="space-y-2">
                      {['hotel', 'apartment', 'hostel', 'resort', 'villa'].map(type => (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={preferences.preferredAccommodationTypes.includes(type)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                updatePreference('preferredAccommodationTypes', [...preferences.preferredAccommodationTypes, type]);
                              } else {
                                updatePreference('preferredAccommodationTypes', preferences.preferredAccommodationTypes.filter(t => t !== type));
                              }
                            }}
                            className="mr-2"
                          />
                          <span className="text-sm capitalize">{type}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Rating
                    </label>
                    <select
                      value={preferences.minAccommodationRating}
                      onChange={(e) => updatePreference('minAccommodationRating', parseFloat(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value={3.0}>3+ Stars</option>
                      <option value={3.5}>3.5+ Stars</option>
                      <option value={4.0}>4+ Stars</option>
                      <option value={4.5}>4.5+ Stars</option>
                      <option value={5.0}>5 Stars Only</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Notification Preferences */}
              <div>
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <Bell className="mr-2 h-5 w-5 text-purple-600" />
                  Notifications
                </h3>
                <div className="space-y-3">
                  {Object.entries(preferences.notificationPreferences).map(([key, value]) => (
                    <label key={key} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => updatePreference('notificationPreferences', {
                          ...preferences.notificationPreferences,
                          [key]: e.target.checked
                        })}
                        className="mr-3"
                      />
                      <span className="text-sm capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div className="flex justify-end space-x-3 p-6 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSaving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Preferences
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
