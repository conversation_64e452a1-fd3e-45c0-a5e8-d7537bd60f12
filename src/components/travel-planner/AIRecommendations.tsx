'use client';

import { useState } from 'react';
import { 
  <PERSON>rkles, 
  MapPin, 
  Calendar, 
  DollarSign, 
  Star, 
  ChevronDown, 
  ChevronUp,
  Lightbulb,
  AlertTriangle,
  Target,
  TrendingUp
} from 'lucide-react';

interface TripRecommendation {
  destination: string;
  confidence: number;
  reasons: string[];
  suggestedDuration: number;
  estimatedBudget: {
    min: number;
    max: number;
  };
  bestTimeToVisit: string;
  highlights: string[];
  activities: string[];
}

interface AIInsights {
  insights: string[];
  recommendations: string[];
  warnings: string[];
  alternatives: string[];
}

interface AIRecommendationsProps {
  recommendations: TripRecommendation[];
  insights?: AIInsights;
  onSelectDestination?: (destination: string, duration: number, budget: number) => void;
}

export default function AIRecommendations({ 
  recommendations, 
  insights,
  onSelectDestination 
}: AIRecommendationsProps) {
  const [expandedRecommendation, setExpandedRecommendation] = useState<string | null>(null);
  const [showInsights, setShowInsights] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600 bg-green-100';
    if (confidence >= 0.8) return 'text-blue-600 bg-blue-100';
    if (confidence >= 0.7) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  if (recommendations.length === 0 && !insights) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <Sparkles className="mr-2 h-5 w-5 text-purple-600" />
        AI-Powered Recommendations
      </h2>

      {/* Destination Recommendations */}
      {recommendations.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">Recommended Destinations</h3>
          <div className="space-y-4">
            {recommendations.map((rec, index) => (
              <div 
                key={rec.destination}
                className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <MapPin className="h-5 w-5 text-blue-600 mr-2" />
                      <h4 className="text-lg font-medium">{rec.destination}</h4>
                      <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(rec.confidence)}`}>
                        {Math.round(rec.confidence * 100)}% match
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {rec.suggestedDuration} days
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        {formatCurrency(rec.estimatedBudget.min)} - {formatCurrency(rec.estimatedBudget.max)}
                      </div>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 mr-1" />
                        {rec.bestTimeToVisit}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-3">
                      {rec.activities.map(activity => (
                        <span 
                          key={activity}
                          className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                        >
                          {activity}
                        </span>
                      ))}
                    </div>

                    <p className="text-sm text-gray-600 mb-3">
                      {rec.reasons.join(' • ')}
                    </p>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => setExpandedRecommendation(
                        expandedRecommendation === rec.destination ? null : rec.destination
                      )}
                      className="p-2 text-gray-400 hover:text-gray-600"
                    >
                      {expandedRecommendation === rec.destination ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </button>
                    
                    <button
                      onClick={() => onSelectDestination?.(
                        rec.destination, 
                        rec.suggestedDuration, 
                        Math.round((rec.estimatedBudget.min + rec.estimatedBudget.max) / 2)
                      )}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                    >
                      Select
                    </button>
                  </div>
                </div>

                {expandedRecommendation === rec.destination && (
                  <div className="border-t border-gray-100 pt-3 mt-3">
                    <h5 className="font-medium mb-2">Highlights</h5>
                    <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                      {rec.highlights.map((highlight, idx) => (
                        <li key={idx}>{highlight}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* AI Insights */}
      {insights && (
        <div>
          <button
            onClick={() => setShowInsights(!showInsights)}
            className="flex items-center justify-between w-full text-left mb-3"
          >
            <h3 className="text-lg font-medium flex items-center">
              <Lightbulb className="mr-2 h-5 w-5 text-yellow-600" />
              AI Travel Insights
            </h3>
            {showInsights ? (
              <ChevronUp className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            )}
          </button>

          {showInsights && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Insights */}
              {insights.insights.length > 0 && (
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    Smart Insights
                  </h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    {insights.insights.map((insight, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="w-1 h-1 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                        {insight}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Recommendations */}
              {insights.recommendations.length > 0 && (
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="font-medium text-green-900 mb-2 flex items-center">
                    <Target className="h-4 w-4 mr-1" />
                    Recommendations
                  </h4>
                  <ul className="text-sm text-green-800 space-y-1">
                    {insights.recommendations.map((rec, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="w-1 h-1 bg-green-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Warnings */}
              {insights.warnings.length > 0 && (
                <div className="bg-yellow-50 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-900 mb-2 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Important Notes
                  </h4>
                  <ul className="text-sm text-yellow-800 space-y-1">
                    {insights.warnings.map((warning, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="w-1 h-1 bg-yellow-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                        {warning}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Alternatives */}
              {insights.alternatives.length > 0 && (
                <div className="bg-purple-50 rounded-lg p-4">
                  <h4 className="font-medium text-purple-900 mb-2 flex items-center">
                    <Sparkles className="h-4 w-4 mr-1" />
                    Alternative Ideas
                  </h4>
                  <ul className="text-sm text-purple-800 space-y-1">
                    {insights.alternatives.map((alt, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="w-1 h-1 bg-purple-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                        {alt}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
