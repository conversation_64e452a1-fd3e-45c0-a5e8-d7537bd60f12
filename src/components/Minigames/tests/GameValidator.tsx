'use client'

import React, { useState, useEffect, ErrorInfo } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON>ircle, 
  XCircle, 
  Alert<PERSON><PERSON>gle, 
  Play,
  Eye,
  Bug,
  RefreshCw
} from 'lucide-react';
import { games } from '../gameRegistry';
import { Game } from '../MinigameHub';

interface GameTestResult {
  gameId: string;
  gameName: string;
  status: 'untested' | 'testing' | 'passed' | 'failed';
  error?: string;
  renderTime?: number;
  canRender: boolean;
  hasError: boolean;
}

// Error boundary component for testing
class GameErrorBoundary extends React.Component<
  { children: React.ReactNode; onError: (error: Error, errorInfo: ErrorInfo) => void },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.props.onError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div className="p-4 bg-red-100 text-red-700 rounded">Component failed to render</div>;
    }

    return this.props.children;
  }
}

export const GameValidator: React.FC = () => {
  const [testResults, setTestResults] = useState<GameTestResult[]>([]);
  const [currentGame, setCurrentGame] = useState<Game | null>(null);
  const [isTestingAll, setIsTestingAll] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Initialize test results
  useEffect(() => {
    const initialResults: GameTestResult[] = games.map(game => ({
      gameId: game.id,
      gameName: game.name,
      status: 'untested',
      canRender: false,
      hasError: false
    }));
    setTestResults(initialResults);
  }, []);

  // Test a single game
  const testGame = async (game: Game): Promise<GameTestResult> => {
    const startTime = performance.now();
    
    return new Promise((resolve) => {
      let hasError = false;
      let errorMessage = '';

      const handleError = (error: Error, errorInfo?: ErrorInfo) => {
        hasError = true;
        errorMessage = error.message;
      };

      // Update status to testing
      setTestResults(prev => prev.map(result => 
        result.gameId === game.id 
          ? { ...result, status: 'testing' }
          : result
      ));

      // Try to render the component
      try {
        const GameComponent = game.component;
        
        if (!GameComponent) {
          resolve({
            gameId: game.id,
            gameName: game.name,
            status: 'failed',
            error: 'Component is undefined',
            canRender: false,
            hasError: true,
            renderTime: performance.now() - startTime
          });
          return;
        }

        // Create a temporary container to test rendering
        const testContainer = document.createElement('div');
        testContainer.style.position = 'absolute';
        testContainer.style.top = '-9999px';
        testContainer.style.left = '-9999px';
        testContainer.style.width = '800px';
        testContainer.style.height = '600px';
        document.body.appendChild(testContainer);

        // Test with React 18 createRoot
        import('react-dom/client').then(({ createRoot }) => {
          const root = createRoot(testContainer);
          
          try {
            root.render(
              React.createElement(GameErrorBoundary, {
                onError: handleError,
                children: React.createElement(GameComponent, {})
              })
            );

            // Give it a moment to render and catch any async errors
            setTimeout(() => {
              const renderTime = performance.now() - startTime;
              
              // Clean up
              root.unmount();
              document.body.removeChild(testContainer);

              resolve({
                gameId: game.id,
                gameName: game.name,
                status: hasError ? 'failed' : 'passed',
                error: hasError ? errorMessage : undefined,
                canRender: !hasError,
                hasError,
                renderTime
              });
            }, 500); // Wait 500ms for component to fully render

          } catch (renderError) {
            // Clean up on error
            try {
              root.unmount();
              document.body.removeChild(testContainer);
            } catch (cleanupError) {
              // Ignore cleanup errors
            }

            resolve({
              gameId: game.id,
              gameName: game.name,
              status: 'failed',
              error: renderError instanceof Error ? renderError.message : 'Render error',
              canRender: false,
              hasError: true,
              renderTime: performance.now() - startTime
            });
          }
        }).catch((importError) => {
          // Clean up
          document.body.removeChild(testContainer);
          
          resolve({
            gameId: game.id,
            gameName: game.name,
            status: 'failed',
            error: `Import error: ${importError.message}`,
            canRender: false,
            hasError: true,
            renderTime: performance.now() - startTime
          });
        });

      } catch (error) {
        resolve({
          gameId: game.id,
          gameName: game.name,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          canRender: false,
          hasError: true,
          renderTime: performance.now() - startTime
        });
      }
    });
  };

  // Test all games
  const testAllGames = async () => {
    setIsTestingAll(true);
    
    for (const game of games) {
      const result = await testGame(game);
      
      setTestResults(prev => prev.map(prevResult => 
        prevResult.gameId === result.gameId ? result : prevResult
      ));

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    setIsTestingAll(false);
  };

  // Test single game
  const testSingleGame = async (game: Game) => {
    const result = await testGame(game);
    setTestResults(prev => prev.map(prevResult => 
      prevResult.gameId === result.gameId ? result : prevResult
    ));
  };

  // Get statistics
  const getStats = () => {
    const total = testResults.length;
    const passed = testResults.filter(r => r.status === 'passed').length;
    const failed = testResults.filter(r => r.status === 'failed').length;
    const untested = testResults.filter(r => r.status === 'untested').length;
    const testing = testResults.filter(r => r.status === 'testing').length;
    
    return { total, passed, failed, untested, testing };
  };

  const stats = getStats();

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <Bug className="w-8 h-8" />
          Game Validator
        </h1>
        <p className="text-gray-600">
          Validates that all minigames can render without errors
        </p>
      </div>

      {/* Controls */}
      <Card className="p-4 mb-6">
        <div className="flex gap-4 items-center">
          <Button 
            onClick={testAllGames} 
            disabled={isTestingAll}
            className="flex items-center gap-2"
          >
            <Play className="w-4 h-4" />
            Test All Games
          </Button>
          
          <Button 
            onClick={() => setShowPreview(!showPreview)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Eye className="w-4 h-4" />
            {showPreview ? 'Hide' : 'Show'} Preview
          </Button>
        </div>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
          <div className="text-sm text-gray-600">Total</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{stats.passed}</div>
          <div className="text-sm text-gray-600">Passed</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          <div className="text-sm text-gray-600">Failed</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">{stats.testing}</div>
          <div className="text-sm text-gray-600">Testing</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-gray-600">{stats.untested}</div>
          <div className="text-sm text-gray-600">Untested</div>
        </Card>
      </div>

      {/* Test Results */}
      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Test Results</h2>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {testResults.map((result) => {
            const game = games.find(g => g.id === result.gameId);
            return (
              <div 
                key={result.gameId}
                className={`p-3 rounded border-l-4 ${
                  result.status === 'passed' ? 'border-green-500 bg-green-50' :
                  result.status === 'failed' ? 'border-red-500 bg-red-50' :
                  result.status === 'testing' ? 'border-blue-500 bg-blue-50' :
                  'border-gray-300 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {result.status === 'passed' && <CheckCircle className="w-5 h-5 text-green-600" />}
                    {result.status === 'failed' && <XCircle className="w-5 h-5 text-red-600" />}
                    {result.status === 'testing' && <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />}
                    {result.status === 'untested' && <div className="w-5 h-5 rounded-full border-2 border-gray-300" />}
                    
                    <div>
                      <div className="font-medium">{result.gameName}</div>
                      <div className="text-sm text-gray-600">{result.gameId}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant={
                      result.status === 'passed' ? 'default' :
                      result.status === 'failed' ? 'destructive' :
                      result.status === 'testing' ? 'secondary' :
                      'outline'
                    }>
                      {result.status}
                    </Badge>
                    
                    {game && result.status !== 'testing' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => testSingleGame(game)}
                      >
                        Test
                      </Button>
                    )}
                    
                    {game && showPreview && result.status === 'passed' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setCurrentGame(game)}
                      >
                        Preview
                      </Button>
                    )}
                  </div>
                </div>
                
                {result.error && (
                  <div className="mt-2 p-2 bg-red-100 rounded text-sm text-red-700">
                    <strong>Error:</strong> {result.error}
                  </div>
                )}
                
                {result.renderTime && (
                  <div className="mt-2 text-xs text-gray-500">
                    Render time: {result.renderTime.toFixed(2)}ms
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </Card>

      {/* Game Preview Modal */}
      {currentGame && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="p-6 max-w-4xl max-h-[80vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">{currentGame.name}</h3>
              <Button 
                variant="outline" 
                onClick={() => setCurrentGame(null)}
              >
                Close
              </Button>
            </div>
            <div className="border rounded p-4 bg-white">
              <GameErrorBoundary onError={(error) => console.error('Preview error:', error)}>
                <currentGame.component />
              </GameErrorBoundary>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default GameValidator;
