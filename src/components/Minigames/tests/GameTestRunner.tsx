'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Play,
  Square,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Bug,
  RefreshCw,
  Download,
  Eye,
  EyeOff
} from 'lucide-react';
import { games } from '../gameRegistry';
import { Game } from '../MinigameHub';

interface TestResult {
  gameId: string;
  gameName: string;
  status: 'pending' | 'running' | 'passed' | 'failed' | 'error';
  error?: string;
  renderTime?: number;
  componentLoaded?: boolean;
  hasRequiredProps?: boolean;
  timestamp?: number;
}

export default function GameTestRunner() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentTestIndex, setCurrentTestIndex] = useState(-1);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [testMode, setTestMode] = useState<'quick' | 'detailed'>('quick');

  // Initialize test results
  useEffect(() => {
    const initialResults: TestResult[] = games.map(game => ({
      gameId: game.id,
      gameName: game.name,
      status: 'pending',
      timestamp: Date.now()
    }));
    setTestResults(initialResults);
  }, []);

  // Test a single game
  const testSingleGame = async (game: Game): Promise<TestResult> => {
    const startTime = performance.now();
    
    try {
      // Test 1: Check if component exists and is a valid React component
      const GameComponent = game.component;
      
      if (!GameComponent) {
        return {
          gameId: game.id,
          gameName: game.name,
          status: 'failed',
          error: 'Component is undefined or null',
          componentLoaded: false,
          timestamp: Date.now()
        };
      }

      // Test 2: Check if it's a function (React component)
      if (typeof GameComponent !== 'function') {
        return {
          gameId: game.id,
          gameName: game.name,
          status: 'failed',
          error: 'Component is not a function',
          componentLoaded: false,
          timestamp: Date.now()
        };
      }

      // Test 3: Try to create React element (basic render test)
      try {
        const React = require('react');
        const element = React.createElement(GameComponent, {});
        
        if (!element) {
          throw new Error('Failed to create React element');
        }
      } catch (renderError) {
        return {
          gameId: game.id,
          gameName: game.name,
          status: 'failed',
          error: `Render error: ${renderError instanceof Error ? renderError.message : 'Unknown render error'}`,
          componentLoaded: true,
          timestamp: Date.now()
        };
      }

      const renderTime = performance.now() - startTime;

      // Test 4: Check for required game properties
      const hasRequiredProps = !!(game.name && game.description && game.category);

      return {
        gameId: game.id,
        gameName: game.name,
        status: 'passed',
        renderTime,
        componentLoaded: true,
        hasRequiredProps,
        timestamp: Date.now()
      };

    } catch (error) {
      return {
        gameId: game.id,
        gameName: game.name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        componentLoaded: false,
        timestamp: Date.now()
      };
    }
  };

  // Run all tests
  const runAllTests = async () => {
    setIsRunning(true);
    setCurrentTestIndex(0);
    setProgress(0);

    const results: TestResult[] = [];

    for (let i = 0; i < games.length; i++) {
      setCurrentTestIndex(i);
      setProgress((i / games.length) * 100);

      // Update current test status
      setTestResults(prev => prev.map(result => 
        result.gameId === games[i].id 
          ? { ...result, status: 'running' }
          : result
      ));

      const result = await testSingleGame(games[i]);
      results.push(result);

      // Update test result
      setTestResults(prev => prev.map(prevResult => 
        prevResult.gameId === result.gameId ? result : prevResult
      ));

      // Small delay between tests to prevent overwhelming
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    setProgress(100);
    setCurrentTestIndex(-1);
    setIsRunning(false);
  };

  // Test a specific game
  const testSpecificGame = async (game: Game) => {
    setTestResults(prev => prev.map(result => 
      result.gameId === game.id 
        ? { ...result, status: 'running' }
        : result
    ));

    const result = await testSingleGame(game);
    
    setTestResults(prev => prev.map(prevResult => 
      prevResult.gameId === result.gameId ? result : prevResult
    ));
  };

  // Stop tests
  const stopTests = () => {
    setIsRunning(false);
    setCurrentTestIndex(-1);
  };

  // Reset tests
  const resetTests = () => {
    const initialResults: TestResult[] = games.map(game => ({
      gameId: game.id,
      gameName: game.name,
      status: 'pending',
      timestamp: Date.now()
    }));
    setTestResults(initialResults);
    setProgress(0);
    setCurrentTestIndex(-1);
  };

  // Export results
  const exportResults = () => {
    const dataStr = JSON.stringify(testResults, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `game-test-results-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  // Get statistics
  const getStats = () => {
    const total = testResults.length;
    const passed = testResults.filter(r => r.status === 'passed').length;
    const failed = testResults.filter(r => r.status === 'failed').length;
    const errors = testResults.filter(r => r.status === 'error').length;
    const pending = testResults.filter(r => r.status === 'pending').length;
    
    return { total, passed, failed, errors, pending };
  };

  const stats = getStats();

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <Bug className="w-8 h-8" />
          Game Test Runner
        </h1>
        <p className="text-gray-600">
          Quick testing tool to verify all minigames load and render correctly
        </p>
      </div>

      {/* Controls */}
      <Card className="p-4 mb-6">
        <div className="flex flex-wrap gap-4 items-center justify-between">
          <div className="flex gap-2">
            <Button 
              onClick={runAllTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              Run All Tests
            </Button>
            <Button 
              onClick={stopTests} 
              disabled={!isRunning}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Square className="w-4 h-4" />
              Stop
            </Button>
            <Button 
              onClick={resetTests} 
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Reset
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={() => setShowDetails(!showDetails)}
              variant="outline"
              className="flex items-center gap-2"
            >
              {showDetails ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showDetails ? 'Hide' : 'Show'} Details
            </Button>
            <Button 
              onClick={exportResults} 
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export
            </Button>
          </div>
        </div>
      </Card>

      {/* Progress */}
      {isRunning && (
        <Card className="p-4 mb-6">
          <div className="mb-2 flex items-center justify-between">
            <span className="text-sm font-medium">
              Testing: {currentTestIndex >= 0 ? games[currentTestIndex]?.name : 'Preparing...'}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}%
            </span>
          </div>
          <Progress value={progress} className="w-full" />
        </Card>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
          <div className="text-sm text-gray-600">Total</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{stats.passed}</div>
          <div className="text-sm text-gray-600">Passed</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          <div className="text-sm text-gray-600">Failed</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">{stats.errors}</div>
          <div className="text-sm text-gray-600">Errors</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
          <div className="text-sm text-gray-600">Pending</div>
        </Card>
      </div>

      {/* Test Results */}
      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Test Results</h2>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {testResults.map((result) => (
            <div
              key={result.gameId}
              className={`p-3 rounded border-l-4 ${
                result.status === 'passed' ? 'border-green-500 bg-green-50' :
                result.status === 'failed' ? 'border-red-500 bg-red-50' :
                result.status === 'error' ? 'border-orange-500 bg-orange-50' :
                result.status === 'running' ? 'border-blue-500 bg-blue-50' :
                'border-gray-300 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {result.status === 'passed' && <CheckCircle className="w-5 h-5 text-green-600" />}
                  {result.status === 'failed' && <XCircle className="w-5 h-5 text-red-600" />}
                  {result.status === 'error' && <AlertTriangle className="w-5 h-5 text-orange-600" />}
                  {result.status === 'running' && <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />}
                  {result.status === 'pending' && <div className="w-5 h-5 rounded-full border-2 border-gray-300" />}

                  <div>
                    <div className="font-medium">{result.gameName}</div>
                    <div className="text-sm text-gray-600">{result.gameId}</div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant={
                    result.status === 'passed' ? 'default' :
                    result.status === 'failed' ? 'destructive' :
                    result.status === 'error' ? 'secondary' :
                    'outline'
                  }>
                    {result.status}
                  </Badge>

                  {!isRunning && result.status !== 'running' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        const game = games.find(g => g.id === result.gameId);
                        if (game) testSpecificGame(game);
                      }}
                    >
                      Retest
                    </Button>
                  )}
                </div>
              </div>

              {showDetails && (
                <div className="mt-3 space-y-2">
                  {result.renderTime && (
                    <div className="text-sm text-gray-600">
                      <strong>Render Time:</strong> {result.renderTime.toFixed(2)}ms
                    </div>
                  )}

                  {result.componentLoaded !== undefined && (
                    <div className="text-sm">
                      <strong>Component Loaded:</strong>
                      <span className={result.componentLoaded ? 'text-green-600 ml-1' : 'text-red-600 ml-1'}>
                        {result.componentLoaded ? '✓' : '✗'}
                      </span>
                    </div>
                  )}

                  {result.hasRequiredProps !== undefined && (
                    <div className="text-sm">
                      <strong>Has Required Props:</strong>
                      <span className={result.hasRequiredProps ? 'text-green-600 ml-1' : 'text-red-600 ml-1'}>
                        {result.hasRequiredProps ? '✓' : '✗'}
                      </span>
                    </div>
                  )}

                  {result.error && (
                    <div className="p-2 bg-red-100 rounded text-sm text-red-700">
                      <strong>Error:</strong> {result.error}
                    </div>
                  )}

                  {result.timestamp && (
                    <div className="text-xs text-gray-500">
                      Last tested: {new Date(result.timestamp).toLocaleString()}
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Game Preview Modal */}
      {selectedGame && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="p-6 max-w-4xl max-h-[80vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">{selectedGame.name}</h3>
              <Button
                variant="outline"
                onClick={() => setSelectedGame(null)}
              >
                Close
              </Button>
            </div>
            <div className="border rounded p-4">
              {/* This would need appropriate props depending on the game */}
              <selectedGame.component />
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
