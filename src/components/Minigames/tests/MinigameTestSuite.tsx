'use client'

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Pause, 
  Square, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock,
  Bug,
  TestTube,
  RefreshCw,
  Download,
  FileText,
  Zap
} from 'lucide-react';
import { games } from '../gameRegistry';
import { Game } from '../MinigameHub';

interface TestResult {
  gameId: string;
  gameName: string;
  status: 'pending' | 'running' | 'passed' | 'failed' | 'error';
  error?: string;
  renderTime?: number;
  interactionTests?: {
    clickable: boolean;
    keyboardInput: boolean;
    gameLogic: boolean;
  };
  performance?: {
    memoryUsage: number;
    renderTime: number;
    fps: number;
  };
  accessibility?: {
    hasAriaLabels: boolean;
    keyboardNavigable: boolean;
    colorContrast: boolean;
  };
}

interface TestSuiteProps {
  onTestComplete?: (results: TestResult[]) => void;
}

export const MinigameTestSuite: React.FC<TestSuiteProps> = ({ onTestComplete }) => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentTestIndex, setCurrentTestIndex] = useState(-1);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [testMode, setTestMode] = useState<'basic' | 'comprehensive' | 'performance'>('basic');
  const testContainerRef = useRef<HTMLDivElement>(null);

  // Initialize test results
  useEffect(() => {
    const initialResults: TestResult[] = games.map(game => ({
      gameId: game.id,
      gameName: game.name,
      status: 'pending'
    }));
    setTestResults(initialResults);
  }, []);

  // Test a single game component
  const testGame = async (game: Game): Promise<TestResult> => {
    const startTime = performance.now();
    
    try {
      // Basic render test
      const GameComponent = game.component;
      
      // Create a test container
      const testDiv = document.createElement('div');
      testDiv.style.position = 'absolute';
      testDiv.style.top = '-9999px';
      testDiv.style.left = '-9999px';
      testDiv.style.width = '800px';
      testDiv.style.height = '600px';
      document.body.appendChild(testDiv);

      // Test component rendering
      let renderError: string | undefined;
      let renderTime = 0;
      
      try {
        const React = require('react');
        const ReactDOM = require('react-dom/client');
        
        const root = ReactDOM.createRoot(testDiv);
        
        await new Promise((resolve, reject) => {
          try {
            root.render(React.createElement(GameComponent, {}));
            renderTime = performance.now() - startTime;
            setTimeout(resolve, 100); // Give component time to render
          } catch (error) {
            reject(error);
          }
        });

        // Basic interaction tests
        const interactionTests = await testInteractions(testDiv);
        
        // Performance tests (if enabled)
        const performance = testMode === 'performance' ? await testPerformance(testDiv) : undefined;
        
        // Accessibility tests (if comprehensive mode)
        const accessibility = testMode === 'comprehensive' ? await testAccessibility(testDiv) : undefined;

        // Cleanup
        root.unmount();
        document.body.removeChild(testDiv);

        return {
          gameId: game.id,
          gameName: game.name,
          status: 'passed',
          renderTime,
          interactionTests,
          performance,
          accessibility
        };

      } catch (error) {
        renderError = error instanceof Error ? error.message : 'Unknown render error';
        
        // Cleanup on error
        if (testDiv.parentNode) {
          document.body.removeChild(testDiv);
        }
      }

      return {
        gameId: game.id,
        gameName: game.name,
        status: 'failed',
        error: renderError,
        renderTime
      };

    } catch (error) {
      return {
        gameId: game.id,
        gameName: game.name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  // Test basic interactions
  const testInteractions = async (container: HTMLElement) => {
    const clickableElements = container.querySelectorAll('button, [role="button"], [onclick]');
    const inputElements = container.querySelectorAll('input, textarea, [contenteditable]');
    
    return {
      clickable: clickableElements.length > 0,
      keyboardInput: inputElements.length > 0,
      gameLogic: container.querySelector('[data-testid], [data-game-state]') !== null
    };
  };

  // Test performance metrics
  const testPerformance = async (container: HTMLElement) => {
    const memoryBefore = (performance as any).memory?.usedJSHeapSize || 0;
    
    // Simulate some interactions
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const memoryAfter = (performance as any).memory?.usedJSHeapSize || 0;
    
    return {
      memoryUsage: memoryAfter - memoryBefore,
      renderTime: 0, // Will be set from main test
      fps: 60 // Placeholder - would need more complex measurement
    };
  };

  // Test accessibility features
  const testAccessibility = async (container: HTMLElement) => {
    const elementsWithAria = container.querySelectorAll('[aria-label], [aria-labelledby], [role]');
    const focusableElements = container.querySelectorAll('button, input, [tabindex]');
    
    return {
      hasAriaLabels: elementsWithAria.length > 0,
      keyboardNavigable: focusableElements.length > 0,
      colorContrast: true // Placeholder - would need color analysis
    };
  };

  // Run all tests
  const runTests = async () => {
    setIsRunning(true);
    setCurrentTestIndex(0);
    setProgress(0);

    const filteredGames = selectedCategory === 'all' 
      ? games 
      : games.filter(game => game.category === selectedCategory);

    const results: TestResult[] = [];

    for (let i = 0; i < filteredGames.length; i++) {
      setCurrentTestIndex(i);
      setProgress((i / filteredGames.length) * 100);

      // Update current test status
      setTestResults(prev => prev.map(result => 
        result.gameId === filteredGames[i].id 
          ? { ...result, status: 'running' }
          : result
      ));

      const result = await testGame(filteredGames[i]);
      results.push(result);

      // Update test result
      setTestResults(prev => prev.map(prevResult => 
        prevResult.gameId === result.gameId ? result : prevResult
      ));

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    setProgress(100);
    setCurrentTestIndex(-1);
    setIsRunning(false);

    if (onTestComplete) {
      onTestComplete(results);
    }
  };

  // Stop tests
  const stopTests = () => {
    setIsRunning(false);
    setCurrentTestIndex(-1);
  };

  // Reset tests
  const resetTests = () => {
    const initialResults: TestResult[] = games.map(game => ({
      gameId: game.id,
      gameName: game.name,
      status: 'pending'
    }));
    setTestResults(initialResults);
    setProgress(0);
    setCurrentTestIndex(-1);
  };

  // Export test results
  const exportResults = () => {
    const dataStr = JSON.stringify(testResults, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `minigame-test-results-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  // Get test statistics
  const getStats = () => {
    const total = testResults.length;
    const passed = testResults.filter(r => r.status === 'passed').length;
    const failed = testResults.filter(r => r.status === 'failed').length;
    const errors = testResults.filter(r => r.status === 'error').length;
    const pending = testResults.filter(r => r.status === 'pending').length;
    
    return { total, passed, failed, errors, pending };
  };

  const stats = getStats();
  const categories = [...new Set(games.map(game => game.category))];

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <TestTube className="w-8 h-8" />
          Minigame Test Suite
        </h1>
        <p className="text-gray-600">
          Comprehensive testing framework for all minigames in the system
        </p>
      </div>

      {/* Test Controls */}
      <Card className="p-4 mb-6">
        <div className="flex flex-wrap gap-4 items-center justify-between">
          <div className="flex gap-2">
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              Run Tests
            </Button>
            <Button 
              onClick={stopTests} 
              disabled={!isRunning}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Square className="w-4 h-4" />
              Stop
            </Button>
            <Button 
              onClick={resetTests} 
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Reset
            </Button>
          </div>
          
          <div className="flex gap-2">
            <select 
              value={selectedCategory} 
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded"
            >
              <option value="all">All Categories</option>
              {categories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
            
            <select 
              value={testMode} 
              onChange={(e) => setTestMode(e.target.value as any)}
              className="px-3 py-2 border rounded"
            >
              <option value="basic">Basic Tests</option>
              <option value="comprehensive">Comprehensive</option>
              <option value="performance">Performance</option>
            </select>
            
            <Button 
              onClick={exportResults} 
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export
            </Button>
          </div>
        </div>
      </Card>

      {/* Progress and Stats */}
      {isRunning && (
        <Card className="p-4 mb-6">
          <div className="mb-2 flex items-center justify-between">
            <span className="text-sm font-medium">
              Testing: {currentTestIndex >= 0 ? games[currentTestIndex]?.name : 'Preparing...'}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}%
            </span>
          </div>
          <Progress value={progress} className="w-full" />
        </Card>
      )}

      {/* Test Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
          <div className="text-sm text-gray-600">Total</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{stats.passed}</div>
          <div className="text-sm text-gray-600">Passed</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          <div className="text-sm text-gray-600">Failed</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">{stats.errors}</div>
          <div className="text-sm text-gray-600">Errors</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
          <div className="text-sm text-gray-600">Pending</div>
        </Card>
      </div>

      {/* Test Results */}
      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Test Results</h2>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {testResults.map((result) => (
            <div
              key={result.gameId}
              className={`p-3 rounded border-l-4 ${
                result.status === 'passed' ? 'border-green-500 bg-green-50' :
                result.status === 'failed' ? 'border-red-500 bg-red-50' :
                result.status === 'error' ? 'border-orange-500 bg-orange-50' :
                result.status === 'running' ? 'border-blue-500 bg-blue-50' :
                'border-gray-300 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {result.status === 'passed' && <CheckCircle className="w-5 h-5 text-green-600" />}
                  {result.status === 'failed' && <XCircle className="w-5 h-5 text-red-600" />}
                  {result.status === 'error' && <AlertTriangle className="w-5 h-5 text-orange-600" />}
                  {result.status === 'running' && <Clock className="w-5 h-5 text-blue-600 animate-spin" />}
                  {result.status === 'pending' && <Clock className="w-5 h-5 text-gray-400" />}

                  <div>
                    <div className="font-medium">{result.gameName}</div>
                    <div className="text-sm text-gray-600">{result.gameId}</div>
                  </div>
                </div>

                <div className="text-right">
                  <Badge variant={
                    result.status === 'passed' ? 'default' :
                    result.status === 'failed' ? 'destructive' :
                    result.status === 'error' ? 'secondary' :
                    'outline'
                  }>
                    {result.status}
                  </Badge>
                  {result.renderTime && (
                    <div className="text-xs text-gray-500 mt-1">
                      {result.renderTime.toFixed(2)}ms
                    </div>
                  )}
                </div>
              </div>

              {result.error && (
                <div className="mt-2 p-2 bg-red-100 rounded text-sm text-red-700">
                  <strong>Error:</strong> {result.error}
                </div>
              )}

              {result.interactionTests && (
                <div className="mt-2 flex gap-4 text-xs">
                  <span className={result.interactionTests.clickable ? 'text-green-600' : 'text-red-600'}>
                    Clickable: {result.interactionTests.clickable ? '✓' : '✗'}
                  </span>
                  <span className={result.interactionTests.keyboardInput ? 'text-green-600' : 'text-red-600'}>
                    Keyboard: {result.interactionTests.keyboardInput ? '✓' : '✗'}
                  </span>
                  <span className={result.interactionTests.gameLogic ? 'text-green-600' : 'text-red-600'}>
                    Game Logic: {result.interactionTests.gameLogic ? '✓' : '✗'}
                  </span>
                </div>
              )}

              {result.performance && (
                <div className="mt-2 text-xs text-gray-600">
                  Memory: {(result.performance.memoryUsage / 1024 / 1024).toFixed(2)}MB |
                  FPS: {result.performance.fps}
                </div>
              )}

              {result.accessibility && (
                <div className="mt-2 flex gap-4 text-xs">
                  <span className={result.accessibility.hasAriaLabels ? 'text-green-600' : 'text-red-600'}>
                    ARIA: {result.accessibility.hasAriaLabels ? '✓' : '✗'}
                  </span>
                  <span className={result.accessibility.keyboardNavigable ? 'text-green-600' : 'text-red-600'}>
                    Keyboard Nav: {result.accessibility.keyboardNavigable ? '✓' : '✗'}
                  </span>
                  <span className={result.accessibility.colorContrast ? 'text-green-600' : 'text-red-600'}>
                    Contrast: {result.accessibility.colorContrast ? '✓' : '✗'}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Test Container (hidden) */}
      <div ref={testContainerRef} style={{ display: 'none' }} />
    </div>
  );
};

export default MinigameTestSuite;
