'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { toast } from '@/components/ui/use-toast'
import { Recycle, Trash2, Leaf, Zap, Timer, Trophy, Target, AlertTriangle } from 'lucide-react'

interface TrashItem {
  id: string
  name: string
  type: 'organic' | 'plastic' | 'paper' | 'metal' | 'e-waste' | 'glass' | 'hazardous'
  emoji: string
  carbonImpact: number // CO2 grams if disposed incorrectly
  fact: string
  x: number
  y: number
  velocity: number
}

interface GameMode {
  id: 'sort-sprint' | 'carbon-countdown' | 'daily-challenge'
  name: string
  description: string
  icon: React.ComponentType
}

interface Bin {
  type: 'organic' | 'plastic' | 'paper' | 'metal' | 'e-waste' | 'glass' | 'hazardous'
  name: string
  color: string
  emoji: string
}

const BINS: Bin[] = [
  { type: 'organic', name: '<PERSON><PERSON><PERSON>', color: 'bg-green-500', emoji: '🌱' },
  { type: 'plastic', name: 'Nhựa', color: 'bg-blue-500', emoji: '♻️' },
  { type: 'paper', name: 'Giấy', color: 'bg-yellow-500', emoji: '📄' },
  { type: 'metal', name: 'Kim Loại', color: 'bg-gray-500', emoji: '🔩' },
  { type: 'e-waste', name: 'Điện Tử', color: 'bg-purple-500', emoji: '📱' },
  { type: 'glass', name: 'Thủy Tinh', color: 'bg-cyan-500', emoji: '🍶' },
  { type: 'hazardous', name: 'Độc Hại', color: 'bg-red-500', emoji: '☢️' }
]

const TRASH_ITEMS: Omit<TrashItem, 'id' | 'x' | 'y' | 'velocity'>[] = [
  { name: 'Vỏ Chuối', type: 'organic', emoji: '🍌', carbonImpact: 50, fact: 'Vỏ chuối phân hủy trong 2-5 tuần!' },
  { name: 'Chai Nhựa', type: 'plastic', emoji: '🍼', carbonImpact: 450, fact: 'Chai nhựa mất 450 năm để phân hủy!' },
  { name: 'Báo Cũ', type: 'paper', emoji: '📰', carbonImpact: 25, fact: 'Tái chế giấy tiết kiệm 60% năng lượng!' },
  { name: 'Pin', type: 'e-waste', emoji: '🔋', carbonImpact: 1000, fact: 'Pin độc hại có thể ô nhiễm 100L nước ngầm!' },
  { name: 'Lon Nhôm', type: 'metal', emoji: '🥤', carbonImpact: 200, fact: 'Tái chế nhôm tiết kiệm 95% năng lượng!' },
  { name: 'Lõi Táo', type: 'organic', emoji: '🍎', carbonImpact: 30, fact: 'Lõi táo là phân bón tuyệt vời!' },
  { name: 'Túi Nilon', type: 'plastic', emoji: '🛍️', carbonImpact: 500, fact: 'Túi nilon mất 1000 năm để phân hủy!' },
  { name: 'Hộp Carton', type: 'paper', emoji: '📦', carbonImpact: 40, fact: 'Carton có thể tái chế 5-7 lần!' },
  { name: 'Điện Thoại Cũ', type: 'e-waste', emoji: '📱', carbonImpact: 800, fact: 'Điện thoại chứa kim loại quý hiếm!' },
  { name: 'Chai Thủy Tinh', type: 'glass', emoji: '🍶', carbonImpact: 100, fact: 'Thủy tinh có thể tái chế vô hạn!' },
  { name: 'Thuốc Hết Hạn', type: 'hazardous', emoji: '💊', carbonImpact: 600, fact: 'Thuốc hết hạn cần xử lý đặc biệt!' },
  { name: 'Vỏ Trứng', type: 'organic', emoji: '🥚', carbonImpact: 20, fact: 'Vỏ trứng giàu canxi cho đất!' }
]

const GAME_MODES: GameMode[] = [
  { id: 'sort-sprint', name: 'Sort Sprint', description: 'Phân loại rác nhanh nhất có thể!', icon: Zap },
  { id: 'carbon-countdown', name: 'Carbon Countdown', description: 'Quản lý lượng khí thải carbon!', icon: Leaf },
  { id: 'daily-challenge', name: 'Daily Challenge', description: 'Thử thách hàng ngày!', icon: Trophy }
]

export function EcoFrenzyGame() {
  const [gameMode, setGameMode] = useState<GameMode['id'] | null>(null)
  const [gameState, setGameState] = useState<'menu' | 'playing' | 'paused' | 'gameOver'>('menu')
  const [score, setScore] = useState(0)
  const [ecoScore, setEcoScore] = useState(100) // Environmental score
  const [carbonFootprint, setCarbonFootprint] = useState(0) // CO2 grams
  const [timeLeft, setTimeLeft] = useState(60)
  const [level, setLevel] = useState(1)
  const [trashItems, setTrashItems] = useState<TrashItem[]>([])
  const [selectedBin, setSelectedBin] = useState<Bin['type'] | null>(null)
  const [streak, setStreak] = useState(0)
  const [showFact, setShowFact] = useState<string | null>(null)

  // Generate random trash item
  const generateTrashItem = useCallback((): TrashItem => {
    const template = TRASH_ITEMS[Math.floor(Math.random() * TRASH_ITEMS.length)]
    return {
      ...template,
      id: Math.random().toString(36).substr(2, 9),
      x: Math.random() * 80 + 10, // 10-90% from left
      y: -10, // Start above screen
      velocity: Math.random() * 2 + 1 + level * 0.5 // Increase speed with level
    }
  }, [level])

  // Start game
  const startGame = (mode: GameMode['id']) => {
    setGameMode(mode)
    setGameState('playing')
    setScore(0)
    setEcoScore(100)
    setCarbonFootprint(0)
    setTimeLeft(mode === 'sort-sprint' ? 60 : 120)
    setLevel(1)
    setTrashItems([])
    setStreak(0)
    setShowFact(null)
  }

  // Game loop
  useEffect(() => {
    if (gameState !== 'playing') return

    const gameLoop = setInterval(() => {
      // Move trash items down
      setTrashItems(prev => prev.map(item => ({
        ...item,
        y: item.y + item.velocity
      })).filter(item => item.y < 110)) // Remove items that fall off screen

      // Add new trash items
      if (Math.random() < 0.3 + level * 0.1) {
        setTrashItems(prev => [...prev, generateTrashItem()])
      }

      // Update timer
      setTimeLeft(prev => {
        if (prev <= 1) {
          setGameState('gameOver')
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(gameLoop)
  }, [gameState, level, generateTrashItem])

  // Handle trash sorting
  const sortTrash = (trashId: string, binType: Bin['type']) => {
    const trash = trashItems.find(item => item.id === trashId)
    if (!trash) return

    const isCorrect = trash.type === binType
    
    if (isCorrect) {
      // Correct sorting
      setScore(prev => prev + 10 + streak * 2)
      setEcoScore(prev => Math.min(100, prev + 2))
      setStreak(prev => prev + 1)
      setShowFact(trash.fact)
      
      toast({
        title: "Tuyệt vời! 🎉",
        description: `+${10 + streak * 2} điểm! ${trash.fact}`,
        duration: 2000,
      })
    } else {
      // Wrong sorting
      setEcoScore(prev => Math.max(0, prev - 5))
      setCarbonFootprint(prev => prev + trash.carbonImpact)
      setStreak(0)
      
      toast({
        title: "Oops! 😅",
        description: `${trash.name} thuộc thùng ${BINS.find(b => b.type === trash.type)?.name}!`,
        variant: "destructive",
        duration: 2000,
      })
    }

    // Remove sorted item
    setTrashItems(prev => prev.filter(item => item.id !== trashId))
    
    // Clear fact after 3 seconds
    setTimeout(() => setShowFact(null), 3000)
  }

  // Level up logic
  useEffect(() => {
    const newLevel = Math.floor(score / 100) + 1
    if (newLevel > level) {
      setLevel(newLevel)
      toast({
        title: "Level Up! 🚀",
        description: `Chào mừng đến Level ${newLevel}!`,
        duration: 2000,
      })
    }
  }, [score, level])

  if (gameState === 'menu') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-400 via-blue-500 to-purple-600 p-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-6xl font-bold text-white mb-4">
              ♻️ Eco Frenzy
            </h1>
            <p className="text-2xl text-white/90 mb-2">Trash or Treasure!</p>
            <p className="text-lg text-white/80">Học với niềm vui – Sống xanh – Giảm khí thải!</p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            {GAME_MODES.map((mode, index) => (
              <motion.div
                key={mode.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer bg-white/90 backdrop-blur"
                      onClick={() => startGame(mode.id)}>
                  <div className="text-center">
                    <mode.icon className="w-12 h-12 mx-auto mb-4 text-green-600" />
                    <h3 className="text-xl font-bold mb-2">{mode.name}</h3>
                    <p className="text-gray-600">{mode.description}</p>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="bg-white/90 backdrop-blur rounded-lg p-6"
          >
            <h2 className="text-2xl font-bold mb-4 text-center">🎮 Cách Chơi</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-bold mb-2">🟢 Sort Sprint:</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Rác bay xuống màn hình! Vuốt hoặc kéo chúng vào đúng thùng rác. 
                  Sai thùng = phạt carbon! Đúng thùng = điểm eco!
                </p>
                
                <h3 className="font-bold mb-2">🔴 Carbon Countdown:</h3>
                <p className="text-sm text-gray-600">
                  Quản lý hộ gia đình trong 1 tuần. Mỗi quyết định ảnh hưởng đến 
                  lượng CO₂ hàng ngày. Giữ dưới ngưỡng cho phép!
                </p>
              </div>
              <div>
                <h3 className="font-bold mb-2">🏆 Daily Challenge:</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Cùng 10 món rác mỗi ngày cho tất cả người chơi. 
                  Chia sẻ điểm số và so sánh!
                </p>
                
                <div className="grid grid-cols-4 gap-2">
                  {BINS.slice(0, 4).map(bin => (
                    <div key={bin.type} className={`${bin.color} text-white text-xs p-2 rounded text-center`}>
                      {bin.emoji} {bin.name}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  if (gameState === 'playing') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-400 via-blue-500 to-purple-600 p-4 relative overflow-hidden">
        {/* Game HUD */}
        <div className="absolute top-4 left-4 right-4 z-10">
          <div className="flex justify-between items-center mb-4">
            <div className="flex gap-4">
              <Badge variant="secondary" className="text-lg px-4 py-2">
                <Trophy className="w-4 h-4 mr-2" />
                {score}
              </Badge>
              <Badge variant="secondary" className="text-lg px-4 py-2">
                <Leaf className="w-4 h-4 mr-2" />
                Eco: {ecoScore}%
              </Badge>
              <Badge variant="destructive" className="text-lg px-4 py-2">
                <AlertTriangle className="w-4 h-4 mr-2" />
                CO₂: {carbonFootprint}g
              </Badge>
            </div>
            <div className="flex gap-4 items-center">
              <Badge variant="outline" className="text-lg px-4 py-2">
                <Timer className="w-4 h-4 mr-2" />
                {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}
              </Badge>
              <Badge variant="outline" className="text-lg px-4 py-2">
                Level {level}
              </Badge>
              {streak > 0 && (
                <Badge variant="default" className="text-lg px-4 py-2 bg-orange-500">
                  🔥 {streak}x
                </Badge>
              )}
            </div>
          </div>

          {/* Progress bars */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <div className="flex justify-between text-sm text-white mb-1">
                <span>Eco Score</span>
                <span>{ecoScore}%</span>
              </div>
              <Progress value={ecoScore} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between text-sm text-white mb-1">
                <span>Carbon Impact</span>
                <span>{carbonFootprint}g CO₂</span>
              </div>
              <Progress value={Math.min(100, carbonFootprint / 50)} className="h-2" />
            </div>
          </div>
        </div>

        {/* Falling trash items */}
        <div className="absolute inset-0 pointer-events-none">
          <AnimatePresence>
            {trashItems.map(item => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, scale: 0 }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  x: `${item.x}%`,
                  y: `${item.y}%`
                }}
                exit={{ opacity: 0, scale: 0 }}
                className="absolute pointer-events-auto cursor-pointer"
                style={{ left: 0, top: 0 }}
                drag
                dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                onDragEnd={(event, info) => {
                  // Check if dropped on a bin
                  const dropX = info.point.x
                  const dropY = info.point.y

                  // Simple collision detection with bins at bottom
                  if (dropY > window.innerHeight - 200) {
                    const binIndex = Math.floor((dropX / window.innerWidth) * BINS.length)
                    const targetBin = BINS[Math.max(0, Math.min(BINS.length - 1, binIndex))]
                    sortTrash(item.id, targetBin.type)
                  }
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <div className="bg-white rounded-lg p-3 shadow-lg border-2 border-gray-200 min-w-[80px] text-center">
                  <div className="text-2xl mb-1">{item.emoji}</div>
                  <div className="text-xs font-medium text-gray-700">{item.name}</div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Sorting bins at bottom */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="grid grid-cols-7 gap-2">
            {BINS.map(bin => (
              <motion.div
                key={bin.type}
                className={`${bin.color} rounded-lg p-4 text-white text-center cursor-pointer relative overflow-hidden`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  // Quick sort for closest item
                  const closestItem = trashItems
                    .filter(item => item.y > 50)
                    .sort((a, b) => b.y - a.y)[0]
                  if (closestItem) {
                    sortTrash(closestItem.id, bin.type)
                  }
                }}
              >
                <div className="text-2xl mb-1">{bin.emoji}</div>
                <div className="text-xs font-medium">{bin.name}</div>

                {/* Bin highlight effect */}
                <motion.div
                  className="absolute inset-0 bg-white/20 rounded-lg"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: selectedBin === bin.type ? 1 : 0 }}
                />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Fact display */}
        <AnimatePresence>
          {showFact && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20"
            >
              <Card className="p-6 bg-white/95 backdrop-blur max-w-md text-center">
                <h3 className="font-bold text-lg mb-2">💡 Bạn có biết?</h3>
                <p className="text-gray-700">{showFact}</p>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Pause button */}
        <Button
          className="absolute top-4 right-4 z-20"
          variant="outline"
          size="sm"
          onClick={() => setGameState('paused')}
        >
          ⏸️ Tạm dừng
        </Button>
      </div>
    )
  }

  if (gameState === 'gameOver') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-400 via-blue-500 to-purple-600 p-4 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="max-w-md w-full"
        >
          <Card className="p-8 text-center bg-white/95 backdrop-blur">
            <h2 className="text-3xl font-bold mb-4">🎮 Game Over!</h2>

            <div className="space-y-4 mb-6">
              <div className="flex justify-between items-center">
                <span>Điểm số:</span>
                <Badge variant="secondary" className="text-lg">{score}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span>Eco Score:</span>
                <Badge variant={ecoScore > 70 ? "default" : "destructive"} className="text-lg">
                  {ecoScore}%
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span>Carbon Footprint:</span>
                <Badge variant="destructive" className="text-lg">{carbonFootprint}g CO₂</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span>Level đạt được:</span>
                <Badge variant="outline" className="text-lg">{level}</Badge>
              </div>
            </div>

            {/* Environmental message */}
            <div className="bg-green-50 p-4 rounded-lg mb-6">
              <h3 className="font-bold text-green-800 mb-2">🌱 Thông điệp môi trường</h3>
              <p className="text-sm text-green-700">
                {ecoScore > 80
                  ? "Tuyệt vời! Bạn là một chiến binh môi trường thực thụ! 🌟"
                  : ecoScore > 60
                  ? "Khá tốt! Hãy tiếp tục cải thiện kỹ năng phân loại rác! 💪"
                  : "Cần cải thiện! Hãy học thêm về phân loại rác để bảo vệ môi trường! 📚"
                }
              </p>
            </div>

            <div className="flex gap-4">
              <Button
                onClick={() => startGame(gameMode!)}
                className="flex-1"
              >
                🔄 Chơi lại
              </Button>
              <Button
                onClick={() => setGameState('menu')}
                variant="outline"
                className="flex-1"
              >
                🏠 Menu
              </Button>
            </div>
          </Card>
        </motion.div>
      </div>
    )
  }

  return null
}
