<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tenant Creation UI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-step {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .test-data {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <h1>🧪 Tenant Creation UI Test Suite</h1>
    <p>This page helps test the tenant creation functionality in the Reception app.</p>

    <div class="test-container">
        <h2>📋 Test Instructions</h2>
        <div class="test-step">
            <strong>Step 1:</strong> Open the Reception Tenants page in the iframe below
        </div>
        <div class="test-step">
            <strong>Step 2:</strong> Click the "Thêm khách thuê" button to open the creation modal
        </div>
        <div class="test-step">
            <strong>Step 3:</strong> Fill in the form with test data (use the buttons below to copy test data)
        </div>
        <div class="test-step">
            <strong>Step 4:</strong> Submit the form and verify the tenant is created successfully
        </div>
        <div class="test-step">
            <strong>Step 5:</strong> Check that the new tenant appears in the list
        </div>
    </div>

    <div class="test-container">
        <h2>🎯 Test Data</h2>
        <p>Use these test data sets to fill the form:</p>
        
        <h3>Test Case 1: Complete Professional</h3>
        <div class="test-data" id="testData1">
Name: Nguyễn Văn Tester
Email: <EMAIL>
Phone: **********
CCCD: 123456789012
Date of Birth: 1990-01-15
Gender: Nam
Nationality: Việt Nam
Hometown: Hà Nội
Occupation: Kỹ sư phần mềm
Industry: IT
Company: Tech Solutions Ltd
Emergency Contact Name: Nguyễn Thị Mẹ
Emergency Contact Phone: **********
Emergency Contact Relationship: Mẹ
Vehicle License Plate: 30A-12345
Vehicle Type: xe máy
Notes: Khách thuê test - tạo bởi UI test suite
        </div>
        <button onclick="copyToClipboard('testData1')">📋 Copy Test Data 1</button>

        <h3>Test Case 2: Minimal Required Fields</h3>
        <div class="test-data" id="testData2">
Name: Trần Thị Minimal
Email: <EMAIL>
Phone: **********
CCCD: 987654321098
        </div>
        <button onclick="copyToClipboard('testData2')">📋 Copy Test Data 2</button>

        <h3>Test Case 3: Student Profile</h3>
        <div class="test-data" id="testData3">
Name: Lê Minh Student
Email: <EMAIL>
Phone: **********
CCCD: 456789123456
Date of Birth: 2001-09-18
Gender: Nam
Nationality: Việt Nam
Hometown: Đà Nẵng
Occupation: Sinh viên
Industry: Giáo dục
Company: Đại học Bách Khoa
Emergency Contact Name: Lê Thị Mẹ
Emergency Contact Phone: **********
Emergency Contact Relationship: Mẹ
Notes: Sinh viên năm cuối, gia đình hỗ trợ thanh toán
        </div>
        <button onclick="copyToClipboard('testData3')">📋 Copy Test Data 3</button>
    </div>

    <div class="test-container">
        <h2>🔧 Test Controls</h2>
        <button onclick="openReceptionApp()">🚀 Open Reception App</button>
        <button onclick="refreshIframe()">🔄 Refresh</button>
        <button onclick="runAutomatedTest()">🤖 Run Automated Test</button>
        <button onclick="clearTestResults()">🧹 Clear Results</button>
    </div>

    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>🖥️ Reception App</h2>
        <div class="iframe-container">
            <iframe id="receptionFrame" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        let testResults = [];

        function addTestResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({ timestamp, message, type });
            updateTestResults();
        }

        function updateTestResults() {
            const container = document.getElementById('testResults');
            container.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.type}">
                    <strong>[${result.timestamp}]</strong> ${result.message}
                </div>`
            ).join('');
            container.scrollTop = container.scrollHeight;
        }

        function clearTestResults() {
            testResults = [];
            updateTestResults();
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                addTestResult(`Test data copied to clipboard: ${elementId}`, 'success');
            }).catch(err => {
                addTestResult(`Failed to copy test data: ${err.message}`, 'error');
            });
        }

        function openReceptionApp() {
            const iframe = document.getElementById('receptionFrame');
            iframe.src = '/minirent/apps/reception/tenants';
            addTestResult('Opening Reception Tenants page...', 'info');
            
            iframe.onload = () => {
                addTestResult('Reception app loaded successfully', 'success');
            };
            
            iframe.onerror = () => {
                addTestResult('Failed to load Reception app', 'error');
            };
        }

        function refreshIframe() {
            const iframe = document.getElementById('receptionFrame');
            iframe.src = iframe.src;
            addTestResult('Refreshing Reception app...', 'info');
        }

        async function runAutomatedTest() {
            addTestResult('Starting automated test...', 'info');
            
            try {
                // Test API endpoint
                const testTenant = {
                    name: `Test Tenant ${Date.now()}`,
                    email: `test.${Date.now()}@example.com`,
                    phone: `096${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`,
                    idNumber: Math.floor(Math.random() * 1000000000).toString().padStart(9, '0'),
                    nationality: 'Việt Nam',
                    occupation: 'Automated Test',
                    industry: 'IT',
                    notes: 'Created by automated UI test'
                };

                const response = await fetch('/api/minirent/tenants', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testTenant),
                });

                if (response.ok) {
                    const result = await response.json();
                    addTestResult(`✅ API test passed - Created tenant ID: ${result.id}`, 'success');
                    
                    // Refresh the iframe to show the new tenant
                    refreshIframe();
                } else {
                    throw new Error(`API request failed: ${response.status}`);
                }
            } catch (error) {
                addTestResult(`❌ Automated test failed: ${error.message}`, 'error');
            }
        }

        // Test checklist
        function createTestChecklist() {
            const checklist = [
                'Modal opens when "Thêm khách thuê" button is clicked',
                'All form fields are present and properly labeled',
                'Required field validation works (name, email, phone, CCCD)',
                'Email format validation works',
                'Form submits successfully with valid data',
                'Success message is shown after creation',
                'New tenant appears in the list',
                'Modal closes after successful creation',
                'Form resets after successful creation',
                'Cancel button works properly'
            ];

            const checklistHtml = checklist.map((item, index) => 
                `<div>
                    <input type="checkbox" id="check${index}" />
                    <label for="check${index}">${item}</label>
                </div>`
            ).join('');

            return `
                <div class="test-container">
                    <h3>✅ Manual Test Checklist</h3>
                    ${checklistHtml}
                </div>
            `;
        }

        // Add checklist to the page
        document.addEventListener('DOMContentLoaded', () => {
            const checklistContainer = document.createElement('div');
            checklistContainer.innerHTML = createTestChecklist();
            document.body.appendChild(checklistContainer);
            
            addTestResult('UI Test Suite loaded successfully', 'success');
            addTestResult('Click "Open Reception App" to start testing', 'info');
        });

        // Monitor iframe for errors
        window.addEventListener('message', (event) => {
            if (event.data.type === 'error') {
                addTestResult(`iframe error: ${event.data.message}`, 'error');
            } else if (event.data.type === 'success') {
                addTestResult(`iframe success: ${event.data.message}`, 'success');
            }
        });
    </script>
</body>
</html>
