/**
 * Comprehensive Test Suite for Tenant Creation in Reception App
 * This script tests the tenant creation functionality extensively
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  receptionUrl: '/minirent/apps/reception/tenants',
  apiUrl: '/api/minirent/tenants',
  dataFile: path.join(__dirname, 'data', 'minirent', 'tenants.json'),
  testTenants: [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '**********',
      idNumber: '123456789012',
      dateOfBirth: '1990-01-15',
      gender: 'male',
      nationality: 'Việt Nam',
      hometown: 'Hà Nội',
      occupation: '<PERSON><PERSON> s<PERSON> phần mềm',
      industry: 'IT',
      company: 'Tech Company',
      emergencyContactName: '<PERSON><PERSON><PERSON><PERSON>h<PERSON> Mẹ',
      emergencyContactPhone: '**********',
      emergencyContactRelationship: 'Mẹ',
      vehicleLicensePlate: '30A-12345',
      vehicleType: 'xe máy',
      notes: 'Khách thuê test - tạo bởi automated test'
    },
    {
      name: 'Trần Thị Test',
      email: '<EMAIL>',
      phone: '**********',
      idNumber: '987654321098',
      dateOfBirth: '1995-05-20',
      gender: 'female',
      nationality: 'Việt Nam',
      hometown: 'Hồ Chí Minh',
      occupation: 'Kế toán',
      industry: 'Tài chính',
      company: 'Finance Corp',
      emergencyContactName: 'Trần Văn Bố',
      emergencyContactPhone: '**********',
      emergencyContactRelationship: 'Bố',
      vehicleLicensePlate: '51B-67890',
      vehicleType: 'ô tô',
      notes: 'Khách thuê test nữ - tạo bởi automated test'
    }
  ]
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function generateRandomTenant() {
  const randomId = Math.random().toString(36).substring(2, 8);
  return {
    name: `Test Tenant ${randomId}`,
    email: `test.tenant.${randomId}@example.com`,
    phone: `096${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`,
    idNumber: Math.floor(Math.random() * **********).toString().padStart(9, '0'),
    dateOfBirth: '1990-01-01',
    gender: Math.random() > 0.5 ? 'male' : 'female',
    nationality: 'Việt Nam',
    hometown: 'Test City',
    occupation: 'Test Job',
    industry: 'IT',
    company: 'Test Company',
    emergencyContactName: 'Emergency Contact',
    emergencyContactPhone: '**********',
    emergencyContactRelationship: 'family',
    vehicleLicensePlate: '',
    vehicleType: '',
    notes: `Generated by test script at ${new Date().toISOString()}`
  };
}

async function readTenantsData() {
  try {
    if (fs.existsSync(TEST_CONFIG.dataFile)) {
      const data = fs.readFileSync(TEST_CONFIG.dataFile, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    log(`Error reading tenants data: ${error.message}`, 'error');
    return [];
  }
}

async function testApiEndpoint(tenantData) {
  try {
    log('Testing API endpoint...');
    
    const response = await fetch(`${TEST_CONFIG.baseUrl}${TEST_CONFIG.apiUrl}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tenantData),
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    log(`API test successful. Created tenant with ID: ${result.id}`, 'success');
    return result;
  } catch (error) {
    log(`API test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function testDataPersistence(tenantId) {
  try {
    log('Testing data persistence...');
    
    const tenants = await readTenantsData();
    const createdTenant = tenants.find(t => t.id === tenantId);
    
    if (!createdTenant) {
      throw new Error('Tenant not found in data file');
    }
    
    log('Data persistence test successful', 'success');
    return createdTenant;
  } catch (error) {
    log(`Data persistence test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function testFieldValidation() {
  log('Testing field validation...');
  
  const testCases = [
    {
      name: 'Missing required fields',
      data: { name: '', email: '', phone: '', idNumber: '' },
      shouldFail: true
    },
    {
      name: 'Invalid email format',
      data: { name: 'Test', email: 'invalid-email', phone: '**********', idNumber: '123456789' },
      shouldFail: true
    },
    {
      name: 'Valid minimal data',
      data: { name: 'Test User', email: '<EMAIL>', phone: '**********', idNumber: '123456789' },
      shouldFail: false
    }
  ];
  
  for (const testCase of testCases) {
    try {
      const response = await fetch(`${TEST_CONFIG.baseUrl}${TEST_CONFIG.apiUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.data),
      });
      
      if (testCase.shouldFail && response.ok) {
        log(`Validation test "${testCase.name}" failed: Expected failure but got success`, 'error');
      } else if (!testCase.shouldFail && !response.ok) {
        log(`Validation test "${testCase.name}" failed: Expected success but got failure`, 'error');
      } else {
        log(`Validation test "${testCase.name}" passed`, 'success');
      }
    } catch (error) {
      log(`Validation test "${testCase.name}" error: ${error.message}`, 'error');
    }
  }
}

async function testBulkCreation() {
  log('Testing bulk tenant creation...');
  
  const createdTenants = [];
  
  for (let i = 0; i < 5; i++) {
    try {
      const tenantData = generateRandomTenant();
      const result = await testApiEndpoint(tenantData);
      createdTenants.push(result);
      
      // Small delay to avoid overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      log(`Bulk creation test failed for tenant ${i + 1}: ${error.message}`, 'error');
    }
  }
  
  log(`Bulk creation test completed. Created ${createdTenants.length} tenants`, 'success');
  return createdTenants;
}

async function testDataIntegrity(tenantId) {
  log('Testing data integrity...');
  
  try {
    // Test GET endpoint
    const response = await fetch(`${TEST_CONFIG.baseUrl}${TEST_CONFIG.apiUrl}/${tenantId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch tenant: ${response.status}`);
    }
    
    const tenant = await response.json();
    
    // Verify required fields
    const requiredFields = ['id', 'name', 'email', 'phone', 'idNumber', 'status', 'createdAt'];
    for (const field of requiredFields) {
      if (!tenant[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    // Verify data structure
    if (!tenant.emergencyContact || typeof tenant.emergencyContact !== 'object') {
      throw new Error('Invalid emergencyContact structure');
    }
    
    if (!tenant.vehicleInfo || typeof tenant.vehicleInfo !== 'object') {
      throw new Error('Invalid vehicleInfo structure');
    }
    
    if (!tenant.paymentHistory || typeof tenant.paymentHistory !== 'object') {
      throw new Error('Invalid paymentHistory structure');
    }
    
    log('Data integrity test passed', 'success');
    return tenant;
  } catch (error) {
    log(`Data integrity test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function runAllTests() {
  log('Starting comprehensive tenant creation tests...');
  
  const testResults = {
    passed: 0,
    failed: 0,
    errors: []
  };
  
  try {
    // Test 1: Field validation
    await testFieldValidation();
    testResults.passed++;
  } catch (error) {
    testResults.failed++;
    testResults.errors.push(`Field validation: ${error.message}`);
  }
  
  // Test 2: Basic tenant creation
  try {
    const tenantData = TEST_CONFIG.testTenants[0];
    const createdTenant = await testApiEndpoint(tenantData);
    await testDataPersistence(createdTenant.id);
    await testDataIntegrity(createdTenant.id);
    testResults.passed++;
  } catch (error) {
    testResults.failed++;
    testResults.errors.push(`Basic creation: ${error.message}`);
  }
  
  // Test 3: Bulk creation
  try {
    await testBulkCreation();
    testResults.passed++;
  } catch (error) {
    testResults.failed++;
    testResults.errors.push(`Bulk creation: ${error.message}`);
  }
  
  // Test 4: Complete tenant with all fields
  try {
    const tenantData = TEST_CONFIG.testTenants[1];
    const createdTenant = await testApiEndpoint(tenantData);
    await testDataPersistence(createdTenant.id);
    await testDataIntegrity(createdTenant.id);
    testResults.passed++;
  } catch (error) {
    testResults.failed++;
    testResults.errors.push(`Complete tenant: ${error.message}`);
  }
  
  // Summary
  log('\n=== TEST SUMMARY ===');
  log(`Total tests: ${testResults.passed + testResults.failed}`);
  log(`Passed: ${testResults.passed}`, 'success');
  log(`Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'success');
  
  if (testResults.errors.length > 0) {
    log('\nErrors:');
    testResults.errors.forEach(error => log(`  - ${error}`, 'error'));
  }
  
  return testResults;
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    log(`Test execution failed: ${error.message}`, 'error');
    process.exit(1);
  });
}

export {
  runAllTests,
  testApiEndpoint,
  testDataPersistence,
  testFieldValidation,
  testBulkCreation,
  testDataIntegrity,
  generateRandomTenant
};
