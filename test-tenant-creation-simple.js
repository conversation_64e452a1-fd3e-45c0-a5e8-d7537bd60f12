/**
 * Simple Test for Tenant Creation API
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BASE_URL = 'http://localhost:3001';
const API_URL = '/api/minirent/tenants';

// Test data
const testTenants = [
  {
    name: 'Nguyễn Văn Test UI',
    email: '<EMAIL>',
    phone: '**********',
    idNumber: '123456789012',
    dateOfBirth: '1990-01-15',
    gender: 'male',
    nationality: 'Việt Nam',
    hometown: 'Hà Nội',
    occupation: '<PERSON><PERSON> sư phần mềm',
    industry: 'IT',
    company: 'Tech Company',
    emergencyContact: {
      name: '<PERSON>uyễn Thị Mẹ',
      phone: '**********',
      relationship: 'Mẹ'
    },
    vehicleInfo: {
      licensePlate: '30A-12345',
      type: 'xe máy'
    },
    notes: '<PERSON><PERSON><PERSON><PERSON> thuê test - tạo bởi UI test'
  },
  {
    name: '<PERSON><PERSON>ần Thị Minimal',
    email: '<EMAIL>',
    phone: '**********',
    idNumber: '987654321098'
  }
];

async function testCreateTenant(tenantData) {
  try {
    console.log(`🧪 Testing tenant creation: ${tenantData.name}`);
    
    const response = await fetch(`${BASE_URL}${API_URL}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tenantData),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`✅ Success! Created tenant with ID: ${result.id}`);
    
    return result;
  } catch (error) {
    console.error(`❌ Failed to create tenant: ${error.message}`);
    throw error;
  }
}

async function testGetTenants() {
  try {
    console.log('🔍 Fetching all tenants...');
    
    const response = await fetch(`${BASE_URL}${API_URL}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const tenants = await response.json();
    console.log(`✅ Found ${tenants.length} tenants`);
    
    return tenants;
  } catch (error) {
    console.error(`❌ Failed to fetch tenants: ${error.message}`);
    throw error;
  }
}

async function testGetTenant(tenantId) {
  try {
    console.log(`🔍 Fetching tenant ${tenantId}...`);
    
    const response = await fetch(`${BASE_URL}${API_URL}/${tenantId}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const tenant = await response.json();
    console.log(`✅ Found tenant: ${tenant.name}`);
    
    return tenant;
  } catch (error) {
    console.error(`❌ Failed to fetch tenant: ${error.message}`);
    throw error;
  }
}

async function runTests() {
  console.log('🚀 Starting Tenant Creation Tests...\n');
  
  let testsPassed = 0;
  let testsFailed = 0;
  
  // Test 1: Get all tenants
  try {
    await testGetTenants();
    testsPassed++;
  } catch (error) {
    testsFailed++;
  }
  
  console.log('');
  
  // Test 2: Create minimal tenant
  try {
    const minimalTenant = await testCreateTenant(testTenants[1]);
    testsPassed++;
    
    // Test 3: Verify the created tenant can be fetched
    try {
      await testGetTenant(minimalTenant.id);
      testsPassed++;
    } catch (error) {
      testsFailed++;
    }
  } catch (error) {
    testsFailed++;
  }
  
  console.log('');
  
  // Test 4: Create complete tenant
  try {
    const completeTenant = await testCreateTenant(testTenants[0]);
    testsPassed++;
    
    // Test 5: Verify the created tenant can be fetched
    try {
      await testGetTenant(completeTenant.id);
      testsPassed++;
    } catch (error) {
      testsFailed++;
    }
  } catch (error) {
    testsFailed++;
  }
  
  console.log('');
  
  // Test 6: Get all tenants again to see the new ones
  try {
    await testGetTenants();
    testsPassed++;
  } catch (error) {
    testsFailed++;
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${testsPassed}`);
  console.log(`❌ Failed: ${testsFailed}`);
  console.log(`📈 Total: ${testsPassed + testsFailed}`);
  
  if (testsFailed === 0) {
    console.log('\n🎉 All tests passed! Tenant creation is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above.');
  }
  
  return { passed: testsPassed, failed: testsFailed };
}

// Run tests
runTests().catch(error => {
  console.error('💥 Test execution failed:', error.message);
  process.exit(1);
});
