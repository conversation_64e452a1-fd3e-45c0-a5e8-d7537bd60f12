/**
 * AI Hospital Migration Test Suite
 *
 * This test suite verifies that the AI Hospital apps have been successfully
 * migrated from src/app/platforms/healthcare/aihospital/ to the main portal
 * system and that redirection works properly.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  test(name, fn) {
    this.tests.push({ name, fn });
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  assertExists(value, message) {
    if (value === null || value === undefined) {
      throw new Error(message);
    }
  }

  async run() {
    console.log('🏥 AI Hospital Migration Test Suite\n');
    console.log('=' .repeat(50));

    for (const test of this.tests) {
      try {
        console.log(`🧪 Running: ${test.name}`);
        await test.fn();
        console.log(`✅ PASSED: ${test.name}\n`);
        this.passed++;
      } catch (error) {
        console.log(`❌ FAILED: ${test.name}`);
        console.log(`   Error: ${error.message}\n`);
        this.failed++;
      }
    }

    console.log('=' .repeat(50));
    console.log(`📊 Test Results:`);
    console.log(`   Total: ${this.tests.length}`);
    console.log(`   Passed: ${this.passed}`);
    console.log(`   Failed: ${this.failed}`);
    console.log(`   Success Rate: ${((this.passed / this.tests.length) * 100).toFixed(1)}%`);

    if (this.failed > 0) {
      process.exit(1);
    }
  }
}

const runner = new TestRunner();

// Test 1: Verify AI Hospital industry configuration exists in portal
runner.test('AI Hospital industry configuration exists in portal', () => {
  const projectRoot = path.join(__dirname, '..');
  const industryConfigPath = path.join(projectRoot, 'src/app/portal/appregistry/industries/ai-hospital.ts');
  runner.assert(fs.existsSync(industryConfigPath), 'AI Hospital industry config should exist in portal');
  
  const content = fs.readFileSync(industryConfigPath, 'utf8');
  runner.assert(content.includes('aiHospitalIndustry'), 'Should export aiHospitalIndustry');
  runner.assert(content.includes("id: 'ai-hospital'"), 'Should have correct industry ID');
  runner.assert(content.includes("name: 'AI Hospital'"), 'Should have correct industry name');
  runner.assert(content.includes("icon: 'Heart'"), 'Should use Heart icon');
});

// Test 2: Verify AI Hospital is registered in industries index
runner.test('AI Hospital is registered in industries index', () => {
  const projectRoot = path.join(__dirname, '..');
  const indexPath = path.join(projectRoot, 'src/app/portal/appregistry/industries/index.ts');
  runner.assert(fs.existsSync(indexPath), 'Industries index should exist');
  
  const content = fs.readFileSync(indexPath, 'utf8');
  runner.assert(content.includes('aiHospitalIndustry'), 'Should import aiHospitalIndustry');
  runner.assert(content.includes('aiHospitalIndustry'), 'Should export aiHospitalIndustry');
});

// Test 3: Verify AI Hospital apps are properly configured
runner.test('AI Hospital apps are properly configured', () => {
  const projectRoot = path.join(__dirname, '..');
  const industryConfigPath = path.join(projectRoot, 'src/app/portal/appregistry/industries/ai-hospital.ts');
  const content = fs.readFileSync(industryConfigPath, 'utf8');
  
  // Check for key AI Hospital apps
  const expectedApps = [
    'ai-hospital-dashboard',
    'ai-patient-management',
    'ai-clinical-decision-support',
    'ai-medical-imaging',
    'ai-emergency-response'
  ];
  
  expectedApps.forEach(appId => {
    runner.assert(content.includes(appId), `Should include app: ${appId}`);
  });
  
  // Check for proper categories
  const expectedCategories = ['Management', 'Patient Care', 'Clinical', 'Emergency', 'Imaging'];
  expectedCategories.forEach(category => {
    runner.assert(content.includes(category), `Should include category: ${category}`);
  });
});

// Test 4: Verify redirection pages are implemented
runner.test('Redirection pages are implemented', () => {
  const redirectionFiles = [
    'src/app/platforms/healthcare/aihospital/page.tsx',
    'src/app/platforms/healthcare/aihospital/layout.tsx',
    'src/app/platforms/healthcare/aihospital/[appId]/page.tsx',
    'src/app/platforms/healthcare/aihospital/app/[path]/page.tsx'
  ];
  
  redirectionFiles.forEach(filePath => {
    const projectRoot = path.join(__dirname, '..');
    const fullPath = path.join(projectRoot, filePath);
    runner.assert(fs.existsSync(fullPath), `Redirection file should exist: ${filePath}`);
    
    const content = fs.readFileSync(fullPath, 'utf8');
    runner.assert(content.includes('useRouter'), 'Should use Next.js router for redirection');
    runner.assert(content.includes('portal-current-industry'), 'Should set industry in localStorage');
    runner.assert(content.includes('ai-hospital'), 'Should set AI Hospital industry');
    runner.assert(content.includes('/portal'), 'Should redirect to portal');
  });
});

// Test 5: Verify portal supports multi-industry functionality
runner.test('Portal supports multi-industry functionality', () => {
  const projectRoot = path.join(__dirname, '..');
  const multiIndustryRegistryPath = path.join(projectRoot, 'src/app/portal/appregistry/multi-industry-registry.ts');
  runner.assert(fs.existsSync(multiIndustryRegistryPath), 'Multi-industry registry should exist');

  const content = fs.readFileSync(multiIndustryRegistryPath, 'utf8');
  runner.assert(content.includes('registerIndustry'), 'Should have registerIndustry method');
  runner.assert(content.includes('setCurrentIndustry'), 'Should have setCurrentIndustry method');
  runner.assert(content.includes('getCurrentIndustry'), 'Should have getCurrentIndustry method');
});

// Test 6: Verify IndustrySelector component exists and supports AI Hospital
runner.test('IndustrySelector component supports AI Hospital', () => {
  const projectRoot = path.join(__dirname, '..');
  const selectorPath = path.join(projectRoot, 'src/app/portal/components/IndustrySelector.tsx');
  runner.assert(fs.existsSync(selectorPath), 'IndustrySelector component should exist');

  const content = fs.readFileSync(selectorPath, 'utf8');
  runner.assert(content.includes('Heart'), 'Should support Heart icon for AI Hospital');
  runner.assert(content.includes('onIndustryChange'), 'Should handle industry changes');
});

// Test 7: Verify useMultiIndustryPortal hook exists
runner.test('useMultiIndustryPortal hook exists and functions', () => {
  const projectRoot = path.join(__dirname, '..');
  const hookPath = path.join(projectRoot, 'src/app/portal/hooks/useMultiIndustryPortal.ts');
  runner.assert(fs.existsSync(hookPath), 'useMultiIndustryPortal hook should exist');

  const content = fs.readFileSync(hookPath, 'utf8');
  runner.assert(content.includes('setCurrentIndustry'), 'Should provide setCurrentIndustry function');
  runner.assert(content.includes('currentIndustry'), 'Should provide currentIndustry state');
  runner.assert(content.includes('allIndustries'), 'Should provide allIndustries state');
});

// Test 8: Verify data directory structure is preserved
runner.test('AI Hospital data directory structure is preserved', () => {
  const projectRoot = path.join(__dirname, '..');
  const dataDir = path.join(projectRoot, 'data/apps/platforms/healthcare/aihospital');
  runner.assert(fs.existsSync(dataDir), 'AI Hospital data directory should exist');
  
  // Check for key data files
  const expectedDataFiles = [
    'dashboard-data.json',
    'patients-data.json',
    'clinical-ai-data.json',
    'emergency-data.json',
    'imaging-data.json'
  ];
  
  expectedDataFiles.forEach(fileName => {
    const filePath = path.join(dataDir, fileName);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(content); // Verify it's valid JSON
      runner.assertExists(data, `Data should be valid JSON in ${fileName}`);
    }
  });
});

// Test 9: Verify README documents the migration
runner.test('README documents the migration properly', () => {
  const projectRoot = path.join(__dirname, '..');
  const readmePath = path.join(projectRoot, 'src/app/platforms/healthcare/aihospital/README.md');
  runner.assert(fs.existsSync(readmePath), 'README should exist');
  
  const content = fs.readFileSync(readmePath, 'utf8');
  runner.assert(content.includes('MIGRATED TO PORTAL'), 'Should indicate migration status');
  runner.assert(content.includes('Redirection'), 'Should document redirection');
  runner.assert(content.includes('/portal'), 'Should reference portal location');
  runner.assert(content.includes('AI Hospital industry'), 'Should mention industry configuration');
});

// Run all tests
runner.run().catch(console.error);
