// Basic tests for the learning platform functionality
// Run with: node tests/learning-platform.test.js

const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  test(name, testFn) {
    this.tests.push({ name, testFn });
  }

  async run() {
    console.log('🧪 Running Learning Platform Tests\n');

    for (const { name, testFn } of this.tests) {
      try {
        await testFn();
        console.log(`✅ ${name}`);
        this.passed++;
      } catch (error) {
        console.log(`❌ ${name}`);
        console.log(`   Error: ${error.message}\n`);
        this.failed++;
      }
    }

    console.log(`\n📊 Test Results:`);
    console.log(`   Passed: ${this.passed}`);
    console.log(`   Failed: ${this.failed}`);
    console.log(`   Total: ${this.tests.length}`);

    if (this.failed === 0) {
      console.log('\n🎉 All tests passed!');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the errors above.');
    }
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message || 'Assertion failed');
    }
  }

  assertEqual(actual, expected, message) {
    if (actual !== expected) {
      throw new Error(message || `Expected ${expected}, but got ${actual}`);
    }
  }

  assertExists(value, message) {
    if (value === null || value === undefined) {
      throw new Error(message || 'Value should exist');
    }
  }
}

const runner = new TestRunner();

// Test data file existence and structure
runner.test('Data files exist and are valid JSON', () => {
  const dataFiles = [
    'data/apps/platforms/edu/users.json',
    'data/apps/platforms/edu/enrollments.json',
    'data/apps/platforms/edu/payments.json'
  ];

  for (const filePath of dataFiles) {
    const fullPath = path.join(process.cwd(), filePath);
    runner.assert(fs.existsSync(fullPath), `Data file should exist: ${filePath}`);
    
    const content = fs.readFileSync(fullPath, 'utf8');
    const data = JSON.parse(content); // This will throw if invalid JSON
    runner.assertExists(data, `Data should be parsed from ${filePath}`);
  }
});

// Test users data structure
runner.test('Users data has correct structure', () => {
  const usersPath = path.join(process.cwd(), 'data/apps/platforms/edu/users.json');
  const usersData = JSON.parse(fs.readFileSync(usersPath, 'utf8'));
  
  runner.assertExists(usersData.users, 'Users array should exist');
  runner.assert(Array.isArray(usersData.users), 'Users should be an array');
  
  if (usersData.users.length > 0) {
    const user = usersData.users[0];
    runner.assertExists(user.id, 'User should have an ID');
    runner.assertExists(user.email, 'User should have an email');
    runner.assertExists(user.name, 'User should have a name');
    runner.assertExists(user.role, 'User should have a role');
    runner.assert(['student', 'teacher', 'admin'].includes(user.role), 'User role should be valid');
  }
});

// Test enrollments data structure
runner.test('Enrollments data has correct structure', () => {
  const enrollmentsPath = path.join(process.cwd(), 'data/apps/platforms/edu/enrollments.json');
  const enrollmentsData = JSON.parse(fs.readFileSync(enrollmentsPath, 'utf8'));
  
  runner.assertExists(enrollmentsData.enrollments, 'Enrollments array should exist');
  runner.assert(Array.isArray(enrollmentsData.enrollments), 'Enrollments should be an array');
  
  if (enrollmentsData.enrollments.length > 0) {
    const enrollment = enrollmentsData.enrollments[0];
    runner.assertExists(enrollment.id, 'Enrollment should have an ID');
    runner.assertExists(enrollment.userId, 'Enrollment should have a user ID');
    runner.assertExists(enrollment.courseId, 'Enrollment should have a course ID');
    runner.assertExists(enrollment.progress, 'Enrollment should have progress data');
    runner.assertExists(enrollment.progress.completionPercentage, 'Progress should have completion percentage');
  }
});

// Test payments data structure
runner.test('Payments data has correct structure', () => {
  const paymentsPath = path.join(process.cwd(), 'data/apps/platforms/edu/payments.json');
  const paymentsData = JSON.parse(fs.readFileSync(paymentsPath, 'utf8'));
  
  runner.assertExists(paymentsData.transactions, 'Transactions array should exist');
  runner.assertExists(paymentsData.plans, 'Plans array should exist');
  runner.assert(Array.isArray(paymentsData.transactions), 'Transactions should be an array');
  runner.assert(Array.isArray(paymentsData.plans), 'Plans should be an array');
  
  if (paymentsData.transactions.length > 0) {
    const transaction = paymentsData.transactions[0];
    runner.assertExists(transaction.id, 'Transaction should have an ID');
    runner.assertExists(transaction.userId, 'Transaction should have a user ID');
    runner.assertExists(transaction.amount, 'Transaction should have an amount');
    runner.assertExists(transaction.status, 'Transaction should have a status');
  }
});

// Test API route files exist
runner.test('API route files exist', () => {
  const apiRoutes = [
    'src/app/api/platforms/edu/auth/register/route.ts',
    'src/app/api/platforms/edu/auth/login/route.ts',
    'src/app/api/platforms/edu/enrollments/route.ts',
    'src/app/api/platforms/edu/progress/route.ts',
    'src/app/api/platforms/edu/payments/route.ts'
  ];

  for (const routePath of apiRoutes) {
    const fullPath = path.join(process.cwd(), routePath);
    runner.assert(fs.existsSync(fullPath), `API route should exist: ${routePath}`);
  }
});

// Test component files exist
runner.test('Key component files exist', () => {
  const components = [
    'src/app/platforms/edu/page.tsx',
    'src/app/platforms/edu/dashboard/page.tsx',
    'src/app/platforms/edu/teacher/page.tsx',
    'src/app/platforms/edu/admin/page.tsx',
    'src/app/platforms/edu/auth/login/page.tsx',
    'src/app/platforms/edu/auth/register/page.tsx',
    'src/app/platforms/edu/apps/edutalk/page.tsx',
    'src/app/platforms/edu/apps/edutalk/teachers/register/page.tsx'
  ];

  for (const componentPath of components) {
    const fullPath = path.join(process.cwd(), componentPath);
    runner.assert(fs.existsSync(fullPath), `Component should exist: ${componentPath}`);
  }
});

// Test utility files exist
runner.test('Utility files exist', () => {
  const utilities = [
    'src/lib/validation.ts',
    'src/lib/error-handler.ts'
  ];

  for (const utilPath of utilities) {
    const fullPath = path.join(process.cwd(), utilPath);
    runner.assert(fs.existsSync(fullPath), `Utility file should exist: ${utilPath}`);
  }
});

// Test data consistency
runner.test('Data consistency checks', () => {
  const usersPath = path.join(process.cwd(), 'data/apps/platforms/edu/users.json');
  const enrollmentsPath = path.join(process.cwd(), 'data/apps/platforms/edu/enrollments.json');
  
  const usersData = JSON.parse(fs.readFileSync(usersPath, 'utf8'));
  const enrollmentsData = JSON.parse(fs.readFileSync(enrollmentsPath, 'utf8'));
  
  const userIds = usersData.users.map(user => user.id);
  
  // Check that all enrollments reference valid users
  for (const enrollment of enrollmentsData.enrollments) {
    runner.assert(
      userIds.includes(enrollment.userId),
      `Enrollment ${enrollment.id} references invalid user ${enrollment.userId}`
    );
  }
});

// Test validation functions
runner.test('Validation functions work correctly', () => {
  // This would require importing the validation module
  // For now, we'll just check that the file exists and is valid TypeScript
  const validationPath = path.join(process.cwd(), 'src/lib/validation.ts');
  const content = fs.readFileSync(validationPath, 'utf8');
  
  runner.assert(content.includes('export class Validator'), 'Validation file should export Validator class');
  runner.assert(content.includes('email'), 'Validation should include email validation');
  runner.assert(content.includes('password'), 'Validation should include password validation');
});

// Test configuration and environment
runner.test('Environment configuration', () => {
  // Check that essential directories exist
  const directories = [
    'data',
    'data/apps',
    'data/apps/platforms/edu',
    'src/app/platforms/edu',
    'src/app/api/platforms/edu'
  ];

  for (const dir of directories) {
    const fullPath = path.join(process.cwd(), dir);
    runner.assert(fs.existsSync(fullPath), `Directory should exist: ${dir}`);
  }
});

// Run all tests
runner.run().catch(console.error);
